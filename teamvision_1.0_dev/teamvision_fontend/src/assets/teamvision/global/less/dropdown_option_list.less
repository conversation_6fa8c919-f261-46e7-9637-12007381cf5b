@import 'app-global-variables.less';
@import 'global-base.less';


.dorpdown_option_menu
{
  position:absolute;
  margin-left:30px;
  z-index:10000;
  display:none;
  background:#FFF;
}

.dorpdown_option_menu_left
{
  position:absolute;
  margin-left:-140px;
  z-index:10000;
  display:none;
  background:#FFF;
}

.dorpdown_option_list
    {
      list-style:none;
      border: 1px solid #f0f0f0;
      width:180px;
      z-index:9999;
      box-shadow:-5px 5px 5px  rgba(0, 0, 0, 0.05);
      border-radius:5px;
      max-height:180px;
      overflow-y:scroll;
    }
.dorpdown_option_list .option-item
{
	margin-left:-40px;
	height:40px;
	border-bottom: 1px solid #f0f0f0;
	vertical-align:middle;
	cursor:pointer;
	padding-top:15px;
	font-weight: 400;
	font-size:13px;
	color:#555;
}

.option-item  .selected
{
	display:inline-block;
}


.option-item span i
{
	display:inline-block;
}


.top{
     width:20px;
     height:20px;
     position:absolute;
     left:45px;
     top:-20px;
     z-index: 100;/*兼容ie8-*/
            }
.top-left{
     width:20px;
     height:20px;
     position:absolute;
     left:150px;
     top:-20px;
     z-index: 100;/*兼容ie8-*/
            }
.top-arrow1,.top-arrow2{
                width:0;
                height:0;
                display:block;
                position:absolute;
                left:0;
                top:0;
                z-index: 5;/*兼容ie8-*/
                border-top:10px transparent dashed;
                border-left:10px transparent dashed;
                border-right:10px transparent dashed;
                border-bottom:10px white solid;
                overflow:hidden;
            }
.top-arrow1{
                border-bottom:10px #f0f0f0 solid;
}
.top-arrow2{
   top:1px;/*重要*/
   border-bottom:10px white solid;
}
