/*通用字体类*/
@import "app-global-variables.less";
@import "global-left-sub-nav.less";

.font-family(@font-family:@body-font-family)
{
  font-family:@font-family;
}

.app-head-background(@background-color:@app-head-background)
{
  background-color:@background-color;
  border: none;
}

.master-page-header-menu-hover(@background-color:@app-head-menu-item-background)
{
  background-color:@background-color;
  // border-color:@border-color;
  border:none;
}


.doraemon-button(@background-color:@button-backaground)
{
  background-color:@background-color;
  color:white;
  font-family:@body-font-family;
  padding:5px 10px 5px 10px;
  .borderradius(3px);
}

.doraemon-font(@font-family:@body-font-family,@font-size:@menu-size,@color:@header-font-color)
{
   font-family:@body-font-family;
  font-size:@font-size;
  color:@color;
}

body
{
  margin: @body-margin;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  background-color:@body-background;
  .font-family();
  display: block;
  height:100%;
  color: #5578aa;
}


#mainContainer {
  height: @master-page-maincontaner-height;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  padding: 60px 0 0 0;
  width: 100%;
}

/*默认按钮样式*/
.button {
  width: 50px;
  line-height: 20px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 1px #333;
  border-radius: 5px;
  /*margin:0 20px 20px 0;*/
  position: relative;
  overflow: hidden;
}

.button.gray
{
  color: #8c96a0;
  text-shadow: 1px 1px 1px #fff;
  border: 1px solid #dce1e6;
  box-shadow: 0 1px 2px #fff inset, 0 -1px 0 #a8abae inset;
  background: -webkit-linear-gradient(to top,#f2f3f7,#e4e8ec);
  background: -moz-linear-gradient(to top,#f2f3f7,#e4e8ec);
  background: linear-gradient(to top,#f2f3f7,#e4e8ec);
}

.operationbutton {
  cursor: pointer;
}

.listview-window-container
{
  padding: 20px 20px 20px 20px;
}

.listview-window
{
  width: 100%;
  height:100%;
}

.listview-window-title
{
  border-bottom: 1px solid @listitem_border_color;
  font-weight: 400;
  font-size:16px;
  padding-top:10px;
  margin-bottom:20px;
  height:40px;
}

.listview-item(@height:70px)
{
  height:@height;
  border-bottom: 1px solid @listitem_border_color;
  margin-left: 0px;
  padding: 5px 10px 10px 0px;
}

.label(@font-size:12px,@color:rgb(255, 255, 255),@background:#999)
{
    font-size: @font-size;
  margin-right: 5px;
  color: rgb(255, 255, 255);
    background:@background;
  border-radius: 3px;
    line-height: 1em;
    padding: 2px 4px;
}

.android-color
{
  color: @androidcolor;
}

.page-content-container()
{
//  padding-top:@page-content-container-padding-top;
//  padding-left:@page-content-container-padding-left;
//  padding-right:@page-content-container-padding-right;
}

.borderradius(@radius) {
   border-top-right-radius: @radius;
   border-top-left-radius: @radius;
   border-bottom-right-radius: @radius;
   border-bottom-left-radius: @radius;
}

.left_sub_nav
{
  .sub-leftnav();
}

.unfinished-check
{
  color:@check-unfinished-color;
  margin-right:30px;
}

.finished-check
{
  color:@check-finished-color;
  margin-right:30px;
}


.postion-absolute
{
  position:absolute;
}

.filed-pop-object(@opacity:0.9)
{
  position:absolute;
  margin-top:5px;
  opacity:@opacity;
  z-index:9000;
  border-radius:7px;
  box-shadow:-5px 5px 5px rgba(0, 0, 0, 0.1);
}

.filed-pop-calender
{
  .filed-pop-object();
}

.filed-pop-meun
{
  .filed-pop-object(@context-menu-opacity);
}


.modal-dialog-form-middle
{
//  margin-top: @modal-dialog-margin-top;
}


.middle-modal-dialog
{
  margin-top:0px;
}

.modal-dialog-alert-middle
{
  margin-top:40%;
}

a
{
  color:#FFA487;
  cursor:pointer;
}

.status-success
{
  color:@green-color;
}

.status-fail
{
  color:red;
}

.status-default
{
  color:#009ad6;
  opacity:0.7;
}

.status-cancel
{
  color:#f58220;
  opacity:0.7;
}


.status-background-success
{
  background-color:@green-color;
}

.status-background-fail
{
  background-color:red;
}

.status-background-default
{
  background-color:#009ad6;
  opacity:0.7;
}

.status-background-cancel
{
  background-color:#f58220;
  opacity:0.7;
}
