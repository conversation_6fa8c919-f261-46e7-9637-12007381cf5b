@app-background:#334866;

/*通用够公共变量*/
@button-backaground:#019775;
@button-color:white;

// @green-color:#2eb06f;

@green-color:#32be77;
@white-color:#FFF;

//网站title字体大小
@title-size:20px;

//一级菜单字体大小：14px
@menu-size:14px;

//页面内容字体大小：12px
@content-size:12px;

@sub-menu-size:13px;

//最小字体10px

@small-size:10px;

// @title-default-background:#d3d7d4;
@head-default-background:#f6f5ec;

@check-unfinished-color:gray;
@check-finished-color:#019775;
@context-menu-opacity:0.9;

/*母版页header属性设置*/
@app-head-height:48px;

// @master-page-header-background: #474D57 rgba(246,245,241,1);
@app-head-background:#ffffff;
@app-head-width: 100%;
@app-head-position: fixed;
@app-head-z-index: 1000;
@app-head-color:#fff;
@app-head-menu-item-width:60px;
@app-head-menu-item-height:60px;
@app-head-menu-item-background:#019775;
// @header-font-color:white;
@header-font-color:#fff;



/*系统 body 页面字体*/
@body-font-family:'微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
@body-background:#eef2f6;
@body-margin:0px 0px 0px 0px;


/*页面主容器属性*/
@master-page-maincontaner-height:89%;
@app-body-head-height:48px;
@app-body-head-leftbar-left:0px;



/*页面左边栏属性变量*/
@master-page-leftnav-width:90px;
@project-page-headnav-width:80px;
// @master-page-leftnav-background:rgba(246,245,241,1) #474D57;
@master-page-leftnav-background:#333;
@master-page-leftnav-right_border-color:#f0f0f0;
// @leftnav-font-color:#555;
@leftnav-font-color:#6a6c6f;


/*left subnav 变量*/
@left-subnav-width:160px;


@androidcolor:#1d953f;

/*page conent container 样式*/
@page-content-container-padding-top:30px;
@page-content-container-padding-left:70px;
@page-content-container-padding-right:50px;

/*list item分隔线颜色*/
@listitem_border_color:#eef2f6;


@modal-dialog-margin-top:100px;


@menu-item-hover-color:rgba(255,255,255,.1);
@app-body-head-item-active-color:#dde4ee;

@sub-menu-item-hover-color:rgba(0,0,0,.02);
@sub-menu-item-active-color:rgba(0,0,0,.05);


/* 草绿色button 样式*/

@button_default_height:30px;
@button_default_width:150px;

