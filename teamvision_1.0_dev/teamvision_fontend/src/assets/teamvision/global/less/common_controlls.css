/*通用够公共变量*/
/*母版页header属性设置*/
/*系统 body 页面字体*/
/*页面主容器属性*/
/*页面左边栏属性变量*/
/*left subnav 变量*/
/*page conent container 样式*/
/*list item分隔线颜色*/
/* 草绿色button 样式*/
/*通用字体类*/
body {
  margin: 0px 0px 0px 0px;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  background-color: #eef2f6;
  font-family: '微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
  display: block;
  height: 100%;
  color: #5578aa;
}
#mainContainer {
  height: 89%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  padding: 60px 0 0 0;
  width: 100%;
}
/*默认按钮样式*/
.button {
  width: 50px;
  line-height: 20px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 1px #333;
  border-radius: 5px;
  /*margin:0 20px 20px 0;*/
  position: relative;
  overflow: hidden;
}
.button.gray {
  color: #8c96a0;
  text-shadow: 1px 1px 1px #fff;
  border: 1px solid #dce1e6;
  box-shadow: 0 1px 2px #fff inset, 0 -1px 0 #a8abae inset;
  background: -webkit-linear-gradient(to top, #f2f3f7, #e4e8ec);
  background: -moz-linear-gradient(to top, #f2f3f7, #e4e8ec);
  background: linear-gradient(to top, #f2f3f7, #e4e8ec);
}
.operationbutton {
  cursor: pointer;
}
.listview-window-container {
  padding: 20px 20px 20px 20px;
}
.listview-window {
  width: 100%;
  height: 100%;
}
.listview-window-title {
  border-bottom: 1px solid #eef2f6;
  font-weight: 400;
  font-size: 16px;
  padding-top: 10px;
  margin-bottom: 20px;
  height: 40px;
}
.android-color {
  color: #1d953f;
}
.left_sub_nav {
  width: 160px;
  float: left;
  height: 100%;
  padding: 0px 0px 0px 0px;
  position: fixed;
  border-right: 1px solid #f0f0f0;
}
.unfinished-check {
  color: gray;
  margin-right: 30px;
}
.finished-check {
  color: #019775;
  margin-right: 30px;
}
.postion-absolute {
  position: absolute;
}
.filed-pop-calender {
  position: absolute;
  margin-top: 5px;
  opacity: 0.9;
  z-index: 9000;
  border-radius: 7px;
  box-shadow: -5px 5px 5px rgba(0, 0, 0, 0.1);
}
.filed-pop-meun {
  position: absolute;
  margin-top: 5px;
  opacity: 0.9;
  z-index: 9000;
  border-radius: 7px;
  box-shadow: -5px 5px 5px rgba(0, 0, 0, 0.1);
}
.middle-modal-dialog {
  margin-top: 0px;
}
.modal-dialog-alert-middle {
  margin-top: 40%;
}
a {
  color: #FFA487;
  cursor: pointer;
}
.status-success {
  color: #32be77;
}
.status-fail {
  color: red;
}
.status-default {
  color: #009ad6;
  opacity: 0.7;
}
.status-cancel {
  color: #f58220;
  opacity: 0.7;
}
.status-background-success {
  background-color: #32be77;
}
.status-background-fail {
  background-color: red;
}
.status-background-default {
  background-color: #009ad6;
  opacity: 0.7;
}
.status-background-cancel {
  background-color: #f58220;
  opacity: 0.7;
}
/*左侧子导航样式#########################################################################*/
.left_sub_menu_container {
  position: fixed;
}
.left_sub_menu_container ul {
  width: 160px;
  text-align: center;
}
.left_sub_menu_item {
  color: #555 !important;
  font-size: 13px;
}
.left_sub_menu_button {
  color: #555 !important;
  font-size: 13px;
}
.left_sub_menu_item_hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.left_sub_meun_active {
  background-color: rgba(0, 0, 0, 0.05) !important;
}
.leftmenu-plus {
  width: 90% !important;
  margin-left: 5px;
  margin-top: 10px;
  padding-left: -5px;
  border: 2px dashed #a1a3a6;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  font-size: 15px !important;
}
.button-inlist {
  height: 25px;
  margin-left: 10%;
  margin-right: 35%;
  margin-bottom: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  opacity: 0.8;
  color: #FFF !important;
}
.remove_icon-inlist {
  color: #d9534f;
  opacity: 0.8;
  display: none;
  padding-top: 10px;
  margin-right: -10px;
}
.sub_side_bar {
  width: 160px;
  float: left;
  height: 100%;
  padding: 0px 0px 0px 0px;
  position: fixed;
  border-right: 1px solid #f0f0f0;
}
.sub_sidebar_brother {
  padding-left: 140px;
  width: 100%;
  float: left;
}
.sub_bar_header {
  padding: 20px 10px 20px 30px;
}
.doraemon-button-default {
  background-color: #019775;
  color: white;
  font-family: '微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
  padding: 5px 10px 5px 10px;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.content-editable {
  padding: 5px;
}
.content-editable:hover {
  padding: 5px;
  background: #d3d7d4;
}
.splitier {
  margin: 10px 0px 10px 0px;
  width: 100%;
  height: 10px;
  border-top: 1px solid #f0f0f0;
}
.login-button {
  height: 25px;
  margin-left: 10px;
  margin-right: 50px;
  margin-top: 15px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  border: 1px solid #555;
  background-color: white!important;
  width: 80px;
  float: right !important;
  color: #767676 !important;
}
.green_radius_button {
  height: 25px;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  display: block;
  text-align: center;
  color: #FFF !important;
  height: 30px !important;
  width: 100px !important;
  opacity: 0.8;
}
.danger_radius_button {
  height: 25px;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  display: block;
  text-align: center;
  color: #FFF !important;
  height: 30px !important;
  width: 100px !important;
  opacity: 0.6;
  background: red !important;
}
.small_green_radius_button {
  height: 25px;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  display: block;
  text-align: center;
  color: #FFF !important;
  height: 25px !important;
  width: 50px !important;
  padding-top: 0px !important;
  opacity: 0.8;
}
.small_gray_radius_button {
  height: 25px;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  display: block;
  text-align: center;
  color: #FFF !important;
  height: 25px !important;
  width: 50px !important;
  padding-top: 2px !important;
  opacity: 0.8;
  background-color: #EEEEEE !important;
  color: #555 !important;
}
.property_nav_button {
  height: 30px;
  padding: 3px 15px 3px 15px !important;
  font-size: 14px;
  border-radius: 10px;
  opacity: 0.8;
  border: 1px dashed #a1a3a6;
  width: 100px;
}
.pn_btn_success {
  height: 30px;
  padding: 3px 15px 3px 15px !important;
  font-size: 14px;
  border-radius: 10px;
  opacity: 0.8;
  border: 1px dashed #a1a3a6;
  width: 100px;
  background-color: #32be77 !important;
  border: none;
  color: #FFF;
}
.pn_btn_danger {
  height: 30px;
  padding: 3px 15px 3px 15px !important;
  font-size: 14px;
  border-radius: 10px;
  opacity: 0.8;
  border: 1px dashed #a1a3a6;
  width: 100px;
  background-color: red!important;
  border: none;
  color: #FFF;
}
.cursor-hand {
  cursor: pointer;
}
.row-operation-group {
  border-radius: 15px;
  border: 1px dashed #a1a3a6;
  background: #f6f5f1;
  padding: 5px 10px 5px 10px;
  opacity: 0.6;
  z-index: 1000;
}
.row-operation {
  margin-top: 30px;
  display: inline-block;
}
.mark {
  height: 100%;
  width: 100%;
  position: fixed;
  _position: absolute;
  top: 0;
  z-index: 1005;
  opacity: 0.8;
  background: #333;
}
.input-2-label {
  border: none;
  outline: medium;
  box-shadow: none;
}
.notify_container {
  position: fixed;
  top: 20px;
  left: 45%;
  z-index: 1200;
}
.nic-square-default {
  width: 100px;
  height: 80px;
  background-color: #555;
  border-radius: 5px;
  display: inline-block;
  border: 1px solid #555;
}
.nic-content-default {
  height: 80px;
  width: 80px;
  line-height: 80px;
  display: inline-block;
  color: #00ff00;
  text-align: center;
}
.nic-circle-30-none {
  width: 30px;
  height: 30px;
  background-color: none;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 15px;
  margin-right: 10px;
}
.nic-circle-30-white {
  width: 30px;
  height: 30px;
  background-color: #FFF;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 15px;
  margin-right: 10px;
  border: none;
}
.nic-circle-50-green {
  width: 80px;
  height: 80px;
  background-color: #32be77;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 40px;
  margin-right: 10px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.24) !important;
}
.nic-content-50-white {
  height: 80px;
  width: 80px;
  line-height: 80px;
  display: inline-block;
  color: #FFF;
  text-align: center;
  font-size: 14px;
}
.nic-circle-25-gblue {
  width: 25px;
  height: 25px;
  background-color: #5B8EC8;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 12.5px;
  margin-right: 10px;
  border: none;
}
.nic-content-25-white {
  height: 25px;
  width: 25px;
  line-height: 25px;
  display: inline-block;
  color: #FFF;
  text-align: center;
  font-size: 13px;
}
.nic-circle-30-green {
  width: 30px;
  height: 30px;
  background-color: #32be77;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 15px;
  margin-right: 10px;
  border: none;
}
.nic-content-30-white {
  height: 30px;
  width: 30px;
  line-height: 30px;
  display: inline-block;
  color: #FFF;
  text-align: center;
  font-size: 14px;
}
.nic-circle-20-green {
  width: 25px;
  height: 25px;
  background-color: #32be77;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 12.5px;
  margin-right: 10px;
  border: none;
}
.nic-circle-30-orange {
  width: 30px;
  height: 30px;
  background-color: #32be77;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 15px;
  margin-right: 10px;
  border: none;
  background-color: #993D00;
}
.nic-content-20-white {
  height: 25px;
  width: 25px;
  line-height: 25px;
  display: inline-block;
  color: #FFF;
  text-align: center;
  font-size: 12px;
}
.nic-circle-30-blue {
  width: 30px;
  height: 30px;
  background-color: #32be77;
  border-radius: 25px;
  display: inline-block;
  border: 1px solid #555;
  border-radius: 15px;
  margin-right: 10px;
  border: none;
  background-color: #337ab7;
  opacity: 1;
}
.nic-content-30-white-10fontsize {
  height: 30px;
  width: 30px;
  line-height: 30px;
  display: inline-block;
  color: #FFF;
  text-align: center;
  font-size: 10px;
}
.nic-number-30-white {
  height: 30px;
  width: 30px;
  line-height: 30px;
  display: inline-block;
  color: #555;
  text-align: center;
}
.nic-number-30-none {
  height: 30px;
  width: 30px;
  line-height: 30px;
  display: inline-block;
  color: none;
  text-align: center;
}
.readlony-background {
  background-color: white!important;
}
#TestingFeature {
  padding: 0px;
}
#TestingAdvice {
  padding: 0px;
}
.portal-list-container {
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  min-height: 300px;
}
