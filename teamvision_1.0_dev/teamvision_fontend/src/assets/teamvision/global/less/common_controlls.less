@import "app-global-variables.less";
@import "global-base.less";
@import "global-left-sub-nav.less";


.doraemon-button-default
{
  .doraemon-button();
}

.content-editable
{
  padding:5px;
}

.content-editable:hover
{
  padding:5px;
  background:#d3d7d4;
}

.splitier
{
  margin:10px 0px 10px 0px;

  width:100%;
  height:10px;
  border-top:1px solid @master-page-leftnav-right_border-color;
}

.login-button
{
  height:25px;
  margin-left:10px;
  margin-right:50px;
  margin-top:15px;
  padding-top:3px !important;
  font-size:14px;
  border-radius:15px;
  border: 1px solid #555;
  background-color:white!important;
  width:80px;
  float:right !important;
  // opacity:0.8;
  color: #767676 !important;
}

.radius_button(@background:@green-color,@color:@white-color)
{
  height:25px;
  margin-left:10px;
  margin-right:10px;
  margin-top:10px;
  padding-top:3px !important;
  font-size:14px;
  border-radius:15px;
  background-color:@background!important;
  display:block;
  text-align: center;
  // opacity:0.8;
  color: @color !important;
}

.green_radius_button
{
  .radius_button();
  height:30px !important;
  width:100px !important;
  opacity:0.8;

}

.danger_radius_button
{
  .radius_button();
  height:30px !important;
  width:100px !important;
  opacity:0.6;
  background:red !important;

}



.small_green_radius_button
{
  .radius_button();
  height:25px !important;
  width:50px !important;
  padding-top:0px !important;
  opacity:0.8;

}

.small_gray_radius_button
{
  .radius_button();
  height:25px !important;
  width:50px !important;
  padding-top:2px !important;
  opacity:0.8;
  background-color:#EEEEEE !important;
  color:#555 !important;

}

.property_nav_button
{
  height:30px;
  padding:3px 15px 3px 15px !important;
  font-size:14px;
  border-radius:10px;
  opacity:0.8;
  border: 1px dashed #a1a3a6;
  width:100px;

}

.pn_btn_success
{
  .property_nav_button();
  background-color:#32be77!important;
  border:none;
  color:#FFF;

}

.pn_btn_danger
{
  .property_nav_button();
  background-color:red!important;
  border:none;
  color:#FFF;

}


.fileupload(@width,@height)
{
  float:left;
  margin:0px 20px 10px 20px;
  position:relative;
  width:@width;
  height:@height;
  border-radius:10px;
  border: 2px dashed #a1a3a6;
  cursor:pointer;
}

.fileupload_span(@margin)
{
  margin:@margin;
}

.fileupload_input(@width,@height)
{
  top:0;
  // filter:alpha(opacity:0);
  opacity: 0;
  width:@width;
  height:@height;
  position:absolute;
}


.cursor-hand
{
  cursor:pointer;
}

.row-operation-group
{
  border-radius:15px;
  border: 1px dashed #a1a3a6;
  background: #f6f5f1;
  padding: 5px 10px 5px 10px;
  opacity:0.6;
  z-index:1000;
}


.split-line(@margin-left:70px,@width:70%)
{
  height:1px;
  width:@width;
  background:#f0f0f0;
  margin-left:@margin-left;
  margin-top:-10px;
  overflow:hidden;
}

.row-operation
{
  margin-top:30px;
  display:inline-block;
}

.mark
{
  height:100%;
  width:100%;
  position:fixed;
  _position:absolute;
  top:0;
  z-index:1005;
  opacity:0.8;
  background:@master-page-leftnav-background;
}

.input-2-label
{
  border:none;
  outline:medium;
  box-shadow:none;
}

.notify_container
{
  position:fixed;
  top:20px;
  left:45%;
  z-index:1200;
}

.nic-circle(@width:50px,@height:50px,@background-color:#F00)
{
  width:@width;
  height:@height;
  background-color:@background-color;
  border-radius:25px;
  display:inline-block;
  border:1px solid #555;
}

.nic-square(@width:100px,@height:80px,@background-color:#555)
{
  width:@width;
  height:@height;
  background-color:@background-color;
  border-radius:5px;
  display:inline-block;
  border:1px solid #555;
}


.nic-content(@line-height:80px,@height:80px,@color:#00ff00)
{
  height:@height;
  width:@height;
  line-height:@line-height;
  display:inline-block;
  color:@color;
  text-align:center;
}

.nic-square-default
{
	.nic-square();
}

.nic-content-default
{
	.nic-content();
}



.nic-circle-30-none
{
  .nic-circle(30px,30px,none);
  border-radius:15px;
  // margin-top:8px;
  margin-right:10px;
}

.nic-circle-30-white
{
  .nic-circle(30px,30px,#FFF);
  border-radius:15px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
}

.nic-circle-50-green
{
  .nic-circle(80px,80px,#32be77);
  border-radius:40px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.24) !important;
}


.nic-content-50-white
{
  .nic-number(80px,80px,#FFF);
  font-size:14px;
}

.nic-circle-25-gblue
{
  .nic-circle(25px,25px,#5B8EC8);
  border-radius:12.5px;
  // margin-top:8px;
  margin-right:10px;
  border:none;

}

.nic-content-25-white
{
  .nic-number(25px,25px,#FFF);
  font-size:13px;
}


.nic-circle-30-green
{
  //#32be77

  .nic-circle(30px,30px,#32be77);
  border-radius:15px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
}

.nic-content-30-white
{
  .nic-number(30px,30px,#FFF);
  font-size:14px;
}

.nic-circle-20-green
{
  .nic-circle(25px,25px,#32be77);
  border-radius:12.5px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
}

.nic-circle-30-orange
{
  .nic-circle(30px,30px,#32be77);
  border-radius:15px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
  background-color: #993D00;
}

.nic-content-20-white
{
  .nic-number(25px,25px,#FFF);
  font-size:12px;
}

.nic-circle-30-blue
{
  .nic-circle(30px,30px,#32be77);
  border-radius:15px;
  // margin-top:8px;
  margin-right:10px;
  border:none;
  background-color:#337ab7;
  opacity:1;
}

.nic-content-30-white-10fontsize
{
  .nic-number(30px,30px,#FFF);
  font-size:10px;
}


.nic-number(@line-height:50px,@height:50px,@color:#F00)
{
  height:@height;
  width:@height;
  line-height:@line-height;
  display:inline-block;
  color:@color;
  text-align:center;
}

.nic-number-30-white
{
  .nic-number(30px,30px,#555);
}
.nic-number-30-none
{
  .nic-number(30px,30px,none);
}

.readlony-background
{
  background-color:white!important;
}

#TestingFeature
{
	padding:0px;
}


#TestingAdvice
{
	padding:0px;
}

.portal-list-container
{
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  min-height: 300px;
}
