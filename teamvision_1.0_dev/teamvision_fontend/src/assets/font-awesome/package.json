{"_args": [["font-awesome@4.7.0", "/Users/<USER>/Documents/OneDrive/coding.net/vue/firstVue"]], "_development": true, "_from": "font-awesome@4.7.0", "_id": "font-awesome@4.7.0", "_inBundle": false, "_integrity": "sha1-j6jPBBGhoxr9B7BtKQK7n8gVoTM=", "_location": "/font-awesome", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "font-awesome@4.7.0", "name": "font-awesome", "escapedName": "font-awesome", "rawSpec": "4.7.0", "saveSpec": null, "fetchSpec": "4.7.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/font-awesome/-/font-awesome-4.7.0.tgz", "_spec": "4.7.0", "_where": "/Users/<USER>/Documents/OneDrive/coding.net/vue/firstVue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/davegandy"}, "bugs": {"url": "http://github.com/FortAwesome/Font-Awesome/issues"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/talbs"}, {"name": "<PERSON>", "url": "http://twitter.com/supercodepoet"}, {"name": "<PERSON>", "url": "http://twitter.com/robmadole"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://twitter.com/gtagliala"}], "dependencies": {}, "description": "The iconic font and CSS framework", "engines": {"node": ">=0.10.3"}, "homepage": "http://fontawesome.io/", "keywords": ["font", "awesome", "fontawesome", "icon", "font", "bootstrap"], "license": "(OFL-1.1 AND MIT)", "name": "font-awesome", "repository": {"type": "git", "url": "git+https://github.com/FortAwesome/Font-Awesome.git"}, "style": "css/font-awesome.css", "version": "4.7.0"}