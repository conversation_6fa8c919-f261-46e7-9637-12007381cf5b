import axios from "axios"


export const getDocumentFolderList = () => {
  let reqUrl = '/api/document/dirs'
  return axios.get(
    reqUrl
  )
}
export const createFolderApi = (data) => {
  let reqUrl = '/api/document/dirs'
  return axios.post(
    reqUrl,
    data
  )
}
export const deleteFolderApi = (dirID) => {
  let reqUrl = '/api/document/dir/' + dirID

  return axios.delete(
    reqUrl,
  )
}

export const updateFolderApi = (dirID, reqData) => {
  let reqUrl = '/api/document/dir/' + dirID
  return axios.patch(
    reqUrl,
    reqData
  )
}

export const getFolderDetailApi = (dirID) => {
  let reqUrl = '/api/document/dir/' + dirID
  return axios.get(
    reqUrl
  )
}

export const getDocumentDetailApi = (documentID) => {
  let reqUrl = '/api/document/document/' + documentID
  return axios.get(
    reqUrl
  )
}

export const patchDocumentDetailApi = (documentID, data) => {
  let reqUrl = '/api/document/document/' + documentID
  return axios.patch(
    reqUrl,
    data
  )
}

export const getDocumentListApi = (urlArgs) => {
  let reqUrl = '/api/document/documents' + urlArgs
  return axios.get(
    reqUrl
  )
}

export const createDocumentApi = (data) => {
  console.log
  let reqUrl = '/api/document/documents'
  return axios.post(
    reqUrl,
    data
  )
}

export const deleteDocument = (documentID) => {
  let reqUrl = '/api/document/document/' + parseInt(documentID)
  return axios.delete(
    reqUrl,
  )
}

export const setFileStatus = (documentID, data) => {
  return axios.request({
    method: 'patch',
    url: '/api/document/document/' + documentID
  })
}

export const getEnumList = () => {
  return axios.request({
    method: 'get',
    url: '/api/document/enum'
  })
}
