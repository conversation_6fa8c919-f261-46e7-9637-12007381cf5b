import axios from "axios"

export const login = ({ email, password }) => {
  const data = {
    email,
    password
  }
  return axios.post(
    '/api/common/user/login',
    data
  )
}

export const getUserInfo = (token) => {
  return axios.get(
    '/api/common/user/0'
  )
}

export const logout = (token) => {
  return axios.delete(
    '/api/common/user/logout'
  )
}

export const getSpaceUserListApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/common/users/list'
  })
}
