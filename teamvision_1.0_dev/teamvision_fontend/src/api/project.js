import axios from "axios"

export const getProjectListApi = urlArgs => {
  let reqUrl = '/api/project/list?my=1'
  reqUrl = reqUrl + '&' + urlArgs
  return axios.get(
    reqUrl
  )
}

export const getProjectCaseReviewListApi = (projectID, urlArgs = '') => {
  let reqUrl = '/api/project/' + projectID + '/casereview'
  if (urlArgs != '') {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(
    reqUrl
  )
}

export const getIssueStatusApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/project/issue/status'
  })
}

export const getIssueSeverityApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/project/issue/severities'
  })
}

export const getIssuePhraseApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/project/issue/project_phrase'
  })
}

export const getIssueCategoriesApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/project/issue/categories'
  })
}

export const getIssuePriorityApi = () => {
  return axios.request({
    method: 'get',
    url: '/api/project/issue/priority'
  })
}
