import store from '../store/index'
import WebApp from '../layout/WebApp.vue'
import EmptyHead from '../layout/EmptyBodyHead.vue'
import CiHeader from '../pages/ci/Head.vue'
import Ci<PERSON><PERSON>H<PERSON>roy from '../pages/ci/citask/CITaskHistory.vue'
import CiTaskSummary from '../pages/ci/CITaskSummary.vue'

const ciRouter = [
  {
    name: 'ciRoot',
    path: '/ci',
    redirect: '/ci/task',
    component: WebApp,
    meta: 'ci',
    children: [
      {
        name: 'citask',
        path: '/ci/task',
        components: {
          bodyhead: CiHeader,
          bodybody: CiTaskSummary
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          hideDynamicCIMenu()
          initHeadTool(true, true, true)
          next()
        }
      },
      {
        name: 'ciTaskHistory',
        path: '/ci/task/:taskID/history',
        components: {
          bodyhead: CiHeader,
          bodybody: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          hideDynamicCIMenu()
          initHeadTool(false, true, false)
          next()
        }
      },
      {
        name: 'ciSettingsGlobalVariable',
        path: '/ci/settings/global_variable',
        components: {
          bodyhead: CiHeader,
          bodybody: function () {
            return import('../pages/ci/settings/GlobalVariable.vue')
          },
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          initHeadTool(false, true, false)
          initDynamicCIMenu(to, '全局变量', true)
          next()
        }
      },
      {
        name: 'ciSettingsAgent',
        path: '/ci/settings/agent',
        components: {
          bodyhead: CiHeader,
          bodybody: function () {
            return import('../pages/ci/settings/CIAgent.vue')
          },
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          initHeadTool(true, true, false)
          initDynamicCIMenu(to, 'Agent', true)
          next()
        }
      },
      {
        name: 'ciSettingsTag',
        path: '/ci/settings/tag',
        components: {
          bodyhead: CiHeader,
          bodybody: function () {
            return import('../pages/ci/settings/CITag.vue')
          },
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          initDynamicCIMenu(to, '标签管理', true)
          initHeadTool(false, true, false)
          next()
        },
      },
      {
        name: 'ciPackageDownload',
        path: '/ci/task/output/:outputID/download',
        components: {
          bodyhead: EmptyHead,
          bodybody: function () {
            return import('../pages/ci/citask/MobileDownloadPackage.vue')
          },
        },
        props: { bodybody: true, bodyhead: true },
        meta: ''
      },
    ]
  }
]

function initDynamicCIMenu(to, menuText, isshow) {
  let headMenu = { text: menuText, path: to.path, icon: 'fa-cogs', parameters: {}, show: isshow }
  store.commit('ciglobal/setCIDynamicMenu', headMenu)
}

function hideDynamicCIMenu() {
  let headMenu = { text: '', path: '', icon: 'fa-cogs', parameters: {}, show: false }
  store.commit('ciglobal/setCIDynamicMenu', headMenu)
}

function initHeadTool(newButton, ciSettings, taskSearchBox) {
  let headTool = { newObject: newButton, setting: ciSettings, taskSearchBox: taskSearchBox }
  store.commit('ciglobal/setCIHeadToolShow', headTool)
}

export {
  ciRouter
}
