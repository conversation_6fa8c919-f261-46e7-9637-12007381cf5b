import Vue from 'vue'
import Router from 'vue-router'

import { apiRouter } from './interface'
import { projectRouter } from './project'
import { homeRouter } from './home'
import { ciRouter } from './ci'
import { systemAdminRouter } from './system-admin'
import { userCenterRouter } from './user-center'
import { loginOutRouter } from './login-out'
import { documentRouter } from './document'
import ViewUI from 'view-design';
import store from '../store/index.js'
import { getToken } from '../libs/util'

Vue.use(Router)

const router = new Router({
  mode: 'history',
  linkActiveClass: 'is-active',
  routes: [
    ...loginOutRouter,
    ...homeRouter,
    ...projectRouter,
    ...apiRouter,
    ...ciRouter,
    ...systemAdminRouter,
    ...userCenterRouter,
    ...documentRouter,
  ]
})


ViewUI.LoadingBar.config({
  color: '#45b97c',
  failedColor: '#f0ad4e',
  height: 5
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log("beforeEach")
  ViewUI.LoadingBar.start()
  const token = getToken()
  //console.log('token===============', token, to.name)
  const LOGIN_PAGE_NAME = 'login'
  if (to.name !== 'projectIssueMobileUpload') {
    if (!token && to.name !== LOGIN_PAGE_NAME) {
      next({
        name: LOGIN_PAGE_NAME,
        query: {
          redirect: to.fullPath
        }
      })
    } else if (!token && to.name === LOGIN_PAGE_NAME) {
      next()
    } else if (token && to.name === LOGIN_PAGE_NAME) {
      next({
        name: 'Home'
      })
    } else {
      let user_info = JSON.parse(localStorage.getItem('UserInfo'))
      store.commit('usercenter/setDefSpace', user_info.ViewData.default_space)
      store.commit('usercenter/setUserInfo', user_info)
      next()
    }
  } else {
    next()
  }
})

router.afterEach(route => {
  ViewUI.LoadingBar.finish()
})

export default router
