
import store from '../store/index'
import WebApp from '../layout/WebApp.vue'
import UCHead from '../pages/user-center/Head.vue'
import UserProfiles from '../pages/user-center/profiles/UserProfiles.vue'

const userCenterRouter = [
  {
    name: 'user-center',
    path: '/ucenter',
    component: WebApp,
    meta: 'ucenter',
    children: [
      {
        name: 'user-center-profiles',
        path: '/ucenter/:userID/profiles',
        components: {
          bodyhead: UCHead,
          bodybody: UserProfiles,
        },

        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu()
          next()
        },
        props: { bodyhead: true, bodybody: true },
        meta: ''
      },
    ]
  }
]

function initBodyMenu() {
  let headMenu = { newObject: false, searchBox: false }
  store.commit('ucenterglobal/setHeadMenu', headMenu)
}

function initGlobalMenu(to) {
  let headMenu = { text: '个人中心', path: to.path, icon: 'md-person', parameters: { meta: 'ucenter' }, show: true }
  store.commit('setDynamicMenu', headMenu)
}

export {
  userCenterRouter
}
