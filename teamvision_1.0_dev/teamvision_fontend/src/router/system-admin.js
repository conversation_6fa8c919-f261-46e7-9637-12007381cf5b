import store from '../store/index'
import WebApp from '../layout/WebApp.vue'

const systemAdminRouter = [
  {
    name: 'system',
    path: '/system',
    component: WebApp,
    meta: 'system',
    children: [
      {
        name: 'systemAdminUser',
        path: '/system/user',
        components: {
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          },
          bodybody: function () {
            return import('../pages/system-admin/user/UserAdmin.vue')
          },
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(true)
          next()
        },
        meta: ''
      },
      {
        name: 'systemSetting',
        path: '/system/setting',
        components: {
          bodybody: function () {
            return import('../pages/system-admin/setting/Setting.vue')
          },
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          }
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(false)
          next()
        },
        meta: ''
      },
      {
        name: 'systemProduct',
        path: '/system/product',
        components: {
          bodybody: function () {
            return import('../pages/system-admin/product/Product.vue')
          },
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          }
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(true)
          next()
        },
        meta: ''
      },
      {
        name: 'systemProjectIssueCategory',
        path: '/system/project/issueCategory',
        components: {
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          },
          bodybody: function () {
            return import('../pages/system-admin/project/ProjectConfigAdmin.vue')
          },
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(false)
          next()
        },
        meta: ''
      },
      {
        name: 'systemProjectIssuePhrase',
        path: '/system/project/issuePhrase',
        components: {
          bodybody: function () {
            return import('../pages/system-admin/project/ProjectConfigAdmin.vue')
          },
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          }
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(false)
          next()
        },
        meta: ''
      },
      {
        name: 'systemAdminUserGroup',
        path: '/system/usergroup',
        components: {
          bodybody: function () {
            return import('../pages/system-admin/user/UserAdmin.vue')
          },
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          }
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu()
          next()
        },
        meta: ''
      },
      {
        name: 'systemWebHook',
        path: '/system/webhook',
        components: {
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          },
          bodybody: function () {
            return import('../pages/system-admin/webhook/WebHook.vue')
          },
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(true)
          next()
        },
        meta: ''
      },
      {
        name: 'systemMessageConfig',
        path: '/system/message/config',
        components: {
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          },
          bodybody: function () {
            return import('../pages/system-admin/message/MessageCenterAdmin.vue')
          },
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(true)
          next()
        },
        meta: '',
      },
      {
        name: 'systemMessageChannel',
        path: '/system/message/channel',
        components: {
          bodyhead: function () {
            return import('../pages/system-admin/Head.vue')
          },
          bodybody: function () {
            return import('../pages/system-admin/message/MessageCenterAdmin.vue')
          },
        },
        beforeEnter: (to, from, next) => {
          initGlobalMenu(to)
          initBodyMenu(true)
          next()
        },
        meta: '',
      },
    ]
  }
]

function initBodyMenu(newObject) {
  let headMenu = { newObject: newObject }
  store.commit('systemglobal/setHeadMenu', headMenu)
}

function initGlobalMenu(to) {
  let headMenu = { text: '系统管理', path: to.path, icon: 'md-settings', parameters: { meta: 'system' }, show: true }
  store.commit('setDynamicMenu', headMenu)
}

export {
  systemAdminRouter
}
