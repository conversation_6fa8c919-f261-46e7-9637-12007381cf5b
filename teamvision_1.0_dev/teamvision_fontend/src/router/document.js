
import store from '../store/index'
import WebApp from '../layout/WebApp.vue'
import Document from '../pages/document/Document.vue'
import Folder from '../pages/document/Folder.vue'
import DocumentContent from '../pages/document/DocumentContent.vue'

const documentRouter = [
  {
    name: 'Document',
    path: '/wiki',
    component: WebApp,
    meta: 'wiki',
    children: [
      {
        path: '/wiki',
        redirect: '/wiki/folder',
        components: {
          bodybody: Document,
        },
        props: { bodybody: true },
        children: [
          {
            name: 'FolderList',
            path: '/wiki/folder',
            component: () => import('../pages/document/Folder.vue'),
            props: true,
            meta: '',
          },
          {
            name: 'Folder',
            path: '/wiki/folder/:folderID',
            component: () => import('../pages/document/Folder.vue'),
            props: true,
            meta: '',
          },
          {
            name: 'DocumentDetail',
            path: '/wiki/document/:docID',
            component: DocumentContent,
            props: true,
            meta: '',
          },
        ],
      },
    ]
  }
]


export {
  documentRouter
}
