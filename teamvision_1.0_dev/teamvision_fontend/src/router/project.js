
// import EnvServiceWebpart from '../components/APIMockWebpart.vue'
import WebApp from '../layout/WebApp.vue'
import ProjectHead from '../pages/project/Head.vue'
import PortalHead from '../pages/project/PortalHead.vue'
import EmptyHead from '../layout/EmptyBodyHead.vue'
import store from '../store/index'
import ProjectTesting from '../pages/project/project-testing/ProjectTesting.vue'
import ProjectTestCase from '../pages/project/project-testing/test-case/ProjectTestCase.vue'
import ProjectTestPlan from '../pages/project/project-testing/test-plan/ProjectTestPlan.vue'
import ProjectTestPlanExecute from '../pages/project/project-testing/test-plan/ProjectTestPlanExecute.vue'
import ProjectAutoCase from '../pages/project/project-testing/auto-case/ProjectAutoCase.vue'
import ProjectTestReport from '../pages/project/project-testing/test-report/ProjectTestReport.vue'
import ProjectTestReportDetail from '../pages/project/project-testing/test-report/ProjectTestReportDetail.vue'
import TestCaseReview from '../pages/project/project-testing/case-review/CaseReview.vue'
import TestCaseReviewDetail from '../pages/project/project-testing/case-review/CaseReviewDetail.vue'
import ProjectList from '../pages/project/ProjectList.vue'
import ProjectReqirementTask from '../pages/project/requirement/ProjectReqirementTask.vue'

const projectRouter = [
  {
    name: 'projectRoot',
    path: '/',
    redirect: '/project',
    component: WebApp,
    meta: 'project',
    children: [
      {
        name: 'projectList',
        path: '/project',
        components: {
          bodyhead: PortalHead,
          bodybody: ProjectList
        },
        meta: ''
      },
      {
        name: 'projectRequirement',
        path: '/project/:projectID/requirement',
        components: {
          bodybody: function () {
            return import('../pages/project/requirement/ProjectRequirement.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: false,
            reqNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectRequirementDetail',
        path: '/project/:projectID/requirement/:requirementID',
        components: {
          bodybody: function () {
            return import('../pages/project/requirement/ProjectRequirement.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: false,
            reqNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectTask',
        path: '/project/:projectID/task',
        components: {
          bodybody: function () {
            return import('../pages/project/projecttask/ProjectTask.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: true,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: true,
            taskFilter: true,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectTaskDetail',
        path: '/project/:projectID/task/:taskId',
        components: {
          bodybody: function () {
            return import('../pages/project/projecttask/ProjectTask.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: true,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: true,
            taskFilter: true,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectTesting',
        path: '/project/:projectID/test/',
        redirect: '/project/:projectID/test/test-case',
        components: {
          // bodyhead: AppBody,
          bodyhead: ProjectHead,
          bodybody: ProjectTesting
        },
        props: { bodyhead: true },
        children: [{
          name: 'testCase',
          path: 'test-case',
          components: {
            projectTest: ProjectTestCase,
          },
          props: { bodyhead: true, bodybody: true, },
          meta: '',
        },
        {
          name: 'testCaseGroup',
          path: 'test-case/:groupID',
          components: {
            projectTest: ProjectTestCase,
          },
          props: { bodyhead: true, bodybody: true, projectTest: true },
          meta: '',
        },
        // {
        //   name: 'testCaseDetail',
        //   path: 'test-case/:groupID/case/:caseID',
        //   components: {
        //     projectTest: ProjectTestCase,
        //   },
        //   props: { default: true, bodyhead: true },
        //   meta: '',
        // },
        {
          name: 'testPlan',
          path: 'test-plan',
          components: {
            projectTest: () => import('../pages/project/project-testing/test-plan/ProjectTestPlan.vue'),
          },
          props: { default: true, bodyhead: true },
          meta: '',
        },
        {
          name: 'testPlanVersion',
          path: 'test-plan/version/:versionID',
          components: {
            projectTest: ProjectTestPlan
          },
          props: { projectTest: true },
          meta: '',
        },
        {
          name: 'testPlanDetail',
          path: 'test-plan/:planId/detail',
          components: {
            projectTest: ProjectTestPlan
          },
          props: { bodyhead: true, default: true, projectTest: true },
          meta: '',
        },
        {
          name: 'testPlanExec',
          path: 'test-plan/:planId/tests',
          components: {
            projectTest: ProjectTestPlanExecute,
          },
          props: { default: true, projectTest: true },
          meta: '',
        },
        {
          name: 'testReport',
          path: 'report',
          components: {
            projectTest: ProjectTestReport
          },
          props: { default: true, bodyhead: true },
          meta: '',
        },
        {
          name: 'projecttestReportDetail',
          path: 'report/:reportID',
          components: {
            projectTest: ProjectTestReportDetail
          },
          props: { projectTest: true },
          meta: '',
        },
        {
          name: 'AutoCase',
          path: 'auto-case',
          components: {
            projectTest: ProjectAutoCase,
          },
          props: { default: true, bodyhead: true },
          meta: '',
        },
        {
          name: 'CaseReviews',
          path: 'case-reviews',
          components: {
            projectTest: TestCaseReview,
          },
          props: { default: true, bodyhead: true },
          meta: '',
        }, {
          name: 'CaseReviewsDetail',
          path: 'case-reviews/:casereviewID',
          components: {
            projectTest: TestCaseReviewDetail,
          },
          props: { default: true, bodyhead: true },
          meta: '',
        }
        ],
        // 路由独享的守卫
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          if (to.params.action === 'report') {
            headMenu.newObject = true
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectFortesting',
        path: '/project/:projectID/fortesting',
        components: {
          bodybody: function () {
            return import('../pages/project/project-fortesting/ProjectFortesting.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: true,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectFortestingDetail',
        path: '/project/:projectID/fortesting/:fortestingId',
        components: {
          bodybody: function () {
            return import('../pages/project/project-fortesting/ProjectFortesting.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: true,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'projectFortestingReport',
        path: '/project/:projectID/fortesting/:fortestingID/report/:reportName',
        components: {
          bodybody: function () {
            return import('../pages/project/project-fortesting/report/Report.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
      },
      {
        name: 'projectMindmap',
        path: '/project/:projectID/mindmap/:mindFileID',
        components: {
          bodybody: function () {
            return import('../pages/project/project-mindmap/mind.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },


      {
        name: 'projectMindmapList',
        path: '/project/:projectID/mindmap',
        components: {
          bodybody: function () {
            return import('../pages/project/project-mindmap/ProjectMindmapFileList.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: true,
            dateRangeBox: false,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: true
          }
          initBodyMenu(headMenu)
          next()
        }
      },

      {
        name: 'projectDocument',
        path: '/project/:projectID/documents/:folderID?',
        components: {
          bodybody: function () {
            return import('../pages/project/document/ProjectDocuments.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: true,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },

      {
        name: 'projectSettings',
        path: '/project/:projectID/settings/:page',
        components: {
          bodyhead: ProjectHead,
          bodybody: function () {
            return import('../pages/project/settings/ProjectSettings.vue')
          },
        },
        props: { bodybody: true, bodyhead: true },
        meta: ''
      },

      {
        name: 'projectStatistics',
        path: '/project/:projectID/statistics/:page?',
        components: {
          bodybody: function () {
            return import('../pages/project/statistics/ProjectStatistics.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            searchBox: false,
            versionBox: true,
            dateRangeBox: true,
            documentNew: false,
            issueExport: false,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },

      {
        name: 'projectDocumentExcel',
        path: '/project/documents/excel/:documentID/:viewMode?',
        components: {
          bodybody: function () {
            return import('../pages/project/document/Excel.vue')
          },
          bodyhead: EmptyHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: ''
      },

      {
        name: 'projectIssue',
        path: '/project/:projectID/issue/:issueID',
        components: {
          bodybody: function () {
            return import('../pages/project/issue/ProjectIssue.vue')
          },
          bodyhead: ProjectHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            searchBox: false,
            versionBox: false,
            dateRangeBox: false,
            documentNew: false,
            issueExport: true,
            taskViewMode: false,
            taskFilter: false,
            importMindFile: false
          }
          initBodyMenu(headMenu)
          next()
        }
      },

      {
        name: 'projectIssueMobileUpload',
        path: '/project/issue/:issueID/mobile/upload',
        components: {
          bodybody: function () {
            return import('../pages/project/issue/MobileUpload.vue')
          },
          bodyhead: EmptyHead
        },
        props: { bodybody: true, bodyhead: true },
        meta: ''
      },
    ]
  }
]

//   {
//     name: 'projectSummary',
//     path: '/project/:projectID/summary',
//     components: {
//       default: function () {
//         return import('../pages/project/project-summary/ProjectSummary.vue')
//       },
//       bodyhead: ProjectHead
//     },
//     props: { default: true, bodyhead: true },
//     meta: '',
//     beforeEnter: (to, from, next) => {
//       let headMenu = {
//         newObject: false,
//         searchBox: false,
//         versionBox: false,
//         dateRangeBox: false,
//         documentNew: false,
//         issueExport: false,
//         taskViewMode: false,
//         taskFilter: false,
//         importMindFile: false
//       }
//       initBodyMenu(headMenu)
//       next()
//     }
//   },


function initBodyMenu(headMenu) {
  store.commit('projectglobal/setHeadMenu', headMenu)
}

export {
  projectRouter
}
