import store from '../store/index'
import HomeHead from '../pages/home/<USER>'
import MyOwnItem from '../pages/home/<USER>'
import HomeKanban from '../pages/home/<USER>/HomeKanbanStatistics.vue'
import HomeTask from '../pages/project/projecttask/ProjectTask.vue'
import TestPlanStatus from '../pages/home/<USER>'
import HomeTestCase from '../pages/home/<USER>'
import Requirements from '../pages/home/<USER>/Requirements.vue'
import ProjectIssue from '../pages/project/issue/ProjectIssue.vue'
import ProjectFortesting from '../pages/project/project-fortesting/ProjectFortesting.vue'
import WebApp from '../layout/WebApp.vue'

const homeRouter = [
  {
    name: 'Home',
    path: '/home',
    redirect: '/home/<USER>',
    component: WebApp,
    //beforeEnter: (to, from, next) => {
    // let login = store.getters['usercenter/isLogin']
    // if (login) {
    //   next({path: '/home/<USER>'})
    // } else {
    //   next()
    // }
    //},
    meta: 'home',
    children: [
      {
        name: 'myOwnItem',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: MyOwnItem
        },
        props: { bodyhead: true, bodybody: true },
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      {
        name: 'homeKanban',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: HomeKanban
        },
        props: { bodyhead: true, bodybody: true },
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      {
        name: 'testPlanStatus',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: TestPlanStatus
        },
        props: { bodyhead: true, default: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      {
        name: 'homeRequirement',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: Requirements
        },
        props: { default: true, bodyhead: true },
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      {
        name: 'homeFortesting',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: ProjectFortesting
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            itemViewMode: true,
            filterMenu: false,
            exportIssue: false
          }
          hideDynamicHomeMenu()
          initBodyMenu(headMenu)
          next()
        }
      },
      {
        name: 'homeTask',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: HomeTask,
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            itemViewMode: true,
            filterMenu: true,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      {
        name: 'homeIssue',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: ProjectIssue
        },
        props: { default: true, bodyhead: true },
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: true,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: true
          }
          initBodyMenu(headMenu)
          initDynamicHomeMenu(to, '问题', true)
          next()
        }
      },
      {
        name: 'homeTestCase',
        path: '/home/<USER>',
        components: {
          bodyhead: HomeHead,
          bodybody: HomeTestCase
        },
        props: { bodyhead: true, bodybody: true },
        meta: '',
        beforeEnter: (to, from, next) => {
          let headMenu = {
            newObject: false,
            itemViewMode: false,
            filterMenu: false,
            exportIssue: false
          }
          initBodyMenu(headMenu)
          hideDynamicHomeMenu()
          next()
        }
      },
      // {
      //   name: 'homeTesting',
      //   path: '/home/<USER>',
      //   components: {
      //     bodybody: function () {
      //       return import('../pages/project/project-testing/ProjectTesting.vue')
      //     },
      //     bodyhead: HomeHead
      //   },
      //   props: { bodyhead: true, default: true },
      //   meta: '',
      //   beforeEnter: (to, from, next) => {
      //     let headMenu = {
      //       newObject: false,
      //       itemViewMode: false,
      //       filterMenu: false,
      //       exportIssue: false
      //     }
      //     hideDynamicHomeMenu()
      //     initBodyMenu(headMenu)
      //     next()
      //   }
      // },
    ],

  },
  { path: '/home', redirect: '/home/<USER>' },
  { path: '/home/<USER>/all', redirect: '/home/<USER>' },
]

function initDynamicHomeMenu(to, menuText, isshow) {
  let headMenu = { text: menuText, path: to.path, icon: 'fa-cogs', parameters: {}, show: isshow }
  store.commit('homeglobal/setHomeDynamicMenu', headMenu)
}

function hideDynamicHomeMenu() {
  let headMenu = { text: '', path: '', icon: 'fa-cogs', parameters: {}, show: false }
  store.commit('homeglobal/setHomeDynamicMenu', headMenu)
}

function initBodyMenu(headMenu) {
  store.commit('homeglobal/setHeadMenu', headMenu)
}

export {
  homeRouter
}
