import Vue from 'vue'
import Vuex from 'vuex'
import task from './project/task'
import issue from './project/issue'
import fortesting from './project/fortesting'
import project from './project/project'
import projectglobal from './project/projectglobal'
import citask from './ci/citask'
import citaskflow from './ci/citaskflow'
import mockapi from './interface/mockapi'
import document from './project/document'
import systemglobal from './system/systemglobal'
import ucenterglobal from './ucenter/ucenterglobal'
import user from './system/user'
import ciglobal from './ci/ciglobal'
import homeglobal from './home/<USER>'
import testcase from './project/testcase'
import testplan from './project/testplan'
import usercenter from './ucenter/usercenter'
import { getSpaceUserListApi } from "../api/user"

import axios from 'axios'

Vue.use(Vuex)

const state = {
  appHeight: 0,
  appHeaderHeight: 48,
  appBodyHeight: 0,
  appBodyWidth: 0,
  appBodyHeaderHeight: 48,
  appBodyMainHeight: 0,
  inProject: false,
  itemViewMode: 'list', // 'board'
  appHeadShow: true,
  dynamicMenu: { text: '', path: '', icon: '', parameters: { meta: '' }, show: false },
  spaceList: [],
  spaceUserList: [],
}

const getters = {
  inProject(state) {
    return state.inProject
  },
  itemViewMode(state) {
    return state.itemViewMode
  },
  dynamicMenu(state) {
    return state.dynamicMenu
  },
}

const mutations = {
  setAppHeight(state, height) {
    state.appHeight = height
  },
  setAppBodyHeight(state) {
    state.appBodyHeight = state.appHeight - state.appHeaderHeight
  },
  setAppBodyMainHeight(state, height) {
    state.appBodyMainHeight = height
  },
  setInProject(state, inProject) {
    state.inProject = inProject
  },
  setAppHeadShow(state, appHeadShow) {
    state.appHeadShow = appHeadShow
  },
  setAppBodyTop(state, bodyTop) {
    state.appBodyTop = bodyTop
  },
  setItemViewMode(state, mode) {
    state.itemViewMode = mode
  },
  setDynamicMenu(state, dynamicMenu) {
    state.dynamicMenu = dynamicMenu
  },
  setAppBodyWidth(state, width) {
    state.appBodyWidth = width
  },
  setSpaceList(state, sList) {
    state.spaceList = sList
  },
  setSpaceUserList(state, userList) {
    state.spaceUserList = userList
  },
  setAppBodyHeaderHeight(state, height) {
    state.appBodyHeaderHeight = height
  },
}

const actions = {
  setAppHeightWidth(context, height, width) {
    context.commit('setAppHeight', height)
    context.commit('setAppBodyHeight')
    context.commit('setAppBodyMainHeight', height - context.state.appHeaderHeight - context.state.appBodyHeaderHeight)
    context.commit('setAppBodyWidth', width)
  },

  setInProjectAction(context, height) {
    context.commit('setInProject', height)
  },

  getSpaceList({ dispatch, commit }) {
    axios.get("/api/project/product_spaces").then((response) => {
      //console.log('response.data.result=',response.data.result)
      commit('setSpaceList', response.data.result)
    },
      (response) => {
        // error callback
      }
    );
  },

  getSpaceUserList(content) {
    getSpaceUserListApi().then(response => {
      content.commit('setSpaceUserList', response.data.result)
    })
  },
}

export default new Vuex.Store({
  modules: {
    task,
    citask,
    project,
    citaskflow,
    mockapi,
    fortesting,
    projectglobal,
    issue,
    document,
    systemglobal,
    ucenterglobal,
    user,
    ciglobal,
    homeglobal,
    testcase,
    testplan,
    usercenter,
  },
  strict: true,
  actions,
  getters,
  state,
  mutations
})
