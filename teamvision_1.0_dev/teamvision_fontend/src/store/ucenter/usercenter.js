import { login, getUserInfo, logout } from '../../api/user'
import { getToken, setToken } from '../../libs/util'

export default {
  namespaced: true,
  state: {
    isLogin: false,
    userInfo: {},
    token: getToken(),
    defSpace: {
      'id': 0,
      'Title': '我的空间'
    },
    selectedApi: 0,
    responseCreateDialogShow: false,
    responseAdded: false
  },
  getters: {
    selectedApi(state) {
      return state.selectedApi
    },
    responseCreateDialogShow(state) {
      return state.responseCreateDialogShow
    },
    responseAdded(state) {
      return state.responseAdded
    }
  },
  mutations: {
    setLogin(state, bool) {
      state.isLogin = bool
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setDefSpace(state, space) {
      state.defSpace = space
    },
    setSelectedApi(state, api) {
      state.selectedApi = api
    },
    setResponseCreateDialogShow(state, show) {
      state.responseCreateDialogShow = show
    },
    setResponseAdded(state, added) {
      state.responseAdded = added
    },
    setToken(state, token) {
      state.token = token
      setToken(token)
    }
  },
  actions: {
    setSelectedApiAction(context, isShow) {
      context.commit('setSelectedApi', isShow)
    },

    setResponseCreateDialogShowAction(context, isShow) {
      context.commit('setResponseCreateDialogShow', isShow)
    },

    setResponseAddedAction(context, added) {
      context.commit('setResponseAdded', added)
    },

    handleLogin({ commit }, { email, password }) {
      email = email.trim()
      return new Promise((resolve, reject) => {
        login({ email, password }).then(res => {
          const respData = res.data
          commit('setToken', respData.result.sessionid)
          resolve(respData)
        }).catch(err => {
          reject(err)
        })
      })
    },

    handleLogOut({ state, commit }) {
      localStorage.clear();
      sessionStorage.clear();
      commit('setLogin', false)
      commit('setToken', '')
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },

    getUserInfo({ state, commit }) {
      return new Promise((resolve, reject) => {
        try {
          getUserInfo(state.token).then(res => {
            const data = res.data
            localStorage.setItem("UserInfo", JSON.stringify(data.result));
            localStorage.setItem("ProductSpace", JSON.stringify(data.result.ViewData.default_space))
            commit('setDefSpace', data.result.ViewData.default_space)
            commit('setUserInfo', data.result)
            commit('setLogin', true)
            setToken(data.result.id, 'uid')
            resolve(data)
          }).catch(err => {
            reject(err)
          })
        } catch (error) {
          reject(error)
        }
      })
    },
  }
}
