import Main from '../layout/WebApp.vue'

export default [
  loginOutRouter,
  {
    name: 'root',
    path: '/',
    redirect: '/home/<USER>',
    component: Main,
    meta: 'root',
    children: [
      {
        name: 'home',
        path: '/home',
        components: {
          bodyhead: HomeHead,
        },
        props: { bodyhead: true},
        meta: '',
        children: [
          {
            name: 'homeKanban',
            path: '/home/<USER>',
            components: {
              bodyhead: HomeHead,
              bodybody: HomeKanban
            },
            props: { bodyhead: true, bodybody: true },
            beforeEnter: (to, from, next) => {
              let headMenu = {
                newObject: false,
                itemViewMode: false,
                filterMenu: false,
                exportIssue: false
              }
              initBodyMenu(headMenu)
              hideDynamicHomeMenu()
              next()
            }
          },
        ]
      },
    ]
  },
]

