import { loadCaseTagList } from '../../pages/project/project-testing/business-service/ProjectTestCaseApiService'

const state = {
  testCaseGroupTitle: '',
  searchKeyword: '',
  searchCaseKeyword: { type: 1, filters: [''] }, //type 1: search case by keyword,2 filter case by tag
  caseFilters: { type: 1, filters: [''] },
  refreshCase: false,
  showCaseID: 0,
  showTestCaseImportXmindDialog: false,
  testCaseViewMode: null,
  testCasePlanShow: true,
  testCaseTagList: []
}
const getters = {
  testCaseGroupTitle(state) {
    return state.testCaseGroupTitle
  },

  searchKeyword(state) {
    return state.searchKeyword
  },

  caseFilters(state) {
    return state.caseFilters
  },

  refreshCase(state) {
    return state.refreshCase
  },

  showCaseID(state) {
    return state.showCaseID
  },

  showTestCaseImportXmindDialog(state) {
    return state.showTestCaseImportXmindDialog
  },

  testCaseViewMode(state) {
    return state.testCaseViewMode
  },

  getCaseTags (state) {
    let tagList = []
    for (let i = 0; i < state.testCaseTagList.length; i++) {
      let tempTag = { label: "", value: 0, color: "" };
      tempTag.label = state.testCaseTagList[i].TagName;
      tempTag.value = state.testCaseTagList[i].TagValue;
      tempTag.color = state.testCaseTagList[i].TagColor;
      tagList.push(tempTag);
    }
    //console.log(tagList)
    return tagList
  },
}

const mutations = {
  setTestCaseGroupTitle(state, title) {
    state.testCaseGroupTitle = title
  },

  setSearchKeyword(state, keyword) {
    state.searchKeyword = keyword
  },

  setCaseFilters(state, filters) {
    state.caseFilters = filters
  },

  setRefreshCase(state, refresh) {
    state.refreshCase = refresh
  },

  setShowCaseID(state, caseID) {
    state.showCaseID = caseID
  },

  setShowTestCaseImportXmindDialog(state, status) {
    state.showTestCaseImportXmindDialog = status
  },

  setTestCaseViewMode(state, status) {
    state.testCaseViewMode = status
  },

  setTestCasePlanWidth(state, width) {
    state.testCasePlanWidth = width
  },

  setTestCasePlanShow(state, bool) {
    state.testCasePlanShow = bool
  },
  settestCaseTagListestCase(state, tagList){
    state.testCaseTagList = tagList
  }
}
const actions = {
  setTestCaseGroupTitleAction(context, isChanged) {
    context.commit('setTestCaseGroupTitle', isChanged)
  },

  getPorjectTestCaseAll() {

  },

  getTestCaseTags(context) {
    loadCaseTagList().then((response) => {
      context.commit('settestCaseTagListestCase', response.data.result)
    },
    );
  },
}
const modules = {}

export default {
  namespaced: true,
  actions,
  getters,
  state,
  mutations,
  modules
}
