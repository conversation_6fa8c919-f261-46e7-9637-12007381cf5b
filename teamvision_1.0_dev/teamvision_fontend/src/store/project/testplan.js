import {
  getPlanCaseTree,
  updateTestPlanCase,
  loadPlanDetail,
  loadVersionsPlans,
  updateTestPlanOwner,
  deleteTestPlanApi,
  loadPlanVersions,
  loadVersionTestPlans
} from "../../pages/project/project-testing/business-service/ProjectTestCaseApiService";

import { findNextTestCaseId } from "../../utils/utils.js"

export default {
  state: {
    testPlanCassTree: null,
    versionsPlans: [],
    refreshVersionPlans: false,
    defTestPlan: {
      id: 0,
      Title: "测试计划",
      CreationTime: "2000-01-01 01:01:01",
      Status: 0,
      view_data: {
        case_coverage: 0,
        failed_case: 0,
        creator_name: "创建者",
        plan_duration: 0,
        unpass_case: [],
        project_case_count: 0,
        no_run: 0,
        pass_rate: 0,
        retest_rate: 0,
        blocked_rate: 0,
        norun_rate: 0,
        passed_case: 0,
        case_count: 0,
        Status: "未开始",
        requirements: []
      },
    },
    defTestPlanId: 0,
    UpdateTestPlanTestCaseButton: false,
    testPlanCaseTreeData: [],
    testPlanCaseTreeDataList: [],
    checkedTestCase: [],
    checkedTestCaseIdList: [],
    currentCaseNode: null,
    refreshPlanCase: false,
    updateExecuteCaseTree: {
      "result": 0,
      "id_list": [],
      "refresh": false
    },
    TestPlanEditDialogShow: false,
  },

  getters: {
    filterTestPlanCaseTreeDataByTestResult(state, status) {
      function filterByTestResult(data, status) {
        return data.filter(item => {
          if (item.TestResult === status) return true;
          if (item.children) {
            item.children = filterByOwner(item.children, ownerId);
            return item.children.length > 0;
          }
          return false;
        });
      }

    },
    filterTestPlanCaseTreeDataByOwner(state, ownerId) {
      let data = state.testPlanCaseTreeData

      function filterByOwner(data, ownerId) {
        return data.filter(item => {
          if (item.Owner === ownerId) return true;
          if (item.children) {
            item.children = filterByOwner(item.children, ownerId);
            return item.children.length > 0;
          }
          return false;
        });
      }
    },
  },

  mutations: {
    setVersionsPlans(state, testPlans) {
      state.versionsPlans = testPlans
    },

    setRefreshVersionPlans(state) {
      state.refreshVersionPlans = !state.refreshVersionPlans
    },

    delVersionPlan(state, planId) {
      for (var v = 0; v < state.versionsPlans.length; v++) {
        for (var p = 0; p < state.versionsPlans[v].view_data.test_plans.length; p++) {
          if (planId == state.versionsPlans[v].view_data.test_plans[p].id) {
            state.versionsPlans[v].view_data.test_plans.splice(p, 1)
            return true
          }
        }
      }
    },

    setDefTestPlan(state, planInfo) {
      //console.log("setDefTestPlan.planInfoplanInfoplanInfo=======", planInfo)
      state.defTestPlan = []
      state.defTestPlan = planInfo
    },

    setDefTestPlanId(state) {
      //console.log("setDefDefTestPlanId.planInfoplanInfoplanInfo=======")
      for (var v = 0; v < state.versionsPlans.length; v++) {
        for (var p = 0; p < state.versionsPlans[v].view_data.test_plans.length; p++) {
          if (state.versionsPlans[v].view_data.test_plans[p].Status != 4) {
            state.defTestPlanId = state.versionsPlans[v].view_data.test_plans[p].id
            return
          }
        }
      }
    },

    setUpdateTestPlanTestCaseButton(state, value) {
      state.UpdateTestPlanTestCaseButton = value
    },

    setExecuteCaseList(state) {
      state.testPlanCaseTreeDataList = []

      function traverseTree(nodes) {
        for (const node of nodes) {
          if (node.IsGroup == false) {
            state.testPlanCaseTreeDataList.push(node);
          }
          if (node.children && node.children.length > 0) {
            traverseTree(node.children);
          }
        }
      };
      if (state.testPlanCaseTreeData) {
        traverseTree(state.testPlanCaseTreeData)
      }
      this.commit('testplan/setCurrentCaseNode')
    },

    initTestCaseTree(state, data) {
      state.testPlanCaseTreeData = data
      this.commit('testplan/setExecuteCaseList')
    },

    setCheckedTestCase(state, data) {
      state.checkedTestCase = []
      state.checkedTestCase = data
    },

    setCheckedTestCaseIdList(state) {
      state.checkedTestCaseIdList = []
      for (let i = 0; i < state.checkedTestCase.length; i++) {
        if (state.checkedTestCase[i].IsGroup == false) {
          state.checkedTestCaseIdList.push(state.checkedTestCase[i].id)
        }
      }
    },

    setCurrentCaseNode(state, data) {
      if (typeof (data) == "undefined") {
        if (state.currentCaseNode == null) {
          state.currentCaseNode = state.testPlanCaseTreeDataList[0]
        } else {
          const index = state.testPlanCaseTreeDataList.indexOf(state.currentCaseNode);
          if (index !== -1 && index < state.testPlanCaseTreeDataList.length - 1) {
            state.currentCaseNode = state.testPlanCaseTreeDataList[index + 1]
          }
          if (index == state.testPlanCaseTreeDataList.length - 1) {
            state.currentCaseNode = state.testPlanCaseTreeDataList[0]
          }
        }
      } else {
        if (data.IsGroup == false) {
          state.currentCaseNode = data
        }
      }
    },

    setRefreshPlanCase(state, value) {
      state.refreshPlanCase = value
    },

    updateTestCaseTree(state, result) {
      this.commit('testplan/setCheckedTestCaseIdList')

      if (state.checkedTestCase.length > 0) {
        state.updateExecuteCaseTree.result = result
        state.updateExecuteCaseTree.id_list = state.checkedTestCaseIdList
        // for (var i = 0; i < state.checkedTestCase.length; i++){
        //     if (state.checkedTestCase[i].IsGroup == false){
        //         state.updateExecuteCaseTree.id_list.push(state.checkedTestCase[i].id)
        //     }
        // }
        state.updateExecuteCaseTree.refresh = true
      } else {
        //state.currentCaseNode.TestResult = result

        state.updateExecuteCaseTree.result = result
        let tmp = new Array()
        tmp.push(state.currentCaseNode.id)
        state.updateExecuteCaseTree.id_list = tmp
        state.updateExecuteCaseTree.refresh = true

        this.commit('testplan/setCurrentCaseNode')
      }
    },

    updateTestCaseTreeData(state, result) {
      //this.commit('testplan/setCheckedTestCaseIdList')

      for (var i = 0; i < state.checkedTestCase.length; i++) {
        if (state.checkedTestCase[i].IsGroup == false) {
          updateTestCaseTreeData.forEach(element => {
            if (element.IsGroup == false) {
              if (element.id == state.checkedTestCase[i]) {
                state.checkedTestCase.TestResult = result
              }
            }
          });
        }

        // state.updateExecuteCaseTree.result = result
        // state.updateExecuteCaseTree.id_list = state.checkedTestCaseIdList
        // for (var i = 0; i < state.checkedTestCase.length; i++){
        //     if (state.checkedTestCase[i].IsGroup == false){
        //         state.updateExecuteCaseTree.id_list.push(state.checkedTestCase[i].id)
        //     }
        // }
        // state.updateExecuteCaseTree.refresh = true
      }
    },

    setTestPlanEditDialogShow(state, isshow) {
      state.TestPlanEditDialogShow = isshow
    },

    setVersionTestPlans(state, versionPlans) {
      //console.log("state.versionsPlans===", state.versionsPlans)
      for (let i = 0; i < state.versionsPlans.length; i++) {
        if (state.versionsPlans[i].id == versionPlans.id) {
          //console.log(state.versionsPlans[i].id , versionPlans.id)
          state.versionsPlans[i].view_data.test_plans = versionPlans.view_data.test_plans
          break
        }
      }
      //console.log("state.versionsPlans===", state.versionsPlans)
    },
  },

  actions: {
    getVersionsPlans({ dispatch, commit }, projectID) {
      loadVersionsPlans(projectID).then(
        (response) => {
          commit('setVersionsPlans', response.data.result)
          //dispatch('getDefTestPlan', response.data.result[0].view_data.test_plans[0].id )
        },
      );
    },

    getPlanVersions({ dispatch, commit }, projectID) {
      loadPlanVersions(projectID).then(
        (response) => {
          commit('setVersionsPlans', response.data.result)
          //dispatch('getDefTestPlan', response.data.result[0].view_data.test_plans[0].id )
        },
      );
    },

    getVersionTestPlans({ dispatch, commit }, { projectID, versionId }) {
      //console.log(projectID, versionId)
      loadVersionsPlans(projectID, versionId).then((response) => {
        commit('setVersionTestPlans', response.data.result[0])
      },
      );
    },

    getDefTestPlan({ dispatch, commit }, planId) {
      commit('setDefTestPlan', []);
      commit('initTestCaseTree', [])
      loadPlanDetail(planId).then((response) => {
        let planInfo = response.data.result;
        commit('setDefTestPlan', planInfo);
        // if (planInfo.CaseCount < 100) {
        //   dispatch('getTestCaseTree', planId)
        // }
      },
      );
    },

    getTestCaseTree(context, planId) {
      context.commit('initTestCaseTree', null)
      getPlanCaseTree(planId).then((response) => {
        context.commit('initTestCaseTree', response.data.result)
      })
    },

    updateTestCaseResult(context, result) {
      let req_data = {
        "TestResult": result
      }
      if (context.state.checkedTestCase.length > 0) {
        for (let i = 0; i < context.state.checkedTestCase.length; i++) {
          if (context.state.checkedTestCase[i].IsGroup == false) {
            updateTestPlanCase(context.state.checkedTestCase[i].id, req_data).then(response => {

            })
          }
        };
      } else {
        updateTestPlanCase(context.state.currentCaseNode.id, req_data).then(response => {
        })
      }
      context.commit('updateTestCaseTree', result)
    },

    updateTestPlanCaseOwner(context, planId, ownerId) {
      let parameters = {
        "owner": ownerId,
        "ids": this.checkedTestCaseIdList
      };
      updateTestPlanOwner(planId, parameters).then((response) => {
        // this.filterByCaseOwner(0, 0, 0);
        this.showAsginOwner = false;
      });
    },

    delTestPlan({ dispatch, commit }, planId) {
      deleteTestPlanApi(planId).then((response) => {
        commit('delVersionPlan', planId)
      },);
    }
  },
  namespaced: true,
}
