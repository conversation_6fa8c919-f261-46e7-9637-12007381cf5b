import {
  getIssueSeverityApi,
  getIssuePhraseApi,
  getIssueCategoriesApi,
  getIssueStatusApi,
  getIssuePriorityApi,
} from '../../api/project'

const state = {
  issueChange: false,
  showIssueDetail: false,
  selectIssueID: 0,
  issueFilters: '',
  searchKeyword: '',
  issueStatusList: [],
  issueSeverityList: [],
  issuePhraseList: [],
  issueCategoriesList: [],
  issuePriorityList:[]
}

const getters = {
  showIssueDetail(state) {
    return state.showIssueDetail
  },

  selectIssueID(state) {
    return state.selectIssueID
  },

  issueChange(state) {
    return state.issueChange
  },

  issueFilters(state) {
    return state.issueFilters
  },

  searchKeyword(state) {
    return state.searchKeyword
  },

  getIssueSeverityList(state) {
    state.issueSeverityList.forEach(element => {
      element.label = element.Name
    });
    return state.issueSeverityList
  }

}

const mutations = {
  setIssueChange(state, isChange) {
    state.issueChange = isChange
  },

  setIssueFilters(state, filters) {
    state.issueFilters = filters
  },

  setShowIssueDetail(state, isShow) {
    state.showIssueDetail = isShow
  },

  setSelectIssueID(state, issueID) {
    state.selectIssueID = issueID
  },

  setSearchKeyword(state, keyword) {
    state.searchKeyword = keyword
  },

  setIssueStatusList(state, sList) {
    state.issueStatusList = sList
  },

  setIssueSeverity(state, sList) {
    state.issueSeverityList = sList
  },

  setIssuePhraseList(state, sList) {
    state.issuePhraseList = sList
  },

  setIssueCategoriesList(state, cList) {
    state.issueCategoriesList = cList
  },

  setIssuePriorityList(state, pList) {
    state.issuePriorityList = pList
  }
}

const actions = {
  setIssueChangeAction(context, isChanged) {
    context.commit('setIssueChange', isChanged)
  },

  getIssueStatus(content) {
    getIssueStatusApi().then(response => {
      content.commit('setIssueStatusList', response.data.result)
    })
  },

  getIssueSeverity(content) {
    getIssueSeverityApi().then(response => {
      content.commit('setIssueSeverity', response.data.result)
    })
  },

  getIssuePhrase(content) {
    getIssuePhraseApi().then(response => {
      content.commit('setIssuePhraseList', response.data.result)
    })
  },

  getIssueCategories(content) {
    getIssueCategoriesApi().then(response => {
      content.commit('setIssueCategoriesList', response.data.result)
    })
  },

  getIssuePriority(content) {
    getIssuePriorityApi().then(response => {
      content.commit('setIssuePriorityList', response.data.result)
    })
  },
}
const modules = {}

export default {
  namespaced: true,
  actions,
  getters,
  state,
  mutations,
  modules
}
