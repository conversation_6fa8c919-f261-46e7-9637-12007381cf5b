import {
  getProjectInfo,
} from "../../pages/project/business-service/ProjectApiService"

const state = {
  createDialogShow: false,
  caseSelectDialogShow: false,
  viewDialogShow: false,
  importXmindFile: false,
  projectVersion: 0,
  project: 0,
  projectID: 0,
  objectChange: false,
  rightSidePanelShow: false,
  taskViewMode: 'board',
  createDocumentType: 0,
  createReqType: 0,
  projectRole: 1,
  dateRange: '',
  headMenu: {
    newObject: true,
    searchBox: false,
    versionBox: false,
    dateRangeBox: false,
    documentNew: false,
    reqNew: false,
    issueExport: false,
    taskViewMode: false,
    taskFilter: false,
    importMindFile: false
  },
  projectList: [
    {
      id: 0,
      members: [],
      versions: [],
      modeles: [],
    }
  ]
}

const getters = {
  caseSelectDialogShow(state) {
    return state.caseSelectDialogShow
  },
  viewDialogShow(state) {
    return state.viewDialogShow
  },
  projectVersion(state) {
    return state.projectVersion
  },
  project(state) {
    return state.project
  },
  objectChange(state) {
    return state.objectChange
  },
  rightSidePanelShow(state) {
    return state.rightSidePanelShow
  },

  taskViewMode(state) {
    return state.taskViewMode
  },

  createDocumentType(state) {
    return state.createDocumentType
  },

  createReqType(state) {
    return state.createReqType
  },

  projectRole(state) {
    return state.projectRole
  },

  headMenu(state) {
    return state.headMenu
  },

  dateRange(state) {
    return state.dateRange
  },

  importXmindFile(state) {
    return state.importXmindFile
  },

  getProjectInfo(state, projectID) {
    projectInfo = state.projectList.find((project) => project.id === projectID);
    return projectInfo
  },
}
const mutations = {
  setCreateDialogShow(state, isShow) {
    state.createDialogShow = isShow
  },
  setCaseSelectDialogShow(state, isShow) {
    state.caseSelectDialogShow = isShow
  },
  setViewDialogShow(state, isShow) {
    state.viewDialogShow = isShow
  },
  setProjectVersion(state, versionID) {
    state.projectVersion = versionID
  },

  setProject(state, project) {
    state.project = project
  },

  setObjectChange(state, isChange) {
    state.objectChange = isChange
  },

  setRightPanelShow(state, isShow) {
    state.rightSidePanelShow = isShow
  },

  setTaskViewMode(state, mode) {
    state.taskViewMode = mode
  },

  setCreateDocumentType(state, type) {
    state.createDocumentType = type
  },

  setCreateReqType(state, type) {
    state.createReqType = type
  },

  setProjectRole(state, role) {
    state.projectRole = role
  },

  setHeadMenu(state, menu) {
    state.headMenu = menu
  },

  setDateRange(state, dateRange) {
    state.dateRange = dateRange
  },
  setImportXmindFile(state, importFile) {
    state.importXmindFile = importFile
  },

  setprojectID(state, id) {
    state.projectID = id
  },

  updateProjectList(state, projectInfo) {
    const index = state.projectList.findIndex((project) => project.id === projectInfo.id);
    if (index === -1) {
      state.projectList.push(projectInfo);
    }

    if (index !== -1) {
      state.projectList.splice(index, 1, projectInfo);
    }
  },

}

const actions = {
  setCreateDialogShowAction(context, isShow) {
    context.commit('setCreateDialogShow', isShow)
  },
  setViewDialogShowAction(context, isShow) {
    context.commit('setViewDialogShow', isShow)
  },
  setProjectVersionAction(context, versionID) {
    context.commit('setProjectVersion', versionID)
  },
  setObjectChangeAction(context, isChanged) {
    context.commit('setObjectChange', isChanged)
  },
  loadProjectInfo({ commit, dispatch }, projectID) {
    getProjectInfo(projectID).then(response => {
      commit('updateProjectList', response.data.result)
    })
  },
}
const modules = {}

export default {
  namespaced: true,
  actions,
  getters,
  state,
  mutations,
  modules
}
