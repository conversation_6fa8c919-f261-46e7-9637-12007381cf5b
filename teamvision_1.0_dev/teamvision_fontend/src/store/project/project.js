import {
  loadProjectFortestings,
  getProjectInfo,
  loadProjectMembers,
  loadProjectVersions,
  loadProjectModules
} from "../../pages/project/business-service/ProjectApiService"
import { getProjectListApi, getProjectCaseReviewListApi } from '../../api/project'
import { getRequirementListApi } from '../../api/reuqirement'

export default {
  state: {
    currentProject: {
      PBTitle: "当前项目"
    },
    projectVersions: [],
    projectMembers: [],
    projectModules: [],
    projectLatestVersion: 0,
    projectCaseCount: 0,
    projectFortestings: [],
    projectCreateDialogShow: false,
    projectSearchKey: '',
    projectAdded: false,
    projectList: [],
    projectsVersions: [],
    showLoadingProjectList: true,
    projectRequirements: [],
    projectCaseReviews: []
  },

  getters: {
    projectCreateDialogShow(state) {
      return state.projectCreateDialogShow
    },

    projectSearch<PERSON><PERSON>(state) {
      return state.projectSearchKey
    },

    projectAdded(state) {
      return state.projectAdded
    },

    getFilterProjectList(state, mutations) {
      mutations.filterProjectList
      return state.projectList
    },

    getFortestingsForStatus(state) {
      let forTestingList = []
      for (let i = 0; i < state.projectFortestings.length; i++) {
        if (state.projectFortestings[i].Status == 2) {
          forTestingList.push(state.projectFortestings[i])
        }
      }
      return forTestingList
    },

    getProjectCaseReviews(state) {
      return state.projectCaseReviews.results
    }
  },

  mutations: {
    setProjectCreateDialogShow(state, isShow) {
      state.projectCreateDialogShow = isShow
    },

    setProjectSearchKey(state, key) {
      state.projectSearchKey = key
    },

    setProjectAdded(state, key) {
      state.projectAdded = key
    },

    setMyProjectList(state, plist) {
      state.projectList = plist
    },

    setProjectsVersions(state) {
      for (let i = 0; i < state.projectList.length; i++) {
        let tempProject = {}
        tempProject.value = state.projectList[i].id
        tempProject.label = state.projectList[i].PBTitle
        tempProject.children = []
        for (let j = 0; j < state.projectList[i].Versions.length; j++) {
          let tempChild = {}
          tempChild.label = state.projectList[i].Versions[j].VVersion
          tempChild.value = state.projectList[i].Versions[j].id
          tempProject.children.push(tempChild)
        }
        state.projectsVersions.push(tempProject)
      }
    },

    filterProjectList(state) {
      for (let i = 0; i < state.projectList.length; i++) {
        state.projectList[i].Display = true;
        let temp = state.projectList[i];
        if (temp.PBTitle.toUpperCase().indexOf(state.projectSearchKey.toUpperCase()) < 0) {
          temp.Display = false;
        }
      }
    },

    setShowLoadingProjectList(state, bool) {
      state.showLoadingProjectList = bool
    },

    setCurrentProject(state, data) {
      state.currentProject = data
      state.projectLatestVersion = data.LatestVersion
      state.projectCaseCount = data.view_data.case_count
    },

    setProjectFortestings(state, data) {
      state.projectFortestings = data.results
    },

    setProjectVersions(state, versions) {
      state.projectVersions = versions
    },

    setProjectMembers(state, members) {
      state.projectMembers = members
    },

    setPorjectModules(state, modules) {
      state.projectModules = modules
    },

    setProjectRequirements(state, reuqirementList) {
      state.projectRequirements = reuqirementList
    },

    setProjectCaseReviews(state, reviewList) {
      state.projectCaseReviews = reviewList
    },

    updateProjectCaseReviews(state, reviewList) {
      state.projectCaseReviews.push(reviewList)
    }

  },

  actions: {
    loadMyProjectList({ state, commit }, urlArgs = 'extinfo=0') {
      return new Promise((resolve, reject) => {
        getProjectListApi(urlArgs).then(res => {
          const data = res.data.result
          commit('setMyProjectList', data)
          //commit('setProjectsVersions')
          commit('setShowLoadingProjectList', false)
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },

    setProjectCreateDialogShowAction(context, isShow) {
      context.commit('setProjectSearchKey', isShow)
    },

    setProjectSearchKeyAction(context, key) {
      context.commit('setProjectSearchKey', key)
    },

    setProjectAddedAction(context, key) {
      context.commit('setProjectAdded', key)
    },

    loadProjectInfo({ commit, dispatch }, projectID) {
      getProjectInfo(projectID).then(response => {
        commit('setCurrentProject', response.data.result)
        commit('setProjectVersions', response.data.result.Versions)
        commit('setProjectMembers', response.data.result.Members)
        commit('setPorjectModules', response.data.result.Modules)
        //dispatch('getProjectMembers', projectID)
        //dispatch('getProjectVersions', projectID)
        dispatch('loadProjectReqirements', { projectID: projectID, urlArgs: 'page_size=100&ordering=-id' })
      })
    },

    getProjectFortestings(context, { projectID, versionId }) {
      loadProjectFortestings(projectID, versionId).then(response => {
        context.commit('setProjectFortestings', response.data.result)
      })
    },

    getProjectMembers({ commit, dispatch }, projectID) {
      loadProjectMembers(projectID).then(response => {
        commit('setProjectMembers', response.data.result)
      })
    },

    getProjectVersions({ commit, dispatch }, projectID) {
      loadProjectVersions(projectID).then(response => {
        commit('setProjectVersions', response.data.result.all_versions)
      })
    },

    getProjectModules({ commit, dispatch }, projectID) {
      loadProjectModules(projectID).then(response => {
        commit('setProjectModules', response.data.result)
      })
    },

    loadProjectReqirements({ commit }, { projectID, urlArgs = '' }) {
      getRequirementListApi(projectID, urlArgs).then(response => {
        const data = response.data.result.results
        commit('setProjectRequirements', data)
      })
    },

    loadProjectTestCaseReviews({ commit }, { projectID, urlArgs = '' }) {
      getProjectCaseReviewListApi(projectID, urlArgs).then(response => {
        const data = response.data.result
        commit('setProjectCaseReviews', data)
      })
    },

  },

  namespaced: true,
}
