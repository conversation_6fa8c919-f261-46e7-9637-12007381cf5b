// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App.vue'
import axios from 'axios'
import VueAxios from 'vue-axios'
import router from './router'
import store from './store'
import 'jquery/dist/jquery'
import cookie from 'js-cookie'

import './assets/teamvision/global/less/global.less'
import './assets/teamvision/global/less/MyIviewTheme.less'

import '@progress/kendo-ui'
import '@progress/kendo-theme-material/dist/all.css'
import '@progress/kendo-ui/js/messages/kendo.messages.zh-cn.js'

// iview
import ViewUI from 'view-design';
import 'view-design/dist/styles/iview.css';
import './assets/teamvision/global/less/MyIviewTheme.less';
Vue.use(ViewUI, {
  size: 'small',
})

// elementUI
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// fontawesome
import 'font-awesome/css/font-awesome.min.css'

// vue-kityminder-editor
import 'vue-kityminder-editor/lib/kityMinder.css';
import kityMinder from 'vue-kityminder-editor'
Vue.use(kityMinder)


// vue-video-player  
import VideoPlayer from 'vue-video-player/src';
import 'vue-video-player/src/custom-theme.css'
import 'video.js/dist/video-js.css'
Vue.use(VideoPlayer)

axios.defaults.withCredentials = true
// axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded'

Vue.prototype.$axios = axios
Vue.prototype.$cookie = cookie
Vue.config.productionTip = false

axios.interceptors.request.use(
  config => {
    //let defSpaceId = store.state.usercenter.defSpace.id
    let defSpace = JSON.parse(localStorage.getItem('ProductSpace'))
    if (defSpace != null) {
      let defaultParams = {
        ProductSpace: defSpace.id,
      }
      config.headers['Product-Space'] = defSpace.id
      // 默认值与接口传来的参数进行合并（注：接口参数与默认值不可重复）
      config.data = Object.assign(defaultParams, config.data)
    }
    return config
  }, function (error) {
    return Promise.reject(error)
  })

axios.interceptors.response.use(function (response) {
  return response;
}, function (error) {
  return Promise.reject(error);
})

const app = new Vue({
  router,
  store,
  axios,
  VueAxios,
  render: h => h(App)
})
app.$mount('#teamvision')
