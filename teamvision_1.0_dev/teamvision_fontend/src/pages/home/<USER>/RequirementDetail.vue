<template>
  <div>
    <Tabs value="name1">
      <TabPane label="需求信息" name="name1">
        <div v-if="reqItem !== 0">
          <Form ref="createRequirement" :model="formItem" label-position="top">
            <div style="display: flex;">
              <div style="flex: 4; padding: 10px; margin-right: 10px;">
                <FormItem label="标题" prop="Title">
                  <Input v-model="formItem.Title" placeholder="需求概述" />
                </FormItem>
                <Row :gutter="12">
                  <Col span="8">
                  <FormItem label="项目" prop="projectID">
                    <Select v-model="formItem.projectID" placeholder="需求所属项目" @on-change="onProjectChange">
                      <Option v-for="project in sourceProject" :key="project.id" :value="project.id">{{ project.PBTitle
                        }}
                      </Option>
                    </Select>
                  </FormItem>
                  </Col>
                  <Col span="8">
                  <FormItem label="版本" prop="Version">
                    <Select v-model="formItem.Version" placeholder="需求所属版本规划" @on-change="onVersionChange">
                      <Option v-for="version in projectVersions" :key="version.id" :value="version.id">{{
                        version.VVersion
                      }}
                      </Option>
                    </Select>
                  </FormItem>
                  </Col>
                  <Col span="8">
                  <FormItem label="模块" prop="Module">
                    <Select v-model="formItem.Module" filteranpm ruble placeholder="需求所属模块">
                      <Option v-for="pm in projectModules" :key="pm.id" :value="pm.id">{{ pm.Name }}</Option>
                    </Select>
                  </FormItem>
                  </Col>
                </Row>
                <FormItem label="描述" prop="Desc">
                  <vue-editor v-model="formItem.Description" :editorToolbar="editorToolBar"
                    placeholder="需求描述"></vue-editor>
                </FormItem>
                <FormItem>
                  <span @click="ok('createRequirement')">
                    <Button type="success" style="width: 80px; height:30px;" shape="circle">保存
                    </Button>
                  </span>
                </FormItem>
              </div>
              <div style="flex: 1; padding: 10px; margin-left: 10px;background: #fbfbfe;">
                <FormItem label="状态" prop="Status">
                  <Select v-model="formItem.Status" placeholder="状态">
                    <Option v-for="status in reqStatus" :key="status.id" :value="status.Status">{{ status.Desc }}
                    </Option>
                  </Select>
                </FormItem>
                <FormItem label="风险" prop="RiskLevel">
                  <Select v-model="formItem.RiskLevel" placeholder="需求风险">
                    <Option v-for="risk in reqRisk" :key="risk.id" :value="risk.Status">{{ risk.Desc }}
                    </Option>
                  </Select>
                </FormItem>
                <FormItem label="优先级" prop="Priority">
                  <Select v-model="formItem.Priority" placeholder="优先级">
                    <Option v-for="priority in reqPriority" :key="priority.id" :value="priority.Status">{{ priority.Desc
                      }}
                    </Option>
                  </Select>
                </FormItem>
                <FormItem label="负责人" prop="Owner">
                  <Select v-model="formItem.Owner" filterable placeholder="需求负责人">
                    <Option v-for="member in projectMembers" :key="member.id" :value="member.PMMember">{{ member.name }}
                    </Option>
                  </Select>
                </FormItem>
                <FormItem label="发版日期" prop="ReleaseDate">
                  <DatePicker type="date" v-model="formItem.ReleaseDate" placeholder="预计发版日期"></DatePicker>
                </FormItem>
                <FormItem label="关联用例" prop="TestPointFile">
                  <Select v-model="formItem.TestPointFile" :filterable="true" placeholder="相关用例所属文件">
                    <Option v-for="file in testPointFileList" :key="file.id" :value="file.id">{{ file.FileName }}
                    </Option>
                  </Select>
                </FormItem>
                <FormItem label="关联提测" prop="FortestingID">
                  <Select v-model="formItem.FortestingID" :filterable="true" placeholder="关联提测">
                    <Option v-for="fortesting in fortestingList" :key="fortesting.id" :value="fortesting.id">{{
                      fortesting.Topic }}
                    </Option>
                  </Select>
                </FormItem>
              </div>
            </div>
          </Form>
          <div>
          </div>
        </div>
      </TabPane>
      <TabPane label="关联任务" name="name2">
        <requirement-task :itemID="reqItem" :projectID="projectID" :version="projectVersion" :itemType="1">
        </requirement-task>
      </TabPane>
      <TabPane label="生命周期" name="name3">需求生命周期事件</TabPane>
    </Tabs>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import { VueEditor } from "vue2-editor";
import RequirementTask from "./ReqirementTask.vue";

export default {
  name: "RequirementDetail",
  props: ["projectID", "reqItem"],
  data() {
    return {
      sourceProject: [],
      testPointFileList: [],
      fortestingList: [],
      reqStatus: [],
      reqRisk: [],
      reqPriority: [],
      editorToolBar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ color: [] }, { background: [] }],
      ],
      formItem: {
        Title: "",
        Module: 0,
        FortestingID: 0,
        Description: "需求描述或者需求文档连接",
        projectID: 0,
        TestPointFile: 0,
        Version: 0,
        ReleaseDate: "",
        Status: 9,
        Owner: 0,
        Creator: 0,
        RiskLevel: 0,
        Desc: "",
        StartDate: "",
        FinisheDate: "",
        DevelopTime: "",
        TestTime: "",
      },
    };
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState('project', ['projectList', 'projectVersions', 'projectModules', 'projectMembers']),

    randomColor: function () {
      return "#99aecc";
    },
  },

  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange", "setProjectVersion",]),

    getCurrentTime() {
      //获取当前时间并打印
      var _this = this;
      var currentData = new Date();
      let yy = currentData.getFullYear();
      let mm = currentData.getMonth() + 1;
      let dd = currentData.getDate();
      let hh = currentData.getHours();
      let mf = currentData.getMinutes() < 10 ? "0" + currentData.getMinutes() : currentData.getMinutes();
      let ss = currentData.getSeconds() < 10 ? "0" + currentData.getSeconds() : currentData.getSeconds();
      _this.gettime = yy + "-" + mm + "-" + dd + "T" + hh + ":" + mf + ":" + ss + ".000001" + "+08:00";
      //console.log(_this.gettime);
      return _this.gettime;
    },

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if ((this.formItem.ReleaseDate + "").length > 10) {
            let s = new Intl.DateTimeFormat("zh-cn");
            this.formItem.ReleaseDate = s
              .format(this.formItem.ReleaseDate)
              .replace("/", "-")
              .replace("/", "-");
          }
          if (this.formItem.Status == 1) {
            this.formItem.StartDate = this.getCurrentTime();
          }
          if (this.formItem.Status == 3) {
            this.formItem.DevelopTime = this.getCurrentTime();
          }
          if (this.formItem.Status == 4) {
            this.formItem.TestTime = this.getCurrentTime();
          }
          if (this.formItem.Status == 6) {
            this.formItem.WaitOnlineTime = this.getCurrentTime();
          }
          if (this.formItem.Status == 7) {
            this.formItem.FinishedDate = this.getCurrentTime();
          }
          this.$axios.put("/api/project/requirement/" + this.reqItem + "/", this.formItem).then(
            (response) => {
              this.$emit("updateReqItem", response.data.result);
              this.$Message.success({
                content: "保存成功",
                duration: 10,
                closable: true,
              });
            },
            (response) => {
              this.$Message.error({
                content: "保存失败",
                duration: 10,
                closable: true,
              });
            }
          );
        }
      });
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url;
      file.id = res.result.file_id;
      this.formItem.uploadList.push(file.id);
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList;
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: "文件格式不正确,格式：'jpg','jpeg','png'",
        duration: 10,
        closable: true,
      });
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: "文件大小超过10M限制",
        duration: 10,
        closable: true,
      });
    },

    onProjectChange(value) {
      let project = value;
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === project) {
          this.projectModules = this.sourceProject[i].Modules;
        }
      }
      this.loadTestCaseFile(value, 0);
      this.loadFortesting(this.formItem.projectID, 0);
    },

    onVersionChange: function (value) {
      this.loadTestCaseFile(this.formItem.projectID, value);
      this.loadFortesting(this.formItem.projectID, value);
    },

    uploadIssueAttachment: function () { },

    loadMyProject: function () {
      this.sourceProject = this.projectList
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === this.formItem.projectID) {
          this.projectModules = this.sourceProject[i].Modules;
        }
      }
    },

    loadTestCaseFile: function (projectID, versionID) {
      if (versionID + "" === "undefined") {
        versionID = 0;
      }
      this.$axios.get("/api/project/" + projectID + "/version/" + versionID + "/mindmap_files").then((response) => {
        this.testPointFileList = response.data.result;
      }, (response) => { }
      );
    },

    loadFortesting: function (projectID, versionID) {
      if (versionID + "" === "undefined") {
        versionID = 0;
      }
      this.$axios.get("/api/project/" + projectID + "/version/" + versionID + "/fortestings?page_size=10000").then((response) => {
        this.fortestingList = response.data.result.results;
      }, (response) => { }
      );
    },

    loadRequirement: function (itemID) {
      this.$axios.get("/api/project/requirement/" + itemID).then((response) => {
        //console.log(response.data.result);
        this.formItem = response.data.result;
        this.loadMyProject();
        this.loadTestCaseFile(this.formItem.projectID, this.formItem.Version);
        this.loadFortesting(this.formItem.projectID, this.formItem.Version);
      }, (response) => { }
      );
    },

    loadReqPriority: function () {
      this.$axios.get("/api/project/task/task_status").then((response) => {
        this.reqStatus = [];
        this.reqPriority = [];
        this.reqRisk = [];
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i];
          if (tempItem.Type === 3) {
            this.reqStatus.push(tempItem);
          }
          if (tempItem.Type === 4) {
            this.reqPriority.push(tempItem);
          }
          if (tempItem.Type === 5) {
            this.reqRisk.push(tempItem);
          }
        }
      }, (response) => { }
      );
    },
  },

  created: function () {
    this.loadReqPriority();
  },
  watch: {
    reqItem: function (value) {
      //console.log(value);
      this.loadRequirement(value);
      //console.log(this.reqStatus);
    },
    projectID: function (value) { },
  },

  components: {
    VueEditor,
    RequirementTask,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;
}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}

.demo-upload-list {
  display: inline-block;
  width: 100px;
  height: 100px;
  text-align: center;
  margin: 5px;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
</style>
