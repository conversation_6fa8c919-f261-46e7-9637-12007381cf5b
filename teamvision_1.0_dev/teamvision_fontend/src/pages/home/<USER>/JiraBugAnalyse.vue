<template>
  <Card dis-hover>
    <p slot="title">
      <Icon type="bug" size="40"></Icon>
      问题统计-jira
      <Button @click="isshow_bug_details = true" type="text" size="small">详细数据</Button>
    </p>
    <p slot="extra">
      <Date-picker :value="defaultDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期"
        size="small" @on-change="handleChange" style="width: 220px; padding-left:10px;padding-right:10px">
      </Date-picker><Button type="primary" shape="circle" icon="ios-search" size="small"
        @click="getBugDataJira">搜索</Button>
    </p>
    <div>
      <Row type="flex" justify="space-between">
        <Col span="8" style="padding: 5px;">
        <div id="projectBugStatisticsJira" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
        <Col span="8" style="padding: 5px;">
        <div id="autoBugStatisticsJira" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
        <Col span="8" style="padding: 5px;">
        <div id="p0BugStatisticsJira" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
      </Row>
      <Drawer title="Bug统计详情" :closable="false" v-model="isshow_bug_details" width="1200">
        <Table border :columns="columns_bug" :data="bug_data"></Table>
        <div style="margin: 10px; overflow: hidden">
          <div style="float: right;">
            <Page :total="bug_data.length - 1" size="small" show-total></Page>
          </div>
        </div>
      </Drawer>
    </div>
  </Card>
</template>

<script>
import * as echarts from "echarts";

export default {
  components: {},
  name: "JiraBugAnalyse",
  data() {
    return {
      columns_bug: [
        {
          type: "index",
          width: 60,
          align: "center",
        },
        {
          title: "项目组",
          key: "group_name",
        },
        {
          title: "总数",
          width: 70,
          key: "bug_total",
        },
        {
          title: "线上",
          width: 70,
          key: "bug_online",
        },
        {
          title: "线下",
          width: 70,
          key: "bug_offline",
        },
        {
          title: "线上隐藏",
          key: "bug_online_hidden",
        },
        {
          title: "自动化",
          key: "bug_automation",
        },
        {
          title: "流量回放",
          key: "bug_flow_playback",
        },
        {
          title: "P0级",
          width: 70,
          key: "bug_p0",
        },
        {
          title: "P1级",
          width: 70,
          key: "bug_p1",
        },
        {
          title: "旧需求引入Bug",
          key: "old_demand_bug_total",
        },
        {
          title: "测试阶段(自动化/全部)",
          key: "bug_distage_test",
        },
        {
          title: "线上全量(自动化/全部)",
          key: "bug_distage_online_full",
        },
        {
          title: "线上灰度(自动化/全部)",
          key: "bug_distage_online_gated",
        },
        {
          title: "沙盒灰度(自动化/全部)",
          key: "bug_distage_sandbox_gated",
        },
      ],
      bug_data: [],
      project_bug_data: [],
      automation_bug_data: [],
      p0p1_bug_data: [],
      defaultDate: [],
      bug_automation: 0,
      bug_manual: 0,
      projectBugEchart: null,
      automationBugEchart: null,
      p0p1BugEchart: null,
      isshow_bug_details: false,
    };
  },
  computed: {},
  methods: {
    get_filter_date: function (days) {
      let nowDate = new Date();
      let nowYear = nowDate.getFullYear();
      let nowMonth = (nowDate.getMonth() + 1).toString().padStart(2, '0');
      let nowDay = nowDate.getDate().toString().padStart(2, '0');
      let endDate = nowYear + "-" + nowMonth + "-" + nowDay;

      let beforeDate = new Date(nowDate.getTime() - days * 24 * 3600 * 1000)
      let startY = beforeDate.getFullYear();
      let startM = (beforeDate.getMonth() + 1).toString().padStart(2, '0');
      let startD = beforeDate.getDate().toString().padStart(2, '0');
      let startDate = startY + "-" + startM + "-" + startD;

      this.defaultDate[0] = startDate;
      this.defaultDate[1] = endDate;
      //console.log("defaultDate=", this.defaultDate)
    },

    getBugDataJira: function () {
      this.$axios.get("/materils_v2.0/data/bug_stat_all?after=" + this.defaultDate[0] + "&before=" + this.defaultDate[1]).then((response) => {
        this.bug_data = response.data;
      });
    },

    createBugStatEchart: function (chart_id, chart_name) {
      let init_echart = echarts.init(document.getElementById(chart_id));
      let option = {
        title: {
          text: chart_name,
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "right",
        },
        series: [
          {
            label: {
              formatter: "{b}: {@c} ({d}%)",
            },
            name: chart_name,
            type: "pie",
            radius: "45%",
            data: [{ value: 0, name: "0" }],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize();   //myChart指自己定义的echartsDom对象
      });
      return init_echart;
    },

    initBugStatEchart: function () {
      this.projectBugEchart = this.createBugStatEchart(
        "projectBugStatisticsJira",
        "各项目 Bug 分析"
      );
      this.automationBugEchart = this.createBugStatEchart(
        "autoBugStatisticsJira",
        "自动化 Bug 占比"
      );
      this.p0p1BugEchart = this.createBugStatEchart(
        "p0BugStatisticsJira",
        "P0/P1 Bug 占比"
      );
    },

    handleChange: function (date) {
      this.defaultDate = date;
    },
  },

  created: function () {
    this.get_filter_date(7);
  },

  mounted: function () {
    this.initBugStatEchart();
    this.getBugDataJira();
  },

  watch: {
    bug_data() {
      this.project_bug_data = [];

      let project_bug_echart_option = this.projectBugEchart.getOption();
      let automation_bug_echart_option = this.automationBugEchart.getOption();
      let p0p1_bug_echart_opthion = this.p0p1BugEchart.getOption();

      for (let i = 0; i < this.bug_data.length; i++) {
        let project_bug = {
          value: this.bug_data[i]["bug_total"],
          name: this.bug_data[i]["group_name"],
        };
        this.project_bug_data.push(project_bug);

        if (this.bug_data[i]["group_name"] == "ALL") {
          let auto_bug = {
            value: this.bug_data[i]["bug_automation"],
            name: "自动化",
          };
          let trff_replay = {
            value: this.bug_data[i]["bug_flow_playback"],
            name: "流量回放",
          };
          let manual_tmp =
            this.bug_data[i]["bug_total"] -
            this.bug_data[i]["bug_automation"] -
            this.bug_data[i]["bug_flow_playback"];
          let manual = { value: manual_tmp, name: "手工" };
          this.automation_bug_data = [];
          this.automation_bug_data.push(auto_bug);
          this.automation_bug_data.push(manual);
          this.automation_bug_data.push(trff_replay);
          automation_bug_echart_option.series[0].data =
            this.automation_bug_data;
          this.automationBugEchart.setOption(automation_bug_echart_option);

          let p0 = { value: this.bug_data[i]["bug_p0"], name: "P0" };
          let p1 = { value: this.bug_data[i]["bug_p1"], name: "P1" };
          let other =
            this.bug_data[i]["bug_total"] -
            this.bug_data[i]["bug_p0"] -
            this.bug_data[i]["bug_p0"];
          let pother = { value: other, name: "其他" };
          this.p0p1_bug_data = [];
          this.p0p1_bug_data.push(p0);
          this.p0p1_bug_data.push(p1);
          this.p0p1_bug_data.push(pother);
          p0p1_bug_echart_opthion.series[0].data = this.p0p1_bug_data;
          this.p0p1BugEchart.setOption(p0p1_bug_echart_opthion);
        }
      }
      this.project_bug_data.pop();
      project_bug_echart_option.series[0].data = this.project_bug_data;
      this.projectBugEchart.setOption(project_bug_echart_option);
    },

    teststatus_data: {
      handler() {
        let option = this.teststatus_chart.getOption();
        option.xAxis[0].data = this.teststatus_data.categories;
        option.series[0].data = this.teststatus_data.series[0].data;
        option.series[1].data = this.teststatus_data.series[1].data;
        option.series[2].data = this.teststatus_data.series[2].data;
        //console.log(option);
        this.teststatus_chart.setOption(option);
      },
      deep: true,
    },
  },
};
</script>




