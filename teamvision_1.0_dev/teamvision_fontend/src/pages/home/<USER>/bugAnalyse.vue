<template>
  <Card dis-hover>
    <p slot="title">
      <Icon type="bug" size="40"></Icon> 问题统计
      <Button @click="isshow_bug_details = true" type="text">详细数据</Button>
    </p>
    <p slot="extra">
      <Date-picker :value="default_date" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期"
        @on-change="handleChange" style="width: 220px; padding-left:10px;padding-right:10px">
      </Date-picker>
      <Select v-model="selectProject" placeholder="选择项目" multiple filterable style="width:200px">
        <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}</Option>
      </Select>
      <Button type="primary" shape="circle" icon="ios-search" @click="getBugData">搜索</Button>
    </p>
    <div>
      <Row type="flex" justify="space-between">
        <Col span="6" style="padding: 5px;">
        <div id="bugStatistics" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
        <Col span="6" style="padding: 5px;">
        <div id="projectBugStatistics" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
        <Col span="6" style="padding: 5px;">
        <div id="autoBugStatistics" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
        <Col span="6" style="padding: 5px;">
        <div id="p0BugStatistics" style="min-width:300px;height:400px;margin-top:16px"></div>
        </Col>
      </Row>
      <Drawer title="Bug统计详情" :closable="false" v-model="isshow_bug_details" width="1200">
        <Table border :columns="columns_bug" :data="bug_data['project']"></Table>
        <div style="margin: 10px; overflow: hidden">
          <div style="float: right;">
            <Page :total="bug_data['project'].length" show-total page-size="40"></Page>
          </div>
        </div>
      </Drawer>
    </div>
  </Card>
</template>

<script>
import * as echarts from "echarts";
import { mapGetters, mapMutations, mapState } from 'vuex'

export default {
  components: {},
  name: "bugAnalyse",
  data() {
    return {
      columns_bug: [
        {
          type: "index",
          width: 60,
          align: "center",
        },
        {
          title: "项目",
          key: "title",
        },
        {
          title: "总数",
          width: 70,
          key: "total",
        },
        {
          title: "P0级",
          width: 70,
          key: "p0",
        },
        {
          title: "P1级",
          width: 70,
          key: "p1",
        },
        {
          title: "P2级",
          width: 70,
          key: "p2",
        },
        {
          title: "P3级",
          width: 70,
          key: "p3",
        },
        {
          title: "自动化",
          width: 80,
          key: "auto",
        },
        {
          title: "流量回放",
          width: 90,
          key: "replay",
        },
      ],
      bug_data: {
        project: []
      },
      selectProject: [],
      default_date: [],
      bug_automation: 0,
      bug_manual: 0,
      bugEchart: null,
      projectBugEchart: null,
      automationBugEchart: null,
      p0p1BugEchart: null,
      isshow_bug_details: false,
    };
  },
  computed: {
    ...mapState('project', ['projectList']),
  },
  methods: {
    getFilterDate: function (days) {
      let nowDate = new Date();
      let nowYear = nowDate.getFullYear();
      let nowMonth = (nowDate.getMonth() + 1).toString().padStart(2, '0');
      let nowDay = nowDate.getDate().toString().padStart(2, '0');
      let endDate = nowYear + "-" + nowMonth + "-" + nowDay;

      let beforeDate = new Date(nowDate.getTime() - days * 24 * 3600 * 1000)
      let startY = beforeDate.getFullYear();
      let startM = (beforeDate.getMonth() + 1).toString().padStart(2, '0');
      let startD = beforeDate.getDate().toString().padStart(2, '0');
      let startDate = startY + "-" + startM + "-" + startD;

      this.default_date[0] = startDate;
      this.default_date[1] = endDate;
    },

    getBugData: function () {
      let ReqUrl = "/api/home/<USER>/issue?st=" + this.default_date[0] + "&et=" + this.default_date[1]
      if (this.selectProject.length > 0) {
        ReqUrl = ReqUrl + "&project=" + this.selectProject
      }
      this.$axios.get(ReqUrl).then((response) => {
        this.bug_data = response.data.result;
        this.updateCharts()
      });
    },

    createBugStatEchart: function (chart_id, chart_name) {
      let init_echart = echarts.init(document.getElementById(chart_id));
      let option = {
        title: {
          text: chart_name,
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: 'scroll',
          right: "5%",
          left: "5%",
          bottom: 0,
        },
        series: [
          {
            label: {
              formatter: "{b}: {@c} ({d}%)",
            },
            name: chart_name,
            type: "pie",
            radius: "50%",
            data: [{ value: 0, name: "0" }],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize(); //myChart指自己定义的echartsDom对象
      });
      return init_echart;
    },

    initBugStatEchart: function () {
      this.bugEchart = this.createBugStatEchart(
        "bugStatistics",
        "Bug 分析"
      );
      this.projectBugEchart = this.createBugStatEchart(
        "projectBugStatistics",
        "Bug 分析(项目)"
      );
      this.automationBugEchart = this.createBugStatEchart(
        "autoBugStatistics",
        "自动化 Bug 占比"
      );
      this.p0p1BugEchart = this.createBugStatEchart(
        "p0BugStatistics",
        "P0/P1 Bug 占比"
      );
    },

    updateCharts: function () {
      let bugChartSeriesData = [
        {
          value: this.bug_data["open"],
          name: '未处理',
        },
        {
          value: this.bug_data["close"],
          name: '已关闭',
        },
        {
          value: this.bug_data["fix"],
          name: '已解决',
        },
        {
          value: this.bug_data["reopen"],
          name: '重新打开',
        },
      ]
      let bug_echart_option = this.bugEchart.getOption();
      bug_echart_option.series[0].radius = ['25%', '50%']
      bug_echart_option.series[0].data = bugChartSeriesData;
      let subTest = '新增: ' + this.bug_data["total"]
      bug_echart_option.title[0].subtext = subTest
      this.bugEchart.setOption(bug_echart_option);

      let severityChartSeriesData = [
        {
          value: this.bug_data['severity']['p0'],
          name: 'p0',
        },
        {
          value: this.bug_data['severity']['p1'],
          name: 'p1',
        },
        {
          value: this.bug_data['severity']['p2'],
          name: 'p2',
        },
        {
          value: this.bug_data['severity']['p3'],
          name: 'p3',
        },
        {
          value: this.bug_data['severity']['p4'],
          name: 'p4',
        },
      ]
      let severityBugEchartOpthion = this.p0p1BugEchart.getOption();
      severityBugEchartOpthion.series[0].data = severityChartSeriesData;
      this.p0p1BugEchart.setOption(severityBugEchartOpthion);


      let autoBugChartSeriesData = [
        {
          value: this.bug_data['discover_way']['manual'],
          name: '手工',
        },
        {
          value: this.bug_data['discover_way']['auto'],
          name: '自动化',
        },
        {
          value: this.bug_data['discover_way']['replay'],
          name: '流量回放',
        },
        {
          value: this.bug_data['discover_way']['monitor'],
          name: '监控',
        },
        {
          value: this.bug_data['discover_way']['other'],
          name: '其他',
        },
      ]
      let automation_bug_echart_option = this.automationBugEchart.getOption();
      automation_bug_echart_option.series[0].data = autoBugChartSeriesData;
      this.automationBugEchart.setOption(automation_bug_echart_option);


      this.projectBugEchartData = [];
      for (let i = 0; i < this.bug_data.project.length; i++) {
        let project_bug = {
          value: this.bug_data.project[i]['total'],
          name: this.bug_data.project[i]['title'],
        };
        this.projectBugEchartData.push(project_bug);
      }
      let projectBugEchartOption = this.projectBugEchart.getOption();
      projectBugEchartOption.series[0].data = this.projectBugEchartData;
      this.projectBugEchart.setOption(projectBugEchartOption);
    },

    handleChange: function (date) {
      this.default_date = date;
    },
  },

  created: function () {
    this.getFilterDate(7)
    this.getBugData()
  },

  mounted: function () {
    this.initBugStatEchart();
  },

  watch: {
    teststatus_data: {
      handler() {
        let option = this.teststatus_chart.getOption();
        option.xAxis[0].data = this.teststatus_data.categories;
        option.series[0].data = this.teststatus_data.series[0].data;
        option.series[1].data = this.teststatus_data.series[1].data;
        option.series[2].data = this.teststatus_data.series[2].data;
        this.teststatus_chart.setOption(option);
      },
      deep: true,
    },
  },
};
</script>
