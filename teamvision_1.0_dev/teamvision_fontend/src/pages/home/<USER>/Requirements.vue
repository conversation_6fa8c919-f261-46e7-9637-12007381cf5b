<template>
  <div>
    <Card :padding="2" :bordered="false" :dis-hover="true">
      <div slot="title">
        <span style="margin-right: 10px;" v-if="$route.name === 'homeRequirement'">
          <span>项目:</span>
          <Select v-model="selectedProject" style="width:200px" size="small" filterable placeholder="选择项目"
            @on-change="onProjectChange">
            <Option :value="0">全部</Option>
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </span>
        <span style="margin-right: 10px;">
          <span>版本规划:</span>
          <Select v-model="selectedVersion" style="width:200px" size="small" filterable placeholder="选择版本"
            @on-change="onVersionChange">
            <Option :value="0">全部</Option>
            <!-- <Option :value="0">需求池</Option> -->
            <Option v-for="item in projectVersions" :value="item.id" :key="item.id">{{ item.VersionLabel }}</Option>
          </Select>
        </span>
        <span style="margin-right: 10px;">
          <span>状态:</span>
          <Select v-model="filterStatus" style="width:100px" size="small" filterable placeholder="选择状态"
            @on-change="filterReqirements">
            <Option :value="0">全部</Option>
            <Option v-for="item in reqStatus" :value="item.Status" :key="item.id">{{ item.Desc }}</Option>
          </Select>
        </span>
        <span style="margin-right: 10px;">
          <span>优先级:</span>
          <Select v-model="filterPriority" style="width:100px" size="small" filterable placeholder="选择优先级"
            @on-change="filterReqirements">
            <Option :value="0">全部</Option>
            <Option v-for="item in reqPriority" :value="item.Status" :key="item.id">{{ item.Desc }}</Option>
          </Select>
        </span>
        <span style="margin-right: 10px;">
          <span>风险:</span>
          <Select v-model="filterRisk" style="width:100px" size="small" filterable placeholder="选择风险"
            @on-change="filterReqirements">
            <Option :value="0">全部</Option>
            <Option v-for="item in reqRisk" :value="item.Status" :key="item.id">{{ item.Desc }}</Option>
          </Select>
        </span>
        <span style="cursor: pointer; ">
          <Button :loading="reloadReq" shape="circle" icon="md-refresh" @click="reloadReqlist"></Button>
        </span>
      </div>
      <div :style="'overflow-y:scroll;height:' + containerHeight + 'px;'">
        <requirement-item v-for="project in projectReqList" :projectReqInfo="project" :dynicLayout="defaultLayout"
          @selectReqItem="selectReqList" @viewReqItem="viewReqItem" :key="project.id">
        </requirement-item>
      </div>
    </Card>
    <Drawer title="需求信息" v-model="showRequirementDetail" :transfer="false" :inner="true" :width="50" :mask="false">
      <requirement-detail :reqItem="viewedReqItem" :projectID="projectID" @updateReqItem="updateReqItem">
      </requirement-detail>
    </Drawer>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import RequirementItem from './RequirementItem.vue'
import RequirementDetail from './RequirementDetail.vue'
import { loadProjectVersions, } from "../../project/business-service/ProjectApiService"

export default {
  name: 'projectRequirement',
  props: ['projectID', 'layout'],
  components: {
    RequirementItem,
    RequirementDetail,
  },
  data() {
    return {
      selectedProject: 0,
      selectedVersion: 0,
      sourceProject: [],
      projectVersions: [],
      //projectModules: [],
      //projectMembers: [],
      selectedReqItems: [],
      projectReqList: [],
      viewedReqItem: 0,
      reqStatus: [],
      reqRisk: [],
      reqPriority: [],
      filterStatus: 0,
      filterPriority: 0,
      filterRisk: 0,
      fileType: '0',
      reloadReq: false,
      showRequirementDetail: false,
      defaultLayout: {
        projectWidth: 0,
        otherLayout: {
          width: 24,
          featureLayout: {
            width: 3,
            requirementWidth: 21
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['objectChange', 'createReqType']),
    ...mapState(['appBodyHeight', 'appBodyMainHeight', 'appBodyWidth']),
    ...mapState('project', ['projectList']),

    containerHeight: function () {
      return this.appBodyHeight - 70
    },

    containerWidth: function () {
      return this.appBodyWidth
    },
  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange', 'setProjectVersion']),

    filterReqirements: function () {
      this.loadRequirementList(this.selectedVersion)
    },

    selectReqList: function (value) {
      this.selectedReqItems = []
      if (value && value.length > 0) {
        for (let i = 0; i < value.length; i++) {
          let tempData = { id: value[i], title: value[i] }
          this.selectedReqItems.push(tempData)
        }
      }
    },

    onProjectChange: function (value) {
      this.selectedProject = value
      loadProjectVersions(this.selectedProject).then(response => {
        this.projectVersions = response.data.result.all_versions
        //this.selectedVersion = this.projectVersions[0].id
      })
      this.loadRequirementList(this.selectedVersion)
    },

    onVersionChange: function (value) {
      this.selectedVersion = value
      this.loadRequirementList(this.selectedVersion)
    },

    viewReqItem: function (value) {
      this.viewedReqItem = value
      this.showRequirementDetail = true
    },

    reloadReqlist: function () {
      this.loadRequirementList(this.selectedVersion)
    },

    loadRequirementList: function (projectVersion) {
      this.reloadReq = true
      let filter = '?Status=' + this.filterStatus
      filter = filter + '&Priority=' + this.filterPriority
      filter = filter + '&Risk=' + this.filterRisk
      filter = filter + '&project=' + this.selectedProject
      filter = filter + '&version=' + this.selectedVersion
      this.$axios.get('/api/home/<USER>' + filter).then(response => {
        this.reloadReq = false
        this.projectReqList = response.data.result
      }, response => {
        this.reloadReq = false
      })
    },

    updateReqItem: function (reqItem) {
      this.loadRequirementList(this.selectedVersion)
    },

    loadReqPriority: function () {
      this.$axios.get('/api/project/task/task_status').then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 3) {
            this.reqStatus.push(tempItem)
          }
          if (tempItem.Type === 4) {
            this.reqPriority.push(tempItem)
          }
          if (tempItem.Type === 5) {
            this.reqRisk.push(tempItem)
          }
        }
      }, response => {
      })
    },

    loadMyProject: function () {
      this.sourceProject = this.projectList
      if (this.projectID && this.projectID !== 0) {
        this.selectedProject = this.projectID
        loadProjectVersions(this.selectedProject).then(response => {
          this.projectVersions = response.data.result.all_versions
        })
      } else {
        this.selectedProject = this.sourceProject[0].id
      }
    },

  },
  created: function () {
    this.loadReqPriority()
    if (this.layout) {
      this.defaultLayout = this.layout
    }
    if (this.$route.name === 'homeRequirement') {
      this.selectedProject = 0
    } else {
      this.selectedProject = this.projectID
    }
    this.reloadReqlist()
  },
  mounted: function () {
    if (this.$route.params.requirementID != undefined) {
      this.viewReqItem(this.$route.params.requirementID)
    }
    this.loadMyProject()
  },
  watch: {
    // selectedProject: function () {
    //   this.loadRequirementList(this.selectedVersion)
    // },

    // selectedVersion: function () {
    //   this.loadRequirementList(this.selectedVersion)
    // },

    layout: function () {
      if (this.layout) {
        this.defaultLayout = this.layout
      }
    }

  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;
}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}
</style>
