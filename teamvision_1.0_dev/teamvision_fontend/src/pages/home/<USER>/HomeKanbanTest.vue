<template>
  <Card dis-hover title="测试看板">
    <a href="#" slot="extra">
      <RadioGroup v-model="search_days" type="button" size="small" @on-change="loadTestKanbanStatistics">
        <Radio :label="7">7天</Radio>
        <Radio :label="15">15天</Radio>
        <Radio :label="30">1个月</Radio>
        <Radio :label="90">3个月</Radio>
      </RadioGroup>
    </a>
    <Row type="flex" justify="space-between">
      <Col span="12" style="padding: 5px;">
      <div v-loading="testStatusLoading" class="demand-content-item test-total">
        <NumbersCard :data="testtaskNum">
          <template slot="header">
            测试吞吐量（当前）
          </template>
        </NumbersCard>
      </div>
      </Col>
      <Col span="12" style="padding: 5px">
      <div v-loading="testStatusLoading">
        <NumbersCard :data="testeffNum">
          <template slot="header">
            测试效率
          </template>
        </NumbersCard>
      </div>
      </Col>
    </Row>
    <Row :gutter="1">
      <Col span="8">
      <div id="teststatus" style="min-width:400px;height:400px;margin-top:16px"></div>
      </Col>
      <Col span="8">
      <div id="testwaittime" style="min-width:400px;height:400px;margin-top:16px"></div>
      </Col>
      <Col span="8">
      <div id="testefficiency" style="min-width:400px;height:400px;margin-top:16px"></div>
      </Col>
    </Row>
  </Card>
</template>

<script>
import HighCharts from "highcharts";
import * as echarts from "echarts";
import { mapState, mapGetters } from "vuex";
import NumbersCard from "../../../components/numbercard/NumbersCard.vue";

export default {
  components: { NumbersCard },
  name: "HomeKanbanTestChart",
  data() {
    return {
      search_days: 7,
      teststatus_chart: null,
      teststatus_data: {
        categories: [],
        series: [],
      },
      testefficiency_chart: null,
      testwaittime_chart: null,
      testStatusLoading: true,
      testtaskData: {
        testtask: "提测",
        test_sum: 0,
        test_done: 0,
        test_wait: 0,
        test_ing: 0,
        demand: "需求",
        demand_sum: 0,
        demand_done: 0,
        demand_waittest: 0,
        demand_testing: 0,
      },
      testtaskKeys: [
        {
          key: "testtask",
          name: "(提测/需求)",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand",
        },
        {
          key: "test_sum",
          name: "总数",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_sum",
        },
        {
          key: "test_done",
          name: "已完成",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_done",
        },
        {
          key: "test_wait",
          name: "待测试",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_waittest",
        },
        {
          key: "test_ing",
          name: "测试中",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_testing",
        },
      ],
      testeffData: {
        testwait: 0,
        testtingtime_avg: 0,
        casecount_avg: 0,
      },
      testeffKeys: [
        {
          key: "testwait",
          name: "提测等待时间",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "小时",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "testtingtime_avg",
          name: "计划平均时长",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "casecount_avg",
          name: "计划平均用例数",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
      ],
    };
  },

  computed: {
    ...mapState(["appBodyHeight"]),
    containerHeight: function () {
      return this.appBodyHeight - 50;
    },

    testtaskNum() {
      const result = {};
      const keys = this.testtaskKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.testtaskData[item.key] || 0,
            title: item.name,
            demand: this.testtaskData[item.demand],
          };
        }
      });
      //console.log("testtaskNum()=", result);
      return result;
    },

    testeffNum() {
      const result = {};
      const keys = this.testeffKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.testeffData[item.key] || 0,
            title: item.name,
            unit: item.unit,
          };
        }
      });
      return result;
    },
  },

  methods: {
    //初始化 需求测试状态分布 堆叠柱状图
    initTestStatusEchart: function () {
      this.teststatus_chart = echarts.init(
        document.getElementById("teststatus")
      );
      let option = {
        title: {
          text: "需求测试状态分布",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        grid: {
          left: "5%",
          right: "5%",
          bottom: "40%",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: ["-"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 16,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "新建",
            type: "bar",
            stack: "testStatus",
            label: {
              show: true,
            },
            data: [0],
          },
          {
            name: "测试中",
            type: "bar",
            stack: "testStatus",
            label: {
              show: true,
            },
            data: [0],
          },
          {
            name: "测试完成",
            type: "bar",
            stack: "testStatus",
            label: {
              show: true,
            },
            data: [0],
          },
        ],
      };
      this.teststatus_chart.setOption(option);
    },

    createTestWaitTimeCharts: function (containerId) {
      var chart = HighCharts.chart(containerId, {
        chart: {
          type: "column",
          zoomType: "x",
        },
        title: {
          text: "测试等待时间",
        },
        subtitle: {
          text: "",
        },
        xAxis: {
          categories: ["-"],
          crosshair: true,
        },
        yAxis: {
          min: 0,
          title: {
            text: "",
          },
          stackLabels: {
            // 堆叠数据标签
            enabled: true,
          },
        },
        tooltip: {
          // head + 每个 point + footer 拼接成完整的 table
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat:
            '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f} 天</b></td></tr>',
          footerFormat: "</table>",
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            dataLabels: {
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "测试等待时间",
            data: [0],
          },
        ],
      });
      return chart;
    },

    //显示点值的折线图
    createLineLabelsLine: function (containerId) {
      var LineChart = HighCharts.chart(containerId, {
        chart: {
          type: "line",
          zoomType: "x",
        },
        title: {
          text: "测试效率",
        },
        xAxis: {
          categories: ["-"],
        },
        yAxis: {
          title: {
            text: "",
          },
        },
        plotOptions: {
          line: {
            dataLabels: {
              // 开启数据标签
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "用例数",
            data: [0],
          },
          {
            name: "测试时长",
            data: [0],
          },
        ],
      });
      return LineChart;
    },

    loadTestTaskStatus: function () {
      this.$axios
        .get("/api/home/<USER>/testtaskstatus")
        .then((response) => {
          let response_data = response.data.result;
          this.testtaskData["test_wait"] = response_data["test_wait"];
          this.testtaskData["test_ing"] = response_data["test_ing"];
          this.testtaskData["test_done"] = response_data["test_done"];
          this.testtaskData["test_sum"] =
            this.testtaskData["test_wait"] +
            this.testtaskData["test_ing"] +
            this.testtaskData["test_done"];

          this.testtaskData["demand_done"] = response_data["demand_online"];
          this.testtaskData["demand_waittest"] =
            response_data["demand_waittest"];
          this.testtaskData["demand_testing"] = response_data["demand_testing"];
          this.testtaskData["demand_sum"] = response_data["demand_sum"];

          this.testStatusLoading = false;
        });
    },

    loadTestStatus: function (value) {
      this.$axios
        .get("/api/home/<USER>/teststatus/" + value)
        .then((response) => {
          let response_data = response.data.result;
          this.teststatus_data.categories = response_data["categories"];
          this.teststatus_data.series = response_data["series"];
        });
    },

    loadTestEff: function (value) {
      this.$axios
        .get("/api/home/<USER>/testeff/" + value)
        .then((response) => {
          let response_data = response.data.result;
          this.testeffData["casecount_avg"] =
            response_data["all_project_testplan_casecount_avg"];
          this.testeffData["testtingtime_avg"] =
            response_data["all_project_testplan_testingtime_avg"];
          this.testefficiency_chart.update({
            xAxis: {
              categories: response_data["projects"],
            },
            series: [
              {
                name: "用例数",
                data: response_data["project_testplan_casecount_avg"],
              },
              {
                name: "测试时长",
                data: response_data["project_testplan_testtingtime_avg"],
              },
            ],
          });
        });
    },

    loadTestWait: function (value) {
      this.$axios
        .get("/api/home/<USER>/testwaittime/" + value)
        .then((response) => {
          let response_data = response.data.result;
          this.testwaittime_chart.update({
            xAxis: {
              categories: response_data["projects"],
            },
            series: [
              {
                data: response_data["testplan_avg_testingtime_list"],
              },
            ],
          });
        });
    },

    initKanbanTestChart: function () {
      this.initTestStatusEchart();
      this.testwaittime_chart = this.createTestWaitTimeCharts(testwaittime);
      this.testefficiency_chart = this.createLineLabelsLine(testefficiency);
    },

    loadTestKanbanStatistics: function () {
      this.loadTestStatus(this.search_days);
      this.loadTestTaskStatus();
      this.loadTestEff(this.search_days);
      this.loadTestWait(this.search_days);
    },
  },

  created: function () {
  },

  mounted: function () {
    this.initKanbanTestChart();
    this.loadTestKanbanStatistics();
  },

  watch: {
    teststatus_data: {
      handler() {
        let option = this.teststatus_chart.getOption();
        option.xAxis[0].data = this.teststatus_data.categories;
        option.series[0].data = this.teststatus_data.series[0].data;
        option.series[1].data = this.teststatus_data.series[1].data;
        option.series[2].data = this.teststatus_data.series[2].data;
        //console.log(option);
        this.teststatus_chart.setOption(option);
      },
      deep: true,
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.test-total {
  flex: 2;
  margin-right: 5px;
  .chart-demand {
    position: relative;
    flex-basis: 90px;
    .charts {
      position: absolute;
      top: -65px;
    }
  }
}
</style>
