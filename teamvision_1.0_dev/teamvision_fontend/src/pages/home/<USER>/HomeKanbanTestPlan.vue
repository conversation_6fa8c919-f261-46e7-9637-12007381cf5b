<template>
  <div>
    <Card dis-hover title="测试计划">
      <p slot="extra">
        <Date-picker :value="filterDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期"
          size="small" @on-change="handleChangeDate" style="width: 220px; padding-left:10px;padding-right:10px">
        </Date-picker><Button type="primary" shape="circle" icon="ios-search" size="small"
          @click="filterTestPlan">搜索</Button>
      </p>
      <Row :gutter="2" type="flex" justify="space-between" align="middle">
        <Col span="12" style="padding: 10px">
        <div id="testPlanCompletionRate" style="min-width:300px; height:400px;"></div>
        </Col>
        <Col span="12" style="padding: 10px">
        <div id="projectTestPlanStatistics" style="min-width:300px; height:400px;"></div>
        </Col>
        <!--
      <Col span="8">
      <Card dis-hover>
        <Row :gutter="2" type="flex" justify="space-between" align="middle">
          <Col span="4">
          <div style="text-align: center;">
            <i-circle :percent="100" stroke-color="#3bc482" :size="86">
              <div class="home-project-circle">
                <p style="font-size: 14px">完成率</p>
                <h1 style="font-size: 18px">{{ case_plan_info.finished_per }}</h1>
              </div>
            </i-circle>
          </div>
          </Col>
          <Col span="3">
          <div style="text-align: center;">
            <p style="font-size: 14px;">总数</p>
            <h1 style="font-size: 18px">{{ case_plan_info.all_data }}</h1>
          </div>
          </Col>
          <Col span="3">
          <div style="text-align: center;">
            <p style="font-size: 14px;">测试中</p>
            <h1 style="font-size: 18px">{{ case_plan_info.testing }}</h1>
          </div>
          </Col>
          <Col span="3">
          <div style="text-align: center;">
            <p style="font-size: 14px;">已完成</p>
            <h1 style="font-size: 18px">{{ case_plan_info.finished }}</h1>
          </div>
          </Col>
          <Col span="3">
          <div style="text-align: center;">
            <p style="font-size: 14px;">已归档</p>
            <h1 style="font-size: 18px">{{ case_plan_info.archive }}</h1>
          </div>
          </Col>
          <Col span="3">
          <div style="text-align: center;">
            <p style="font-size: 14px;">需求数</p>
            <h1 style="font-size: 18px">{{this.all_require_num}}</h1>
          </div>
          </Col>
        </Row>
      </Card>
      </Col>
      -->
      </Row>
      <!--
    <Row :gutter="4" type="flex" justify="space-between" style="height:420px; padding-top:20px">
      <Col span="12" style="padding-right: 10px">
        <div style="min-width:300px;height:400px;" id="planFinishedPercentStatistics"></div>
      </Col>
      <Col span="12" >
        <div style="min-width:300px;height:400px;" id="requireNumStatistics"></div>
      </Col>
    </Row>
    -->
      <Row :gutter="4" type="flex" justify="space-between" style="height:420px; padding-top:20px">
        <Col span="12" style="padding-right: 10px">
        <div style="min-width:300px;height:400px;" id="project-testplan-avg-cases"></div>
        </Col>
        <Col span="12">
        <div style="min-width:300px;height:400px;" id="project-requirement-avg-cases"></div>
        </Col>
      </Row>
    </Card>
    <Drawer title="测试计划平均用例数趋势图" width="920px" :closable="false" v-model="showTestPlanAvgCaseGrowthTrend">
      <div slot="header" class="ivu-drawer-header-inner-2">
        <Row>
          <Col span="8" class="ivu-drawer-header-inner-2">测试计划用例趋势</Col>
          <Col span="8">
          <Select v-model="selectProject" size="small" placeholder="请选择项目" multiple filterable
            @on-change="handlerSelectProjectChange" @on-open-change="handlerOpenChange">
            <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}</Option>
          </Select>
          </Col>
          <Col span="8">
          <Date-picker :value="testPlanCaseFilterDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
            placeholder="选择日期" size="small" @on-change="handleAvgCaseChangeDate"
            style="width: 220px; padding-left:10px;padding-right:10px">
          </Date-picker>
          <Button type="primary" shape="circle" icon="ios-search" size="small"
            @click="getProjectTestPlanRequirementAvgCasesTrend">搜索</Button>
          </Col>
        </Row>
      </div>
      <Row :gutter="24">
        <Col span="24">
        <div id="project-testplan-avg-cases-trend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="project-testplan-avg-cases-exec-trend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="project-testplan-cases-trend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="project-testplan-cases-exec-trend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
    </Drawer>
    <Drawer title="需求平均用例数趋势图" width="920px" :closable="false" v-model="showRequirementAvgCaseGrowthTrend">
      <div slot="header" class="ivu-drawer-header-inner-2">
        <Row>
          <Col span="8" class="ivu-drawer-header-inner-2">需求用例趋势</Col>
          <Col span="8">
          <Select v-model="selectProject" size="small" placeholder="请选择项目" multiple filterable
            @on-open-change="handlerOpenChange" @on-change="handlerOpenChange">
            <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}</Option>
          </Select>
          </Col>
          <Col span="8">
          <Date-picker :value="testPlanCaseFilterDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
            placeholder="选择日期" size="small" @on-change="handleAvgCaseChangeDate"
            style="width: 220px; padding-left:10px;padding-right:10px">
          </Date-picker>
          <Button type="primary" shape="circle" icon="ios-search" size="small"
            @click="getProjectTestPlanRequirementAvgCasesTrend">搜索</Button>
          </Col>
        </Row>
      </div>
      <Row :gutter="24">
        <Col span="24">
        <div id="project-requirement-avg-cases-trend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
    </Drawer>
  </div>
</template>

<script>
import HighCharts from "highcharts";
import * as echarts from "echarts";
import { mapState, mapGetters } from "vuex";
import { getCurrDateDifference } from "../../../utils/utils.js"

export default {
  components: {},
  name: "HomeKanbanTestPlan",
  data() {
    return {
      filterDate: [],
      date_info: 1,
      testplansLoading: false,
      case_plan_info: {},
      all_require_num: "",
      testcaseData: {
        case: "新增",
        allcase: "全部",
        testcase: 0,
        all_testcase: 0,
        autocase: 0,
        all_autocase: 0,
        autopercent: "-",
        all_autopercent: 0,
      },
      showTestPlanAvgCaseGrowthTrend: false,
      showRequirementAvgCaseGrowthTrend: false,
      selectProject: [],
      testPlanCaseFilterDate: [],
      testcaseKeys: [
        {
          key: "case",
          name: "(新增/全部)",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "allcase",
        },
        {
          key: "testcase",
          name: "功能用例",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "all_testcase",
        },
        {
          key: "autocase",
          name: "自动化用例",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "all_autocase",
        },
        {
          key: "autopercent",
          name: "自动化率",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "%",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "all_autopercent",
        },
      ],
      testplanstaticticsChart: null,
      projecttestplanstatisticsChart: null,
      ProjectTestplanAvgCasesChart: null,
      ProjectTestplanAvgCasesChartData: {
        title: {
          text: "测试计划平均用例数",
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "6%",
          right: "2%",
          bottom: "40%",
        },
        toolbox: {
          right: 40,
          feature: {
            myCaseGrowthTrendTool: {
              show: true,
              title: '趋势图',
              icon: 'path://M958.620955 427.539376l-42.606377 0c-11.773138 0-21.301142 9.528004-21.301142 21.301142l0 553.857317c0 11.732206 9.529027 21.301142 21.301142 21.301142l42.606377 0c11.774161 0 21.301142-9.56996 21.301142-21.301142L979.922097 448.840518C979.922097 437.06738 970.395116 427.539376 958.620955 427.539376zM660.38962 555.352367l-42.60433 0c-11.775184 0-21.301142 9.528004-21.301142 21.301142l0 426.044326c0 11.732206 9.526981 21.301142 21.301142 21.301142l42.60433 0c11.774161 0 21.303188-9.56996 21.303188-21.301142L681.692809 576.654532C681.692809 564.880371 672.162758 555.352367 660.38962 555.352367zM362.159308 257.121032l-42.60433 0c-11.775184 0-21.302165 9.487072-21.302165 21.302165l0 724.275661c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.60433 0c11.774161 0 21.301142-9.56996 21.301142-21.301142l0-724.275661C383.46045 266.60708 373.932446 257.121032 362.159308 257.121032zM63.927973 768.374018 21.32262 768.374018c-11.775184 0-21.302165 9.528004-21.302165 21.300119l0 213.023698c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.605354 0c11.774161 0 21.302165-9.56996 21.302165-21.301142L85.230138 789.67516C85.230138 777.902022 75.703158 768.374018 63.927973 768.374018z',
              onclick: (e) => {
                this.showTestPlanAvgCaseGrowthTrend = !this.showTestPlanAvgCaseGrowthTrend
              }
            },
          }
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 16,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "平均用例数",
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
            data: [],
          },
          {
            name: "平均执行用例数",
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
            data: [],
          },
        ],
      },
      ProjectRequirementAvgCasesChart: null,
      ProjectRequirementAvgCasesChartData: {
        title: {
          text: "需求平均用例数",
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "6%",
          right: "2%",
          bottom: "40%",
        },
        toolbox: {
          right: 40,
          feature: {
            myCaseGrowthTrendTool: {
              show: true,
              title: '增长趋势',
              icon: 'path://M958.620955 427.539376l-42.606377 0c-11.773138 0-21.301142 9.528004-21.301142 21.301142l0 553.857317c0 11.732206 9.529027 21.301142 21.301142 21.301142l42.606377 0c11.774161 0 21.301142-9.56996 21.301142-21.301142L979.922097 448.840518C979.922097 437.06738 970.395116 427.539376 958.620955 427.539376zM660.38962 555.352367l-42.60433 0c-11.775184 0-21.301142 9.528004-21.301142 21.301142l0 426.044326c0 11.732206 9.526981 21.301142 21.301142 21.301142l42.60433 0c11.774161 0 21.303188-9.56996 21.303188-21.301142L681.692809 576.654532C681.692809 564.880371 672.162758 555.352367 660.38962 555.352367zM362.159308 257.121032l-42.60433 0c-11.775184 0-21.302165 9.487072-21.302165 21.302165l0 724.275661c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.60433 0c11.774161 0 21.301142-9.56996 21.301142-21.301142l0-724.275661C383.46045 266.60708 373.932446 257.121032 362.159308 257.121032zM63.927973 768.374018 21.32262 768.374018c-11.775184 0-21.302165 9.528004-21.302165 21.300119l0 213.023698c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.605354 0c11.774161 0 21.302165-9.56996 21.302165-21.301142L85.230138 789.67516C85.230138 777.902022 75.703158 768.374018 63.927973 768.374018z',
              onclick: (e) => {
                this.showRequirementAvgCaseGrowthTrend = !this.showRequirementAvgCaseGrowthTrend
              }
            },
          }
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 16,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "平均用例数",
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
            data: [],
          },
        ],
      },
      ProjectTestplanAvgCasesTrendChart: null,
      ProjectTestplanAvgCasesExecTrendChart: null,
      ProjectTestplanCasesTrendChart: null,
      ProjectTestplanCasesExecTrendChart: null,
      ProjectRequirementAvgCasesTrendChart: null,
    };
  },

  computed: {
    ...mapState(["appBodyHeight"]),
    ...mapState('project', ['projectList']),

    testplansNum() {
      //console.log("testplansNum()");
      const result = {};
      const keys = this.testcaseKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.testcaseData[item.key] || 0,
            title: item.name,
            allcase: this.testcaseData[item.allcase],
          };
        }
      });
      //console.log("testtaskNum()=", result);
      return result;
    },
  },

  methods: {
    setFilterDate: function () {
      this.filterDate = getCurrDateDifference(7)
      this.testPlanCaseFilterDate = getCurrDateDifference(365)
    },

    handleChangeDate: function (date) {
      this.filterDate = date;
    },

    handleAvgCaseChangeDate: function (date) {
      this.testPlanCaseFilterDate = date;
    },

    filterTestPlan_bak: function (value) {
      this.$axios.get("/api/home/<USER>/project_testplan_statictics/3").then((response) => {
        let response_data = response.data.result;
        this.case_plan_info = response_data.totle_info;
        this.all_require_num = response_data.all_require_num;
        HighCharts.chart("planFinishedPercentStatistics", response_data.finished_percent_chart)
        HighCharts.chart("requireNumStatistics", response_data.require_num_chart)
      }, (response) => { }
      );
    },
    createTestPlanStaticticsChart: function () {
      let initChart = echarts.init(
        document.getElementById("testPlanCompletionRate")
      );
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: "vertical",
          left: "right",
        },
        series: [
          {
            type: 'gauge',
            radius: '50%',
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.3, '#fd666d'],
                  [0.7, '#37a2da'],
                  [1, '#67e0e3']
                ]
              }
            },
            pointer: {
              itemStyle: {
                color: 'auto'
              }
            },
            axisTick: {
              show: false,
              distance: -20,
              length: 8,
              lineStyle: {
                color: '#fff',
                width: 2
              }
            },
            splitLine: {
              show: false,
              distance: -32,
              length: 30,
            },
            axisLabel: {
              color: 'auto',
              distance: 18,
              fontSize: 12
            },
            title: {
              offsetCenter: ['0%', '85%'],
              fontSize: 12
            },
            detail: {
              fontSize: 12,
              offsetCenter: ['0%', '50%'],
              valueAnimation: true,
              formatter: '完成率: {value}%',
              color: 'auto'
            },
            data: [
              {
                value: 0,
                name: '总数: 0\n需求数: 0'
              }
            ]
          },
          {
            name: '测试计划',
            type: 'pie',
            radius: ['55%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: "{b}: {@c}",
            },
            emphasis: {
            },
            data: [
              { name: '新建', value: 0 },
              { name: '测试中', value: 0 },
              { name: '已完成', value: 0 },
              { name: '已暂停', value: 0 },
            ]
          }
        ]
      };
      initChart.setOption(option);
      window.addEventListener("resize", function () {
        initChart.resize();  //myChart指自己定义的echartsDom对象
      });
      return initChart
    },

    filterTestPlan: function (value) {
      this.getTestPlanStatictisc()
      this.getProjectTestPlanStatictisc()
      this.getProjectTestPlanCaseAvg()
      this.getProjectRequirementAvgCases()
    },

    createProjectTestPlanStatistics: function () {
      let initChart = echarts.init(document.getElementById("projectTestPlanStatistics"));
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "6%",
          right: "2%",
          bottom: "40%",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: ["-"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 18,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: '需求数',
            type: 'bar',
            label: {
              show: true,
              position: "top",
            },
            itemStyle: {
              color: "#61a0a8",
            },
            data: [0]
          },
          {
            name: '新建',
            type: 'bar',
            stack: 'Ad',
            label: {
              show: true,
              position: "inside",
            },
            data: [0]
          },
          {
            name: '测试中',
            type: 'bar',
            stack: 'Ad',
            label: {
              show: true,
              position: "inside",
            },
            data: [0]
          },
          {
            name: '测试完成',
            type: 'bar',
            stack: 'Ad',
            label: {
              show: true,
              position: "inside",
            },
            data: [0]
          },
          {
            name: '暂停',
            type: 'bar',
            stack: 'Ad',
            label: {
              show: true,
              position: "inside",
            },
            data: [0]
          },
        ]
      }
      initChart.setOption(option);
      window.addEventListener("resize", function () {
        initChart.resize();   //myChart指自己定义的echartsDom对象
      });
      return initChart
    },

    createProjectTestplanAvgCasesChart: function (chart_id) {
      let initChart = echarts.init(document.getElementById(chart_id));
      initChart.setOption(this.ProjectTestplanAvgCasesChartData);
      window.addEventListener("resize", function () {
        initChart.resize();
      });
      return initChart
    },

    createProjectRequirementAvgCasesChart: function (chart_id) {
      let initChart = echarts.init(document.getElementById(chart_id));
      initChart.setOption(this.ProjectRequirementAvgCasesChartData);
      window.addEventListener("resize", function () {
        initChart.resize();
      });
      return initChart
    },

    getTestPlanStatictisc: function () {
      let reqUrl = "/api/home/<USER>/testplan?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.testplanstaticticsChart.getOption();
        option.series[0].data[0].name = '总数: ' + response_data.testplan_sum + '\n需求数: ' + response_data.require_num
        option.series[0].data[0].value = response_data.finish_rate
        option.series[1].data = response_data.data
        this.testplanstaticticsChart.setOption(option);
      });
    },

    getProjectTestPlanStatictisc: function () {
      let reqUrl = "/api/home/<USER>/projecttestplan?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.projecttestplanstatisticsChart.getOption();
        option.xAxis[0].data = response_data.xAxis[0].data
        option.series[0].data = response_data.series[0].data
        option.series[1].data = response_data.series[1].data
        option.series[2].data = response_data.series[2].data
        option.series[3].data = response_data.series[3].data
        option.series[4].data = response_data.series[4].data
        this.projecttestplanstatisticsChart.setOption(option)
      });
    },

    getProjectTestPlanCaseAvg: function () {
      let reqUrl = "/api/home/<USER>/testplan/testcase/avg?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectTestplanAvgCasesChart.getOption();
        option.xAxis[0].data = []
        option.series[0].data = []
        option.series[1].data = []
        response_data.forEach((item) => {
          option.xAxis[0].data.push(item.name)
          option.series[0].data.push(item.avg_cases)
          option.series[1].data.push(item.avg_case_exec)
        })
        this.ProjectTestplanAvgCasesChart.setOption(option)
      });
    },

    getProjectRequirementAvgCases: function () {
      let reqUrl = "/api/home/<USER>/requirement/testcase/avg?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectRequirementAvgCasesChart.getOption();
        option.xAxis[0].data = []
        option.series[0].data = []
        response_data.forEach((item) => {
          option.xAxis[0].data.push(item.name)
          option.series[0].data.push(item.avg_cases)
        })
        this.ProjectRequirementAvgCasesChart.setOption(option)
      });
    },

    getProjectTestPlanRequirementAvgCasesTrend: function () {
      let reqUrl = "/api/home/<USER>/testplan-requirement/testcase/trend?st=" + this.testPlanCaseFilterDate[0] + "&et=" + this.testPlanCaseFilterDate[1]
      if (this.selectProject.length > 0) {
        reqUrl = reqUrl + "&project=" + this.selectProject
      }
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let testplan_avg_case_option = this.ProjectTestplanAvgCasesTrendChart.getOption();
        let testplan_avg_case_exec_option = this.ProjectTestplanAvgCasesExecTrendChart.getOption();
        let testplan_case_option = this.ProjectTestplanCasesTrendChart.getOption();
        let testplan_case_exec_option = this.ProjectTestplanCasesExecTrendChart.getOption();
        let require_avg_case_option = this.ProjectRequirementAvgCasesTrendChart.getOption();

        testplan_avg_case_option.xAxis[0].data = []
        testplan_avg_case_option.xAxis[0].data = response_data['date_list']

        testplan_avg_case_exec_option.xAxis[0].data = []
        testplan_avg_case_exec_option.xAxis[0].data = response_data['date_list']

        testplan_case_option.xAxis[0].data = []
        testplan_case_option.xAxis[0].data = response_data['date_list']

        testplan_case_exec_option.xAxis[0].data = []
        testplan_case_exec_option.xAxis[0].data = response_data['date_list']

        require_avg_case_option.xAxis[0].data = []
        require_avg_case_option.xAxis[0].data = response_data['date_list']

        testplan_avg_case_option.series = []
        testplan_avg_case_exec_option.series = []
        testplan_case_option.series = []
        testplan_case_exec_option.series = []
        require_avg_case_option.series = []
        response_data.project.forEach((item) => {
          let tmp1 = {
            name: item.name,
            type: 'line',
            data: item.total_test_plan_avg_cases
          }
          let tmp2 = {
            name: item.name,
            type: 'line',
            data: item.total_test_plan_avg_cases_exec
          }
          let tmp3 = {
            name: item.name,
            type: 'line',
            data: item.total_test_plan_sum_cases
          }
          let tmp4 = {
            name: item.name,
            type: 'line',
            data: item.total_test_plan_sum_cases_exec
          }
          let tmp5 = {
            name: item.name,
            type: 'line',
            data: item.total_requirement_avg_cases
          }
          testplan_avg_case_option.series.push(tmp1)
          testplan_avg_case_exec_option.series.push(tmp2)
          testplan_case_option.series.push(tmp3)
          testplan_case_exec_option.series.push(tmp4)
          require_avg_case_option.series.push(tmp5)
        })
        this.ProjectTestplanAvgCasesTrendChart.setOption(testplan_avg_case_option, true)
        this.ProjectTestplanAvgCasesExecTrendChart.setOption(testplan_avg_case_exec_option, true)
        this.ProjectTestplanCasesTrendChart.setOption(testplan_case_option, true)
        this.ProjectTestplanCasesExecTrendChart.setOption(testplan_case_exec_option, true)
        this.ProjectRequirementAvgCasesTrendChart.setOption(require_avg_case_option, true)
      });
    },
    handlerSelectProjectChange(projects) {
      this.selectProject = projects
      this.getProjectTestPlanRequirementAvgCasesTrend()

    },
    handlerOpenChange(is_open) {
      if (is_open == false) {
        this.getProjectTestPlanRequirementAvgCasesTrend()
      }
    },
    initLineChart: function (chartID, chartTitle) {
      let init_echart = echarts.init(document.getElementById(chartID));
      let option = {
        title: {
          text: chartTitle,
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          type: 'scroll',
          //orient: 'vertical',
          right: "5%",
          left: "5%",
          //top: "10%",
          bottom: 0,
        },
        grid: {
          left: "5%",
          right: "5%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],
        },
        yAxis: {
          type: "value",
          series: []
        },
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 10,
          },

        ],
        series: [],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize();
      });

      return init_echart
    },

    initTestPlanChart: function () {
      this.testplanstaticticsChart = this.createTestPlanStaticticsChart()
      this.projecttestplanstatisticsChart = this.createProjectTestPlanStatistics()
      this.ProjectTestplanAvgCasesChart = this.createProjectTestplanAvgCasesChart("project-testplan-avg-cases");
      this.ProjectRequirementAvgCasesChart = this.createProjectRequirementAvgCasesChart("project-requirement-avg-cases");

      this.ProjectTestplanAvgCasesTrendChart = this.initLineChart("project-testplan-avg-cases-trend", "测试计划平均用例数变化趋势")
      this.ProjectTestplanAvgCasesExecTrendChart = this.initLineChart("project-testplan-avg-cases-exec-trend", "测试计划平均用例执行数变化趋势")
      this.ProjectTestplanCasesTrendChart = this.initLineChart("project-testplan-cases-trend", "测试计划关联用例数变化趋势")
      this.ProjectTestplanCasesExecTrendChart = this.initLineChart("project-testplan-cases-exec-trend", "测试计划执行用例数变化趋势")
      this.ProjectRequirementAvgCasesTrendChart = this.initLineChart("project-requirement-avg-cases-trend", "测试需求平均用例数变化趋势");

    },
  },

  created: function () { },

  mounted: function () {
    this.setFilterDate()
    //this.filterTestPlan(this.date_info);
    this.initTestPlanChart();
    this.getTestPlanStatictisc();
    this.getProjectTestPlanStatictisc();
    this.getProjectTestPlanCaseAvg();
    this.getProjectRequirementAvgCases();
    this.getProjectTestPlanRequirementAvgCasesTrend();
  },

  watch: {
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.test-total {
  flex: 2;
  margin-right: 5px;

  .chart-demand {
    position: relative;
    flex-basis: 90px;

    .charts {
      position: absolute;
      top: -65px;
    }
  }
}

.home-kanban-testcase-circle {
  & h1 {
    color: #3f414d;
    font-size: 28px;
    font-weight: normal;
  }

  & p {
    color: #657180;
    font-size: 14px;
    margin: 10px 0 15px;
  }

  & span {
    display: block;
    padding-top: 15px;
    color: #657180;
    font-size: 14px;

    &:before {
      content: "";
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e0e3e6;
      position: relative;
      top: -15px;
    }
  }

  & span i {
    font-style: normal;
    color: #3f414d;
  }
}
</style>
