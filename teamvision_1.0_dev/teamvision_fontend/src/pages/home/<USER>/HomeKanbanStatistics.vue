<template>
  <div :style="'height:' + containerHeight + 'px;overflow-y:scroll; padding: 2px'">
    <div>
      <HomeKanbanTestCaseChart> </HomeKanbanTestCaseChart>
    </div>
    <!--  暂时不显示
    <div style="padding:10px 10px 10px 10px;">
      <Card title="需求看板">
        <a href="#" slot="extra">
          <RadioGroup v-model="filter_demand" type="button" size="small" @on-change="loadDemandStatistics">
            <Radio :label="7">7天</Radio>
            <Radio :label="15">15天</Radio>
            <Radio :label="30">1个月</Radio>
            <Radio :label="90">3个月</Radio>
          </RadioGroup>
        </a>
        <Row :gutter="16">
          <Col span="8">
          <div v-loading="demandDataLoading" class="demand-content-item demand-total">
            <NumbersCard :data="demandThroughputNum">
              <template slot="header">
                需求吞吐量
              </template>
              <template slot="chart">
                <el-progress type="circle" :width="80" :stroke-width="6" :format="progress_format" :percentage="demandThroughputData['finish_ratio']">
                </el-progress>
              </template>
            </NumbersCard>
          </div>
          </Col>
          <Col span="8">
          <div v-loading="workloadLoading">
            <NumbersCard :data="workloadNum">
              <template slot="header">
                工作量
              </template>
            </NumbersCard>
          </div>
          </Col>
          <Col span="8">
          <div v-loading="testcaseLoading">
            <NumbersCard :data="leadtimeNum">
              <template slot="header">
                平均前置时间
              </template>
            </NumbersCard>
          </div>
          </Col>
        </Row>
        <div></div>
        <Row :gutter="16">
          <Col span="8">
          <div id="demandstatus" style="width:400px;height:400px;margin-top:16px"></div>
          </Col>
          <Col span="8">
          <div id="demandworkload" style="width:400px;height:400px;margin-top:16px"></div>
          </Col>
          <Col span="8">
          <div id="leadtime" style="width:400px;height:400px;margin-top:16px"></div>
          </Col>
        </Row>
      </Card>
      <Divider></Divider>
    </div>
    -->
    <div>
      <HomeKanbanTestPlan></HomeKanbanTestPlan>
    </div>
    <div v-if="isShowJiraBug">
      <JiraBugAnalyse></JiraBugAnalyse>
    </div>
    <div>
      <bugAnalyse></bugAnalyse>
    </div>
  </div>
</template>

<script>
import HighCharts from "highcharts";
import { mapState, mapGetters } from "vuex";
import NumbersCard from "../../../components/numbercard/NumbersCard.vue";
import JiraBugAnalyse from "./JiraBugAnalyse.vue";
import bugAnalyse from "./bugAnalyse.vue";
import KanbanTest from "./HomeKanbanTest.vue";
import HomeKanbanTestCaseChart from "./HomeKanbanTestCase.vue";
import HomeKanbanTestPlan from "./HomeKanbanTestPlan.vue";

export default {
  components: {
    NumbersCard,
    JiraBugAnalyse,
    KanbanTest,
    HomeKanbanTestCaseChart,
    HomeKanbanTestPlan,
    bugAnalyse,
  },
  name: "TestPlanChart",
  data() {
    return {
      filter_demand: 7,
      demandstatus_chart: null,
      leadtime_chart: null,
      demandDataLoading: false,
      demandThroughputData: {
        demand_sum: 0,
        demand_finish: 0,
        demand_waitprocess: 0,
        demand_processing: 0,
        finish_ratio: 0,
      },
      demandThroughputKeys: [
        {
          key: "demand_sum",
          name: "总数",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "demand_finish",
          name: "已完成",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_finish",
        },
        {
          key: "demand_waitprocess",
          name: "待处理",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_waitprocess",
        },
        {
          key: "demand_processing",
          name: "进行中",
          hideChart: false,
          type: "bar",
          stack: 1,
          unit: "个",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
          demand: "demand_processing",
        },
      ],
      workloadLoading: false,
      workloadData: {
        work_plan: 0,
        work_actual: 0,
      },
      workloadKeys: [
        {
          key: "work_plan",
          name: "计划",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "work_actual",
          name: "实际",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
      ],
      leadtimeData: {
        leadtime_demand: 0,
        leadtime_develop: 0,
        leadtime_test: 0,
      },
      leadtimeKeys: [
        {
          key: "leadtime_demand",
          name: "需求",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "leadtime_develop",
          name: "开发",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
        {
          key: "leadtime_test",
          name: "测试",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "天",
          label: {
            show: true,
            // formatter: '{a}: {c}'
          },
          chartDefaultShow: true,
        },
      ],
      projecttestcaserepeat_chart: null,
    };
  },

  computed: {
    ...mapState(['appBodyHeight', 'appBodyMainHeight',]),
    ...mapState('usercenter', ['defSpace']),

    isShowJiraBug: function () {
      if (this.defSpace.id == 1) {
        return true
      } else {
        return false
      }
    },
    // ...mapState({
    //   ProductSpace: state => state.defSpace
    // }),

    containerHeight: function () {
      return this.appBodyHeight;
    },

    demandThroughputNum() {
      const result = {
        chart: {
          slot: "chart",
          class: "chart-demand",
        },
      };
      const keys = this.demandThroughputKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.demandThroughputData[item.key] || 0,
            title: item.name,
          };
        }
      });
      return result;
    },

    workloadNum() {
      const result = {};
      const keys = this.workloadKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.workloadData[item.key] || 0,
            title: item.name,
            unit: item.unit,
          };
        }
      });
      return result;
    },

    leadtimeNum() {
      const result = {};
      const keys = this.leadtimeKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.leadtimeData[item.key] || 0,
            title: item.name,
            unit: item.unit,
          };
        }
      });
      return result;
    },
  },

  methods: {
    progress_format: function (percentage) {
      return "完成率" + `${percentage}%`;
    },

    // 加载需求状态分布图数据
    loadDemandStatus: function (value) {
      this.$axios
        .get("/api/home/<USER>/projectdemandstatus/" + value)
        .then((response) => {
          let response_data = response.data.result;
          this.demandstatus_chart.update({
            xAxis: {
              categories: response_data["projects"],
            },
            series: [
              {
                name: "新建",
                data: response_data["new_create_list"],
              },
              {
                name: "待处理",
                data: response_data["wait_proecesing_list"],
              },
              {
                name: "已完成",
                data: response_data["finish_list"],
              },
            ],
          });
        });
    },

    // 加载前置时间
    loadLeadTimeStatus: function (value) {
      this.$axios
        .get("/api/home/<USER>/demandavgleadtime/" + value)
        .then((response) => {
          let response_data = response.data.result;

          this.leadtimeData["leadtime_demand"] =
            response_data["demand_time_sum"];
          this.leadtimeData["leadtime_develop"] =
            response_data["develop_time_sum"];
          this.leadtimeData["leadtime_test"] = response_data["test_time_sum"];

          this.leadtime_chart.update({
            xAxis: {
              categories: response_data["projects"],
            },
            series: [
              {
                name: "需求",
                data: response_data["project_demand_time"],
              },
              {
                name: "开发",
                data: response_data["project_develop_time"],
              },
              {
                name: "测试",
                data: response_data["project_test_time"],
              },
            ],
          });
        });
    },

    loadBugSt: function (value) {
      this.$axios
        .get("/api/home/<USER>/demandavgleadtime/" + value)
        .then((response) => {
          let response_data = response.data.result;

          this.leadtimeData["leadtime_demand"] =
            response_data["demand_time_sum"];
          this.leadtimeData["leadtime_develop"] =
            response_data["develop_time_sum"];
          this.leadtimeData["leadtime_test"] = response_data["test_time_sum"];

          this.leadtime_chart.update({
            xAxis: {
              categories: response_data["projects"],
            },
            series: [
              {
                name: "需求",
                data: response_data["project_demand_time"],
              },
              {
                name: "开发",
                data: response_data["project_develop_time"],
              },
              {
                name: "测试",
                data: response_data["project_test_time"],
              },
            ],
          });
        });
    },

    // 创建需求状态分布 堆叠柱状图
    createDemandStatusChart: function (containerId) {
      var demandStatusStackedColumnHightChart = HighCharts.chart(containerId, {
        chart: {
          type: "column",
          zoomType: "x",
        },
        title: {
          text: "需求状态分布",
        },
        xAxis: {
          categories: ["-"],
        },
        yAxis: {
          allowDecimals: false,
          min: 0,
          title: {
            text: "",
          },
          stackLabels: {
            // 堆叠数据标签
            enabled: true,
          },
        },
        tooltip: {
          formatter: function () {
            return (
              "<b>" +
              this.x +
              "</b><br/>" +
              this.series.name +
              ": " +
              this.y +
              "<br/>" +
              "总量: " +
              this.point.stackTotal
            );
          },
        },
        plotOptions: {
          column: {
            stacking: "normal",
            borderWidth: 0,
            dataLabels: {
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "新建",
            data: [0],
          },
          {
            name: "待处理",
            data: [0],
          },
          {
            name: "已完成",
            data: [0],
          },
        ],
      });
      return demandStatusStackedColumnHightChart;
    },

    //创建 需求工时统计
    creatdemandworkloadChart: function (containerId) {
      var demandworkloadChart = HighCharts.chart(containerId, {
        chart: {
          type: "line",
          zoomType: "x",
        },
        title: {
          text: "需求工时统计",
        },
        xAxis: {
          categories: ["-"],
        },
        yAxis: {
          title: {
            text: "",
          },
        },
        plotOptions: {
          line: {
            dataLabels: {
              // 开启数据标签
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "计划工时",
            data: [0],
          },
          {
            name: "实际工时",
            data: [0],
          },
        ],
      });
      return demandworkloadChart;
    },

    //创建 前置时间统计
    createleadtimeChart: function (containerId) {
      var chart = HighCharts.chart(containerId, {
        chart: {
          type: "column",
        },
        title: {
          text: "前置时间统计",
        },
        subtitle: {
          text: "",
        },
        xAxis: {
          categories: ["-"],
          crosshair: true,
        },
        yAxis: {
          min: 0,
          title: {
            text: "",
          },
        },
        tooltip: {
          // head + 每个 point + footer 拼接成完整的 table
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat:
            '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f} 天</b></td></tr>',
          footerFormat: "</table>",
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            borderWidth: 0,
          },
        },
        series: [
          {
            name: "需求",
            data: [0],
          },
          {
            name: "开发",
            data: [0],
          },
          {
            name: "测试",
            data: [0],
          },
        ],
      });
      return chart;
    },

    // 需求吞吐量
    loadDemandTroughput: function (value) {
      this.$axios
        .get("/api/home/<USER>/demandtroughput/" + value)
        .then((response) => {
          let response_data = response.data.result;
          this.demandThroughputData["demand_finish"] =
            response_data["demand_finish"];
          this.demandThroughputData["demand_waitprocess"] =
            response_data["demand_waitprocess"];
          this.demandThroughputData["demand_processing"] =
            response_data["demand_processing"];
          this.demandThroughputData["demand_sum"] = response_data["demand_sum"];
          this.demandThroughputData["finish_ratio"] =
            response_data["finish_ratio"];
        });
    },

    loadDemandStatistics: function () {
      this.loadDemandTroughput(this.filter_demand);
      this.loadDemandStatus(this.filter_demand);
      this.loadLeadTimeStatus(this.filter_demand);
    },
  },

  created: function () {
    //this.loadDemandStatistics();
  },

  mounted: function () {
    //this.demandstatus_chart = this.createDemandStatusChart(demandstatus);
    //this.demandworkload_chart = this.creatdemandworkloadChart(demandworkload);
    //this.leadtime_chart = this.createleadtimeChart(leadtime);
    //this.loadDemandTroughput(this.filter_demand);
    //this.loadDemandStatus(this.filter_demand);
    //this.loadLeadTimeStatus(this.filter_demand);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.charts-bar {
  max-width: 100%;
  margin-top: 20px;
}

.demand-total {
  flex: 2;
  margin-right: 5px;

  .chart-demand {
    position: relative;
    flex-basis: 90px;

    .charts {
      position: absolute;
      top: -65px;
    }
  }
}

.testcase-total {
  flex: 2;
  margin-right: 5px;
}

.time-total {
  flex: 2;
}

.number-card__item {
  flex-basis: 40px;
  padding: 0 5px;

  .number-card__title {
    font-size: 12px;
  }
}

.ivu-card-bordered {
  //border : none;
}
</style>
