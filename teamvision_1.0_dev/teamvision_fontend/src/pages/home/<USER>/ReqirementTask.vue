<template>
  <div>
    <Card :dis-hover="true">
      <div slot="title">
        <Row align="middle" type="flex" style="min-height: 40px;" v-if="!showNewTask">
          <Col span="24">
          <div style="cursor: pointer; width: inherit;text-align: center;">
            <span @click="addNewTask" style="margin-right: 20px;">
              <Tooltip content="添加新任务" transfer="">
                <Button shape="circle" icon="ios-add"
                  style="width: 24px;height: 24px;background-color: #22aaaa;border-color:#22aaaa;color:white"></Button>
              </Tooltip>
            </span>
            <span @click="shwoLinkTaskDialog" v-if="itemType !== 3">
              <Tooltip content="关联任务" transfer="">
                <Button shape="circle" icon="ios-link" style="width: 24px;height: 24px;"></Button>
              </Tooltip>
            </span>
          </div>
          </Col>
        </Row>
        <Row style="padding: 0px 10px 1px 10px;" v-if="showNewTask">
          <Col span="8" style="padding-right: 10px;">
          <span>
            <Input v-model="formData.Title" maxlength="50" show-word-limit placeholder="任务标题" />
          </span>
          </Col>
          <Col span="4" style="padding-right: 10px;">
          <span>
            <DatePicker transfer v-model="formData.DeadLine" type="date" placeholder="截止日期"></DatePicker>
          </span>
          </Col>
          <Col span="4" style="padding-right: 10px;">
          <span>
            <Select v-model="formData.Owner" :max-tag-count="1" multiple filterable placeholder="默认为创建者">
              <Option v-for="member in projectMembersList" :key="member.id" :value="member.PMMember">{{ member.name
                }}
              </Option>
            </Select>
          </span>
          </Col>
          <Col span="3">
          <Select v-model="formData.TaskType" placeholder="任务类型">
            <Option v-for="item in projectTaskTypes" :value="item.id" :key="item.id">{{ item.TypeName }}</Option>
          </Select>
          </Col>
          <Col span="2" style="padding-right: 10px;">
          <span>
            <Input v-model="formData.WorkHours" :number="true" placeholder="工时默认8小时" />
          </span>
          </Col>
          <Col span="3" style="padding-right: 10px;">
          <span>
            <ButtonGroup shape="circle">
              <Button @click.native="newTask" icon="md-checkmark"></Button>
              <Button @click.native="cancelNewTask" icon="md-close"></Button>
            </ButtonGroup>
          </span>
          </Col>
        </Row>
      </div>
      <Row v-for="task, index in reqTaskList" :key="task.id" style="height: 50px;">
        <Col span="15" style="padding: 10px;display: inline-flex;">
        <span style="display: inline-block;">#{{ task.id }}</span>
        <span>
          <label-editor-date @updateValue="updateTaskDeadLine" style="display: inline-block;"
            :displayText="task.DeadLineFormat.label" :value="task.DeadLine" :itemID="task.id">
            <template slot-scope="slotProps">
              <span style="width: 10px;display: inline-block">
                <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
              </span>
              <Tooltip transfer content="截止日期">
                <i style="padding: 2px 5px 0px 0px; border: 1px solid #f6f5f1; font-size: 12px;border-radius: 5px;">
                  <Icon type="ios-calendar" color="#19be6b" /> {{ task.DeadLineFormat.label }}
                </i>
              </Tooltip>
            </template>
          </label-editor-date>
        </span>
        <span>
          <label-editor-input :id="task.id" :editing="1 === 0" :displayWidth="200" @cancelUpdate="cancelUpdateTaskTitle"
            style="display: inline-block;" @updateValue="updateTaskTitle" placeHolder="模块名称" :displayText="task.Title">
          </label-editor-input>
        </span>
        </Col>
        <Col span="4" style="padding: 10px;">
        <label-editor-select @updateValue="updateTaskStatus" :optionList="taskStatusList"
          :displayText="task.ViewData.Status" :value="task.Status" :itemID="task.id">
          <template slot-scope="slotProps">
            <span style="width: 10px;display: inline-block">
              <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
            </span>
            <span style="border: 1px solid #f6f5f1;padding: 3px; font-size: 12px; border-radius: 5px;">
              <Icon type="ios-radio-button-on" color="#19be6b" />
              {{ task.ViewData.Status }}
            </span>
          </template>
        </label-editor-select>
        </Col>
        <Col span="3" style="padding: 5px;">
        <label-editor-select @updateValue="updateTaskOwner" :optionList="selectEditorMembers"
          :displayText="task.OwnerName" :value="task.Owner[0]" :itemID="task.id">
          <template slot-scope="slotProps">
            <span style="width: 22px;display: inline-block">
              <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
            </span>
            <span style="font-size: 12px;">
              <Tooltip transfer content="负责人">
                <tag style="background-color:#22aaaa">{{ task.OwnerName }}</tag>
              </Tooltip>
            </span>
          </template>
        </label-editor-select>
        </Col>
        <Col span="2" style="padding: 10px;">
        <span style="cursor: pointer;" @click="removeLinkedTask(task.id, task.Title, index)">
          <Tooltip transfer content="解除关联">
            <Icon :size="20" type="ios-remove-circle-outline" />
          </Tooltip>
        </span>
        </Col>
      </Row>
    </Card>
    <Modal :value="showLinkTaskDialog" title="关联任务" :width="600" @on-visible-change="onModalStatusChange"
      @on-cancel="cancel" :styles="{ bottom: '20px', top: '30%' }">
      <Transfer :data="projectTaskKeys" :target-keys="targetTaskKeys" filterable :filter-method="filterMethod"
        @on-change="moveTaskLink"></Transfer>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" @click="addTaskLink(itemID)" shape="circle">添加
        </Button>
        <Button type="default" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import labelEditorSelect from '../../../components/common/LabelEditor-Select.vue'
import labelEditorDate from '../../../components/common/LabelEditor-Date.vue'
export default {
  name: 'ProjectReqTask',
  props: ['itemID', 'projectID', 'version', 'itemType'],
  data() {
    return {
      reqTaskList: [],
      targetTaskKeys: [],
      showNewTask: false,
      showLinkTaskDialog: false,
      projectTaskTypes: [],
      projectTaskList: [],
      projectTaskKeys: [],
      projectMembersList: [],
      selectEditorMembers: [],
      taskStatusList: [],
      taskPriority: [{ id: 1, label: '普通' }, { id: 2, label: '紧急' }, { id: 3, label: '非常紧急' }],
      formData: {
        Title: '',
        projectID: 0,
        Version: 0,
        TaskType: 1,
        DeadLine: '',
        Priority: 1,
        WorkHours: 8,
        linkItemID: 0,
        linkItemType: 0,
        Parent: null,
        Owner: [],
        Status: 0,
        Description: ''
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapState('project', ['projectMembers']),
  },
  methods: {
    ...mapMutations('task', ['setTaskChange']),

    addNewTask: function () {
      this.showNewTask = true
      this.formData.Owner = []
      this.formData.Owner.push(this.userInfo.id)
    },

    newTask: function () {
      this.showNewTask = false
      if (this.itemType === 3) {
        this.formData.Parent = this.itemID
      } else {
        this.formData.Parent = null
      }

      if (this.formData.Owner.length === 0) {
        this.formData.Owner.push(this.userInfo.id)
      }

      this.$axios.post('/api/project/' + this.formData.projectID + '/version/' + this.formData.Version + '/project_tasks', this.formData).then(response => {
        this.reqTaskList.push(response.data.result)
        this.$Message.success({
          content: '任务添加并关联成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '任务添加失败',
          duration: 10,
          closable: true
        })

      })
    },

    cancelNewTask: function () {
      this.showNewTask = false
    },

    updateTaskTitle: function (value, id) {
      let parameters = { Title: value }
      if (value !== '') {
        this.updateTaskProperty(id, parameters, '标题')
      }
    },

    updateTaskStatus: function (newValue, oldValue, id) {
      let parameters = { Status: newValue }
      if (newValue + '' !== '0') {
        this.updateTaskProperty(id, parameters, '状态')
      }
    },

    updateTaskDeadLine: function (newValue, oldValue, id) {
      let parameters = { DeadLine: newValue }
      if (newValue) {
        this.updateTaskProperty(id, parameters, '截止日期')
      }
    },

    updateTaskOwner: function (newValue, oldValue, id) {
      let parameters = { Owner: newValue, Unit: 1 }
      if (oldValue > 0) {
        this.$axios.patch('/api/project/task/' + id + '/owner/' + oldValue, parameters).then(response => {
          this.loadReqTasks(this.itemID)
          this.$Message.success({
            content: '任务执行人更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '任务执行人更新失败',
            duration: 10,
            closable: true
          })
        })
      } else {
        this.createTaskOwner(id, newValue)
      }
    },

    createTaskOwner: function (taskID, ownerID) {
      let parameters = {
        Unit: 1,
        TaskType: 1,
        Owner: ownerID,
        Version: this.formData.Version,
        Task: taskID
      }
      this.$axios.post('/api/project/task/task_owners', parameters).then(response => {
        this.loadReqTasks(this.itemID)
        this.$Message.success({
          content: '任务执行人更新成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '任务执行人更新失败',
          duration: 10,
          closable: true
        })
      })
    },

    moveTaskLink(newTargetKeys) {
      this.targetTaskKeys = newTargetKeys
    },

    updateTaskProperty: function (id, parameters, propertyName) {
      this.$axios.patch('/api/project/task/' + id + '/', parameters).then(response => {
        for (let i = 0; i < this.reqTaskList.length; i++) {
          if (this.reqTaskList[i].id + '' === id + '') {
            this.reqTaskList.splice(i, 1, response.data.result)
          }
        }
        this.$Message.success({
          content: '任务' + propertyName + '更新成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '任务' + propertyName + '更新失败',
          duration: 10,
          closable: true
        })
      })
    },

    cancelUpdateTaskTitle: function () {
    },

    shwoLinkTaskDialog: function () {
      this.showLinkTaskDialog = true
    },

    cancel: function () {
      this.showLinkTaskDialog = false
    },

    filterMethod(data, query) {
      return data.label.indexOf(query) > -1;
    },

    onModalStatusChange: function (value) {
      this.initTargetKeys()
    },

    initTargetKeys: function () {
      try {
        this.targetTaskKeys = []
        for (let i = 0; i < this.reqTaskList.length; i++) {
          this.targetTaskKeys.push(this.reqTaskList[i].id)
        }
      } catch (error) {
        //console.log('init targetUserKey fail')
      }
    },

    getTaskList: function (projectID) {
      let url = '/api/project/' + projectID + '/version/' + this.version + '/project_tasks?' + 'page_size=10000' + '&Parent__isnull=True'
      this.$axios.get(url).then(response => {
        this.projectTaskKeys = []
        this.projectTaskList = response.data.result.results
        for (let i = 0; i < this.projectTaskList.length; i++) {
          this.projectTaskKeys.push({
            key: this.projectTaskList[i].id,
            label: this.projectTaskList[i].Title,
            description: ''
          })
        }
      }, response => {
        // error callback
      })
    },

    initTask: function () {
      this.formData.projectID = this.projectID
      this.formData.Version = this.version
      this.formData.linkItemID = this.itemID
      this.formData.linkItemType = this.itemType
    },

    loadTaskStatus: function () {
      this.$axios.get('/api/project/task/task_status').then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          tempItem['label'] = tempItem.Desc
          tempItem['id'] = tempItem.Status
          if (tempItem.Type === 1) {
            this.taskStatusList.push(tempItem)
          }
        }
      }, response => {
      })
    },

    addTaskLink(itemID) {
      let itemTypeName = 'requirement'
      if (this.itemType === 1) {
        itemTypeName = 'requirement'
      }
      if (this.itemType === 2) {
        itemTypeName = 'fortesting'
      }
      let url = '/api/project/' + itemTypeName + '/' + itemID + '/project_tasks'
      let parameters = { 'task_ids': this.targetTaskKeys }
      this.$axios.post(url, parameters).then(response => {
        this.loadReqTasks(itemID)
        this.initTargetKeys()
        this.$Message.success({
          content: '关联任务成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '关联任务失败',
          duration: 10,
          closable: true
        })
      })
      this.showLinkTaskDialog = false

    },
    removeLinkedTask(taskID, taskTitle, index) {
      this.$Modal.confirm({
        title: '解除确认',
        content: '<p>确定解除任务【' + taskTitle + ']的关联</p>',
        onOk: () => {
          if (this.itemType !== 3) {
            this.removeLink(taskID, index)
          } else {
            this.deleteTask(taskID, index)
          }
        },
        onCancel: () => {
        }
      })
    },

    removeLink(taskID, index) {
      let itemTypeName = 'requirement'
      if (this.itemType !== 1) {
        itemTypeName = 'fortesting'
      }
      let url = '/api/project/' + itemTypeName + '/' + this.itemID + '/task/' + taskID
      this.$axios.delete(url).then(response => {
        this.reqTaskList.splice(index, 1)
        this.$Message.success({
          content: '解除关联成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '解除关联失败',
          duration: 10,
          closable: true
        })
      })
    },

    deleteTask(taskID, index) {
      //console.log(taskID)
      this.$axios.delete('/api/project/task/' + taskID).then(response => {
        this.reqTaskList.splice(index, 1)
      }, response => { })
    },

    onFinishedTask: function (value) {
      this.$emit('finished-task', this.task.id, this.task.Status)
    },

    loadProjectMembers() {
      this.projectMembersList = []
      this.selectEditorMembers = []
      for (let i = 0; i < this.projectMembers.length; i++) {
        let tempItem = this.projectMembers[i]
        tempItem['label'] = tempItem.name
        tempItem['id'] = tempItem.PMMember
        this.selectEditorMembers.push(tempItem)
        this.projectMembersList.push(this.projectMembers[i])
      }
    },

    loadReqTasks(itemID) {
      let itemTypeName = 'requirement'
      if (this.itemType === 1) {
        itemTypeName = 'requirement'
      }
      if (this.itemType === 2) {
        itemTypeName = 'fortesting'
      }
      let url = '/api/project/' + itemTypeName + '/' + itemID + '/project_tasks'
      if (this.itemType === 3) {
        url = '/api/project/task/' + itemID + '/child_tasks'
      }
      this.$axios.get(url).then(response => {
        this.reqTaskList = response.data.result
        this.initTargetKeys()
      }, response => {

      })
    },
    loadProjectTaskType: function () {
      this.$axios.get('/api/project/task_type').then(response => {
        this.projectTaskTypes = response.data.result
      }, response => {
        // error callback
      })
    },
  },
  created: function () {
    this.initTask()
    this.loadReqTasks(this.itemID)
    this.loadTaskStatus()
    this.loadProjectTaskType()
    this.formData.Version = this.version
  },
  mounted: function () {
    //this.loadProjectMembers()
  },

  watch: {
    itemID: function (value) {
      this.initTask()
      this.loadReqTasks(value)
      this.getTaskList(this.projectID)
    },
    projectID: function (value) {
      this.getTaskList(value)
      this.formData.projectID = this.projectID
      this.formData.Version = this.version
    }
  },
  components: {
    labelEditorInput,
    labelEditorSelect,
    labelEditorDate
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
