<template>
  <Card dis-hover title="测试用例">
    <a href="#" slot="extra">
      <Date-picker :value="filterDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期"
        @on-change="handleChangeDate" style="width: 220px; padding-left:10px;padding-right:10px">
      </Date-picker>
      <Button type="primary" shape="circle" icon="ios-search" @click="loadTestKanbanStatistics">搜索</Button>
    </a>
    <Row :gutter="2" type="flex" justify="space-between" align="middle">
      <Col span="12" style="padding: 5px" class="demand-content-item work-total">
      <div v-loading="testcaseLoading">
        <NumbersCard :data="testcaseNum">
          <template slot="header">
            测试用例统计（个）
          </template>
        </NumbersCard>
      </div>
      </Col>
      <Col span="12" style="padding: 5px" class="demand-content-item work-total">
      <div v-loading="testcaseLoading">
        <NumbersCard :data="testcaseRate">
          <template slot="header">
            自动化/重复率（%）
          </template>
        </NumbersCard>
      </div>
      </Col>
      <!--
      <Col span="12" style="padding-right: 0px;">
          <Card dis-hover>
            <Row :gutter="40" type="flex" justify="space-between" style="height: 180px;">
              <Col span="6">
                <i-circle :percent="100" stroke-color="#5cb85c" :size="110" style="padding-top:40px">
                  <div class="home-kanban-testcase-circle">
                    <h1 style="padding-top:80px">{{ case_total_count.CaseCount }}</h1>
                    <p style="font-size: 12px">功能用例总数</p>
                  </div>
                </i-circle>
              </Col>
              <Col span="6">
                <i-circle :percent="100" stroke-color="#585eaa" :size="110" style="padding-top:40px">
                  <div class="home-kanban-testcase-circle">
                    <h1 style="padding-top:80px">{{ case_total_count.AutoCount }}</h1>
                    <p style="font-size: 12px">自动化用例总数</p>
                  </div>
                </i-circle>
              </Col>
              <Col span="6">
                <i-circle :percent="100" stroke-color="#eeb017" :size="110" style="padding-top:40px">
                  <div class="home-kanban-testcase-circle">
                    <h1 style="padding-top:80px">{{ case_count_info.all_case }}</h1>
                    <p style="font-size: 12px">新增功能Case</p>
                  </div>
                </i-circle>
              </Col>
              <Col span="6">
                <i-circle :percent="100" stroke-color="#ee3917" :size="110" style="padding-top:40px">
                  <div class="home-kanban-testcase-circle">
                    <h1 style="padding-top:80px">{{ auto_count_info.all_case }}</h1>
                    <p style="font-size: 12px">新增自动化Case</p>
                  </div>
                </i-circle>
              </Col>
            </Row>
          </Card>
      </Col>
-->
    </Row>
    <Row :gutter="2" type="flex" justify="space-between" style="padding-top:20px">
      <Col span="12" style="padding: 10px">
      <div id="projectnewtestcasecount" style="min-width:300px;height:400px;"></div>
      </Col>
      <Col span="12" style="padding: 10px">
      <div id="autotestPercentChart" style="min-width:300px;height:400px"></div>
      </Col>
    </Row>
    <Row :gutter="2" type="flex" justify="space-between" style="padding-top:20px">
      <Col span="12" style="padding: 10px">
      <div id="testcaseRankChart" style="min-width:300px;height:400px"></div>
      </Col>
      <Col span="12" style="padding: 10px">
      <div id="testcaserepeatrate" style="min-width:300px;height:400px;"></div>
      </Col>
    </Row>
    <Drawer title="测试用例增长趋势图" width="920px" :closable="false" v-model="showCaseGrowthTrend">
      <div slot="header" class="ivu-drawer-header-inner-2">
        <Row>
          <Col span="12" class="ivu-drawer-header-inner-2">测试用例增长趋势图</Col>
          <Col span="12">
          <Select v-model="selectProject" size="small" placeholder="请选择项目" multiple filterable
            @on-open-change="handlerOpenChange" @on-change="handlerOpenChange">
            <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}</Option>
          </Select>
          </Col>
        </Row>
      </div>
      <Row :gutter="24">
        <Col span="24">
        <div id="space_testcase_sum_growthtrend_chart" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="testcase_sum_growthtrend_chart" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col>
        <div id="testcase_growthtrend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="autocase_sum_growthtrend_chart" style="width:900px;height:400px"></div>
        </Col>
      </Row>
      <br>
      <Row :gutter="24">
        <Col span="24">
        <div id="autocase_growthtrend" style="width:900px;height:400px"></div>
        </Col>
      </Row>
    </Drawer>
  </Card>
</template>

<script>
import HighCharts from "highcharts";
import * as echarts from "echarts";
import { mapGetters, mapState } from "vuex";
import NumbersCard from "../../../components/numbercard/NumbersCard.vue";
import { getCurrDateDifference } from "../../../utils/utils.js"

export default {
  components: { NumbersCard },
  name: "HomeKanbanTestCaseChart",
  data() {
    return {
      filterDate: [],
      selectProject: [],
      case_count_info: {},
      case_total_count: {},
      projectnewtestcasecount_chart: null,
      ProjectTestCaseGrowthCountData: {
        categories: [],
        series: [],
      },
      testefficiency_chart: null,
      testwaittime_chart: null,
      testcaseLoading: true,
      testcaseData: {
        case: "新增",
        allcase: "全部",
        testcase: 0,
        all_testcase: 0,
        autocase: 0,
        all_autocase: 0,
        autopercent: "-",
        auto_percent: 0,
      },
      testcaseKeys: [
        {
          key: "case",
          name: "数量\\类型",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "allcase",
        },
        {
          key: "testcase",
          name: "功能用例",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "all_testcase",
        },
        {
          key: "autocase",
          name: "自动化用例",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
          allcase: "all_autocase",
        },
      ],
      testcaserateData: {
        testcaserate: "比例",
        autorate: 0,
        repeatrate: 0,
        autoexec: 0,
      },
      testcaserateKeys: [
        {
          key: "testcaserate",
          name: "百分比\\类型",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
        },
        {
          key: "autorate",
          name: "自动化率",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
        },
        {
          key: "autoexec",
          name: "自动化执行率",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
        },
        {
          key: "repeatrate",
          name: "重复率",
          hideChart: false,
          type: "bar",
          stack: undefined,
          unit: "个",
          label: {
            show: true,
          },
          chartDefaultShow: true,
        },
      ],
      projectautocasepercent_chart: null,
      projecttestcaserank_chart: null,
      ProjectTestCaseSumGrowthTrendChart: null,
      ProjectTestCaseGrowthTrendChart: null,
      ProjectAutoCaseSumGrowthTrendChart: null,
      ProjectAutoCaseGrowthTrendChart: null,
      ProjectTestCaseRepeatEchart: null,
      auto_count_info: {},
      showCaseGrowthTrend: false,
      SpaceTestCaseSumGrowthTrendChart: null,
    };
  },

  computed: {
    ...mapState('project', ['projectList']),

    testcaseNum() {
      //console.log("testcaseNum()");
      const result = {};
      const keys = this.testcaseKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.testcaseData[item.key] || 0,
            title: item.name,
            allcase: this.testcaseData[item.allcase],
          };
        }
      });
      //console.log("testtaskNum()=", result);
      return result;
    },

    testcaseRate() {
      const result = {};
      const keys = this.testcaserateKeys;
      keys.map((item) => {
        if (!item.hideChart) {
          result[item.key] = {
            number: this.testcaserateData[item.key] || 0,
            title: item.name,
          };
        }
      });
      //console.log("testtaskNum()=", result);
      return result;
    }
  },

  methods: {
    setFilterDate: function () {
      this.filterDate = getCurrDateDifference(7)
    },

    loadCaseTotalCount: function () {
      this.$axios.get("/api/home/<USER>/case_total_count").then(
        (response) => {
          this.case_total_count = response.data.result;
        },
        (response) => { }
      );
    },

    handleChangeDate: function (date) {
      this.filterDate = date;
      this.loadTestKanbanStatistics()
    },

    //创建测试用例 基础柱状图
    initNewTestCaseCountEchart: function (chart_id) {
      let init_echart = echarts.init(
        document.getElementById(chart_id)
      );
      let option = {
        title: {
          text: "新增用例数量",
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "6%",
          right: "2%",
          bottom: "40%",
        },
        toolbox: {
          right: 40,
          feature: {
            myCaseGrowthTrendTool: {
              show: true,
              title: '增长趋势',
              icon: 'path://M958.620955 427.539376l-42.606377 0c-11.773138 0-21.301142 9.528004-21.301142 21.301142l0 553.857317c0 11.732206 9.529027 21.301142 21.301142 21.301142l42.606377 0c11.774161 0 21.301142-9.56996 21.301142-21.301142L979.922097 448.840518C979.922097 437.06738 970.395116 427.539376 958.620955 427.539376zM660.38962 555.352367l-42.60433 0c-11.775184 0-21.301142 9.528004-21.301142 21.301142l0 426.044326c0 11.732206 9.526981 21.301142 21.301142 21.301142l42.60433 0c11.774161 0 21.303188-9.56996 21.303188-21.301142L681.692809 576.654532C681.692809 564.880371 672.162758 555.352367 660.38962 555.352367zM362.159308 257.121032l-42.60433 0c-11.775184 0-21.302165 9.487072-21.302165 21.302165l0 724.275661c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.60433 0c11.774161 0 21.301142-9.56996 21.301142-21.301142l0-724.275661C383.46045 266.60708 373.932446 257.121032 362.159308 257.121032zM63.927973 768.374018 21.32262 768.374018c-11.775184 0-21.302165 9.528004-21.302165 21.300119l0 213.023698c0 11.732206 9.526981 21.301142 21.302165 21.301142l42.605354 0c11.774161 0 21.302165-9.56996 21.302165-21.301142L85.230138 789.67516C85.230138 777.902022 75.703158 768.374018 63.927973 768.374018z',
              onclick: (e) => {
                this.showCaseGrowthTrend = !this.showCaseGrowthTrend
              }
            },
          }
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: ["-"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 16,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "功能Case",
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
            itemStyle: {
              color: "#ee6666",
            },
            data: [0],
          },
          {
            name: "自动化Case",
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
            itemStyle: {
              color: "#3ba272",
            },
            data: [0],
          },
        ],
      };
      //console.log("this.projectnewtestcasecount_chart=", this.projectnewtestcasecount_chart)
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize();
      });
      return init_echart

      /*
      //highcharts 换 echarts
      var chart = HighCharts.chart(containerId, {
        chart: {
          type: "column",
          zoomType: "x",
        },
        title: {
          text: "新增用例数量",
        },
        xAxis: {
          categories: [],
        },
        yAxis: {
          min: 0,
          title: {
            text: "",
          },
        },
        tooltip: {
          shared: true,
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            dataLabels: {
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "测试用例",
            data: [0],
          },
          {
            name: "自动化Case",
            data: [0],
          },
        ],
      });
      return chart;
*/
    },
    createTestWaitTimeCharts: function (containerId) {
      var chart = HighCharts.chart(containerId, {
        chart: {
          type: "column",
          zoomType: "x",
        },
        title: {
          text: "测试等待时间",
          x: 'center'
        },
        subtitle: {
          text: "",
        },
        xAxis: {
          categories: ["-"],
          crosshair: true,
        },
        yAxis: {
          min: 0,
          title: {
            text: "",
          },
          stackLabels: {
            // 堆叠数据标签
            enabled: true,
          },
        },
        tooltip: {
          // head + 每个 point + footer 拼接成完整的 table
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat:
            '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f} 天</b></td></tr>',
          footerFormat: "</table>",
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            dataLabels: {
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "测试等待时间",
            data: [0],
          },
        ],
      });
      return chart;
    },
    //显示点值的折线图
    createLineLabelsLine: function (containerId) {
      var LineChart = HighCharts.chart(containerId, {
        chart: {
          type: "line",
          zoomType: "x",
        },
        title: {
          text: "测试效率",
          x: 'center'
        },
        xAxis: {
          categories: ["-"],
        },
        yAxis: {
          title: {
            text: "",
          },
        },
        plotOptions: {
          line: {
            dataLabels: {
              // 开启数据标签
              enabled: true,
            },
          },
        },
        series: [
          {
            name: "用例数",
            data: [0],
          },
          {
            name: "测试时长",
            data: [0],
          },
        ],
      });
      return LineChart;
    },
    initLineChart: function (chart_id, chart_title) {
      let init_echart = echarts.init(
        document.getElementById(chart_id)
      );
      let option = {
        title: {
          text: chart_title,
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          type: 'scroll',
          //orient: 'vertical',
          right: "5%",
          left: "5%",
          //top: "10%",
          bottom: 0,
        },
        grid: {
          left: "5%",
          right: "5%",
          bottom: "10%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            //saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["-"],
        },
        yAxis: {
          type: "value",
        },
        series: [],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize();
      });
      return init_echart
    },
    // 创建自动化率柱状图
    initAutoCasePercentEchart: function (chart_id, chart_title) {
      let init_echart = echarts.init(
        document.getElementById(chart_id)
      );
      let option = {
        title: {
          text: chart_title,
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "6%",
          right: "2%",
          bottom: "40%",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: ["-"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 18,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "自动化率",
            type: "bar",
            data: [0],
          },
          {
            name: "自动化执行率",
            type: "bar",
            data: [0],
          },
          {
            name: "自动化执行覆盖率",
            type: "bar",
            data: [0],
          },
        ],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize();   //myChart指自己定义的echartsDom对象
      });
      return init_echart
    },
    // 创建 功能/自动化/自动化执行用例排行 状图
    initProjectCaseRankEchart: function (chart_id, chart_title) {
      let init_echart = echarts.init(
        document.getElementById(chart_id)
      );
      let option = {
        title: {
          text: chart_title,
          x: 'center'
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: "7%",
        },
        grid: {
          left: "7%",
          right: "2%",
          bottom: "40%",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
              rotate: 40,
            },
            data: ["-"],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 18,
          },
          {
            type: "inside",
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: "功能用例数",
            type: "bar",
            data: [0],
          },
          {
            name: "归档用例数",
            type: "bar",
            data: [0],
          },
          {
            name: "自动化用例数",
            type: "bar",
            data: [0],
          },
          {
            name: "自动化执行用例数",
            type: "bar",
            data: [0],
          },
        ],
      };
      init_echart.setOption(option);
      window.addEventListener("resize", function () {
        init_echart.resize(); //myChart指自己定义的echartsDom对象
      });
      return init_echart
    },

    loadProjectTestCaseRank: function () {
      this.$axios.get("/api/home/<USER>/project_testcase_rank").then((response) => {
        let response_data = response.data.result;
        let option = this.projecttestcaserank_chart.getOption();
        option.xAxis[0].data = response_data.xAxis[0].data;
        option.series[0].data = response_data.series[0].data;
        option.series[1].data = response_data.series[1].data;
        option.series[2].data = response_data.series[2].data;
        option.series[3].data = response_data.series[3].data;
        this.projecttestcaserank_chart.setOption(option);
      },
        (response) => { }
      );
    },

    loadProjectAutoTestPercent: function () {
      // 自动化率
      let reqUrl = "/api/home/<USER>/project_autocase_percent?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.projectautocasepercent_chart.getOption();
        option.xAxis[0].data = response_data.xAxis[0].data;
        option.series[0].data = response_data.series[0].data;
        option.series[1].data = response_data.series[1].data;
        option.series[2].data = response_data.series[2].data;
        this.projectautocasepercent_chart.setOption(option);
      });
    },


    loadProjectNewTestCaseCount: function () {
      let reqUrl = "/api/home/<USER>/projecttestcasecount?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        this.ProjectTestCaseGrowthCountData.categories = response_data["categories"];
        this.ProjectTestCaseGrowthCountData.series = response_data["series"];
      });
    },

    loadNewTestCaseCount: function () {
      this.testcaseLoading = true;
      let reqUrl = "/api/home/<USER>/testcasecount?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        this.testcaseData["testcase"] = response_data["new_testcase"];
        this.testcaseData["autocase"] = response_data["new_autocase"];
        this.testcaseData["all_testcase"] = response_data["all_testcase_valid"].toString() + "/" + response_data["all_testcase"].toString();
        this.testcaseData["all_autocase"] = response_data["all_autocase"];
        this.testcaseData["auto_percent"] = (response_data["auto_percent"] * 100).toFixed(2).toString() + "%";
        this.testcaserateData["autorate"] = (response_data["auto_percent"] * 100).toFixed(2).toString() + "%";
        this.testcaserateData["autoexec"] = response_data["auto_exec_rate"].toString() + "%";
      });
      this.testcaseLoading = false;
    },

    loadProductSpaceTestCaseRepeat: function (value) {
      this.testcaseLoading = true;
      this.$axios.get("/api/home/<USER>/testcaserepeatrate").then((response) => {
        let response_data = response.data.result;
        this.testcaserateData["repeatrate"] = (response_data["TestCaseRepeatRate"] * 100).toFixed(2).toString() + "%";
      });
      this.testcaseLoading = false;
    },

    // 测试用例重复率
    loadProjectTestCaseRepeatRate: function () {
      this.$axios.get("/api/home/<USER>/project_testcase_repeatrate").then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectTestCaseRepeatEchart.getOption();
        option.xAxis[0].data = response_data.xAxis.data
        option.series = response_data.series
        this.ProjectTestCaseRepeatEchart.setOption(option)
      },
        (response) => { }
      );
    },

    // 测试用例增长趋势
    loadProjectTestCaseSumGrowthTrend: function () {
      let reqUrl = "/api/home/<USER>/growthtrend/testcase/sum?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      if (this.selectProject.length > 0) {
        reqUrl = reqUrl + "&project=" + this.selectProject
      }
      this.$axios.get(reqUrl).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectTestCaseSumGrowthTrendChart.getOption();
        option.xAxis[0].data = response_data.xAxis.data
        option.series = response_data.series
        this.ProjectTestCaseSumGrowthTrendChart.setOption(option, true)
        //console.log("option==", option)
      },
        (response) => { }
      );
    },

    // 测试用例新增趋势
    loadProjectTestCaseGrowthTrend: function () {
      let req_url = "/api/home/<USER>/growthtrend/testcase/new/180"
      if (this.selectProject.length > 0) {
        req_url = req_url + "?project=" + this.selectProject
      }
      this.$axios.get(req_url).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectTestCaseGrowthTrendChart.getOption();
        option.xAxis[0].data = response_data.xAxis.data
        option.series = response_data.series
        this.ProjectTestCaseGrowthTrendChart.setOption(option, true)
      },
        (response) => { }
      );
    },

    // 自动化用例增长趋势
    loadProjectAutoCassSumGrowthTrend: function () {
      let req_url = "/api/home/<USER>/growthtrend/autocase/sum?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      if (this.selectProject.length > 0) {
        req_url = req_url + "&project=" + this.selectProject
      }
      this.$axios.get(req_url).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectAutoCaseSumGrowthTrendChart.getOption();
        option.xAxis[0].data = response_data.xAxis.data
        option.series = response_data.series
        this.ProjectAutoCaseSumGrowthTrendChart.setOption(option, true)
      },
        (response) => { }
      );
    },

    // 自动化用例新增趋势
    loadProjectAutoCassGrowthTrend: function () {
      let req_url = "/api/home/<USER>/growthtrend/autocase/new/180"
      if (this.selectProject.length > 0) {
        req_url = req_url + "?project=" + this.selectProject
      }
      this.$axios.get(req_url).then((response) => {
        let response_data = response.data.result;
        let option = this.ProjectAutoCaseGrowthTrendChart.getOption();
        option.xAxis[0].data = response_data.xAxis.data
        option.series = response_data.series
        this.ProjectAutoCaseGrowthTrendChart.setOption(option, true)
      },
        (response) => { }
      );
    },

    // 所有用例新增趋势
    loadSpaceTestCaseGrowthTrend: function () {
      let req_url = "/api/home/<USER>/growthtrend/case?st=" + this.filterDate[0] + "&et=" + this.filterDate[1]
      this.$axios.get(req_url).then((response) => {
        let response_data = response.data.result;
        let option = this.SpaceTestCaseSumGrowthTrendChart.getOption();
        option.yAxis = [
          {
            type: 'value',
            name: '总数',
          },
          {
            type: 'value',
            name: '新增',
          }
        ],
          option.xAxis[0].data = response_data.xAxis.data
        option.xAxis[0].axisTick = {
          alignWithLabel: false
        }
        option.xAxis[0].boundaryGap = true,
          option.series = response_data.series
        this.SpaceTestCaseSumGrowthTrendChart.setOption(option, true)
      },
        (response) => { }
      );
    },

    loadTestEff: function (value) {
      this.$axios.get("/api/home/<USER>/testeff/" + value).then((response) => {
        let response_data = response.data.result;
        this.testeffData["casecount_avg"] =
          response_data["all_project_testplan_casecount_avg"];
        this.testeffData["testtingtime_avg"] =
          response_data["all_project_testplan_testingtime_avg"];
        this.testefficiency_chart.update({
          xAxis: {
            categories: response_data["projects"],
          },
          series: [
            {
              name: "用例数",
              data: response_data["project_testplan_casecount_avg"],
            },
            {
              name: "测试时长",
              data: response_data["project_testplan_testtingtime_avg"],
            },
          ],
        });
      });
    },

    loadTestWait: function (value) {
      this.$axios.get("/api/home/<USER>/testwaittime/" + value).then((response) => {
        let response_data = response.data.result;
        this.testwaittime_chart.update({
          xAxis: {
            categories: response_data["projects"],
          },
          series: [
            {
              data: response_data["testplan_avg_testingtime_list"],
            },
          ],
        });
      });
    },

    initKanbanTestChart: function () {
      this.projectnewtestcasecount_chart = this.initNewTestCaseCountEchart("projectnewtestcasecount");
      this.ProjectTestCaseRepeatEchart = this.initLineChart("testcaserepeatrate", "测试用例重复率");
      this.projectautocasepercent_chart = this.initAutoCasePercentEchart("autotestPercentChart", "自动化率");
      this.projecttestcaserank_chart = this.initProjectCaseRankEchart("testcaseRankChart", "功能/自动化/自动化执行用例数");
      this.ProjectTestCaseSumGrowthTrendChart = this.initLineChart("testcase_sum_growthtrend_chart", "功能用例增长趋势");
      this.ProjectTestCaseGrowthTrendChart = this.initLineChart("testcase_growthtrend", "功能用例新增趋势");
      this.ProjectAutoCaseSumGrowthTrendChart = this.initLineChart("autocase_sum_growthtrend_chart", "自动化用例增长趋势")
      this.ProjectAutoCaseGrowthTrendChart = this.initLineChart("autocase_growthtrend", "自动化用例新增趋势")
      this.SpaceTestCaseSumGrowthTrendChart = this.initLineChart("space_testcase_sum_growthtrend_chart", "用例增长趋势");
    },

    loadTestKanbanStatistics: function () {
      this.loadProjectNewTestCaseCount();
      this.loadNewTestCaseCount();
      this.loadProductSpaceTestCaseRepeat()
      //this.loadTestEff(this.SearchDays);
      //this.loadTestWait(this.SearchDays);
      this.loadSpaceTestCaseGrowthTrend()
      this.loadProjectTestCaseSumGrowthTrend()
      this.loadProjectAutoCassSumGrowthTrend()
    },

    handlerOpenChange(is_open) {
      if ((typeof is_open) == "undefined") {
        this.loadProjectTestCaseSumGrowthTrend()
        this.loadProjectAutoCassSumGrowthTrend()
        this.loadProjectTestCaseGrowthTrend()
        this.loadProjectAutoCassGrowthTrend()
      } else if (is_open == false) {
        this.loadProjectTestCaseSumGrowthTrend()
        this.loadProjectAutoCassSumGrowthTrend()
        this.loadProjectTestCaseGrowthTrend()
        this.loadProjectAutoCassGrowthTrend()
      }
    },
  },

  created: function () { },

  mounted: function () {
    this.setFilterDate()
    //this.loadCaseTotalCount();
    this.loadProjectTestCaseRepeatRate();
    this.loadProjectTestCaseSumGrowthTrend();
    this.loadProjectTestCaseGrowthTrend();
    this.initKanbanTestChart();
    this.loadTestKanbanStatistics();
    this.loadProjectAutoTestPercent();
    this.loadProjectTestCaseRank();
    this.loadProjectAutoCassSumGrowthTrend();
    this.loadProjectAutoCassGrowthTrend();
  },

  watch: {
    selectProject() {
      this.handlerOpenChange()
    },

    ProjectTestCaseGrowthCountData: {
      handler() {
        let option = this.projectnewtestcasecount_chart.getOption();
        option.xAxis[0].data = this.ProjectTestCaseGrowthCountData.categories;
        option.series[0].data =
          this.ProjectTestCaseGrowthCountData.series[0].data;
        option.series[1].data =
          this.ProjectTestCaseGrowthCountData.series[1].data;
        //console.log(option);
        this.projectnewtestcasecount_chart.setOption(option);
      },
      deep: true,
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.test-total {
  flex: 2;
  margin-right: 5px;

  .chart-demand {
    position: relative;
    flex-basis: 90px;

    .charts {
      position: absolute;
      top: -65px;
    }
  }
}

.home-kanban-testcase-circle {
  & h1 {
    color: #3f414d;
    font-size: 28px;
    font-weight: normal;
  }

  & p {
    color: #657180;
    font-size: 14px;
    margin: 10px 0 15px;
  }

  & span {
    display: block;
    padding-top: 15px;
    color: #657180;
    font-size: 14px;

    &:before {
      content: "";
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e0e3e6;
      position: relative;
      top: -15px;
    }
  }

  & span i {
    font-style: normal;
    color: #3f414d;
  }

  ivu-drawer-header-inner-2 {
    display: inline-block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    color: #5578aa;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
