<template>
  <div v-if="projectReq.ViewData.Requirements.length > 0">
    <Row align="middle" class="border1" type="flex">
      <Col :span="layout.projectWidth" v-if="layout.projectWidth > 0" style="text-align: center;">
      <div style="padding: 16px;">
        <div style="height: 10px; background-color:#5578aa"></div>
        <div class="projectBorder">
          <div>
            <Icon type="ios-radio-button-on" color="#5578aa"></Icon> {{ projectReq.PBTitle }}
          </div>
          <div style="padding: 6px;font-size: 12px;">
            需求总数： {{ projectReq.ViewData.RequirementCounts }}
          </div>
        </div>
      </div>
      </Col>
      <Col :span="layout.otherLayout.width" style="border-left: 1px solid #eef2f6;">
      <Row align="middle" type="flex" style="border-bottom: 1px solid #f5f7f9;">
        <Col span="2">
        <Avatar :src="projectReq.PBAvatar"></Avatar>{{ projectReq.PBTitle }}
        </Col>
        <Col :lg="layout.otherLayout.featureLayout.requirementWidth"
          :md="layout.otherLayout.featureLayout.requirementWidth - 2"
          style="padding: 16px; border-left: 1px solid #eef2f6;">
        <div v-for="req, index in projectReq.ViewData.Requirements">
          <Row align="middle" type="flex">
            <Col span="24">
            <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
              <Col :lg="12" :md="21">
              <span style="width:24px; display: inline-block;margin-left: 5px;">
                <tooltip content="需求优先级">
                  <Avatar shape="square" style="background-color: #5578aa;" size="small">
                    {{ req.ViewData.Priority }}
                  </Avatar>
                </tooltip>
              </span>
              <span
                style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 14px; margin-left: 10px;margin-right: 10px;">
                <Tooltip content="风险">
                  {{ req.ViewData.Risk }}
                </Tooltip>
              </span>
              <!--<span style="padding-right: 10px;color: #5578aa;width: 60px;display: inline-block;">-->
              <!--<tooltip content="规划版本">-->
              <!--<i>[{{ req.ViewData.Version }}]</i>-->
              <!--</tooltip>-->
              <!--</span>-->
              <span @click="viewReqItem(req.id)" style="color: #5578aa;text-decoration: underline;cursor:pointer;">
                {{ req.Title }}
              </span>
              </Col>
              <Col :lg="3" :md="3">
              <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                <Icon type="ios-radio-button-on" color="#5578aa"></Icon> {{ req.ViewData.Status }}
              </span>
              </Col>
              <Col :lg="7" :md="0" :sm="0">
              <span style="padding: 5px;">
                <tooltip content="需求负责人">
                  <Tag style="background-color: #22aaaa;">{{ req.ViewData.Owner }}</Tag>
                </tooltip>
              </span>
              <span style="padding: 5px;">
                <tooltip content="当前负责人">
                  <Tag style="background-color: #5578aa;">{{ req.ViewData.ExecutiveOwner }}
                  </Tag>
                </tooltip>
              </span>
              <span style="margin-left: 20px;width:100px;display: inline-block;">
                <tooltip content="预计上线日期">
                  <span
                    style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;padding-right: 10px;">
                    <i>{{ req.ViewData.ReleaseDate }}</i>
                  </span>
                </tooltip>
              </span>
              </Col>
              <Col :lg="2">
              <span style="display: inline-block; margin-right: 10px;text-align: center;">
                <Dropdown @on-click="onReqOperation">
                  <span style=" color: #5578aa ">
                    <Icon type="ios-more" size="32" />
                  </span>
                  <DropdownMenu slot="list">
                    <DropdownItem :name="'delete:' + index">删除</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </span>
              </Col>
            </Row>
            </Col>
          </Row>
        </div>
        <Row style="padding: 15px 10px 1px 10px;" v-if="clickOnReqID">
          <Col span="10">
          <span>
            <Input v-model="reqFormData.Title" :maxlength="50" show-word-limit placeholder="需求标题" />
          </span>
          </Col>
          <Col span="8">
          <span style="margin-left: 10px; margin-right: 10px;">
            <DatePicker v-model="reqFormData.ReleaseDate" type="date" placeholder="预计发布日期"></DatePicker>
          </span>
          <span style="margin-left: 10px; margin-right: 10px;">
            <Select v-model="reqFormData.Priority" style="width: 100px;">
              <Option v-for="priority in reqPriority" :value="priority.Status" :key="priority.id">{{ priority.Desc
                }}
              </Option>
            </Select>
          </span>
          </Col>
        </Row>
        </Col>
      </Row>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'


export default {
  name: 'projectReuqirementItem',
  props: ['dynicLayout', 'projectReqInfo'],
  data() {
    return {
      data: [99, 71, 78, 25, 36, 92],
      line: '',
      selectedReqItems: [],
      localLayout: {
        projectWidth: 0,
        otherLayout: {
          width: 0,
          featureLayout: {
            width: 0,
            requirementWidth: 0
          }
        }
      },
      mouseOnModuleID: 0,
      clickOnReqID: 0,
      reqStatus: [],
      reqRisk: [],
      reqPriority: [],
      projectReq: { ViewData: { Modules: [] } },
      reqFormData: {
        Title: '',
        Module: 0,
        projectID: 0,
        Version: 0,
        Priority: 3,
        ReleaseDate: '',
        Status: 1,
        Owner: 0,
        Creator: 0,
        RiskLevel: 1
      }
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapGetters(['appBodyHeight', 'appBodyWidth']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyHeight - 132
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    layout: function () {
      if (this.dynicLayout) {
        return this.dynicLayout
      } else {
        return this.localLayout
      }
    }

  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange']),

    onMouseOnReq: function (value) {
      this.showNewReq = true
      this.mouseOnModuleID = value
    },

    onMouseOutReq: function () {
      this.showNewReq = false
      //        this.mouseOnReqID = 0
    },

    viewReqItem: function (value) {
      this.$emit('viewReqItem', value)
    },


    loadReqPriority: function () {
      this.$axios.get('/api/project/task/task_status').then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 3) {
            this.reqStatus.push(tempItem)
          }
          if (tempItem.Type === 4) {
            this.reqPriority.push(tempItem)
          }
          if (tempItem.Type === 5) {
            this.reqRisk.push(tempItem)
          }
        }
      }, response => {
      })
    },


    onMouseOverModule: function (value) {
      this.showNewModule = true
      this.mouseOnModuleID = value
    },
    onMouseOverModule: function (value) {
      this.showNewModule = true
      this.mouseOnModuleID = value
    },

    onMouseOutModule: function () {
      this.showNewModule = false
    },



    onReqOperation: function (op) {
      let opArray = op.split(':')
      if (opArray[0] === 'delete') {
        this.deleteReq(parseInt(opArray[1]), parseInt(opArray[2]))
      }

      if (opArray[0] === 'fortesting') {
        this.doFortesting(parseInt(opArray[1]), parseInt(opArray[2]))
      }
    },

    deleteReq: function (reqIndex) {
      let deleteReq = this.projectReq.ViewData.Modules[moduleIndex].ViewData.Requirements[reqIndex]
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除ID 为[' + deleteReq.id + ']的需求',
        onOk: () => {
          this.$axios.delete('/api/project/requirement/' + deleteReq.id).then(response => {
            this.projectReq.ViewData.Modules[moduleIndex].ViewData.Requirements.splice(reqIndex, 1)
            this.$Message.success({
              content: '需求删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '需求删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })
    },

    findReqIdExists: function (req, reqList) {
      let result = false
      for (let i = 0; i < reqList.length; i++) {
        if (req === reqList[i]) {
          result = true
          break
        }
      }
      return result
    },

    getReqByID: function (id, projectReqInfo) {
      let result = null
      for (let i = 0; i < projectReqInfo.ViewData.Modules.length; i++) {
        for (let j = 0; j < projectReqInfo.ViewData.Modules[i].ViewData.Requirements.length; j++) {
          if (id === projectReqInfo.ViewData.Modules[i].ViewData.Requirements[j].id) {
            result = projectReqInfo.ViewData.Modules[i].ViewData.Requirements[j]
            break
          }
        }

      }
      return result
    },


  },
  created: function () {
    this.loadReqPriority()
    this.projectReq = this.projectReqInfo

  },
  watch: {
    projectReqInfo: function (value) {
      this.projectReq = value
    }

  },

  components: {
    labelEditorInput,
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}

.newFeatureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border: 1px dashed #99aecc;

}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}
</style>
