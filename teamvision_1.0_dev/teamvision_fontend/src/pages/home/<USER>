<template>
  <div>
    <Card :padding="0" style="margin-top: 0px;" dis-hover>
      <!--<div class="home-summary-card-title">项目进展:-->
      <!--</div>-->
      <p slot="title">任务状态</p>
      <span slot="extra">
        <RadioGroup v-model="state" type="button" size="small" @on-change="filterFortestings">
          <Radio label="0">全部</Radio>
          <Radio label="1">未发布</Radio>
          <Radio label="2">已发布</Radio>
        </RadioGroup>
      </span>
      <span slot="extra" style="margin-left: 20px; ">
        <RadioGroup v-model="dateRange" type="button" size="small" @on-change="filterFortestings">
          <Radio label="-15">15天</Radio>
          <Radio label="-30">30天</Radio>
          <Radio label="-90">90天</Radio>
        </RadioGroup>
      </span>
      <Card dis-hover :padding="0" :bordered="false" :style="'height:' + summaryHeight + 'px' + '; overflow-y:scroll;'">
        <Row v-for="project in projectStateList" :key="project.id" style="border-bottom: 1px solid #f0f0f0">
          <Col span="24" style="border-radius: 10px 0px 0px 10px">
          <div
            style="height: 54px;padding: 16px;border-bottom: 1px solid #e8eaec;border-top: 1px solid #e8eaec;border-radius: 5px 0px 0px 5px;">
            <span style="font-size: 16px;font-weight: bold;margin-right: 50px;">
              <router-link :to="'/project/' + project.id + '/issue/all'">
                <span style="padding-right:5px;">
                  <Avatar shape="square" :src="project.PBAvatar"></Avatar>
                </span>
                <span style="color:#1e2b3d;text-decoration: underline;">
                  {{ project.PBTitle }}
                </span>
              </router-link>
            </span>
            <span style="font-size: 12px;margin-right: 20px;">测试需求共计: {{ project.ViewData.Fortestings.length }}个</span>
            <span style="font-size: 12px;margin-right: 20px;">待处理问题:</span>
            <span style="font-size: 12px;border: 1px solid black;border-radius: 5px;padding: 2px 5px 2px 5px;">
              <Tooltip content="待解决问题/总数">
                <span style="color: darkred"> {{ project.ViewData.IssueCount.opened }}</span>/
                <span style="color: darkblue">
                  {{ project.ViewData.IssueCount.total }}
                </span>
              </Tooltip>
            </span>
          </div>
          <div style="margin-left: 40px;">
            <Row v-for="fortesting in project.ViewData.Fortestings" :key="fortesting.id">
              <div style="height: 60px;padding: 16px 16px 10px 16px;">
                <Col span="17">
                <span>

                  <Tooltip v-if="fortesting.SelfValidateResult === -1" transfer content="开发自测结果未通过"
                    style="margin-right: 5px;">
                    <Icon type="ios-close-circle-outline" :size="16" color="#b22c46" />
                  </Tooltip>
                  <Tooltip v-if="fortesting.SelfValidateResult === 0" transfer content="开发自测结果未关联"
                    style="margin-right: 5px;">
                    <Icon type="ios-information-circle-outline" :size="16" color="orange" />
                  </Tooltip>
                  <Tooltip v-if="fortesting.SelfValidateResult > 0" transfer content="开发自测结果通过"
                    style="margin-right: 5px;">
                    <Icon type="ios-checkmark-circle-outline" :size="16" color="green" />
                  </Tooltip>
                  <Tooltip content="预计发版日期">
                    <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
                      <Icon type="ios-calendar" color="#19be6b" /> {{ fortesting.ReleaseDate }}
                    </i>
                  </Tooltip>
                </span>
                <i style="padding-right: 10px;">[{{ fortesting.VersionName }}]</i>
                <span>
                  {{ fortesting.Topic }}
                </span>
                </Col>
                <Col span="2">
                <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                  <Icon type="ios-radio-button-on" color="#19be6b" />
                  {{ fortesting.StatusName }}
                </span>
                </Col>
                <Col span="5" style="margin-top: -6px;">
                <span style="font-size: 12px;padding-right: 10px;" v-if="fortesting.CommitorName !== ''">
                  <Tooltip content="开发负责人">
                    <Avatar shape="square" style="background-color: #87d068">{{ fortesting.CommitorName }}</Avatar>
                  </Tooltip>
                </span>
                <span style="font-size: 12px;padding-right: 0px;" v-if="fortesting.TesterName !== ''">
                  <Tooltip content="测试负责人">
                    <Avatar shape="square" style="background-color: #5578aa;">{{ fortesting.TesterName }}</Avatar>
                  </Tooltip>
                </span>
                </Col>
              </div>
            </Row>
          </div>
          </Col>
        </Row>
      </Card>
    </Card>
  </div>
</template>

<script>
import store from '../../store/index.js'
import { mapGetters, mapMutations } from 'vuex'

export default {
  components: {},
  name: 'ProjectTaskStateList',
  data() {
    return {
      dateRange: '-15',
      state: '0',
      projectStateList: []
    }
  },

  computed: {
    ...mapState(['appBodyHeight']),
    summaryHeight: function () {
      return this.appBodyHeight - 400
    }
  },

  methods: {

    loadFortestingList: function () {
      this.$axios.get('/api/project/0/fortestings/dashboard?date_range=' + this.dateRange + '&status=' + this.state).then(response => {
        this.projectStateList = response.data.result
      }, response => {
      })

    },

    filterFortestings: function (value) {
      console.log(value)
      this.loadFortestingList()
    }

  },
  created: function () {
    this.loadFortestingList()
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
