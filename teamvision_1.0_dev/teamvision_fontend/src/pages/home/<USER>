<template>
  <div>
    <Row>
      <Col span="8" style="padding-right: 6px;">
      <Card dis-hover>
        <p slot="title">功能用例排行</p>
        <div style="height: 150px;" id="projectTestCaseStatistics"></div>
      </Card>
      </Col>
      <Col span="8" style="padding-right: 6px;">
      <Card dis-hover>
        <p slot="title">自动化用例排行</p>
        <div style="height: 150px;" id="projectAutoCaseStatistics"></div>
      </Card>
      </Col>
      <Col span="8" style="padding-right: 0px;">
      <Card dis-hover>
        <p slot="title">用例数量</p>
        <Row>
          <Col span="12">
          <div style="height: 150px; text-align: center;">
            <i-circle :percent="100" stroke-color="#5cb85c" :size="150">
              <div class="home-project-circle">
                <h1>{{ case_total_count.CaseCount }}</h1>
                <p>功能用例总数</p>
                <span></span>
              </div>
            </i-circle>
          </div>
          </Col>
          <Col span="12">
          <div style="height: 150px; text-align: center;">
            <i-circle :percent="100" stroke-color="#585eaa" :size="150">
              <div class="home-project-circle">
                <h1>{{ case_total_count.AutoCount }}</h1>
                <p>自动化用例总数</p>
                <span></span>
              </div>
            </i-circle>
          </div>
          </Col>
        </Row>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
import HighCharts from 'highcharts'

export default {
  components: {},
  name: 'TestCaseChart',
  data() {
    return {
      case_total_count: {}
    }
  },

  methods: {
    loadProjectAutoCaseStatistics: function () {
      this.$axios.get('/api/home/<USER>/project_autocase_column').then(response => {
        let option = this.createColumnChart(response.data.result)
        HighCharts.chart('projectAutoCaseStatistics', option)
      }, response => {
      })

    },

    loadProjectTestCaseStatistics: function () {
      this.$axios.get('/api/home/<USER>/project_testcase_column').then(response => {
        let option = this.createColumnChart(response.data.result)
        HighCharts.chart('projectTestCaseStatistics', option)
      }, response => {
      })

    },

    loadCaseTotalCount: function () {
      this.$axios.get('/api/home/<USER>/case_total_count').then(response => {
        this.case_total_count = response.data.result
      }, response => {
      })
    },

    //生成饼图
    createPieChart: function (data) {
      let option = {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false
        },
        title: {
          text: data['chart_title']
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.y}</b>'
        },
        legend: {
          layout: data['legend_layout'],
          align: data['legend_align']
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: false
            },
            showInLegend: data['show_legend']
          }
        },
        series: [{
          type: data['chart_type'],
          name: data['chart_title'],
          data: data['series_data']
        }]
      }
      return option
    },
    //生成简单柱状图
    createColumnChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_type']
        },
        legend: {
          layout: data['legend_layout'],
          align: data['legend_align']
        },
        xAxis: {
          categories: data['xaxis'],
          crosshair: true
        },
        yAxis: {
          min: 0,
          title: {
            text: data['series_name']
          }
        },
        tooltip: {
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            colorByPoint: true,
            showInLegend: data['show_legend']
          }
        },
        series: data['series_data']
      }
      return option
    },

  },

  created: function () {
    this.loadProjectAutoCaseStatistics()
    this.loadProjectTestCaseStatistics()
    this.loadCaseTotalCount()
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.home-project-circle {
  & h1 {
    color: #3f414d;
    font-size: 28px;
    font-weight: normal;
  }

  & p {
    color: #657180;
    font-size: 14px;
    margin: 10px 0 15px;
  }

  & span {
    display: block;
    padding-top: 15px;
    color: #657180;
    font-size: 14px;

    &:before {
      content: '';
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e0e3e6;
      position: relative;
      top: -15px;
    }

    ;
  }

  & span i {
    font-style: normal;
    color: #3f414d;
  }
}
</style>
