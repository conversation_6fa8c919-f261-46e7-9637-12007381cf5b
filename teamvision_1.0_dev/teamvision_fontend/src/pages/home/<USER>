<template>
  <div style="color:#5578aa;padding: 2px;">
    <test-case-chart></test-case-chart>
    <Card :padding="0" style="margin-top:4px;" dis-hover>
      <span slot="title" style="height:24px;">
        <span style="margin-right: 10px;">
          <Select v-model="selectedProject" style="width:200px" size="small" filterable clearable placeholder="选择项目"
            @on-change="filterMindFile">
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </span>
        <RadioGroup v-model="fileType" type="button" size="small" @on-change="filterMindFile">
          <Radio label="0">全部</Radio>
          <Radio label="1">用例</Radio>
          <Radio label="2">BVT记录</Radio>
          <Radio label="3">执行记录</Radio>
        </RadioGroup>
      </span>
      <Card dis-hover :padding="0" :bordered="false" :style="'height:' + summaryHeight + 'px' + '; overflow-y:scroll;'">
        <div v-for="project in projectStateList" :key="project.id" style="border-bottom: 1px solid #f0f0f0">
          <div
            style="height: 54px;padding: 16px;border-bottom: 1px solid #e8eaec;border-top: 1px solid #e8eaec;border-radius: 5px 0px 0px 5px;">
            <span style="font-size: 16px;font-weight: bold;margin-right: 50px;">
              <router-link :to="'/project/' + project.id + '/issue/all'">
                <span style="padding-right:5px;">
                  <Avatar shape="square" size="small" :src="project.PBAvatar"></Avatar>
                </span>
                <span style="color:#5578aa;text-decoration: underline;">
                  {{ project.PBTitle }}
                </span>
              </router-link>
            </span>
            <span style="font-size: 12px;margin-right: 20px;">相关文件共计: {{ project.ViewData.MindFiles.length }}个</span>
          </div>
          <div style="margin-left: 40px;">
            <Row v-for="mindFile in project.ViewData.MindFiles" :key="mindFile.id" style="height: 40px;">
              <Col span="14">
              <span>
                <tooltip v-if="mindFile.FileType === 1" content="测试用例">
                  <Avatar shape="square" size="small" style="color: #fff;background-color: #5578aa;">用例</Avatar>
                </tooltip>
                <tooltip v-if="mindFile.FileType === 2" content="BVT测试记录">
                  <Avatar shape="square" size="small" style="color: #fff;background-color: #22aaaa">BVT</Avatar>
                </tooltip>
                <tooltip v-if="mindFile.FileType === 3" content="用例执行记录">
                  <Avatar shape="square" size="small" style="color: #fff;background-color: #22aaaa">测试</Avatar>
                </tooltip>
              </span>
              <span style="width: 200px;">
                <Tooltip content="更新日期">
                  <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
                    <Icon type="ios-calendar" color="#19be6b" /> {{ mindFile.view_data.UpdateTime }}
                  </i>
                </Tooltip>
                <Tooltip content="版本">
                  <i style="padding-right: 10px;">[{{ mindFile.view_data.VersionName }}]</i>
                </Tooltip>
              </span>
              <router-link :to="'/project/' + project.id + '/testcase/' + mindFile.id">
                <span style="color:#5578aa;text-decoration: underline;">
                  {{ mindFile.FileName }}
                </span>
              </router-link>
              </Col>
              <Col span="8">
              <span v-if="mindFile.FileType === 1" style=" width: 150px;display: inline-block">
                <Tooltip content="自动化用例数/用例数">
                  <span style="border: 1px solid #eef2f6;padding:3px;border-radius: 3px; font-size: 12px;">
                    <span style="color:#19be6b;">{{ mindFile.view_data.AutoPointCounts }}</span>/
                    <span>{{ mindFile.view_data.PointCounts }}</span>
                  </span>
                </Tooltip>
              </span>
              <span v-if="mindFile.FileType === 2 || mindFile.FileType === 3"
                style="color:#8c96a0;width: 150px;display: inline-block">
                <span style="border: 1px solid #2f271d;padding:3px;border-radius: 3px; font-size: 12px;">
                  <tooltip content="成功/失败/待确认">
                    <span style="color:#19be6b;">{{ mindFile.view_data.FinishedPointCounts[0] }}/</span>
                    <span style="color:brown;">{{ mindFile.view_data.FinishedPointCounts[1] }}/</span>
                    <span style="color:orange;">{{ mindFile.view_data.FinishedPointCounts[2] }}</span>
                  </tooltip>
                </span>
              </span>
              </Col>
              <Col span="2">
              <Tooltip content="创建人" transfer>
                <Avatar shape="square" size="small" style="background-color:#629333">
                  {{ mindFile.view_data.Owner }}
                </Avatar>
              </Tooltip>
              </Col>
            </Row>
          </div>
        </div>
        <Spin v-if="loading" size="large" fix></Spin>
      </Card>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import TestCaseChart from './TestCaseChart.vue'

export default {
  components: {
    TestCaseChart,
  },
  name: 'HomeTestCase',
  data() {
    return {
      fileType: '1',
      projectStateList: [],
      projectList: [],
      selectedProject: 0,
      loading: false
    }
  },

  computed: {
    ...mapState(['appBodyHeight', 'appBodyMainHeight']),
    summaryHeight: function () {
      return this.appBodyHeight - 300
    }
  },

  methods: {

    loadMindFileList: function () {
      if (!this.selectedProject) {
        this.selectedProject = 0
      }
      this.loading = true
      this.$axios.get('/api/project/' + this.selectedProject + '/mindfiles/dashboard?FileType=' + this.fileType).then(response => {
        this.loading = false
        this.projectStateList = response.data.result

      }, response => {
        this.loading = false
      })

    },

    loadProjectList: function () {
      this.$axios.get('/api/project/list').then(response => {
        this.projectList = response.data.result
      }, response => {
      })

    },

    filterMindFile: function (value) {
      this.loadMindFileList()
    }
  },
  created: function () {
    this.loadMindFileList()
    this.loadProjectList()
  },
  mounted: function () {
  },
  components: {
    TestCaseChart
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
