<template>
  <div style="padding: 2px">
    <Card style="border-color: #eef2f6;color: #5578aa" dis-hover>
      <p slot="title">测试计划</p>
      <span slot="extra">
        <span>
          <Select v-model="selectProject" style="width:300px" placeholder="按项目筛选" multiple filterable
            @on-open-change="handlerOpenChange" @on-change="handlerOpenChange">
            <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}</Option>
          </Select>
        </span>
        <span style="margin-left: 20px;">
          <RadioGroup v-model="state" type="button" @on-change="filterFortestings">
            <Radio label="0">全部</Radio>
            <Radio label="1">新建</Radio>
            <Radio label="2">测试中</Radio>
            <Radio label="3">已完成</Radio>
            <Radio label="4">已归档</Radio>
          </RadioGroup>
        </span>
        <span style="margin-left: 20px; ">
          <RadioGroup v-model="dateRange" type="button" @on-change="filterFortestings">
            <Radio label="7">7天</Radio>
            <Radio label="15">15天</Radio>
            <Radio label="30">30天</Radio>
            <Radio label="90">90天</Radio>
          </RadioGroup>
        </span>
      </span>
      <Card dis-hover :padding="0" :bordered="false" :style="'height:' + summaryHeight + 'px' + '; overflow-y:scroll;'">
        <div v-for="project in projectStateList" :key="project.id">
          <div style="height: 54px;padding: 16px;border-bottom: 1px solid #e8eaec;border-top: 1px solid #e8eaec;">
            <span style="font-size: 16px;font-weight: bold;margin-right: 50px;">
              <span style="padding-right:5px;">
                <Avatar shape="square" :src="project.PBAvatar"></Avatar>
              </span>
              <span> {{ project.PBTitle }} </span>
            </span>
          </div>
          <Row v-for="planInfo in project.plan_info" :key="planInfo.id" style="margin-left: 40px;height: 40px;">
            <Col span="6">
            <router-link :to="'/project/' + project.id + '/test/test-plan/' + planInfo.id + '/detail'">
              <Tooltip content="测试计划名称">
                <Icon type="ios-radio-button-on" color="#19be6b" /> {{ planInfo.Title }}
              </Tooltip>
            </router-link>
            </Col>
            <Col span="3">
            <Tooltip content="开始时间">
              <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
                <Icon type="ios-calendar" color="#19be6b" /> {{ planInfo.CreationTime }}
              </i>
            </Tooltip>
            </Col>
            <Col span="3">
            <Tooltip content="版本">
              <i style="padding-right: 10px;">{{ planInfo.Version }}</i>
            </Tooltip>
            </Col>
            <Col span="2">
            <Icon type="ios-radio-button-on" color="#19be6b" /> {{ planInfo.Status }}
            </Col>
            <Col span="5">
            <Tooltip content="关联提测">
              <p v-for="item in planInfo.Fortesting">
                <Icon type="ios-radio-button-on" color="#19be6b" /> {{ item }}
              </p>
            </Tooltip>
            </Col>
            <Col span="1">
            <Tooltip content="需求数">
              <Icon type="ios-radio-button-on" color="#19be6b" /> {{ planInfo.requireNum }}
            </Tooltip>
            </Col>
            <Col span="1">
            <Tooltip content="Case数">
              <Icon type="ios-radio-button-on" color="#19be6b" /> {{ planInfo.CaseCount }}
            </Tooltip>
            </Col>
            <Col span="2">
            <span style="font-size: 16px;padding-right: 10px;">
              <Tooltip content="负责人">
                <tag style="background-color:#22aaaa">{{ planInfo.Owner }}</tag>
              </Tooltip>
            </span>
            </Col>
          </Row>
        </div>
        <Spin v-if="loading" size="large" fix></Spin>
      </Card>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { appendQueryParams } from "../../utils/utils.js"

export default {
  components: {},
  name: 'ProjectFortestingStateList',
  props: ['projectID'],
  data() {
    return {
      dateRange: 7,
      state: 0,
      projectStateList: [],
      loading: false,
      projectID: 0,
      selectProject: [],
    }
  },

  computed: {
    ...mapState(['appBodyHeight', 'appBodyMainHeight']),
    ...mapState('project', ['projectList']),
    summaryHeight: function () {
      return this.appBodyHeight - 80
    }
  },

  methods: {
    loadFortestingList: function () {
      let reqUri = '/api/home/<USER>/' + this.dateRange

      let reqArgs = {}
      if (this.selectProject.length > 0) {
        reqArgs['project'] = this.selectProject
      }

      if (this.state != 0) {
        reqArgs['state'] = this.state
      }
      var reqUrl = appendQueryParams(reqUri, reqArgs)
      this.loading = true
      this.$axios.get(reqUrl).then(response => {
        this.projectStateList = response.data.result
        this.loading = false
      }, response => {
        this.loading = false
      })
    },

    handlerOpenChange(is_open) {
      if ((typeof is_open) == "undefined") {
        this.loadFortestingList()
      } else if (is_open == false) {
        this.loadFortestingList()
      }
    },

    filterFortestings: function (value) {
      this.loadFortestingList()
    },

    showFortestingDetail: function (fortestingID) {
      this.$emit('view-fortesting', fortestingID)
    }

  },
  created: function () {
    this.loadFortestingList()
  },
  watch: {
    projectID: function (value) {
      this.loadFortestingList()
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
