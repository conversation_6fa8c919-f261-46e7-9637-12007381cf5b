<template>
  <div class="app-body-header-bar-default">
    <div class="app-body-header-leftbar-default pull-left">
      <ul class="app-body-head-menu">
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>">
            <Icon type="ios-albums" :size="16" />工作台
          </a>
        </router-link>
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>">
            <Icon type="ios-stats" :size="16" />看板
          </a>
        </router-link>
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>">
            <Icon type="ios-book" :size="16" />计划
          </a>
        </router-link>
        <router-link :to="{ name: 'homeRequirement', params: { projectID: 0, layout: reqLayout } }" tag="li"
          active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
          <a href="">
            <Icon type="ios-flask"></Icon> 需求
          </a>
        </router-link>
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="#"><i class="fa fa-fw  fa-flag"></i>提测</a>
        </router-link>
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>"><i class="fa fa-fw  fa-bus"></i>任务</a>
        </router-link>
        <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>">
            <Icon :size="16" type="ios-bug" /> 问题
          </a>
        </router-link>
        <!--<router-link v-if="homeDynamicMenu.show" :to="homeDynamicMenu.path" tag="li" class="app-body-head-menu-item-active app-body-head-menu-item">-->
        <!--<a href="/ci"><Icon :size="16" type="ios-bug" /> {{ homeDynamicMenu.text }}</a>-->
        <!--</router-link>-->

        <!-- <router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active"
          class="app-body-head-menu-item">
          <a href="/home/<USER>">
            <Icon type="ios-key" :size="16" />用例
          </a>
        </router-link> -->

        <!--<router-link to="/home/<USER>" tag="li" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">-->
        <!--<a href="/home/<USER>"><Icon type="ios-stats" :size="16" /> 统计</a>-->
        <!--</router-link>-->

      </ul>
    </div>
    <div class="app-body-header-rightbar-default pull-right">
      <span @click="newTask" v-if="headMenu.newObject">
        <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
      </span>
      <span v-if="routerName === 'homeIssue'" style="padding-left: 10px">
        <Divider type="vertical" />
        <Tooltip content="导出issue" transfer>
          <span class="cursor-hand" @click="exportIssueExcel">
            <Icon type="md-log-out" :size="20" color="#5578aa" />
          </span>
        </Tooltip>
        <!-- <Tooltip content="导出issue" transfer>
          <a style="color:inherit"
            :href="'/api/project/issue/export?Title__icontains=' + searchKeyword + '&' + issueFilters">
            <Icon type="md-log-out" :size="20" />
          </a>
        </Tooltip> -->
      </span>
      <span v-if="headMenu.filterMenu" style="padding-left: 10px;" class="cursor-hand">
        <Divider type="vertical" />
        <Tooltip content="筛选" transfer>
          <span @click="showRightPanel">
            <Icon type="ios-funnel-outline" :size="24" />
          </span>
        </Tooltip>
      </span>
      <span v-if="headMenu.itemViewMode" style="padding-left: 10px;" class="cursor-hand">
        <Divider type="vertical" />
        <Dropdown transfer @on-click="changeViewMode">
          <a href="javascript:void(0)" style="color: inherit;">
            <span>
              <Icon type="md-apps" :size="24" />
            </span>
            <Icon type="ios-arrow-down"></Icon>
          </a>
          <DropdownMenu slot="list">
            <DropdownItem name="board">
              <Icon type="md-podium" /> 看板
            </DropdownItem>
            <DropdownItem name="list">
              <Icon type="ios-paw" />列表
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </span>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from "vuex";

export default {
  name: "MyHead",
  data() {
    return {
      reqLayout: {
        projectWidth: 0,
        otherLayout: {
          width: 24,
          featureLayout: {
            width: 3,
            requirementWidth: 21,
          },
        },
      },
    };
  },
  computed: {
    ...mapGetters("issue", ["issueFilters", "searchKeyword"]),
    ...mapGetters("homeglobal", ["homeDynamicMenu", "headMenu"]),
    ...mapGetters(["itemViewMode"]),

    routerName: function () {
      return this.$route.name;
    },
  },

  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被 confirm 前调用
    // 不！能！获取组件实例 `this`
    // 因为当守卫执行前，组件实例还没被创建
    //console.log(to);
    next();
  },
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但是该组件被复用时调用
    // 举例来说，对于一个带有动态参数的路径 /foo/:id，在 /foo/1 和 /foo/2 之间跳转的时候，
    // 由于会渲染同样的 Foo 组件，因此组件实例会被复用。而这个钩子就会在这个情况下被调用。
    // 可以访问组件实例 `this`
    //console.log(to);
    this.setItemViewMode("board");
    next();
  },
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    //console.log(to);
    this.setItemViewMode("board");
    next();
  },
  methods: {
    ...mapMutations("projectglobal", [
      "setCreateDialogShow",
      "setRightPanelShow",
    ]),
    ...mapMutations(["setItemViewMode"]),
    newTask() {
      this.setCreateDialogShow(true);
    },

    showRightPanel() {
      this.setRightPanelShow(true);
    },

    changeViewMode: function (value) {
      this.setItemViewMode(value);
    },

    exportIssueExcel: function () {
      this.$Message.info({
        content: "导出中。。。",
        duration: 6
      })
      let realUrl = "/api/project/issue/export?";
      if (this.searchKeyword !== "") {
        realUrl = realUrl + "search=" + this.searchKeyword + "&";
      }

      if (this.issueFilters !== "") {
        realUrl = realUrl + this.issueFilters;
      }
      this.$axios({ url: realUrl, method: "get", responseType: "arraybuffer", }).then((response) => {
        let url = window.URL.createObjectURL(
          new Blob([response.data], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        let disposition = response.headers["content-disposition"];
        let fileName = decodeURIComponent(disposition.split("filename=")[1]);
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        link.remove();
      },
        (response) => { }
      );
    },
  },
  created: function () { },
  mounted: function () {
    this.setRightPanelShow(false);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import "../../layout/appBody";
@import "../../assets/teamvision/global/less/global";
</style>
