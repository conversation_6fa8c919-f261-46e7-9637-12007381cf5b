<template>
  <div>
    <Card dis-hover>
      <p slot="title">我处理的</p>
      <Row :gutter="2" type="flex" justify="space-between" style="color: #5578aa;">
        <Col span="6" style="padding: 10px 5px 10px 5px;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">问题:</div>
          <div class="summary-card">
            <div class="summary-card__left">
              <div class="summary-card__title">创建的</div>
              <div class="summary-card__number">{{ toDoSummary.create_issue_count }}</div>
            </div>
            <el-divider direction="vertical" class="summary-card_divider"> </el-divider>
            <div class="summary-card__right">
              <div class="summary-card__title">待处理</div>
              <div class="summary-card__number">{{ toDoSummary.todo_issue_count }}</div>
            </div>
          </div>
        </Card>
        </Col>
        <Col span="6" style="padding: 10px 5px 10px 5px;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">提测:</div>
          <div class="summary-card">
            <div class="summary-card__left">
              <div class="summary-card__title">总数</div>
              <div class="summary-card__number">{{ toDoSummary.all_fortesting_count }}</div>
            </div>
            <el-divider direction="vertical" class="summary-card_divider"> </el-divider>
            <div class="summary-card__right">
              <div class="summary-card__title">待处理</div>
              <div class="summary-card__number">{{ toDoSummary.todo_fortesting_count }}</div>
            </div>
          </div>
        </Card>
        </Col>
        <Col span="6" style="padding: 10px 5px 10px 5px;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">测试计划:</div>
          <div class="summary-card">
            <div class="summary-card__left">
              <div class="summary-card__title">计划数</div>
              <div class="summary-card__number">{{ toDoSummary.my_testplan_count }}</div>
            </div>
            <el-divider direction="vertical" class="summary-card_divider"> </el-divider>
            <div class="summary-card__right">
              <div class="summary-card__title">用例数</div>
              <div class="summary-card__number">{{ toDoSummary.testplan_case_count }}</div>
            </div>
          </div>
        </Card>
        </Col>
        <Col span="6" style="padding: 10px 5px 10px 5px;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">任务:</div>
          <div class="summary-card">
            <div class="summary-card__left">
              <div class="summary-card__title">总数</div>
              <div class="summary-card__number">{{ toDoSummary.all_task_count }}</div>
            </div>
            <el-divider direction="vertical" class="summary-card_divider"> </el-divider>
            <div class="summary-card__right">
              <div class="summary-card__title">待处理</div>
              <div class="summary-card__number">{{ toDoSummary.todo_task_count }}</div>
            </div>
          </div>
        </Card>
        </Col>
      </Row>
    </Card>
    <Card style="border-color: #eef2f6;color: #5578aa" dis-hover>
      <p slot="title">待办事项({{ this.toDoItems.length }}项)</p>
      <Row style="height: 60px; border-bottom: 1px solid #e8eaec;">
        <Col span="12"> 事项 </Col>
        <Col span="4">状态</Col>
        <Col span="4">项目</Col>
        <Col span="4">创建时间</Col>
      </Row>
      <Row v-for="item in toDoItems" :key="item.id" style="height: 50px;border-bottom: 1px solid #e8eaec;">
        <Col span="12">
        <span v-if="item.type === 'fortestings'">
          <span class="item-label">提测</span>
          <router-link :to="'/project/' + item.projectID + '/fortesting/' + item.id"
            style="text-decoration:underline; padding-left: 6px;">{{ item.Topic }}</router-link>
        </span>
        <span v-else-if="item.type === 'issue'">
          <span class="item-label" style="background-color: brown">问题</span>
          <router-link :to="'/project/' + item.Project + '/issue/' + item.id"
            style="text-decoration:underline; padding-left: 6px;">{{ item.Title }}</router-link>
        </span>
        <span v-else-if="item.type === 'task'">
          <span class="item-label" style="background-color: dimgray">任务</span>
          <router-link :to="'/project/' + item.projectID + '/task/' + item.id"
            style="text-decoration:underline; padding-left: 6px;">{{ item.Title }}</router-link>
        </span>
        <span v-else-if="item.type === 'testplan'">
          <span class="item-label" style="background-color: #00bbff">计划</span>
          <router-link :to="'/project/' + item.Project + '/test/test-plan/' + item.id + '/detail'"
            style="text-decoration:underline; padding-left: 6px;">{{ item.title }}</router-link>
        </span>
        </Col>
        <Col span="4">
        <span v-if="item.type === 'fortestings'"> {{ item.StatusName }}</span>
        <span v-else-if="item.type === 'issue'"> {{ item.view_data.status_name }}</span>
        <span v-else-if="item.type === 'task'"> {{ item.ViewData.Status }}</span>
        <span v-else-if="item.type === 'testplan'"> {{ item.status }}</span>
        </Col>
        <Col span="4">
        <Avatar shape="square" v-if="item.type === 'fortestings'" :src="activeProject[item.projectID]"></Avatar>
        <Avatar shape="square" v-else-if="item.type === 'issue'" :src="activeProject[item.Project]"></Avatar>
        <Avatar shape="square" v-else-if="item.type === 'task'" :src="activeProject[item.projectID]"></Avatar>
        <Avatar shape="square" v-else-if="item.type === 'testplan'" :src="activeProject[item.Project]"></Avatar>
        </Col>
        <Col span="4">
        <span v-if="item.type === 'fortestings'"> {{ item.CreationTime }}</span>
        <span v-else-if="item.type === 'issue'"> {{ item.create_date }}</span>
        <span v-else-if="item.type === 'task'"> {{ item.CreationTime }}</span>
        <span v-else-if="item.type === 'testplan'"> {{ item.CreationTime }}</span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'

export default {
  components: {},
  name: 'MyownItem',
  data() {
    return {
      toDoSummary: {},
      activeProject: {},
      toDoItems: [],
    }
  },

  computed: {
    ...mapState('usercenter', ['userInfo']),
  },

  methods: {
    loadToDoSummary: function () {
      this.$axios.get('/api/home/<USER>/summary').then(response => {
        this.toDoSummary = response.data.result
      }, response => { })
    },

    loadMyProjectList: function () {
      this.$axios.get('/api/project/list?latest=1&my=1').then(response => {
        for (let project in response.data.result) {
          this.activeProject[response.data.result[project].id] = response.data.result[project].PBAvatar
        }
      }, response => { })
    },

    loadMyTestplanList: function (statusList) {
      // for (let status in statusList){
      // this.$axios.get('/api/project/1/testplan_list_info/-15_'+ statusList[status] +'/?Owners=' + this.userInfo.id).then(response => {
      this.$axios.get('/api/project/1/testplan_list_info?status=1,2').then(response => {
        //console.log(response.data.result)
        // let testplanList = []
        // for (let i in response.data.result){

        //   for (let test_plan in response.data.result[i].plan_info){
        //     response.data.result[i].plan_info[test_plan].projectID = response.data.result[i].projectID
        //     response.data.result[i].plan_info[test_plan].PBAvatar = response.data.result[i].PBAvatar
        //     response.data.result[i].plan_info[test_plan].type = "testplan"
        //     testplanList.push(response.data.result[i].plan_info[test_plan])
        //   }
        // }
        // console.log(testplanList)
        // this.toDoItems = this.toDoItems.concat(testplanList)
        this.addType(response.data.result, "testplan")
        //console.log(response.data.result)
        this.toDoItems = this.toDoItems.concat(response.data.result)
      }, response => { })
      // }
    },

    loadTodoFortestingsList: function (statusList) {
      for (let status in statusList) {
        this.$axios.get('/api/project/0/version/0/fortestings?Status=' + statusList[status] + '&Testers__in=0,' + this.userInfo.id).then(response => {
          this.addType(response.data.result.results, "fortestings")
          this.toDoItems = this.toDoItems.concat(response.data.result.results)
        }, response => { })
      }
    },

    loadTodoIssueList: function () {
      this.$axios.get('/api/project/0/version/0/issues?Status__in=2,4,5,0&Processor__in=' + this.userInfo.id).then(response => {
        this.addType(response.data.result.results, "issue")
        this.toDoItems = this.toDoItems.concat(response.data.result.results)
      }, response => { })
    },

    loadTaskList: function () {
      this.$axios.get('/api/project/0/version/0/project_tasks?Status__in=0,1&Owner__in=' + this.userInfo.id).then(response => {
        this.addType(response.data.result.results, "task")
        this.toDoItems = this.toDoItems.concat(response.data.result.results)
      }, response => { })
    },

    addType: function (value, type) {
      for (let item in value) {
        value[item].type = type
      }
    },


  },

  created: function () {
    this.loadToDoSummary()
    this.loadMyProjectList()
    // loadMyTestplanList :"1 新建，2 测试中，3 已完成，4 已归档, 5 暂停"
    // this.loadMyTestplanList([1,2])
    this.loadMyTestplanList()
    // loadTodoFortestingsList : "1:待提测，2：已提测，3：测试中，4：测试完成，5：已上线"
    this.loadTodoFortestingsList([1, 2, 3])
    this.loadTodoIssueList()
    this.loadTaskList()
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.summary-card {
  position: absolute;
  height: 100%;
  width: 100%;
  bottom: -40px;
  text-align: center;

  .summary-card__left {
    position: relative;
    float: left;
    width: 35%;
    text-align: center;
  }

  .summary-card__right {
    position: relative;
    float: right;
    width: 55%;
    text-align: center;
    //bottom: 50px;
  }

  .summary-card__title {
    font-size: 14px;
  }

  .summary-card__number {
    font-size: 18px;
    font-weight: 200;
  }

  .summary-card_divider {
    position: relative;
    height: 50px;
    text-align: center;
    right: 25%;
    bottom: 0px;
  }
}

.summary-number-title {
  text-align: left;
  margin-left: -10px;
  margin-top: -18px;
  font-size: 16px;
}

.item-label {
  border: 1px;
  padding: 4px;
  border-radius: 3px;
  font-size: 14px;
  background-color: #3ba272;
  color: #ffffff
}
</style>
