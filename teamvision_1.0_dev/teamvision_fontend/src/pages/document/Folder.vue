<template>
  <div style="margin-left: 2px;">
    <div style="padding: 16px;display: grid;align-items: center;justify-items: center;">
      <Input prefix='ios-document' v-model="searchKeyword" style="width: 600px;" search :border="false"
        @on-search="searchFile" size="large" placeholder="输入文件标题全局搜索" />
    </div>
    <Card :bordered="false" dis-hover>
      <div slot="title">
        <span style="display: inline-block;padding-right: 10px;">
          <Icon color="#2d8cf0" size="24" type="ios-folder" />
        </span>
        <span v-if="!isSearchDocument">{{ folderInfo.name }}({{ documentCount }})</span>
        <span v-else>搜索结果</span>
      </div>
      <div slot="extra">
        <div v-if="!isSearchDocument">
          <ButtonGroup>
            <Button @click="shareFolder">分享</Button>
          </ButtonGroup>
          <Button :loading="creatingDocument" @click="newDocument" type="primary">新建</Button>
        </div>
      </div>
      <div>
        <Breadcrumb>
          <BreadcrumbItem :replace="true" v-for="(item, index) in breadcrumbs" :to="item.to"
            :key="'breadcrumb' + index">{{ item.text }}
          </BreadcrumbItem>
        </Breadcrumb>
        <div v-if="!isSearchDocument">
          <p style="color: #808695">负责人</p>
          <p style="font-size: 14px;font-weight: 700;">
            <!-- <span v-for="user in folderInfo.view_data.create_username">{{ user + ' ' }} </span> -->
          </p>
        </div>
      </div>
      <DocumentList ref="docList" :searchKeyword="searchKeyword" :folderID="folderInfo.id" :showUpdateFlile="false"
        @documentsCount="documentsCount">
      </DocumentList>
    </Card>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import DocumentList from "./DocumentList.vue";
import { createDocumentApi, getFolderDetailApi } from "../../api/document.js";
import { copyLink } from '../../utils/utils.js';

export default {
  name: 'Folder',
  components: {
    DocumentList
  },
  props: {
    folderID: {
      type: [Number, String],
      default: 0,
      required: false
    },
  },
  data() {
    return {
      folderInfo: {
        name: "全部文档",
        id: 0
      },
      folderOwnerList: [],
      breadcrumbs: [],
      currentUser: {},
      creatingDocument: false,
      documentCount: 0,
      searchKeyword: '',
    };
  },
  computed: {
    ...mapState(['userInfo']),
    isSearchDocument() {
      return this.searchKeyword.length > 0;
    },
    tableHeight() {
      let browserHeight = window.innerHeight;
      return browserHeight - 230;
    }
  },
  methods: {
    forceRefresh() {
      location.reload();
    },
    documentsCount(count) {
      this.documentCount = count;
    },
    searchFile() {
      this.searchKeyword.trim();
      console.log(this.searchKeyword);
      this.$refs.docList.listFolderDocumentList();
    },
    getFolderInfo() {
      if (parseInt(this.folderID) > 0) {
        getFolderDetailApi(parseInt(this.folderID)).then(response => {
          this.folderInfo = response.data.result;
        });
      }
    },
    newDocument() {
      this.creatingDocument = true;
      let data = {
        "dir_id": parseInt(this.folderID),
        "title": "新文档标题",
        "detail": ""
      };

      createDocumentApi(data).then(response => {
        if (response.data.code === 201) {
          this.creatingDocument = true;
          let tempDocument = response.data.result;
          this.$router.push({ name: 'DocumentDetail', params: { docID: parseInt(tempDocument.id) } });
        } else {
          this.$Message.error({
            content: response.data.msg
          });
        }
      });
    },

    shareFolder() {
      copyLink(window.location.href);
    },
  },
  created() {
    this.getFolderInfo();
  },

  mounted: function () {
  },

  watch: {
    folderID(newValue, oldValue) {
      this.getFolderInfo();
    },
    '$route'(to, from) {
      this.folderInfo = {
        name: "全部文档",
        id: 0
      };
      console.log(to);
      this.getFolderInfo();
    }
  }
};
</script>

<style scoped lang="less">
.file-header-menu-button {
  height: 32px;
  width: 32px;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
  padding: 4px;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.file-header-menu-input {
  height: 32px;
  width: max-content;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.header-file-title .ivu-input {
  background-color: #f5f7f9;
  transition: box-shadow 0.3s ease;
}

.header-file-title .ivu-input:hover {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-right-group {
  margin-left: auto;
  padding-right: 16px;
}
</style>