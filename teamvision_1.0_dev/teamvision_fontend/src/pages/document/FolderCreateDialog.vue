<template>
  <Modal :width="400" :value="showDialog" :title="dialogName" @on-ok="save" @on-cancel="cancel">
    <Form :model="folderData" :label-width="80">
      <FormItem label="名称">
        <Input v-model="folderData.name" placeholder="名称"></Input>
      </FormItem>
      <FormItem label="描述">
        <Input v-model="folderData.description" placeholder="描述"></Input>
      </FormItem>
      <!-- <FormItem label="成员">
        <Select placeholder="添加分类维护人,可多选" multiple filterable allow-create @on-change="selectOwner"
          @on-create="createNewOwner">
        </Select>
      </FormItem> 
      <FormItem>
        <Table ref="selection" :columns="folderTableColumns" :data="folderOwners" height="350">
          <template slot="username" slot-scope="{ row, index }">
            <Tag>{{ row.username }}</Tag>
          </template>
<template slot="op" slot-scope="{ row, index }">
            <span @click="removeOwner(row, index)">
              <Icon size="18" type="ios-trash"></Icon>
            </span>
          </template>
</Table>
</FormItem>
-->
    </Form>
  </Modal>
</template>

<script>
import { mapState } from 'vuex';
import { createFolderApi, getFolderDetailApi, updateFolderApi } from '../../api/document';

export default {
  name: 'DocumentCreateDialog',
  components: {
  },
  props: {
    folderDetail: {
      type: Object,
      required: true,
      default: {
        id: null,
        name: '',
      }
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      newDirTitle: '',
      folderData: {
        id: null,
        name: '',
        description: "",
        parentId: null,
      },
      folderOwners: [],
      createUserList: [],
      folderTableColumns: [
        {
          title: '姓名',
          key: 'name'
        },
        {
          title: '用户名',
          key: 'username',
          slot: 'username',
        },
        {
          title: '操作',
          slot: 'op',
          width: 100
        },
      ]
    };
  },

  computed: {
    dialogName() {
      let title = '新建文档目录';
      if (this.folderDetail.id > 0) {
        title = "编辑文档目录";
      }
      return title;
    }
  },

  methods: {
    // createNewOwner(value) {
    //   let temp = { name: value, username: value };
    //   let data = { "usernameList": [value] };
    //   getHrUserInfo(data).then(response => {
    //     let index = this.folderOwners.findIndex(obj => obj.username === temp.username);
    //     if (response.data.code === 200) {
    //       if (index === -1) {
    //         this.createUserList.push(temp);
    //         this.folderOwners.push(temp);

    //       } else {
    //         this.folderOwners[index].name = temp.name;

    //       }
    //     }
    //     if (response.data.code === 501) {
    //       this.folderOwners.splice(index, 1);

    //       Message.error({
    //         content: response.data.msg
    //       });
    //     }
    //   });
    // },
    // selectOwner(value) {
    //   let selectUsers = [...value];
    //   for (let i = 0; i < selectUsers.length; i++) {
    //     let temp = { username: '', name: '' };
    //     temp.username = selectUsers[i].value;
    //     temp.name = selectUsers[i].label;
    //     let index = this.folderOwners.findIndex(obj => obj.username === temp.username);
    //     if (index === -1) {
    //       this.folderOwners.push(temp);
    //     }
    //   }
    // },
    // removeOwner(data, index) {
    //   this.$Modal.confirm({
    //     title: '删除确认',
    //     content: '您即将删除流程分类负责人[' + data.name + ']',
    //     onOk: () => {
    //       this.folderOwners.splice(index, 1);
    //       Message.success({
    //         content: '分类删除成功'
    //       });
    //     },
    //     onCancel: () => { }
    //   });
    // },
    save() {
      if (this.folderData.id == this.folderDetail.id) {
        updateFolderApi(this.folderDetail.id, this.folderData).then(response => {
          this.$Message.success({
            content: '修改成功'
          })
          this.$emit('closeDialog');
        }, response => {
        })
      }
    },
    cancel() {
      this.$emit('closeDialog');
    },

    initFolderData() {
      if (this.folderDetail.id > 0) {
        this.folderData.id = this.folderDetail.id;
        this.folderData.name = this.folderDetail.name;
        this.folderData.description = this.folderDetail.description;
      }
    }
  },
  mounted() {
  },

  watch: {
    folderDetail: {
      handler(val) {
        this.initFolderData();
      },
      deep: true,
    }
  },
};
</script>

<style scoped>
.knowledge-dir-header {
  background-color: inherit;
  border-bottom: 1px solid #f4f4f4;
  padding: 5px;
  height: 90px;
  position: relative;
}

.knowledge-dir-header-op-bar {
  position: absolute;
  bottom: 0;
  height: 48px;
  color: rgba(34, 47, 62, .5);
  font-weight: bold;
  width: 100%;
}

.knowledge-dir-header-op-addicon {
  cursor: pointer;
}
</style>