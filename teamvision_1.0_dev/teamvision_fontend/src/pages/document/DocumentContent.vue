<template>
  <div>
    <Card :dis-hover="true" :bordered="false" style="height: 48px;background-color:#f5f7f9" :padding="0">
      <div style="display: flex;flex-direction: row;padding: 8px 8px 8px 16px;">
        <div>
          <span class="file-header-menu-button">
            <RouterLink :to="'/wiki/folder/' + fileInfo.dir_id">
              <Icon type="md-arrow-back" :size="24" />
            </RouterLink>
          </span>
          <Divider type="vertical"></Divider>
        </div>
        <div>
          <span class="file-header-menu-input">
            <Input v-model="fileInfo.title" :border="false" :disabled="documentReadOnly" class="header-file-title"
              placeholder="Enter something..." />
          </span>
        </div>
        <div class="header-right-group">
          <span style="padding-right: 10px;color:#2c3e50;font-size: 12px;">
            {{ fileOwner }} 更新于 {{ fileInfo.update_time }}
          </span>
          <Button @click="handleSave" type="primary">保存</Button>
          <Button @click="shareDocument" type="primary">分享</Button>
        </div>
      </div>
    </Card>
    <TinyEditor v-model="fileInfo.content" :disabled="documentReadOnly" @saveContent="saveContent"></TinyEditor>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getDocumentDetailApi, patchDocumentDetailApi } from '../../api/document';
import TinyEditor from './TinyEditor.vue';
import { RouterLink } from 'vue-router';
import { debounce } from 'lodash'

export default {
  name: 'DocumentContent',
  components: {
    TinyEditor,
    RouterLink
  },
  props: {
    docID: {
      type: [Number, String],
      default: 0,
      required: true
    },
  },
  data() {
    return {
      fileInfo: {
        id: 0,
        title: '',
        content: ''
      },
      saving: false,
    };
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    fileOwner() {
      if (this.fileInfo.id > 0) {
        if (this.fileInfo.update_id) {
          return this.fileInfo.view_data.update_username;
        } else {
          return this.fileInfo.view_data.create_username;
        }
      } else {
        return '';
      }
    },
    documentReadOnly() {
      return this.userInfo.id !== this.fileInfo.create_id;
    }
  },
  methods: {
    handleSave: debounce(function () {
      this.saveContent()
    }, 1000),
    saveContent(content) {
      let data = {
        "title": this.fileInfo.title,
        "content": this.fileInfo.content,
      }
      patchDocumentDetailApi(parseInt(this.fileInfo.id), data).then(response => {
        if (response.data.code === 200) {
          this.$Message.success({
            content: "保存成功"
          });
        }
      });
    },
    shareDocument() {
      Copy({
        text: window.location.origin + '/document/' + this.docID
      });
    },
    getFileInfo() {
      getDocumentDetailApi(this.docID).then(response => {
        this.fileInfo = response.data.result;
      });
    }
  },
  created: function () {
    this.getFileInfo();
  },
  mounted: function () {
  },
  beforeDestroy: function () {
    this.saveContent();
  },
  watch: {
    docID(newValue, oldValue) {
      this.getFileInfo();
    }
  },
};
</script>

<style scoped>
.file-header-menu-button {
  height: 32px;
  width: 32px;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
  padding: 4px;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.file-header-menu-input {
  height: 32px;
  width: max-content;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.header-file-title .ivu-input {
  background-color: #f5f7f9;
  transition: box-shadow 0.3s ease;
}

.header-file-title .ivu-input:hover {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-right-group {
  margin-left: auto;
  padding-right: 16px;
}
</style>