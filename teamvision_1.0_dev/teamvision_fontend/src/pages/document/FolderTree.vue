<template>
  <div>
    <Header class="knowledge-dir-header">
      <Row class="knowledge-dir-header-op-bar">
        <Col span="20">
        <span>文档目录</span>
        </Col>
        <Col span="4" class="knowledge-dir-header-op">
        <Poptip placement="bottom-end">
          <Button size="small" icon="md-add" type="primary" shape="circle"></Button>
          <div slot="content" style="width: 200px;">
            <Input show-word-limit maxlength="100" search icon="md-return-left" placeholder="回车创建"
              @on-search="addRootFolder" />
          </div>
        </Poptip>
        </Col>
      </Row>
    </Header>
    <div class="css-CaseGroupTree-SectionTreeView">
      <Spin v-if="loadingTree" fix size="large"></Spin>
      <el-tree ref="folderTree" :data="folderTreeData" :show-checkbox="false" @node-click="clickTreeNode" node-key="id"
        :highlight-current="true" :default-expanded-keys="defaultExpandKeys" :filter-node-method="filterNode"
        icon-class="el-icon-caret-right" :default-expand-all="true" :expand-on-click-node="false" :props="defaultProps">
        <span class="custom-tree-node" slot-scope="{ node, data }" @mouseenter="onMouseOver(data.id)"
          @mouseleave="onMouseOut(data.id)">
          <span class="css-folderIcon-SectionIcon"></span>
          <span class="css-SectionName-textEllipsis">
            <router-link :to="'/wiki/folder/' + data.id" tag="span">
              <span style="text-overflow: ellipsis; white-space: nowrap;">{{ data.name }}</span>
            </router-link>
          </span>
          <span class="css-SectionControls" v-show="showNodeToolID === data.id">
            <Poptip v-show="showAddButtern(node)" placement="bottom-end" style="display: block;">
              <span style="padding:0px 1px 0px 1px;">
                <Icon type="md-add" />
              </span>
              <div slot="content" style="width: 200px;">
                <Input show-word-limit maxlength="20" search icon="md-return-left" placeholder="回车创建"
                  @on-search="createFolder" />
              </div>
            </Poptip>
            <span @click="editFolder(data)" style="display: block;padding:0px 1px 0px 1px;">
              <Icon type="md-create" />
            </span>
            <span @click="deleteFolder(data)" style="display: block;padding:0px 1px 0px 1px;">
              <Icon type="ios-trash" />
            </span>
          </span>
        </span>
      </el-tree>
    </div>
    <FolderCreateDialog :showDialog="showDialog" :folderDetail="editFolderDetail" @closeDialog="closeDialog">
    </FolderCreateDialog>
  </div>
</template>

<script>
import FolderCreateDialog from './FolderCreateDialog.vue';
import { createFolderApi, getDocumentFolderList, updateFolderApi, deleteFolderApi } from "../../api/document";

export default {
  name: 'DocumentDirTree',
  props: ['',],
  data() {
    return {
      showDialog: false,
      loadingTree: false,
      columnItemHeight: 200,
      showNodeToolID: 0,
      editFolderDetail: {
        id: 0,
        name: '',
      },
      selectCasesCount: 0,
      defaultExpandKeys: [],
      formData: {
        Title: '新建用例',
        Parent: 0,
        IsGroup: false,
        Project: 0,
        Priority: 2,
        Module: 0,
        RunTimes: 0
      },
      folderTreeData: [],
      defaultProps: {
        label: 'name',
        children: 'children',
        isLeaf: false,
      },
    }
  },
  computed: {
    isAdmin() {
      ///return this.$store.state.userInfo.isAdmin
      return true;
    },
  },
  methods: {
    addRootFolder(name) {
      this.showNodeToolID = 0
      this.createFolder(name)
    },

    remove: function (node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
    },

    onMouseOver: function (id) {
      this.showNodeToolID = id
    },

    onMouseOut: function (id) {
      this.showNodeToolID = 0
    },

    showAddButtern: function (node) {
      if (node.level > 5) {
        return false;
      } else {
        return true;
      }
    },

    handleNodeClick(data, node, element) {
      this.$emit('clickNode', data, node, element)
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.Title.indexOf(value) !== -1;
    },

    closeDialog: function () {
      this.showDialog = false;
      this.editFolderDetail = {}
      this.loadFolderTree()
    },

    loadFolderTree: function () {
      this.loadingTree = false

      getDocumentFolderList().then(response => {
        this.folderTreeData = response.data.result.results
        this.loadingTree = false
      }, response => {
      })
    },

    clickTreeNode: function (data, node, event) {
      //this.$router.replace({ name: 'Folder', params: { folderID: data.id } })
    },

    createFolder: function (name) {
      let parameters = {
        "name": name,
        "parent_id": this.showNodeToolID,
      }
      createFolderApi(parameters).then(response => {
        this.$Message.success({
          content: '目录创建成功'
        })
        this.loadFolderTree()
      }, response => {
      })
    },

    deleteFolder: function (data, index) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除目录[' + data.name + ']',
        onOk: () => {
          deleteFolderApi(parseInt(data.id)).then(response => {
            let index = this.folderTreeData.findIndex(obj => obj.id === data.id);
            if (index !== -1) {
              this.folderTreeData.splice(index, 1);
            }
            this.$Message.success({
              content: '目录删除成功'
            })
          })
        },
        onCancel: () => { }
      })
    },

    editFolder: function (data) {
      this.editFolderDetail = data;
      this.showDialog = true;
    },
  },

  created: function () {
    this.loadFolderTree()
  },

  mounted: function () {
  },

  watch: {
  },

  components: {
    FolderCreateDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.knowledge-dir-header {
  background-color: inherit;
  border-bottom: 1px solid #f4f4f4;
  padding: 5px;
  height: 64px;
  position: relative;
}

.knowledge-dir-header-op-bar {
  position: absolute;
  bottom: 0;
  height: 64px;
  color: #202020;
  //font-weight: bold;
  width: 100%;
  font-size: 18px;
}

.knowledge-dir-header-op-addicon {
  cursor: pointer;
}

.demo-badge {
  /*width: 42px;*/
  /*height: 22px;*/
  margin-right: 10px;
  background: #eee;
  /*border-radius: 6px;*/
  display: inline-block;
  font-size: 8px;

}

.css-142qn3c-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-1bcfq0a-folderIcon-SectionIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjEgKDUxMTQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5mb2xkZXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iVGVzdC1DYXNlLS0tQ3JlYXRlLSZhbXA7LUVkaXQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSLnlKjkvovnrqHnkIYt5Yib5bu655So5L6LLemAieaLqeaooeWdlyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTQ2LjAwMDAwMCwgLTcyLjAwMDAwMCkiPgogICAgICAgICAgICA8ZyBpZD0ibGVmdC1zaWRlIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJjYXNlLXNlY3Rpb24tbGlzdCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4wMDAwMDAsIDY0LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJjYXNlLXNlY3Rpb24iPgogICAgICAgICAgICAgICAgICAgICAgICA8ZyBpZD0ic2VjdGlvbi1yb3ciPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGcgaWQ9ImZvbGRlciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDYuMDAwMDAwLCA4LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJ5ZWxsb3ciPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMS4wMDAwMDApIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0wLDIgTDE2LDIgTDE2LDEzIEMxNiwxMy41NTIyODQ3IDE1LjU1MjI4NDcsMTQgMTUsMTQgTDEsMTQgQzAuNDQ3NzE1MjUsMTQgNi43NjM1Mzc1MWUtMTcsMTMuNTUyMjg0NyAwLDEzIEwwLDIgWiIgaWQ9IlJlY3RhbmdsZS0xMCIgZmlsbD0iI0ZGRDk0QiI+PC9wYXRoPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTEsMCBMNi44NTcxNDI4NiwwIEw4LDIgTDAsMiBMMCwxIEMtNi43NjM1Mzc1MWUtMTcsMC40NDc3MTUyNSAwLjQ0NzcxNTI1LDEuMDE0NTMwNjNlLTE2IDEsMCBaIiBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRjlDNDMzIj48L3BhdGg+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==) center center / 16px 16px no-repeat;
}

.SectionName-textEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 0 4px;
  overflow: hidden;
  color: #606266;
  margin: 0 4px;
  width: 100px;
}

.css-142qn3c-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 36px;
  cursor: pointer;
  padding: 4px;
}

.css-SectionControls {
  flex: 0 0 auto;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding-left: 6px;
  padding-right: 6px;
  //width: 52px;
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  outline-color: transparent;
  box-shadow: none;
}

.custom-tree-node {
  //flex: 1;
  display: flex;
  height: 32px;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  //padding-right: 8px;
  -webkit-box-align: center;
  flex: 1 1 auto;
  padding-left: 0px;
  min-width: 4px;
  //max-width:80%;
}

.css-folderIcon-SectionIcon {
  display: inline-block;
  width: 26px;
  height: 26px;
  flex: 0 0 26px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-SectionName-textEllipsis {
  min-width: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 2px 4px 2px 4px;
  padding-left: 4px;
  overflow: hidden;
  color: #606266;
  margin: 0px 4px 0px 4px;
}
</style>
