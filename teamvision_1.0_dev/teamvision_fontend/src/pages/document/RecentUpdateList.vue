<template>
  <div style="margin-left: 2px;">
    <PageHeader>
      <template slot="title">
        <span style="display: inline-block;padding-right: 10px;">
          <Icon color="#2d8cf0" size="24" type="ios-alarm" />
        </span>
        <span>最近1个月更新流程</span>
      </template>
      <!-- <template slot="action">
                <ButtonGroup>
                    <Button>添加成员</Button>
                </ButtonGroup>
                <Button type="primary">新建</Button>
            </template> -->
      <template slot="content">
        <Breadcrumb>
          <BreadcrumbItem :replace="true" v-for="(item, index) in breadcrumbs" :to="item.to"
            :key="'breadcrumb' + index">
            {{ item.text }}</BreadcrumbItem>
        </Breadcrumb>
      </template>
      <template slot="extra">
        <p style="color: #808695">最近一个月更新数量</p>
        <p style="font-size: 14px">{{ documentList.length }}次</p>
      </template>
    </PageHeader>
    <DocumentList :id="null" :searchKeyword="searchKeyword" :showUpdateFlile="true"></DocumentList>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getDocumentListApi } from '../../api/document';
import DocumentList from "./DocumentList.vue";

export default {
  name: 'YourComponentName',
  components: {
    DocumentList
  },
  props: {
    searchKeyword: {
      type: [String, Number],
      required: false,
      default: ''
    }
  },
  data() {
    return {
      breadcrumbs: [],
      documentList: [],
      tableHeight: window.innerHeight - 230,
    };
  },
  computed: {
    computedSearchKeyword() {
      return this.searchKeyword;
    }
  },
  watch: {
    computedSearchKeyword(newValue) {
      this.listDocumentList();
    }
  },
  mounted() {
    this.listDocumentList();
    window.addEventListener('resize', this.updateTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateTableHeight);
  },
  methods: {
    listDocumentList() {
      let data = {
        "folderId": null,
        "title": null,
        "status": null //为空全部
      };
      if (this.searchKeyword.length === 0) {
        data.title = null;
      } else {
        data.title = this.searchKeyword;
      }
      this.documentList = [];
      getDocumentListApi(data).then(response => {
        let fileList = [...response.data.result];
        for (let i = 0; i < fileList.length; i++) {
          if (fileList[i].updateTime && fileList[i].updateTime !== null) {
            const date = new Date(fileList[i].updateTime);
            if (this.isDateWithinLastMonth(date)) {
              this.documentList.push(fileList[i]);
            }
          }
        }
      });
    },
    isDateWithinLastMonth(date) {
      const now = new Date();
      const oneMonthAgo = new Date(now);
      oneMonthAgo.setMonth(now.getMonth() - 1);
      oneMonthAgo.setHours(0, 0, 0, 0);
      const endOfOneMonthAgo = new Date(oneMonthAgo);
      endOfOneMonthAgo.setDate(oneMonthAgo.getDate() + 1);
      endOfOneMonthAgo.setHours(23, 59, 59, 999);

      return date >= oneMonthAgo;
    },
    updateTableHeight() {
      this.tableHeight = window.innerHeight - 230;
    }
  }
};
</script>

<style>
.file-header-menu-button {
  height: 32px;
  width: 32px;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
  padding: 4px;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.file-header-menu-input {
  height: 32px;
  width: max-content;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.header-file-title .ivu-input {
  background-color: #f5f7f9;
  transition: box-shadow 0.3s ease;
}

.header-file-title .ivu-input:hover {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-right-group {
  margin-left: auto;
  padding-right: 16px;
}
</style>