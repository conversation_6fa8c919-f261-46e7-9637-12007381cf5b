<template>
  <div>
    <FolderTree :dirList="newDirList"></FolderTree>
  </div>
</template>

<script>
import FolderTree from "./FolderTree.vue";

export default {
  name: 'DirPanel',
  components: {
    FolderTree
  },
  props: {
    userDirList: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  computed: {
    newDirList() {
      return this.userDirList;
    }
  }
};
</script>

<style scoped></style>