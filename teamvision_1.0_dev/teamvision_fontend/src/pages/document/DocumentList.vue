<template>
  <div>
    <Table ref="selection" :columns="folderItemColumns" :data="documentList" :height="tableHeight">
      <template slot="title" slot-scope="{ row, index }">
        <router-link :to="'/wiki/document/' + row.id">{{ row.title }}</router-link>
      </template>
      <template slot="updateTime" slot-scope="{ row, index }">
        <span v-if="row.update_time"> {{ row.update_time }} </span>
      </template>
      <template slot="status" slot-scope="{ row, index }">
      </template>
      <template slot="owner" slot-scope="{ row, index }">
        <span v-if="row.updateName && row.update_username.length > 0">
          {{ row.view_data.update_username }}
        </span>
        <span v-else>{{ row.view_data.create_username }}</span>
      </template>
      <template slot="op" slot-scope="{ row, index }">
        <Dropdown @on-click="documentOperation">
          <span style="cursor: pointer;">
            <Icon size="18" type="ios-more" />
          </span>
          <template slot="list">
            <DropdownMenu>
              <DropdownItem v-if="userInfo.id === row.create_id" :name="index + '_delete'">删除</DropdownItem>
              <DropdownItem :name="index + '_share'">分享</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </template>
    </Table>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getDocumentDetailApi, getDocumentListApi, deleteDocument, setFileStatus } from '../../api/document';
import { copyLink } from '../../utils/utils.js';

export default {
  name: 'DocumentList',
  components: {
  },
  props: {
    folderID: {
      type: [Number, String],
      default: 0,
      required: true
    },
    showUpdateFlile: {
      type: [Boolean],
      default: false,
      required: true
    },
    searchKeyword: {
      type: [String, Number],
      required: false,
      default: ''
    }
  },
  data() {
    return {
      documentList: [],
      statusEnumList: [],
      folderItemColumns: [
        {
          title: 'ID',
          width: 100,
          key: 'id'
        },
        {
          title: '名称',
          key: 'title',
          slot: 'title'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          slot: 'updateTime',
          width: 200,
        },
        {
          title: '维护人',
          slot: 'owner',
          width: 100,
        },
        {
          title: '操作',
          slot: 'op',
          width: 100,
        },
      ],
      tableHeight: window.innerHeight - 180,

    };
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
  },

  methods: {
    // copyLink(link) {
    //   try {
    //     // 使用 navigator.clipboard 复制链接
    //     navigator.clipboard.writeText(link);
    //     setTimeout(() => {
    //       alert('复制到粘贴板 成功！');
    //     }, 500);
    //   } catch (err) {
    //     alert('复制失败，请手动复制链接！');
    //   }
    // },

    listFolderDocumentList() {
      this.documentList = [];
      var urlArgs = '?';
      if (this.folderID > 0) {
        urlArgs = urlArgs + 'dir_id=' + this.folderID;
      };
      if (this.searchKeyword !== '') {
        urlArgs = urlArgs + '&title=' + this.searchKeyword;
      };

      getDocumentListApi(urlArgs).then(response => {
        this.documentList = response.data.result.results;
        this.$emit('documentsCount', response.data.result.count);
      });
    },

    documentOperation(op) {
      let opArray = op.split('_');
      let item = this.documentList[parseInt(opArray[0])];
      console.log(item)
      if (opArray[1] === 'delete') {
        this.$Modal.confirm({
          title: '删除确认',
          content: '您即将删除流程[' + item.title + ']',
          onOk: () => {
            deleteDocument(item.id).then(response => {
              this.documentList.splice(parseInt(opArray[0]), 1);
            });
          },
          onCancel: () => { }
        });
      }
      if (opArray[1] === 'share') {
        copyLink(window.location.origin + '/wiki/document/' + item.id);
      }
    },

    updateTableHeight() {
      this.tableHeight = window.innerHeight - 230;
    },
  },

  mounted() {
    this.listFolderDocumentList();
    window.addEventListener('resize', this.updateTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateTableHeight);
  },

  watch: {
    folderID(newValue) {
      this.folderID = newValue;
      this.listFolderDocumentList();
    },
  },
};
</script>

<style>
.file-header-menu-button {
  height: 32px;
  width: 32px;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
  padding: 4px;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.file-header-menu-input {
  height: 32px;
  width: max-content;
  cursor: pointer;
  display: inline-flex;
  justify-items: center;
  align-items: center;
}

.file-header-menu-button:hover {
  background-color: #cccccc;
}

.header-file-title .ivu-input {
  background-color: #f5f7f9;
  transition: box-shadow 0.3s ease;
}

.header-file-title .ivu-input:hover {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-right-group {
  margin-left: auto;
  padding-right: 16px;
}
</style>