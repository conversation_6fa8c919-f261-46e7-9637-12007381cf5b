<!-- <template>
  <div>
    <editor v-model="editorContent" :init="editorConfig" :disabled="false" @saveContent="saveContent" @change="contentChange">
      <textarea id="editor" name="content">asdfasdfsdfsd</textarea>
      <button type="submition">提交</button>
    </editor>
    <iframe style="height: 0px;" src="" frameborder="0" name="frameName"></iframe>
  </div>
</template>

<script>
export default {
  name: 'TinyEditor',
  components: {
    Editor,
  },
  props: {
    id: {
      type: [String, Number],
      required: true,
      default: 0
    },
    content: {
      type: String,
      required: false,
      default: ''
    },
    readOnly: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  data() {
    return {
      editorContent: 'asdfasdfsdsdfsdfsdfsdfsdsd',

      editorConfig: {
        selector: 'editor',
        menubar: false,
        inline: false,
        resize: false,
        height: this.containerHeight,
        plugins: 'export powerpaste save preview importcss searchreplace autolink directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount charmap quickbars emoticons accordion tableofcontents checklist mentions',
        toolbar_location: 'auto',
        toolbar_sticky: true,
        removed_menuitems: 'newdocument',
        toolbar: "save fullscreen preview powerpaste export | undo redo | accordion accordionremove | blocks fontfamily fontsize | bold italic underline strikethrough | forecolor backcolor removeformat | align numlist bullist | link image | table media | lineheight outdent indent | charmap emoticons |code | print | pagebreak anchor codesample | ltr rtl | tableofcontents tableofcontentsupdate ",
        promotion: false,
        statusbar: false,
        language: 'zh-Hans',
        content_css: 'default',
        quickbars_selection_toolbar: 'bold italic | quicklink h1 h2 h3 h4 h5 h6 blockquote checklist  | quickimage quicktable|addcomment',
        quickbars_insert_toolbar: 'bold italic h1 h2 h3 h4 h5 h6 underline strikethrough checklist | align numlist bullist | quickimage quicktable',
        tinycomments_mode: 'embedded',
        tinycomments_author: 'Demo',
        tableofcontents_depth: 5,
        tableofcontents_includeheader: false,
        contenteditable: false,
        fullscreen_native: true,
        tinycomments_can_resolve: (req, done, fail) => {
          const allowed = req.comments.length > 0 &&
            req.comments[0].author === 'Demo';
          done({
            canResolve: allowed || 'Demo' === 'admin'
          });
        },
        setup: (editor) => {
          editor.ui.registry.addSidebar('mysidebar', {
            tooltip: 'My sidebar',
            icon: 'comment',
            onSetup: (api) => {
              console.log('Render panel', api.element());
              return () => {
                console.log('Removing sidebar');
              };
            },
            onShow: (api) => {
              console.log('Show panel', api.element());
              api.element().innerHTML = 'Hello world!';
            },
            onHide: (api) => {
              console.log('Hide panel', api.element());
            }
          });
          if (this.documentReadOnly) {
            editor.on('keydown', (e) => {
              this.editorContent = this.content;
              console.log(e);
              if ((e.metaKey || e.ctrlKey) && (e.key === 'c' || e.key === 'a' || e.key === 'Meta' || e.key === 'Ctrl')) {
                // Do nothing
              } else {
                this.$message.error({
                  content: '您没有编辑流程的权限，请联系流程管理员'
                });
              }
            });
            editor.on('keyup', (e) => {
              this.editorContent = this.content;
            });
          }
        },
      }
    };
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    tinymce.init({})
    this.editorContent = this.content;
  },
  methods: {
    contentChange(event, editor) {
      console.log(event);
      console.log(editor.getContent());
    },
    saveContent(event, editor) {
      if (!this.documentReadOnly) {
        this.$emit('saveContent', editor.getContent());
      }
    },
    submit(event, editor) {
      console.log(event);
      console.log(editor.getContent());
    }
  }
};
</script> 
-->


<template>
  <div>
    <editor v-model="contentValue" :init="init" :disabled="disabled" @saveContent="saveContent">
    </editor>
  </div>
</template>

<script>
import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "../../assets/tinymce/skins/ui/oxide/skin.css";
import "../../assets/tinymce/langs/zh_CN.js";
import "tinymce/themes/silver/theme";
import "tinymce/icons/default";
import "tinymce/plugins/image";
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import "tinymce/plugins/lists";
import "tinymce/plugins/contextmenu";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import "tinymce/plugins/code";
import "tinymce/plugins/codesample";
import "tinymce/plugins/hr"
import "tinymce/plugins/link"
import "tinymce/plugins/wordcount"
import "tinymce/plugins/textpattern"
import "tinymce/plugins/help"
import "tinymce/plugins/print"
import "tinymce/plugins/preview"
import "tinymce/plugins/searchreplace"
import "tinymce/plugins/autolink"
import "tinymce/plugins/directionality"
import "tinymce/plugins/visualblocks"
import "tinymce/plugins/visualchars"
import "tinymce/plugins/fullscreen"
import "tinymce/plugins/template"
import "tinymce/plugins/charmap"
import "tinymce/plugins/pagebreak"
import "tinymce/plugins/nonbreaking"
import "tinymce/plugins/anchor"
import "tinymce/plugins/insertdatetime"
import "tinymce/plugins/advlist"
import "tinymce/plugins/imagetools"
import "tinymce/plugins/help"
import "tinymce/plugins/emoticons"
import "tinymce/plugins/autosave"
import "tinymce/skins/content/default/content.css";
import "tinymce/skins/ui/oxide/content.min.css";
import "tinymce/skins/content/default/content.css";

export default {
  name: "tinymce",
  components: {
    Editor,
  },
  props: {
    //传入一个value，使组件支持v-model绑定
    value: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      //初始化配置
      init: {
        api_key: 'gwboy5j5rq1vagsjmfa55k6hyi8szkgd7e3t621czrovqvp3',
        language_url: "/assets/tinymce/langs/zh_CN.js",
        skin_url: "../..//assets/tinymce/skins/ui/oxide",
        content_css: "../../assets/tinymce/skins/content/default/content.css",
        language: "zh_CN",
        height: window.innerHeight - 50,
        width: window.innerWidth - 350,
        //plugins: "code codesample table hr fullscreen advlist lists autosave",
        plugins: "print preview searchreplace autolink directionality visualblocks visualchars fullscreen link template code codesample table hr nonbreaking anchor insertdatetime advlist lists wordcount imagetools textpattern help autosave bdmap indent2em axupimgs",
        toolbar: "undo redo | formatselect fontselect fontsizeselect | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | lists table |  subscript superscript |removeformat hr | code codesample | insertdatetime print preview | fullscreen",
        branding: true,
        menubar: false,
        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
        images_upload_handler: (blobInfo, success, failure) => {
          const img = "data:image/jpeg;base64," + blobInfo.base64();
          success(img);
        },
        resize: true,
        autosave_ask_before_unload: false,
        fontsize_formats: "12px 14px 16px 18px 24px 36px 48px 56px 72px",
        font_formats:
          "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
        promotion: false,
      },
      contentValue: this.value,

    };
  },
  computed: {
  },

  methods: {
    //添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events

    onClick(e) {
      this.$emit("onClick", e, tinymce);
    },
    saveContent(event, editor) {
      if (!this.disabled) {
        this.$emit('saveContent', editor.getContent());
      }
    },
  },
  watch: {
    value(newValue) {
      this.contentValue = newValue;
    },
    contentValue(newValue) {
      this.$emit("input", newValue);
    },
  },
  mounted() {
    tinymce.init({});
  },
};
</script>
<style scoped>
.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  #sample {
    width: 100%;
    padding-top: 0px;
  }

  #sample textarea {
    padding: 8px;
    height: 48px;
    border-color: #e8eaec;
  }
}
</style>
