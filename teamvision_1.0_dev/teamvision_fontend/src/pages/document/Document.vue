<template>
  <Layout :style="'height:' + appHeight + 'px;'">
    <Sider style="background-color: #ffffff;" :width="280">
      <FolderTree :isAdmin="isAdmin"></FolderTree>
    </Sider>
    <Content>
      <router-view></router-view>
    </Content>
  </Layout>
</template>
<script>

import { mapMutations, mapGetters, mapState, mapActions } from "vuex";

import Folder from "./Folder.vue";
import DocumentContent from "./DocumentContent.vue";
import RecentUpdateList from "./RecentUpdateList.vue";
import { getDocumentFolderList, getDocumentListApi } from "../../api/document.js";
import FolderTree from "./FolderTree.vue";
import { RouterView } from 'vue-router';

export default {
  name: 'Document',
  components: {
    FolderTree,
    Folder,
    DocumentContent,
    RecentUpdateList,
  },
  props: {
    documentID: {
      type: [Number, String],
      required: false,
      default: 0
    }
  },
  data() {
    return {
      reload: false,
      userDirList: [],
      searchKeyword: '',
      currentUser: {},
      route: this.$route
    };
  },
  computed: {
    ...mapState(['appHeight', 'appBodyHeight', 'appBodyMainHeight']),

    isAdmin() {
      let result = false;
      if (this.currentUser && this.currentUser.userRole) {
        result = this.currentUser.userRole.roleId === 1;
      }
      return result;
    },
    objectId() {
      console.log(this.route.params.id);
      return this.route.params.id;
    },
  },
  methods: {
    ...mapMutations(['setAppBodyHeaderHeight']),
    onOKDirAdd(value) {
      // this.userDirList.push(value);
      this.listFolderList();
    },

    listFolderList() {
      getDocumentFolderList({ "status": 1, "parentId": null }).then(response => {
        this.userDirList.length = 0;
        this.userDirList.push(response.data.result);
      });
    },

  },
  created() {
    this.currentUser = this.$store.state.user;
  },
  mounted() {
    this.listFolderList();
    this.setAppBodyHeaderHeight(0);
  },
  beforeDestroy() {
    console.log("destroyedasdfadfasdfasdfsd");
    this.setAppBodyHeaderHeight(48);
  },
  watch: {
  },
};
</script>

<style scoped></style>