<template>
  <div class="app-body-header-bar-default">
    <Row align="middle">
      <Col :lg="16" :md="14" :sm="16" :xs="16">
      <div class="app-body-header-leftbar-default">
        <ul class="app-body-head-menu">
          <router-link to="/ci/task" tag="li" active-class="app-body-head-menu-item-active"
            class="app-body-head-menu-item">
            <a href="/ci/task"><i class="fa fa-fw  fa-bus"></i>任务</a>
          </router-link>
          <router-link v-if="ciDynamicMenu.show" :to="ciDynamicMenu.path" tag="li"
            class="app-body-head-menu-item-active app-body-head-menu-item">
            <a href="/ci">
              <Icon :size="24" type="ios-settings" /> {{ ciDynamicMenu.text }}
            </a>
          </router-link>
        </ul>
      </div>
      </Col>
      <Col :lg="4" :md="4" :sm="0" :xs="0">
      <div style="height: inherit">
        <AutoComplete v-if="ciHeadToolShow.taskSearchBox" @on-search="filterCITask" size="small" :transfer="true"
          v-model="ciTaskSearchKey" icon="ios-search" placeholder="输入任务名称，查找任务！" style="width:100%">
          <div style="overflow-y: scroll;max-height:400px;">
            <div class="demo-auto-complete-item" v-for="item in projectCITasks" :key="item" v-if="item.CITaskCount > 0">
              <div class="demo-auto-complete-group">
                <span style="padding-left: 10px;">
                  <Avatar shape="square" :src="item.PBAvatar" size="small" />
                </span>
                <span style="padding-left: 10px;">{{ item.ProjectName }}</span>
                <span> ({{ item.CITaskCount }})</span>
              </div>
              <Option v-for="option in item.CITasks" v-if="option.Display" :value="option.TaskName" :key="option.ID">
                <a :href="option.TaskHistoryURL" class="demo-auto-complete-title">{{ option.TaskName }}</a>
                <!--<span class="demo-auto-complete-count">{{ option.TaskName }} 人关注</span>-->
              </Option>
            </div>
          </div>
        </AutoComplete>
      </div>
      </Col>
      <Col :lg="4" :md="6" :sm="8" :xs="8">
      <div class="app-body-header-rightbar-default" style="height: inherit">
        <span v-if="ciHeadToolShow.newObject" @click="onAddCITask">
          <Avatar class="cursor-hand" style="background-color: #32be77;" icon="md-add" size="small" />
        </span>
        <Divider type="vertical" />
        <span class="cursor-hand">
          <Dropdown transfer>
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-settings" color="#5578aa" />
              </span>
              <Icon type="ios-arrow-down"></Icon>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem name="1">
                <Icon type="md-podium" />
                <router-link to="/ci/settings/global_variable" tag="span">CI变量</router-link>
              </DropdownItem>
              <DropdownItem name="2">
                <Icon type="ios-desktop" />
                <router-link to="/ci/settings/agent" tag="span">Agent</router-link>
              </DropdownItem>
              <DropdownItem name="3">
                <Icon type="ios-bookmark" />
                <router-link to="/ci/settings/tag" tag="span">标签</router-link>
              </DropdownItem>
              <!--<DropdownItem name="4"><Icon type="ios-cube" />-->
              <!--<router-link to="/ci/settings/module'" tag="span">凭证</router-link>-->
              <!--</DropdownItem>-->
            </DropdownMenu>
          </Dropdown>
        </span>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from "vuex";

export default {
  name: "CIHead",
  props: ["menuItem", "flowID"],
  data() {
    return {
      ciTaskSearchKey: "",
      projectCITasks: [],
    };
  },
  computed: {
    ...mapGetters("ciglobal", ["ciDynamicMenu", "ciHeadToolShow"]),
  },
  methods: {
    ...mapMutations("citask", ["setCITaskSearchKey"]),
    ...mapMutations("ciglobal", ["setCINewObject"]),
    loadCITaskList: function () {
      this.$axios.get("/api/ci/project/my").then(
        (response) => {
          this.projectCITasks = response.data.result;
        },
        (response) => { }
      );
    },
    filterCITask: function (value) {
      this.initCITaskStatus();
      this.setCITaskSearchKey(value);
      if (value.trim() !== "") {
        for (let i = 0; i < this.projectCITasks.length; i++) {
          this.projectCITasks[i].CITaskCount = 0;
          for (let j = 0; j < this.projectCITasks[i].CITasks.length; j++) {
            let temp = this.projectCITasks[i].CITasks[j];
            if (temp.TaskName.toUpperCase().indexOf(value.toUpperCase()) > -1) {
              temp.Display = true;
              this.projectCITasks[i].CITaskCount++;
            } else {
              temp.Display = false;
            }
          }
        }
      }
    },
    initCITaskStatus: function () {
      for (let i = 0; i < this.projectCITasks.length; i++) {
        this.projectCITasks[i].CITaskCount = 0;
        for (let j = 0; j < this.projectCITasks[i].CITasks.length; j++) {
          let temp = this.projectCITasks[i].CITasks[j];
          temp.Display = true;
          this.projectCITasks[i].CITaskCount++;
        }
      }
    },
    onAddCITask: function () {
      this.setCINewObject(true);
    },
  },
  created: function () {
    this.loadCITaskList();
  },
  components: {},
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import "../../layout/appBody";
@import "../../layout/appHead";
@import "../../assets/teamvision/global/less/global";

.demo-auto-complete-item {
  padding: 4px 0;
  border-bottom: 1px solid #f6f6f6;
  color: #5578aa;
}

.demo-auto-complete-group {
  font-size: 12px;
  padding: 4px 6px;
}

.demo-auto-complete-group span {
  color: #666;
  font-weight: bold;
}

.demo-auto-complete-group a {
  float: right;
}

.demo-auto-complete-count {
  float: right;
  color: #999;
}

.demo-auto-complete-more {
  display: block;
  margin: 0 auto;
  padding: 4px;
  text-align: center;
  font-size: 12px;
}

.demo-auto-complete-title {
  color: inherit;
  padding-left: 20px;
}

.task-search-itemgroup-badge {
  background-color: #388e8e !important;
}

.body-head-color a {
  color: #6a6c6f;
}
</style>
