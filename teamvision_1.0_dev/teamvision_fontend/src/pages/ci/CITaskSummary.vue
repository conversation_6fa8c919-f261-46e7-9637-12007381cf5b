<template>
  <div class="ci-task-container" :style="'height:100%; overflow-y:scroll;'">
    <Row :gutter="16">
      <Col :lg="16" :md="16" :sm="12" :xs="24">
      <div>
        <ci-task></ci-task>
      </div>
      </Col>
      <Col :lg="8" :md="8" :sm="12" :xs="0">
      <div>
        <ci-agent></ci-agent>
      </div>
      </Col>
    </Row>
    <c-i-task-create-dialog></c-i-task-create-dialog>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from "vuex";
import ciTaskFlow from './citaskflow/CITaskFlow.vue'
import ciAgent from './agent/CIAgent.vue'
import ciTask from './citask/CITask.vue'
import ciTaskFlowItem from './citaskflow/ci_task_flow_item/CITaskFlowItem.vue'
import CITaskCreateDialog from './citask/CITaskCreateDialog.vue'
import CITaskFlowCreateDialog from './citaskflow/CITaskFlowCreateDialog.vue'
export default {
  name: 'ciTaskContainer',
  props: ['menuItem', 'flowID'],
  data() {
    return {
      showTaskConfig: true,
      showParameterGroup: false,
      toDoSummary: {
        taskCount: 0,
        issueCount: 0,
        fortestingCount: 0,
      },
      activeProject: [],
      activity: {
        next: '',
        data: [],
        count: 0
      }
    }
  },
  computed: {
    ...mapState(['appBodyHeight']),

    ContainerHeight: function () {
      return this.appBodyHeight
    },
    agentContainerHeight: function () {
      return this.appBodyHeight - 80
    }
  },
  components: {
    CITaskFlowCreateDialog,
    CITaskCreateDialog,
    ciAgent,
    ciTaskFlow,
    ciTask,
    ciTaskFlowItem
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.ci-task-container {
  padding: 10px;
}
</style>
