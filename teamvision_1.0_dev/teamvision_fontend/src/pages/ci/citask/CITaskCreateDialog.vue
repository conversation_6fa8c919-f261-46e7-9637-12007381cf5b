<template>
  <Modal :value="ciNewObject" title="添加新任务" :width="600" @on-cancel="cancel" :styles="{ bottom: '20px', top: '50px' }">
    <div style="height:400px;overflow-y: scroll;overflow-x: hidden">
      <Form ref="ciTaskCreate" :model="formItem" :label-width="80" :rules="ruleCustom">
        <FormItem label="项目" prop="Project">
          <Select v-model="formItem.Project" placeholder="请选择项目" @on-change="onProjectChange">
            <Option v-for="project in projects" :key="project.id" :value="project.id" :label="project.PBTitle">
              <span style="margin-right: 10px;">
                <Avatar shape="square" :src="project.PBAvatar" />
              </span>
              <span>{{ project.PBTitle }}</span>
            </Option>
          </Select>
        </FormItem>
        <FormItem label="标题" prop="TaskName">
          <Input v-model="formItem.TaskName" placeholder="任务名称50个字符以内！" />
        </FormItem>
        <FormItem label="构建记录保留个数" prop="HistoryCleanStrategy">
          <Input v-model="formItem.HistoryCleanStrategy" placeholder="默认10个" />
        </FormItem>
        <FormItem label="任务类型" prop="TaskType">
          <Select v-model="formItem.TaskType" :filterable="true" placeholder="任务类型" @on-change="onTaskTypeChange">
            <Option :key="0" :value="0">复制任务</Option>
            <Option :key="4" :value="4">构建</Option>
            <Option :key="1" :value="1">测试</Option>
          </Select>
        </FormItem>
        <FormItem label="任务列表" v-if="showTaskList" prop="CopyTaskID">
          <Select v-model="formItem.CopyTaskID" :filterable="true" placeholder="复制任务列表">
            <Option v-for="task in ciTaskList" :key="task.id" :value="task.id">{{ task.TaskName }}</Option>
          </Select>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button v-if="ciNewObject" type="success" style="width: 80px; height:30px;" shape="circle"
        @click="addCITask('ciTaskCreate')">添加</Button>
      <!--<Button type="ghost"  style="width: 80px; height:30px;"  shape="circle" @click="cancel">取消</Button>-->
    </div>
  </Modal>
</template>

<script>
import { mapState, mapGetters, mapMutations } from "vuex";
import { ciTaskValidateRules } from './CITaskCreateDialog'

export default {
  name: 'CITaskCreateDialog',
  data() {
    return {
      projects: [],
      ciTaskList: [],
      showTaskList: true,
      formItem: {
        CopyTaskID: 0,
        TaskName: '',
        Project: 0,
        TaskType: 0,
        HistoryCleanStrategy: 10
      },
      ruleCustom: {
        ...ciTaskValidateRules
      }
    }

  },
  computed: {
    ...mapGetters('ciglobal', ['ciNewObject']),
    ...mapState(['appBodyHeight']),
    ...mapState('project', ['projectList']),

    containerHeight: function () {
      return this.appBodyHeight - 100
    },
  },
  methods:
  {
    ...mapMutations('citask', ['setCITaskCreateDialogShow', 'setCITaskAdded']),
    ...mapMutations('ciglobal', ['setCINewObject']),
    addCITask(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCINewObject(false)
          this.$axios.post('/api/ci/task/list', this.formItem).then(response => {
            this.setCITaskAdded(true)
          }, response => {
            this.setCINewObject(false)
            this.$Message.error({
              content: '创建任务失败，请联系管理员或者重试',
              duration: 10
            })
          })
        }
      })
    },
    cancel() {
      this.setCINewObject(false)
    },

    onTaskTypeChange: function (value) {
      if (value === 0) {
        this.showTaskList = true
      }
      else {
        this.showTaskList = false
      }
    },

    onProjectChange: function () {
      // console.log(this.formItem)
    },

    loadMyCITasks: function () {
      this.$axios.get('/api/ci/task/my?page_size=10000').then(response => {
        this.ciTaskList = response.data.result.results
      }, response => {
        // error callback
      })
    }

  },
  created() {
    this.projects = this.projectList
    this.loadMyCITasks()
  },
  mounted() {

  },
  watch: {

  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
