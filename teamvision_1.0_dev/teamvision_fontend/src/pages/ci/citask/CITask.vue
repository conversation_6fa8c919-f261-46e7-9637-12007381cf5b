<template>
  <div v-loading="spinShow" :style="'min-height:' + containerHeight + 'px;'">
    <ci-task-item v-for="task in myCITaskList.items" :key="task.id" :item="task"></ci-task-item>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from "vuex";
import ciTaskItem from "./CITaskItem.vue";
export default {
  name: "ciTask",
  data() {
    return {
      myCITask: {
        count: 0,
        next: "",
        items: [],
      },
      spinShow: false,
    };
  },
  computed: {
    ...mapGetters("citask", ["ciTaskSearchKey", "ciTaskAdded"]),
    ...mapState(['appBodyHeight']),
    containerHeight: function () {
      return this.appBodyHeight;
    },
    ciTaskCount: function () {
      return this.myCITask.count;
    },
    myCITaskList: function () {
      if (this.ciTaskSearchKey === "") {
        this.initCITaskStatus();
      } else {
        this.filterCITask(this.ciTaskSearchKey);
      }
      if (this.ciTaskAdded) {
        this.loadCITaskList();
        this.setCITaskAdded(false);
      }
      return this.myCITask;
    },
  },
  methods: {
    ...mapMutations("citask", ["setCITaskAdded"]),
    loadCITaskList: function () {
      this.$axios.get("/api/ci/task/my?page_size=10000").then(
        (response) => {
          this.myCITask.count = response.data.result.count;
          this.myCITask.next = response.data.result.next;
          this.myCITask.items = response.data.result.results;
          this.spinShow = false;
        },
        (response) => {
          this.spinShow = false;
        }
      );
    },
    filterCITask: function (key) {
      this.initCITaskStatus();
      for (let i = 0; i < this.myCITask.items.length; i++) {
        let temp = this.myCITask.items[i];
        if (temp.TaskName.toUpperCase().indexOf(key.toUpperCase()) < 0) {
          temp.Display = false;
          this.myCITask.count--;
        }
      }
    },
    initCITaskStatus: function () {
      this.myCITask.count = this.myCITask.items.length;
      for (let i = 0; i < this.myCITask.items.length; i++) {
        let temp = this.myCITask.items[i];
        temp.Display = true;
      }
    },
  },
  mounted: function () {
    if (this.ciTaskCount === 0) {
      this.spinShow = true;
    }
  },
  created: function () {
    this.loadCITaskList();
  },
  watch: {
    ciTaskCount: function () {
      if (this.ciTaskCount !== 0) {
        this.spinShow = false;
      }
    },
  },
  components: {
    ciTaskItem,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
