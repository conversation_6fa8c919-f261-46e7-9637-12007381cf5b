<template>
  <Row :gutter="16">
    <Col span="6">
    <Card :shadow="false" :dis-hover="true">
      <div slot="title">本阶段步骤</div>
      <div :style="'max-height:' + containerHeight + 'px;overflow-y:auto'">
        <Anchor show-ink :bounds="15" container=".stageLogContainer" style="margin-left: 5px;">
          <AnchorLink style="height: 80px;" v-for="log in steplogs" :key="log.id" :href="'#log_container' + log.id"
            :title="log.step_name" />
        </Anchor>
      </div>
    </Card>
    </Col>
    <Col span="18">
    <Card :shadow="false" :dis-hover="true">
      <div slot="title">阶段日志</div>
      <div class="stageLogContainer"
        :style="'word-break: normal;white-space: normal;height:' + containerHeight + 'px;overflow-y:scroll'">
        <div v-for="log in steplogs" :key="log.id" :id="'log_container' + log.id" style="min-height: 400px;">
          <Divider orientation="left">{{ log.step_name }}
            <span v-if="!log.show_content" style="margin-left: 20px;">
              <Button type="primary" ghost shape="circle" size="small" :loading="showLoading"
                @click="loadStepLog(log.id)">
                <span v-if="!showLoading" style="text-decoration: underline;">加载日志</span>
                <span v-else>加载中....</span>
              </Button>
            </span>

          </Divider>
          <div style="background-color: black;color:limegreen;" v-html="log.log_content"></div>
        </div>
      </div>
    </Card>
    </Col>
  </Row>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  components: {},
  name: 'CITaskStageLog',
  props: ['stageHistoryID'],
  data() {
    return {
      steplogs: [],
      showLoading: false
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyMainHeight - 163
    }
  },
  methods: {
    getStepLogs: function (stageHistoryID) {
      if (stageHistoryID > 0) {
        this.$axios.get('/api/ci/task/stage_history/' + stageHistoryID + '/logs').then(response => {
          this.steplogs = response.data.result
        }, response => {
        })
      }
    },

    loadStepLog: function (outputID) {
      for (let i = 0; i < this.steplogs.length; i++) {
        if (this.steplogs[i].id === outputID) {
          this.showLoading = true
          this.steplogs[i].log_content = ''
          break
        }
      }
      this.$axios.get('/api/ci/task/output/' + outputID + '/log').then(response => {
        for (let i = 0; i < this.steplogs.length; i++) {
          if (this.steplogs[i].id === outputID) {
            this.steplogs[i].log_content = response.data.result.log_content
            this.steplogs[i].show_content = false
            this.showLoading = false
            break
          }
        }
      }, response => {

      })
    }
  },
  created: function () {
    this.steplogs = []
    this.showLoading = false
    this.getStepLogs(this.stageHistoryID)
  },

  watch: {
    stageHistoryID: function (value) {
      this.steplogs = []
      this.showLoading = false
      this.getStepLogs(value)
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
