<template>
  <div @mouseover="mouseOnCiTask(item)" @mouseout="mouseOutCiTask">
    <Card :padding="0" class="citask-card" v-if="item.Display" :bordered="false" style="border-radius: 0px !important;">
      <div style="height: 5px;">
        <div v-if="mouserOnTaskItemID === 0" :style="'height: 2px;background-color: ' + item.TaskTagInfo.Color"></div>
        <div v-if="mouserOnTaskItemID === item.id" :style="'height: 4px;background-color: ' + item.TaskTagInfo.Color">
        </div>
      </div>
      <div class="citask-card-title">#{{ item.id }}
        <router-link style="color: #5578aa;text-decoration:underline;" :to="'/ci/task/' + item.id + '/history'">
          {{ item.TaskName }}
        </router-link>
        <span class="pull-right" style="padding-right: 10px">
          <span v-if="mouserOnTaskItemID === item.id">
            <span :style="'font-size: 12px; padding:3px 5px; border: 0px solid ' + item.TaskTagInfo.Color">
              <Icon type="ios-pricetag" :color="item.TaskTagInfo.Color" />
              {{ item.TaskTagInfo.Name }}
            </span>
          </span>
          <span style="cursor: pointer;" @click="openConfigPanel">
            <Icon type="ios-settings-outline" :size="20" />
          </span>
        </span>
      </div>
      <div class="citask-card-body">
        <span style="width: 220px;display: inline-block;text-align: left"></span>
      </div>
      <div class="citask-card-footer">
        <span style="width: 220px;display: inline-block;text-align: left">
          <div style="margin-left: 0px;color: #5578aa;">
            <Icon v-if="item.LastRunStatus === 0" type="ios-time-outline" style="color:#337ab7;"></Icon>
            <Icon v-if="item.LastRunStatus === 1" type="ios-bicycle" style="color:#388e83"></Icon>
            <Icon v-if="item.LastRunStatus === 2" type="ios-checkmark-circle" style="color:#388e83"></Icon>
            <Icon v-if="item.LastRunStatus === 4" type="ios-close-circle" style="color:darkred"></Icon>
            <Icon v-if="item.LastRunStatus === 3" type="ios-remove-circle" style="color:olive"></Icon>
            {{ item.LastRunTime }}
          </div>
        </span>
        <span style="width:80px;display: inline-block;text-align: center">
          <Dropdown v-if="item.ParameterGroups.length > 0" trigger="click" @on-click="onDPItemClick">
            <Button type="default" shape="circle" style="padding: 3px 15px;">构建
              <Icon type="md-arrow-dropdown" />
            </Button>
            <DropdownMenu slot="list" style="max-height: 500px;overflow-y: scroll;top:0px;">
              <DropdownItem v-for="dpitem in item.ParameterGroups" :key="dpitem.id" :name="dpitem.id + ',' + item.id">
                <Icon v-if="dpitem.default" type="checkmark-round"></Icon> {{ dpitem.title }}
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <Button v-if="item.ParameterGroups.length === 0" type="default" shape="circle" @click="onBuildClick(item.id)"
            style="padding: 3px 15px;">构建</Button>
        </span>
      </div>
    </Card>
    <Drawer title="CITask任务配置" width="80" :inner="false" :styles="styles" :transfer="true" :mask="true" :closable="true"
      v-model="showTaskConfig">
      <div slot="header">
        <span style="margin-right: 10px; font-size: 16px; font-weight: bold">{{ item.TaskName }} 任务配置</span>
      </div>
      <ci-task-stage :taskBasicInfo="item" :taskID="item.id"></ci-task-stage>
    </Drawer>
    <Modal v-model="showParameterDialog" title="构建参数" :mask-closable="false"
      @on-ok="startBuildTask(selectedParameterGroup.id)" @on-cancel="closeParameterDialog">
      <Form ref="resloveIssue" :model="formItem" :label-width="80">
        <FormItem v-for="parameter in selectedParameterGroup.parameters" :key="parameter.key" :label="parameter.key"
          prop="ResloveResult">
          <Input :name="parameter.key" :value="parameter.value" :desc="parameter.description"
            placeholder="任务流名称50个字符以内！" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
import CiTaskStage from './CITaskStage.vue'

export default {
  name: 'ciTaskItem',
  props: ['item'],
  data() {
    return {
      styles: {
        background: '#f5f7f9'
      },
      showParameterDialog: false,
      formItem: {},
      showTaskConfig: false,
      selectedParameterGroup: {},
      mouserOnTaskItemID: 0
    }
  },
  methods: {
    onDPItemClick: function (itemID) {
      this.showParameterDialog = true
      let taskStartParameters = itemID.split(',')
      this.selectedParameterGroup = this.getParameterGroup(taskStartParameters[0])
    },

    getParameterGroup: function (parameterID) {
      let result = {}
      for (let i = 0; i < this.item.ParameterGroups.length; i++) {
        if (this.item.ParameterGroups[i].id === parameterID) {
          result = this.item.ParameterGroups[i]
          break
        }
      }
      return result
    },

    mouseOnCiTask: function (item) {
      this.mouserOnTaskItemID = item.id
    },

    mouseOutCiTask: function () {
      this.mouserOnTaskItemID = 0
    },

    closeParameterDialog: function () {
      this.showParameterDialog = false
    },

    startBuildTask(parameterID) {
      let parameters = { parameters: [] }
      for (let i = 0; i < this.$refs['resloveIssue'].$el.length; i++) {
        let temp = {}
        temp['key'] = this.$refs['resloveIssue'].$el[i].name
        temp['value'] = this.$refs['resloveIssue'].$el[i].value
        temp['description'] = this.$refs['resloveIssue'].$el[i].desc
        parameters.parameters.push(temp)
      }

      this.$axios.patch('/api/ci/task/parameter_group/' + parameterID + '/', parameters).then(response => {
        this.$axios.get('/api/ci/task/' + this.item.id + '/start/?BuildParameter=' + parameterID).then(response => {
          this.$Message.success({
            content: response.data.result.message,
            duration: 10
          })
        }, response => {
          this.$Message.error({
            content: response.data.result.message,
            duration: 10
          })
        })
      }, response => {
        this.$Message.error({
          content: response.data.result.message,
          duration: 10
        })
      })
      this.showParameterDialog = false
    },

    openConfigPanel: function () {
      this.showTaskConfig = true
    },

    onBuildClick(taskID) {
      this.$axios.get('/api/ci/task/' + taskID + '/start/').then(response => {
        this.$Message.success({
          content: response.data.result.message,
          duration: 10
        })
      }, response => {
        this.$Message.error({
          content: response.data.result.message,
          duration: 10
        })
      })
    },
  },

  created: function () {
  },
  watch: {
    item: function (value) {
    }
  },
  components: {
    CiTaskStage
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.citask-card {
  width: 350px;
  min-height: 100px;
  float: left;
  margin-left: 10px;
  margin-top: 10px;
}

.citask-card-title {
  margin-right: -16px;
  margin-top: -10px;
  padding: 16px;
  padding-left: 15px;
  height: 46px;
  text-align: left;
  border-bottom: 1px solid #f5f7f9
}

.citask-card-body {
  padding: 16px 16px 5px 16px;

}

.citask-card-footer {
  padding: 0px 16px 10px 16px;
  height: 40px;
}
</style>
