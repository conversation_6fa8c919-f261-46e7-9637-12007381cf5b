<template>
  <div style="padding: 10px;">
    <Row style="margin-bottom: 20px;">
      <Col :span="stepIndex">
      <Steps :current="currentStep">
        <Step>
          <span slot="icon" style="background-color:rgb(245, 247, 249);">
            <Icon type="ios-arrow-dropright-circle" color="black" />
          </span>
          <span style="background-color:rgb(245, 247, 249);" slot="title">基本信息</span>
        </Step>
        <Step class="cursor-hand" v-for="step in stageList" v-if="step.stage_order_index !== 0" :key="step.id"
          :title="step.stage_title" icon="ios-arrow-dropright-circle"
          @click.native="clickStep(step.id, step.stage_order_index)"></Step>
      </Steps>
      </Col>
      <Col :span="24 - stepIndex">
      <div @click="addStage" style="float: left; margin-left: 15px;cursor:pointer ;">
        <Icon :size="30" type="ios-add-circle-outline" />
      </div>
      </Col>
    </Row>
    <Row>
      <Col ref="goodShow" span="24" class="goodShow"
        :style="'height:' + taskConfigContainerHeight + 'px; overflow-y:scroll;'">
      <div style="width: 90%; margin-left: auto;margin-right: auto;">
        <Card style="margin-bottom: 16px;" :bordered="false" dis-hover shadow>
          <div slot="title">基本信息</div>
          <div>
            <Form ref="ciTaskBasicCreate" :model="formItem" :label-width="120" :rules="ruleCustom">
              <FormItem label="任务名称" prop="TaskName">
                <Input v-model="formItem.TaskName" placeholder="任务名称50个字符以内！" />
              </FormItem>
              <FormItem label="项目" prop="Project">
                <Select v-model="formItem.Project" placeholder="请选择项目">
                  <Option v-for="project in projects" :key="project.id" :value="project.id" :label="project.PBTitle">
                    <span style="margin-right: 10px;">
                      <Avatar shape="square" :src="project.PBAvatar" />
                    </span>
                    <span>{{ project.PBTitle }}</span>
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="构建记录保留个数" prop="HistoryCleanStrategy">
                <Input v-model="formItem.HistoryCleanStrategy" placeholder="默认10个" />
              </FormItem>
              <FormItem label="分类标签" prop="TagID">
                <Select v-model="formItem.TagID" placeholder="任务分类标签">
                  <Option v-for="tag in taskTags" :key="tag.id" :value="tag.id" :label="tag.TagName">
                    <Tag :color="tag.TagColor"></Tag>
                    <span>{{ tag.TagName }}</span>
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="任务描述" prop="Description">
                <Input v-model="formItem.Description" placeholder="为任务提供一段简短描述！" />
              </FormItem>
              <FormItem label="Agent选择" prop="AgentFilter">
                <RadioGroup vertical v-model="formItem.AgentFilterType" @on-change="changeAgentFilterType">
                  <Radio :label="2" style="margin-bottom: 0px;">
                    <Icon type="social-apple"></Icon>
                    <span style="display: inline-block;">Agent标签
                    </span>
                  </Radio>
                  <Select style="margin-left: 25px;margin-bottom: 10px;" multiple v-model="formItem.AgentTags"
                    placeholder="Agent标签">
                    <Option v-for="tag in agentTags" :key="tag.id" :value="tag.id">{{ tag.TagName }}</Option>
                  </Select>
                  <Radio :label="1">
                    <Icon type="social-android"></Icon>
                    <span>指定机器</span>
                  </Radio>
                  <Select style="margin-left: 25px;" v-model="formItem.AgentID" placeholder="指定执行机器">
                    <Option v-for="agent in agents" :key="agent.id" :value="agent.id">{{ agent.Name }}</Option>
                  </Select>
                </RadioGroup>
              </FormItem>
              <FormItem label="定时触发" prop="TaskTrigger">
                <Input style="width:300px" v-model="formItem.Schedule" placeholder="00:09:12/**:09:**" />
                <Poptip content="00:09:12/**:09:00 分别表示每天9点12分执行，每隔9分钟执行一次">
                  <Icon type="ios-help-circle" :size="20" color="#5578aa" style="cursor: pointer;" />
                </Poptip>
              </FormItem>
              <FormItem label="执行策略" prop="ExecuteStrategy">
                <Select v-model="formItem.ExecuteStrategy" placeholder="默认步骤失败，中断任务执行">
                  <Option :value="1">步骤失败，结束任务执行</Option>
                  <Option :value="2">步骤失败，继续任务执行</Option>
                </Select>
              </FormItem>
              <FormItem label="报告推送" prop="channelID">
                <Select v-model="formItem.msg_channel_ids" multiple>
                  <Option v-for="item in messageChannelList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>
              <FormItem label="报告推送通过率" prop="passingRate">
                <Slider v-model="formItem.passing_rate" :step="5" show-stops :marks="marks" :min="60"></Slider>
              </FormItem>
            </Form>
          </div>
        </Card>
        <Card v-for="stage in stageList" :key="stage.stage_order_index" :ref="stage.id" style="margin-bottom: 16px;"
          :bordered="false" dis-hover shadow>
          <div slot="title">
            <div>
              <label-editor-input @updateValue="updateStageTitle" placeHolder="阶段标题" :id="stage.id" :displayWidth="200"
                :displayText="stage.stage_title"></label-editor-input>
            </div>
          </div>
          <div slot="extra">
            <Dropdown trigger="click" transfer style="margin-right: 10px;" @on-click="addStepPlugin">
              <Button shape="circle" size="small">
                <Icon type="ios-add" size="18" />步骤
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem :name="stage.id + ':' + stepPlugin.id" v-for="stepPlugin in stepPlugins"
                  :key="stepPlugin.id">
                  <Tag :color="stepPlugin.PluginLabelColor"></Tag>{{ stepPlugin.PluginName }}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
            <i-switch v-model="stage.is_on">
              <span slot="open">on</span>
              <span slot="close">off</span>
            </i-switch>
            <span @click="removeStage(stage.id)" class="cursor-hand">
              <Icon type="ios-close" :size="20"></Icon>
            </span>
          </div>
          <div>
            <draggable :options="{ group: stage.id }" v-model="stage.steps" :id="stage.id" @end="onEnd">
              <transition-group class="dragable-area" :key="1" :id="stage.id">
                <div v-for="step in stage.steps" :key="step.id">
                  <Card v-if="step.step_config.step_id === 1" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="请输入步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">仓库地址:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.repository_url" type="text" placeholder="代码仓库" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">本地地址:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.local_directory" type="text" placeholder="本地目录" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">Checkout策略:</Col>
                        <Col span="21" offset="-4">
                        <Select v-model="step.step_config.checkout_stragegy" placeholder="代码检出策略">
                          <Option value="1" label="Always check out a fresh copy">
                          </Option>
                          <Option value="2" label="Use svn update as much as possible">
                          </Option>
                        </Select>
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">用户名:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.user" type="text" placeholder="代码库用户" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">密码:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.password" type="password" placeholder="代码仓库密码" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 2" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}
                      </span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="请输入步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">仓库地址:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.repository_url" type="text" placeholder="代码仓库" />
                        </Col>
                      </Row>

                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">分支/Tag:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.branch" type="text"
                          placeholder="留空默认master;分支origin/branch;非空且不包含'/'表示tag" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">Checkout策略:</Col>
                        <Col span="21" offset="-4">
                        <Select v-model="step.step_config.checkout_stragegy" placeholder="代码检出策略">
                          <Option value="1" label="Wipe out & shallow clone (无变更日志,耗时短)">
                          </Option>
                          <Option value="2" label="Wipe out & full clone (有变更日志,耗时长)">
                          </Option>
                        </Select>
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">本地地址:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.local_directory" type="text" placeholder="本地目录" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">用户名:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.user" type="text" placeholder="代码库用户" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">密码:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.password" type="password" placeholder="代码仓库密码" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 3" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">命令行</Col>
                        <Col span="21" offset="-4">
                        <Input @on-focus="onFocus" v-model="step.step_config.command_line" type="textarea"
                          :rows="rowLength" placeholder="脚本内容" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">上传文件</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.upload_file_path" type="text"
                          placeholder="上传文件路径，多个文件以逗号隔开。支持通配符上传。" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 4" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>
                        {{ step.step_config.step_name }}
                      </span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">AppName <span
                          style="color:red;">*</span></Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.app_name" type="text" :rows="rowLength"
                          placeholder="IOS app name(扫码安装必填)" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">AppID
                        <span style="color:red;">*</span></Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.app_id" type="text" :rows="rowLength"
                          placeholder="IOS app id(扫码安装必填)" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">Bundle-Version <span
                          style="color:red;">*</span></Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.bundle_version" type="text" :rows="rowLength"
                          placeholder="IOS bundle version(扫码安装必填)" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">构建命令</Col>
                        <Col span="21" offset="-4">
                        <Input @on-focus="onFocus" v-model="step.step_config.command_line" type="textarea"
                          :rows="rowLength" placeholder="构建命令行" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">输出目录</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.upload_file_path" type="text"
                          placeholder="构建产物输出路径，多个文件以逗号隔开。支持通配符上传。" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 5" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">构建命令行</Col>
                        <Col span="21" offset="-4">
                        <Input @on-focus="onFocus" v-model="step.step_config.command_line" type="textarea"
                          :rows="rowLength" placeholder="脚本内容" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">输出目录</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.upload_file_path" type="text"
                          placeholder="上传文件路径，多个文件以逗号隔开。支持通配符上传。" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 6" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">用例</Col>
                        <Col span="21" offset="-4">
                        <Select v-model="step.step_config.autocase_filter" multiple placeholder="选择用例集合">
                          <Option v-for="tag in caseTags" :key="tag.id" :value="tag.id">{{ tag.TagName }}({{ tag.TagDesc
                            }})</Option>
                        </Select>
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">项目目录:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.project_root_dir" type="text" placeholder="本地目录" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">上传文件</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.upload_file_path" type="text"
                          placeholder="上传文件路径，多个文件以逗号隔开。支持通配符上传。" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 7" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">用例</Col>
                        <Col span="21" offset="-4">
                        <Select v-model="step.step_config.autocase_filter" multiple placeholder="选择用例集合">
                          <Option v-for="tag in caseTags" :key="tag.id" :value="tag.id">{{ tag.TagName }}({{ tag.TagDesc
                            }})</Option>
                        </Select>
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">项目目录:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.project_root_dir" type="text" placeholder="本地目录" />
                        </Col>
                      </Row>

                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">上传文件</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.upload_file_path" type="text"
                          placeholder="上传文件路径，多个文件以逗号隔开。支持通配符上传。" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 8" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>
                        {{ step.step_config.step_name }}
                      </span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">服务器IP:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.target_server" type="text" placeholder="*.*.*.*" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">SSH 端口:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.ssh_port" type="text" placeholder="默认22" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">User:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.user" type="text" placeholder="远程登录用户" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">Password:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.password" type="password" placeholder="远程用户密码" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">SSHKey:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.ssh_key" type="textarea" placeholder="待推送文件本地相对路径" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">远程服务器目录:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.target_dir" type="text" placeholder="文件远程目录" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">命令行</Col>
                        <Col span="21" offset="-4">
                        <Input @on-focus="onFocus" v-model="step.step_config.command_line" type="textarea"
                          :rows="rowLength" placeholder="脚本内容" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                  <Card v-if="step.step_config.step_id === 9" style="margin-bottom: 16px;" dis-hover>
                    <div slot="title">
                      <span>
                        <Icon type="ios-navigate-outline"></Icon>
                      </span>
                      <span>{{ step.step_config.step_name }}</span>
                    </div>
                    <div slot="extra">
                      <i-switch v-model="step.is_on">
                        <span slot="open">on</span>
                        <span slot="close">off</span>
                      </i-switch>
                      <span class="cursor-hand" @click="removeStep(step.id, stage.id)">
                        <Icon type="ios-close" :size="20"></Icon>
                      </span>
                    </div>
                    <div>
                      <Row style="margin-bottom: 16px;">
                        <Col span="3" style="text-align: right;padding-right: 16px; ">
                        <span>用途</span>
                        </Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.purpose_name" placeholder="步骤用途" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">命令行:</Col>
                        <Col span="21" offset="-4">
                        <Input @on-focus="onFocus" v-model="step.step_config.command_line" type="textarea"
                          :rows="rowLength" placeholder="pytest 执行命令" />
                        </Col>
                      </Row>
                      <Row style="margin-bottom: 16px;">
                        <Col style="text-align: right;padding-right: 16px;" span="3">执行结果目录:</Col>
                        <Col span="21" offset="-4">
                        <Input v-model="step.step_config.pytest_result_file_dir" type="text"
                          placeholder="pytest执行结果文件目录(Allure格式)" />
                        </Col>
                      </Row>
                    </div>
                  </Card>
                </div>
              </transition-group>
            </draggable>
          </div>
        </Card>
      </div>
      </Col>
    </Row>
    <div style="position: fixed; bottom: 10px; left: 60%;">
      <span @click="saveTaskConfig('ciTaskBasicCreate')">
        <Icon class="cursor-hand" type="ios-checkmark-circle" color="#32be77" size="46" />
      </span>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { mapState, mapGetters, mapMutations } from 'vuex'
import { ciTaskValidateRules } from './CITaskCreateDialog'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import { stringify } from '@progress/kendo-ui/js/kendo.core'

export default {
  name: 'CITaskStage',
  props: ['taskBasicInfo', 'taskID'],
  data() {
    return {
      projects: [],
      agents: [],
      stepPlugins: [],
      agentTags: [],
      taskTags: [],
      caseTags: [],
      formItem: {
      },
      ruleCustom: {
        ...ciTaskValidateRules
      },
      stageList: [],
      currentStep: 0,
      rowLength: 4,
      tagLeft: '100px;',
      showParameterGroupConfig: '',
      newStage: {
        task_id: 0,
        stage_title: '新建阶段',
        stage_order_index: 1,
        is_on: true,
        svn_steps: [],
        git_steps: [],
        command_steps: [],
        ios_build_steps: [],
        android_build_steps: [],
        gat_apitest_steps: [],
        gat_uitest_steps: [],
        ssh_steps: []
      },
      stepDatas: {},
      messageChannelList: [],
      marks: {
        50: '50%',
        60: '60%',
        70: '70%',
        80: {
          style: {
            color: '#ff0000'
          },
          label: this.$createElement('strong', '80%')
        },
        90: '90%',
        100: '100%'
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },

    taskConfigContainerHeight: function () {
      return this.appBodyMainHeight - 100
    },

    stepIndex: function () {
      if (this.stageList.length < 3) {
        return 10
      }
      else {
        if ((this.stageList.length * 2 + 10) >= 24) {
          return 23
        } else {
          return this.stageList.length * 2 + 10
        }
      }
    }

  },
  methods: {

    ...mapMutations('citask', ['setCITaskConfigChanged']),

    onFocus: function () {
      let index = this.showParameterGroupConfig.split(/\r?\n/).length
      if (index > 4) {
        this.rowLength = index
      }
    },
    clickStep: function (stageID, stageIndex) {
      this.currentStep = stageIndex
      this.$refs.goodShow.$el.scrollTop = this.$refs[stageID][0].$el.offsetTop
    },

    getAgents: function () {
      this.$axios.get('/api/common/agents').then(response => {
        this.agents = response.data.result
      }, response => {

      })
    },

    changeAgentFilterType: function (value) {
      if (value === 1) {
        this.formItem.AgentTags = []
      }
      if (value === 2) {
        this.formItem.AgentID = 0
      }
      // console.log(this.formItem)
    },

    addStage: function () {
      let nextIndex = 1
      let tempStage = JSON.parse(JSON.stringify(this.newStage))
      if (this.stageList.length !== 0) {
        nextIndex = this.stageList[this.stageList.length - 1].stage_order_index + 1
      }
      tempStage.stage_order_index = nextIndex
      tempStage.task_id = this.taskID
      this.$axios.post('/api/ci/task/' + this.taskID + '/stages', tempStage).then(response => {
        this.stageList.push(response.data.result)
      }, response => {
      })
      this.currentStep = nextIndex
    },

    addStepPlugin: function (stepInfo) {
      let stepPluginInfo = stepInfo.split(':')
      let tempStep = JSON.parse(JSON.stringify(this.stepDatas[stepPluginInfo[1]]))
      let targetStage = null
      for (let i = 0; i < this.stageList.length; i++) {
        if (this.stageList[i].id === stepPluginInfo[0]) {
          targetStage = this.stageList[i]
        }
      }
      tempStep.step_order_index = this.getStepOrderIndex(targetStage.steps)
      tempStep.stage_id = stepPluginInfo[0]
      this.$axios.post('/api/ci/task/task_stage/' + stepPluginInfo[0] + '/steps', tempStep).then(response => {
        targetStage.steps.push(response.data.result)
        this.setCITaskConfigChanged(true)
      }, response => {
        this.$Message.error({
          content: '添加构建步骤失败',
          duration: 10,
          closable: true
        })
      })
    },

    removeStep: function (stepID, stageID) {
      this.$axios.delete('/api/ci/task/task_step/' + stepID).then(response => {
        this.setCITaskConfigChanged(true)
        for (let i = 0; i < this.stageList.length; i++) {
          if (this.stageList[i].id === stageID) {
            let targetStage = this.stageList[i]
            for (let j = 0; j < targetStage.steps.length; j++) {
              let tempStep = targetStage.steps[j]
              if (tempStep.id === stepID) {
                targetStage.steps.splice(j, 1)
              }
            }
          }
        }
      }, response => {
        this.$Message.error({
          content: '删除构建步骤失败',
          duration: 10,
          closable: true
        })
      })
    },

    getStepOrderIndex: function (steps) {
      if (steps.length === 0) {
        return 1
      }
      else {
        return steps[steps.length - 1].step_order_index + 1
      }
    },

    findPluginData: function (pluginID, steps) {
      let result = {}
      for (let i = 0; i < steps.length; i++) {
        if (steps[i].id === pluginID) {
          result = steps[i]
          break
        }
      }
      return result
    },

    saveTaskConfig: function (name) {
      this.saveTaskBasic(name)
      this.saveRunConfig()
    },

    getTaskConfig: function () {
      this.$axios.get('/api/ci/task/' + this.taskID + '/config').then(response => {
        this.stageList = response.data.result.config_stages
      }, response => {
      })
    },

    onEnd: function (arg) {
      let stageID = arg.from.id
      for (let i = 0; i < this.stageList.length; i++) {
        if (stageID === this.stageList[i].id) {
          for (let j = 0; j < this.stageList[i].steps.length; j++) {
            this.stageList[i].steps[j].step_order_index = j + 1
          }
          break
        }
      }
    },

    removeStage: function (stageID) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '点击确定删除构建阶段。',
        onOk: () => {
          for (let i = 0; i < this.stageList.length; i++) {
            if (this.stageList[i].id === stageID) {
              this.stageList.splice(i, 1)
              break
            }
          }
          this.$axios.delete('/api/ci/task/task_stage/' + stageID).then(response => {
            this.setCITaskConfigChanged(true)
            this.$Message.success({
              content: '阶段已经成功移除',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '移除构建阶段失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {
        }
      })
    },

    getStepTemplates: function () {
      this.$axios.get('/api/ci/task/steps').then(response => {
        this.stepDatas = response.data.result
      }, response => {
      })
    },

    saveTaskBasic: function (name) {
      this.formItem.msg_channel = this.formItem.msg_channel_ids.join(',');
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.patch('/api/ci/task/' + this.taskID + '/', this.formItem).then(response => {
            this.$Message.success({
              content: '配置保存成功',
              duration: 6
            })
          }, response => {
            this.$Message.error({
              content: '配置保存失败，请联系管理员或者重试',
              duration: 6
            })
          })
        }
      })
    },

    saveRunConfig: function () {
      let parameters = { config_stages: this.stageList }
      this.$axios.post('/api/ci/task/' + this.taskID + '/config/', parameters).then(response => {
      }, response => {
      })
    },

    updateStageTitle: function (value, id) {
      let parameters = { stage_title: value }
      if (value.trim !== '') {
        this.$axios.patch('/api/ci/task/task_stage/' + id + '/', parameters).then(response => {
          for (let i = 0; i < this.stageList.length; i++) {
            if (this.stageList[i].id === id) {
              this.stageList[i].stage_title = value
              break
            }
          }
          this.$Message.success({
            content: '阶段名称更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '阶段名称更新失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    loadMyProject: function () {
      this.$axios.get('/api/project/list?extinfo=0&home=1').then(response => {
        this.projects = response.data.result
      }, response => {
        // error callback
      })
    },

    getAgentTags: function () {
      this.$axios.get('/api/project/tags?TagType__in=1,3').then(response => {
        this.agentTags = response.data.result
      }, response => {

      })
    },

    getTaskTags: function () {
      this.$axios.get('/api/project/tags?TagType__in=1,2').then(response => {
        this.taskTags = response.data.result
      }, response => {

      })
    },

    getTaskPlugins: function () {
      this.$axios.get('/api/ci/task/plugins').then(response => {
        this.stepPlugins = response.data.result
      }, response => {

      })
    },

    getCaseTags: function () {
      this.$axios.get('/api/ci/case_tags?TagType=1').then(response => {
        let all = { id: 0, TagName: 'All', TagDesc: '全部用例' }
        this.caseTags.push(all)
        this.caseTags.push(...response.data.result)
      }, response => {

      })
    },

    getMessageChannelList() {
      this.$axios.get('/api/message/channel/list').then(response => {
        this.messageChannelList = response.data.result
      }
      )
    },
  },

  created: function () {
    this.loadMyProject()
    this.formItem = this.taskBasicInfo
    this.getAgents()
    this.getTaskTags()
    this.getAgentTags()
    this.getTaskConfig()
    this.getStepTemplates()
    this.getTaskPlugins()
    this.getTaskPlugins()
    this.getCaseTags()
    this.getMessageChannelList()
  },

  watch: {
    taskBasicInfo: function (value) {
      this.formItem = value
    }
  },
  components: {
    draggable,
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.goodShow {
  overflow-y: scroll;
  padding-top: 10px;
}
</style>