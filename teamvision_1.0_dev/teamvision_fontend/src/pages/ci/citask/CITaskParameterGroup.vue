<template>
  <div style="padding-top:16px;">
    <div style="width: 60%;margin-left: auto;margin-right: auto; margin-bottom: 16px;">
      <Input v-model="formData.group_name" search enter-button="添加" @on-search="createGroup" icon="ios-clock-outline"
        :maxlength="50" placeholder="输入参数组名称，回车创建参数组" style="width: 100%" />
    </div>
    <Card v-for="group in parameterGroupList" :padding="10" :key="group.id" :bordered="false" dis-hover
      class="member-card cursor-hand">
      <Row>
        <Col :xl="20" :lg="20" :md="18" :sm="18">
        <span @click="showGroupDetail(group.id)" style="font-weight: bold; text-decoration: underline;">
          {{ group.group_name }}
        </span>
        <span v-if="group.is_default" style="padding-left: 10px;">
          <Tag color="#202d40" class="member-tag">默认</Tag>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="6" :sm="6">
        <span>
          <Dropdown transfer @on-click="processGroupOperation">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem :name="group.id + ':' + group.group_name + ':' + 'delete'">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
              <DropdownItem :name="group.id + ':' + group.group_name + ':' + 'copy'">
                <Icon type="ios-copy" />
                <span>复制</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'

export default {
  name: 'CITaskParameterGroup',
  props: ['parameterGroups', 'taskID'],
  data() {
    return {
      parameterGroupList: [],
      formData: {
        task_id: 0,
        group_name: '',
        is_default: false,
        parameters: [],
        step_plugin_settings: [],
        description: ''
      }
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),

    createGroup: function () {
      this.formData.task_id = this.taskID
      if (this.formData.group_name.trim() !== '') {
        this.$axios.post('/api/ci/task/' + this.taskID + '/parameter_groups/', this.formData).then(response => {
          this.parameterGroupList.push(response.data.result)
          this.$Message.success({
            content: '参数组添加成功',
            duration: 10,
            closable: true
          })
          this.formData.group_name = ''
        }, response => {
          this.$Message.error({
            content: '参数组添加失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    processGroupOperation: function (value) {
      let groupInfo = value.split(':')
      if (groupInfo[2] === 'delete') {
        this.deleteGroup(groupInfo[0], groupInfo[1])
      }
      if (groupInfo[2] === 'copy') {
        this.copyGroup(groupInfo[0])
      }
    },

    copyGroup: function (groupID) {
      this.$axios.post('/api/ci/task/parameter_group/' + groupID + '/copy').then(response => {
        this.parameterGroupList.push(response.data.result)
        this.$Message.success({
          content: '参数组复制成功',
          duration: 10,
          closable: true
        })
        this.formData.group_name = ''
      }, response => {
        this.$Message.error({
          content: '参数组复制失败',
          duration: 10,
          closable: true
        })
      })

    },

    deleteGroup: function (groupID, groupName) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除参数组[' + groupName + ']',
        onOk: () => {
          this.$axios.delete('/api/ci/task/parameter_group/' + groupID + '/').then(response => {
            for (let i = 0; i < this.parameterGroupList.length; i++) {
              if (this.parameterGroupList[i].id === groupID) {
                this.parameterGroupList.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '参数组删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '参数组删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })

    },

    showGroupDetail: function (value) {
      this.$emit('showGroup', value)
    }

  },

  created: function () {
    this.parameterGroupList = this.parameterGroups
  },

  mounted: function () {
  },

  watch: {
    parameterGroups: function () {
      this.parameterGroupList = this.parameterGroups
    },
    taskID: function (value) {
      //console.log(value)
    }
  },

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 50px;
  margin-bottom: 2px;
}

.member-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
}</style>
