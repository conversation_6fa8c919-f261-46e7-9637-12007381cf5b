<template>
  <div style="">
    <Modal :value="true" :closable="false" fullscreen footer-hide>
      <Row style="height: 40%">
        <Col>
        <div style="width: 80%;margin-left: auto;margin-right: auto;text-align: center;margin-top: 10%;">
          <div class="download-page-title">
            <p>
              TeamVision
            </p>
          </div>
          <div class="download-page-subtitle">
            <p>
              <small style="margin-left:30px">让工作更高效。</small>
            </p>
          </div>
        </div>
        </Col>
      </Row>
      <Row style="height: 35%">
        <Col>
        <div style="width: 80%;margin-left: auto;margin-right: auto;text-align: center;">
          <img src="../../../assets/teamvision/global/images/logo.jpg"
            style="width: 50px;height: 50px; border-radius: 20px;" />
        </div>
        </Col>
      </Row>
      <Row>
        <Col style="width: 80%;margin-left: auto;margin-right: auto;text-align: center;">
        <Button v-if="outputData.ProductType === 2 || outputData.ProductType === 3" class="cursor-hand" type="primary"
          size="default" shape="circle">
          <a :href="downloadURL">
            <Icon type="ios-cloud-download"></Icon>安装
          </a>
        </Button>
        <Button v-if="outputData.ProductType === 6" class="cursor-hand" type="primary" size="default" shape="circle"><a
            :href="downloadURL">
            <Icon type="ios-cloud-download"></Icon>下载
          </a></Button>
        </Col>
      </Row>
    </Modal>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { mapGetters, mapMutations } from 'vuex'

export default {
  name: 'IssueAttachmentsMobileUpload',
  props: ['outputID'],
  data() {
    return {
      outputData: {},
      downloadURL: ''
    }
  },
  computed: {
  },
  methods:
  {
    ...mapMutations('issue', ['setIssueChange', 'setShowIssueDetail', 'setSelectIssueID']),
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setProject']),
    getOutputInfo: function (outputID) {

      this.$axios.get('/api/ci/task/output/' + outputID + '/').then(response => {
        this.outputData = response.data.result
        this.$axios.put('/api/ci/task/output/' + outputID + '/prepare').then(response => {
          this.downloadURL = response.data.result.package_url
        }, response => {
        })
      }, response => {
      })
    }
  },
  created: function () {
    this.getOutputInfo(this.outputID)
  },
  mounted: function () {
  },
  watch: {
    outputID: function (value) {
      this.getOutputInfo(value)
    }
  },

  components: {
    draggable
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.download-page-title {
  font-family: 'Dotum' !important;
  font-size: 2.5rem;
  opacity: .4;
  font-weight: 400;
  text-align: center;
  color: #323A45;
}

.download-page-subtitle {
  font-family: '幼圆' !important;
  font-size: 25px !important;
  margin-top: 25px;
  margin-bottom: 25px;
  display: block;
  font-weight: 100;
  text-align: center;
  letter-spacing: 12px;
  color: #323A45;
  opacity: 0.6 !important;
}

.download-page- {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;

}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.ivu-drawer-body {
  width: 100%;
  height: calc(100% - 51px);
  padding: 0px !important;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  position: absolute;
  overflow: auto;
}
</style>
