<template>
  <div :style="'height:' + containerHeight + 'px; overflow-y:scroll;'">
    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover>
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" /> 通过率
        </div>
        <div id="stepPassrateTrend" style="height: 240px;"></div>
      </Card>
      </Col>
    </Row>
    <Card :bordered="false" dis-hover class="member-card cursor-hand">
      <div style="font-weight: 500;" slot="title">
        <Button @click="exportTestResult" shape="circle" icon="md-log-out" size="small"
          :loading="exporting">导出结果</Button>
      </div>
      <Row type="flex" justify="space-between">
        <Col span="5" style="padding: 20px; color: #00b0ff;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">Total:</div>
          <div class="summary-number-content">
            <span style="color: inherit" @click="filterCaseResult(0)">
              {{ testResult.Total }}
            </span>
          </div>
        </Card>
        </Col>
        <Col span="5" style="padding: 20px;color: forestgreen;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">Pass:</div>
          <div class="summary-number-content">
            <span style="color: inherit;text-decoration: underline" @click="filterCaseResult(3)">
              {{ testResult.Pass }}
            </span>
          </div>
        </Card>
        </Col>
        <Col span="5" style="padding: 20px;color:darkred;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">Fail:</div>
          <div class="summary-number-content">
            <span style="color: inherit;text-decoration: underline" @click="filterCaseResult(2)">
              {{ testResult.Fail }}
            </span>
          </div>
        </Card>
        </Col>
        <Col span="5" style="padding: 20px;color:orange;">
        <Card :padding="20" style="height: 100px;" dis-hover>
          <div class="summary-number-title">Aborted:</div>
          <div class="summary-number-content">
            <span style="color: inherit;text-decoration: underline" @click="filterCaseResult(1)">
              {{ testResult.Aborted }}
            </span>
          </div>
        </Card>
        </Col>
        <Col span="4" style="padding: 20px;color:orange;">
        <Card :padding="0" style="height: 100px;" dis-hover>
          <!--<div class="summary-number-title">异常分析:</div>-->
          <div class="summary-number-content">
            <div :id="failCategoryPie" style="color: inherit; height: 115px;">
            </div>
          </div>
        </Card>
        </Col>
      </Row>
    </Card>
    <Table border :columns="columns1" :data="caseResults.results" stripe :loading="loadingCaseResult"></Table>
    <div style="margin: 10px;overflow: hidden">
      <div style="float: right;">
        <Page :total="caseResults.count" :current="1" @on-change="changePage"></Page>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import HighCharts from 'highcharts'

export default {
  name: 'TestResultDetail',
  props: ['testResultID', 'stepID'],
  data() {
    return {
      testResult: {},
      caseResults: [],
      resultType: 0,
      passRateTrendData: {},
      exporting: false,
      columns1: [
        {
          title: 'ID',
          key: 'id',
          width: 100,
          sortable: true,
        },
        {
          title: 'CaseID',
          key: 'TestCaseID',
          width: 120,
          sortable: true,
        },
        {
          title: '描述',
          key: 'CaseDesc'
        },
        {
          title: '名称',
          key: 'TestCaseName'
        },
        {
          title: '结果',
          key: 'ResultFormat',
          width: 120,
          sortable: true,
          render: (h, params) => {
            const row = params.row;
            const color = row.Result === 2 ? 'error' : row.Result === 3 ? 'success' : 'error'

            return h('Tag', {
              props: {
                type: 'dot',
                color: color,
                size: 'small'
              }
            }, row.ResultFormat)
          }
        },
        {
          title: '异常分析',
          key: 'FailCategory'
        },
        {
          title: '异常信息',
          key: 'Exception'
        }
      ],
      id: 'testRunSummary',
      loadingCaseResult: false,
      failCategoryPie: 'failCategoryPie',
      pieChartOption: {
        chart: {
          type: 'line',
        },
        title: {
          text: ''
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: true
            },
            showInLegend: false
          }
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
        },
        series: [{
          name: '失败分析',
          data: [
          ]
        }]

      }

    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),

    containerHeight: function () {
      return this.appBodyMainHeight
    }

  },

  methods: {

    getTestResult: function (groupID) {
      if (groupID > 0) {
        this.$axios.get('/api/ci/auto_testing_result/' + groupID + '/').then(response => {
          this.testResult = response.data.result
        }, response => {
        })
      }
    },

    getCaseResults: function (groupID, resultType) {
      let resultFilter = ''
      if (resultType !== 0) {
        resultFilter = '&Result=' + resultType
      }
      this.loadingCaseResult = true
      this.$axios.get('/api/ci/auto_case_results?TaskResultID=' + groupID + resultFilter).then(response => {
        this.caseResults = response.data.result
        this.loadingCaseResult = false
      }, response => {

      })
    },

    getPassRateTrendData: function (step_id) {
      this.$axios.get('/api/ci/task/step/' + step_id + '/passrate_trend').then(response => {
        let option = this.createTrendLineChart(response.data.result)
        HighCharts.chart('stepPassrateTrend', option)
      }, response => {

      })
    },

    //生成趋势线图
    createTrendLineChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_title']
        },
        xAxis: {
          categories: data['xaxis']
        },
        yAxis: {
          title: {}
        },
        plotOptions: {
          line: {
            dataLabels: {
              enabled: true          // 开启数据标签
            },
            enableMouseTracking: true // 关闭鼠标跟踪，对应的提示框、点击事件会失效
          }
        },
        series: data['series_data']
      }
      return option
    },

    changePage: function (value) {
      let resultFilter = ''
      if (this.resultType !== 0) {
        resultFilter = '&Result=' + this.resultType
      }
      this.$axios.get('/api/ci/auto_case_results?TaskResultID=' + this.testResultID + '&page=' + value + resultFilter).then(response => {
        this.caseResults = response.data.result
      }, response => {

      })
    },

    filterCaseResult: function (value) {
      this.resultType = value
      this.getCaseResults(this.testResultID, value)
    },

    exportTestResult: function () {
      let realUrl = '/api/ci/task/testresult/export_case_result/' + this.testResultID
      this.exporting = true
      this.$axios({ url: realUrl, method: 'get', responseType: 'arraybuffer' }).then(response => {
        this.exporting = false
        let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/vnd.ms-excel' }))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', this.testResultID + '.xls')
        document.body.appendChild(link)
        link.click()
        link.remove()
      }, response => {
      })
    }

  },

  created: function () {
    this.caseResults = []
    this.getTestResult(this.testResultID)
    this.getCaseResults(this.testResultID, 0)
    this.getPassRateTrendData(this.stepID)
  },

  mounted: function () {
  },

  watch: {
    testResultID: function (value) {
      this.caseResults = []
      this.getTestResult(value)
      this.getCaseResults(value, 0)
      this.getPassRateTrendData(this.stepID)
    },
  },

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  margin-bottom: 10px;
}

.summary-number-title {
  text-align: left;
  margin-left: -10px;
  margin-top: -18px;
  font-size: 18px;
}

.summary-number-content {
  text-align: center;
  font-size: 30px;
  /*text-decoration: underline;*/
}

.content {
  padding-left: 5px;
}
</style>
