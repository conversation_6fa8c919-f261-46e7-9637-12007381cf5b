<template>
  <div>
    <Card class="citask-card" :bordered="false" :dis-hover="true">
      <span style="font-size: 16px; margin-left: 10px;">
        <span style="padding-right:50px; font-size: 18px; font-weight: 400;">
          <Dropdown @on-click="changeCiTask">
            <span style="color: #5578aa">
              任务名称：{{ taskBasicInfo.TaskName }}
              <Icon type="md-arrow-dropdown" />
            </span>
            <DropdownMenu slot="list" style="max-height: 600px; overflow-y: scroll; color:#5e5e5e;">
              <DropdownItem v-for="task in myCITask.items" :key="task.id" :name="task.id">
                <span style="color:#5578aa;"> {{ task.TaskName }}</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        <Button @click="startBuildTask" v-if="taskParameterGroups.length === 0" size="default" shape="circle"
          style="color: black; margin-right: 10px;">立即构建</Button>
        <Dropdown @on-click="showParameterGroupDialog" v-if="taskParameterGroups.length > 0"
          style="margin-left: 20px;color: black; margin-right: 10px;">
          <Button shape="circle">立即构建<Icon type="ios-arrow-down"></Icon></Button>
          <DropdownMenu slot="list">
            <DropdownItem :name="group.id" v-for="group in taskParameterGroups" :key="group.id">{{ group.group_name }}
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Divider type="vertical"></Divider>
        <ButtonGroup shape="circle" size="default" type="dashed">
          <Button @click="configTask" icon="ios-color-wand-outline">配置</Button>
          <Button @click="parameterGroup" icon="ios-sunny-outline">参数组</Button>
          <Button v-if="taskBasicInfo.Creator === userInfo.id" @click="removeTask(taskBasicInfo.TaskName)"
            icon="ios-crop" style="color:red;">删除</Button>
        </ButtonGroup>
      </span>
    </Card>
    <ci-task-history-list @showLogDetail="showLogDeail" @showTestResult="showTestresultDeail" :taskID="taskID">
    </ci-task-history-list>
    <div>
      <Drawer title="CITask任务配置" width="80" :inner="true" :styles="styles" :transfer="false" :mask="true"
        :closable="true" v-model="showTaskConfig">
        <div slot="header">
          <span style="margin-right: 10px; font-size: 16px; font-weight: bold">{{ taskBasicInfo.TaskName }} 任务配置</span>
        </div>
        <ci-task-stage :taskBasicInfo="taskBasicInfo" :taskID="taskID"></ci-task-stage>
      </Drawer>
      <Drawer title="CITask参数组" width="80" @on-close="groupListDrawerClose" :inner="true" :styles="styles"
        :transfer="false" :mask="true" :closable="true" v-model="showParameterGroup">
        <div slot="header">
          <span style="margin-right: 10px; font-size: 16px; font-weight: bold">{{ taskBasicInfo.TaskName }}参数组</span>
        </div>
        <c-i-task-parameter-group :parameterGroups="taskParameterGroups" :taskID="taskID" @showGroup="showGroup">
        </c-i-task-parameter-group>
      </Drawer>
      <Drawer width="60" :inner="true" :styles="styles" :transfer="false" :mask="false" :closable="true"
        v-model="showParameterGroupConfig">
        <div slot="header">
          <span style="margin-right: 10px; font-size: 16px; font-weight: bold">{{ selectedGroup.group_name }}</span>
        </div>
        <parameter-group-detail :parameterGroup="selectedGroup" @saveGroup="saveParameterGroup">
        </parameter-group-detail>
      </Drawer>
      <Drawer title="日志" width="60" :inner="true" :styles="styles" :transfer="false" :mask="true" :closable="true"
        v-model="showBuildLog">
        <div slot="header">
          <span style="margin-right: 10px; font-size: 16px; font-weight: bold">#{{ stageHistory.ViewFormat.BuildVersion
            }} {{ stageHistory.ViewFormat.StageName }}</span>
          <ButtonGroup shape="circle" size="small">
            <Button type="default" v-if="stageHistory.ViewFormat.PreviousStageHisory !== null"
              @click="switchStageLog(stageHistory.ViewFormat.PreviousStageHisory)">
              <Icon type="ios-arrow-back"></Icon>上一阶段
            </Button>
            <Button type="default" v-if="stageHistory.ViewFormat.NextStageHistory !== null"
              @click="switchStageLog(stageHistory.ViewFormat.NextStageHistory)">下一阶段<Icon type="ios-arrow-forward">
              </Icon>
            </Button>
          </ButtonGroup>
        </div>
        <ci-task-stage-log :stageHistoryID="stageHistoryID"></ci-task-stage-log>
      </Drawer>
      <Drawer width="70" :inner="true" :styles="styles" :transfer="false" :mask="true" :closable="true"
        v-model="showTestResult">
        <div slot="header">
          <span style="margin-right: 10px; font-size: 16px; font-weight: bold">#{{ stageHistory.ViewFormat.BuildVersion
            }} {{ stageHistory.ViewFormat.StageName }} {{ selectedTestResult.pusposeName }}</span>
        </div>
        <c-i-task-test-result-detail :testResultID="selectedTestResult.resultID" :stepID="selectedTestResult.step_id">
        </c-i-task-test-result-detail>
      </Drawer>
    </div>
    <Modal v-model="showParameterDialog" title="构建参数" :mask-closable="false"
      @on-ok="startBuildWithParameter(selectedParameterGroup.id)" @on-cancel="closeParameterDialog">
      <Form ref="parameterGroupDialog" :model="formItem" :label-width="80">
        <FormItem v-for="parameter in selectedParameterGroup.parameters" :key="parameter.key" :label="parameter.key"
          prop="ResloveResult">
          <Input :name="parameter.key" :value="parameter.value" :desc="parameter.description"
            placeholder="任务名称50个字符以内！" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import CiTaskStage from './CITaskStage.vue'
import CiTaskHistoryList from './CITaskHistoryList.vue'
import CiTaskStageLog from './CITaskStageLog.vue'
import CITaskParameterGroup from './CITaskParameterGroup.vue'
import ParameterGroupDetail from './CITaskParameterGroupDetail.vue'
import CITaskTestResultDetail from './CITaskTestResultDetail.vue'

export default {
  name: 'ciTaskHistory',
  props: ['taskID'],
  data() {
    return {
      styles: {
        background: '#f5f7f9'
      },
      myCITask: {
        count: 0,
        next: '',
        items: []
      },
      showTaskConfig: false,
      showParameterGroup: false,
      showParameterGroupConfig: false,
      showParameterDialog: false,
      showBuildLog: false,
      showTestResult: false,
      taskParameterGroups: [],
      selectedTestResult: { resultID: 0, pusposeName: '', step_id: '0' },
      selectedGroup: {},
      formItem: {},
      taskBasicInfo: {},
      selectedParameterGroup: {},
      taskConfigInfo: {},
      stageHistoryID: 0,
      stageHistory: {
        ViewFormat: { StageName: '' }
      }
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapState(['appBodyMainHeight']),

    containerHeight: function () {
      return this.appBodyMainHeight - 30
    },
  },
  methods: {
    ...mapMutations('citask', ['setCITaskStart']),

    showParameterGroupDialog: function (itemID) {
      this.showParameterDialog = true
      this.selectedParameterGroup = this.getParameterGroup(itemID)
    },

    loadCITaskList: function () {
      this.$axios.get('/api/ci/task/my?page_size=10000').then(response => {
        this.myCITask.count = response.data.result.count
        this.myCITask.next = response.data.result.next
        this.myCITask.items = response.data.result.results
      }, response => {
      })
    },

    getParameterGroup: function (parameterID) {
      let result = {}
      for (let i = 0; i < this.taskBasicInfo.ParameterGroups.length; i++) {
        if (this.taskBasicInfo.ParameterGroups[i].id === parameterID) {
          result = this.taskBasicInfo.ParameterGroups[i]
          break
        }
      }
      return result
    },

    closeParameterDialog: function () {
      this.showParameterDialog = false
    },

    startBuildWithParameter(parameterID) {
      let parameters = { parameters: [] }
      for (let i = 0; i < this.$refs['parameterGroupDialog'].$el.length; i++) {
        let temp = {}
        temp['key'] = this.$refs['parameterGroupDialog'].$el[i].name
        temp['value'] = this.$refs['parameterGroupDialog'].$el[i].value
        temp['description'] = this.$refs['parameterGroupDialog'].$el[i].desc
        parameters.parameters.push(temp)
      }
      this.$axios.patch('/api/ci/task/parameter_group/' + parameterID + '/', parameters).then(response => {
        this.$axios.get('/api/ci/task/' + this.taskBasicInfo.id + '/start/?BuildParameter=' + parameterID).then(response => {
          this.$Message.success({
            content: response.data.result.message,
            duration: 10
          })
          this.setCITaskStart(true)
        }, response => {
          this.$Message.error({
            content: response.data.result.message,
            duration: 10
          })
        })
      }, response => {
        this.$Message.error({
          content: response.data.result.message,
          duration: 10
        })
      })

      this.showParameterDialog = false
    },

    configTask: function () {
      this.showTaskConfig = true
    },

    parameterGroup: function () {
      this.showParameterGroup = true
    },

    showLogDeail: function (stageHistoryID) {
      this.showBuildLog = true
      this.stageHistoryID = stageHistoryID
      this.getStageHistory(stageHistoryID)
    },

    showTestresultDeail: function (stageHistoryID, testResultID, purposeName, step_id) {
      this.showTestResult = true
      this.stageHistoryID = stageHistoryID
      this.selectedTestResult.resultID = testResultID
      this.selectedTestResult.pusposeName = purposeName
      this.selectedTestResult.step_id = step_id
      this.getStageHistory(stageHistoryID)
    },

    switchStageLog: function (stageHistoryID) {
      this.stageHistoryID = stageHistoryID
      this.getStageHistory(stageHistoryID)
    },

    getTaskParameterGroups: function () {
      this.$axios.get('/api/ci/task/' + this.taskID + '/parameter_groups/').then(response => {
        this.taskParameterGroups = response.data.result
      }, response => {
      })
    },

    startBuildTask() {
      this.$axios.get('/api/ci/task/' + this.taskID + '/start/').then(response => {
        this.setCITaskStart(true)
        this.$Message.success({
          content: response.data.result.message,
          duration: 10
        })
      }, response => {
        this.$Message.error({
          content: response.data.result.message,
          duration: 10
        })
      })
    },

    getTaskBasicInfo: function (taskID) {
      this.$axios.get('/api/ci/task/' + taskID).then(response => {
        this.taskBasicInfo = response.data.result
      }, response => {
      })
    },

    removeTask: function (taskName) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除参数组[' + taskName + ']',
        onOk: () => {
          this.$axios.delete('/api/ci/task/' + this.taskID).then(response => {
            this.$Message.success({
              content: '任务删除成功',
              duration: 10,
              closable: true
            })
            this.$router.push('/ci/task')
          }, response => {
            this.$Message.error({
              content: '任务删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {
        }
      })
    },

    saveParameterGroup: function (value) {
      for (let i = 0; i < this.taskParameterGroups.length; i++) {
        this.taskParameterGroups[i].is_default = false
        if (this.taskParameterGroups[i].id === value.id) {
          this.taskParameterGroups.splice(i, 1, value)
        }
      }

    },
    groupListDrawerClose: function (value) {
      this.showParameterGroupConfig = false
    },

    showGroup: function (value) {
      this.showParameterGroupConfig = true
      for (let i = 0; i < this.taskParameterGroups.length; i++) {
        if (this.taskParameterGroups[i].id === value) {
          this.selectedGroup = this.taskParameterGroups[i]
          break
        }
      }
    },
    getStageHistory: function (historyID) {
      if (historyID > 0) {
        this.$axios.get('/api/ci/task/stage_history/' + historyID + '/').then(response => {
          this.stageHistory = response.data.result
        }, response => {
        })
      }
    },
    changeCiTask: function (taskId) {
      let route = '/ci/task/' + taskId + '/history'
      this.$router.push(route)
    }
  },

  created: function () {
    this.getTaskParameterGroups()
    this.getTaskBasicInfo(this.taskID)
    this.loadCITaskList()
  },

  watch: {
    stageHistoryID: function (value) {
      this.getStageHistory(value)
    },
    taskID: function (value) {
      //console.log(value)
      this.getTaskBasicInfo(value)
      this.getTaskParameterGroups(value)
    }
  },

  components: {
    CiTaskStage,
    CiTaskHistoryList,
    CiTaskStageLog,
    CITaskParameterGroup,
    ParameterGroupDetail,
    CITaskTestResultDetail
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.citask-card {
  width: 100%;
  height: 55px;
  /*margin-bottom: 16px;*/
}

.history-card {
  /*margin-left:10px;*/
  /*margin-right: 10px;*/
  /*margin-top:1px;*/
  min-height: 80px;
}

.tag {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d3d7d4;
  position: relative;
  margin-top: 25px;
  background-color: #FFF;
  border-radius: 5px;
  padding: 10px;
  font-size: 12px;
}

.tag-em {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -40px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #d3d7d4;
  font-size: 0;
  line-height: 0;
}

.tag-span {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -33px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #FFF;
  font-size: 0;
  line-height: 0;
}

.goodShow {
  overflow-y: scroll;
  padding-top: 10px;
}
</style>
