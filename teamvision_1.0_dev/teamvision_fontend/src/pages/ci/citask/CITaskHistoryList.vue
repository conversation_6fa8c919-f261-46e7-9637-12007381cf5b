<template>
  <div>
    <Scroll v-loading="showLoading" :on-reach-bottom="handleReachBottom" :height="containerHeight">
      <div style="margin:5px;" v-for="(history, index) in historyList" :key="history.id">
        <Card class="history-card" :bordered="false" dis-hover shadow>
          <Row>
            <Col span="2" style="width: 80px; margin-right: 5px;">
            <Icon v-if="history.Status === 4" type="ios-close-circle" :size="24" style="color:darkred"></Icon>
            <span v-if="history.Status === 1">
              <Spin style="display: inline-block;margin-right: 0px;"></Spin>
            </span>
            <Icon v-if="history.Status === 2" type="ios-checkmark-circle" :size="24" style="color:#388e83"></Icon>
            <Icon v-if="history.Status === 3" type="ios-remove-circle" :size="24" style="color:olive"></Icon>
            <Icon v-if="history.Status === 0" type="ios-time" :size="24" style="color:darkblue"></Icon>
            #{{ history.BuildVersion }}
            </Col>
            <Col span="22">
            <Steps :current="history.ViewFormat.CurrentStageIndex" status="process" size="small">
              <Step v-if="stageHistory.ViewFormat.Status === 'wait'"
                v-for="(stageHistory, index) in history.StageHistory" style="text-decoration: underline;"
                :key="stageHistory.id" @click.native="showRunDetail(history, stageHistory, index)" class="cursor-hand"
                :id="'stepContainer_' + stageHistory.id" :status="stageHistory.ViewFormat.Status" content="">
                <span style="text-decoration: underline;color:#5578aa;" slot="title">{{
                  stageHistory.ViewFormat.StageName }}</span>
              </Step>
              <Step v-if="stageHistory.ViewFormat.Status === 'process'"
                v-for="(stageHistory, index) in history.StageHistory" style="text-decoration: underline;"
                :key="stageHistory.id" @click.native="showRunDetail(history, stageHistory, index)" class="cursor-hand"
                :id="'stepContainer_' + stageHistory.id" :status="stageHistory.ViewFormat.Status" content="">
                <span style="text-decoration: underline;color:#5578aa;" slot="title">{{
                  stageHistory.ViewFormat.StageName }}</span>
              </Step>
              <Step v-if="stageHistory.ViewFormat.Status === 'finish'"
                v-for="(stageHistory, index) in history.StageHistory" style="text-decoration: underline;"
                :key="stageHistory.id" @click.native="showRunDetail(history, stageHistory, index)" class="cursor-hand"
                :id="'stepContainer_' + stageHistory.id" :status="stageHistory.ViewFormat.Status" content="">
                <span style="text-decoration: underline;color:#5578aa;" slot="title">{{
                  stageHistory.ViewFormat.StageName }}</span>
                <span slot="icon">
                  <Icon type="md-checkmark-circle-outline" color="green" />
                </span>
              </Step>
              <Step v-if="stageHistory.ViewFormat.Status === 'error'"
                v-for="(stageHistory, index) in history.StageHistory" style="text-decoration: underline;"
                :key="stageHistory.id" @click.native="showRunDetail(history, stageHistory, index)" class="cursor-hand"
                :id="'stepContainer_' + stageHistory.id" :status="stageHistory.ViewFormat.Status" content="">
                <span style="text-decoration: underline;color:#5578aa;" slot="title">{{
                  stageHistory.ViewFormat.StageName }}</span>
                <span slot="icon">
                  <Icon color="red" type="md-close-circle" />
                </span>
              </Step>
            </Steps>
            <Row>
              <Col style="padding-left: 30px;padding-bottom: 0px;padding-top: 5px;color: #c5c8ce; font-size: 12px;">
              <!--<span>任务执行信息：</span>-->
              <span>由 {{ history.ViewFormat.StartBy }} 触发</span>
              <span>{{ history.ViewFormat.StartTime }}开始</span>
              <span>在Agent {{ history.ViewFormat.AgentName }}上执行</span>
              <span>持续{{ history.ViewFormat.Duration }}</span>
              <!--<span @click="showLog(1)" style="text-decoration: underline;cursor: pointer; padding-left: 20px;">查看日志</span>-->
              </Col>
            </Row>
            <div v-show="stageHistory.ViewFormat.Show" v-for="stageHistory in history.StageHistory"
              :key="stageHistory.id" class="tag">
              <em :style="'left:' + stageHistory.ViewFormat.TagLeft" class="tag-em"></em>
              <span :style="'left:' + stageHistory.ViewFormat.TagLeft" class="tag-span"></span>
              <div style="">
                <Row>
                  <Col style="padding-left: 10px;padding-bottom: 10px;">
                  <span @click="closeRunDetail(history.id, stageHistory.id)"
                    style="color:#515a6e; text-decoration: underline;" class="cursor-hand">收起
                    <Icon type="ios-close" />
                  </span>
                  <span>本阶段持续时间：{{ stageHistory.ViewFormat.Duration }}</span>
                  <span @click="showLog(stageHistory.id)"
                    style="text-decoration: underline;cursor: pointer; padding-left: 20px;">阶段日志</span>
                  <!--<span v-if="stageHistory.ViewFormat.StageOutputPackage.length >0" style="text-decoration: underline;cursor: pointer; padding-left: 20px;">-->
                  <!--产品归档-->
                  <!--</span>-->
                  <!--<span v-if="stageHistory.ViewFormat.StageOutputChangeLog.length >0" style="text-decoration: underline;cursor: pointer; padding-left: 20px;">-->
                  <!--变更记录-->
                  <!--</span>-->
                  <span @click="delStageHistory(history, index)"
                    style="text-decoration: underline;cursor: pointer; padding-left: 20px;">删除本次构建</span>
                  <span v-for="codeInfo in stageHistory.ViewFormat.StageOutputCodeVersion" :key="codeInfo.version"
                    style="text-decoration: underline;cursor: pointer; padding-left: 20px;">
                    <span>{{ codeInfo.repo }}</span>
                    <span>{{ codeInfo.version }}</span>
                  </span>
                  </Col>
                </Row>
                <div>
                  <Row v-for="testResult in stageHistory.ViewFormat.TestResults" :key="testResult.id">
                    <Col span="24" style="padding: 10px;">
                    <span style="padding-right: 10px;">[{{ testResult.purpose_name }}] 测试结果:</span>
                    <span>
                      <Tooltip content="成功">
                        <Tag style="text-decoration: underline;" color="success"
                          @click.native="showTestResult(stageHistory.id, testResult.result_id, testResult.purpose_name, testResult.step_id)">
                          {{ testResult.pass }}</Tag>
                      </Tooltip>
                      <Tooltip content="失败">
                        <Tag style="text-decoration: underline;" color="error"
                          @click.native="showTestResult(stageHistory.id, testResult.result_id, testResult.purpose_name, testResult.step_id)">
                          {{ testResult.fail }}</Tag>
                      </Tooltip>
                      <Tooltip content="未执行">
                        <Tag style="text-decoration: underline;" color="warning"
                          @click.native="showTestResult(stageHistory.id, testResult.result_id, testResult.purpose_name, testResult.step_id)">
                          {{ testResult.aborted }}</Tag>
                      </Tooltip>
                    </span>
                    </Col>
                  </Row>
                  <Row>
                    <!--<Col span="1" style="width: 100px;">-->
                    <!--<span style="padding-left: 10px;" v-if="stageHistory.ViewFormat.StageOutputPackage.length >0">-->
                    <!--构建产物:-->
                    <!--</span>-->
                    <!--</Col>-->
                    <Col v-for="product in stageHistory.ViewFormat.StageOutputPackage" :key="product.id" span="3"
                      style="max-width: 150px;">
                    <span class="product-container">
                      <span style="display:block;">
                        <tooltip :content="product.product_name">
                          <div style="text-overflow:ellipsis;overflow: hidden;max-width: 120px;">{{ product.product_name
                            }}</div>
                        </tooltip>
                        <div style="width:80px;margin-right: auto;margin-left: auto;">
                          <img :src="'/api/ci/task/output/' + product.id + '/qrcode'"
                            style="height: 80px;width: 80px; border-radius: 10px;" />
                        </div>
                      </span>
                      <span style="display: block; width: 30px; margin-right: auto;margin-left: auto;">
                        <span @click="downloadPackage(product.id, product.product_name)"
                          style="text-decoration: underline;cursor: pointer;">下载</span>
                      </span>
                    </span>
                    </Col>
                  </Row>
                </div>
                <Row>
                  <Col style="padding-left: 10px;padding-top: 5px;">
                  <span v-for="codeInfos in stageHistory.ViewFormat.StageOutputCodeVersion" :key="codeInfos.id">
                    <div v-for="commitInfo in codeInfos.commit_info" :key="commitInfo.id" style="color: #999999">
                      <span style="margin-right: 20px;">{{ commitInfo.code_repo }}</span>
                      <span style="margin-right: 20px;">{{ commitInfo.version }}</span>
                    </div>
                  </span>
                  </Col>
                </Row>
              </div>
            </div>
            </Col>
          </Row>
        </Card>
      </div>
    </Scroll>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import CILogShowDialog from '../agent/CILogShowDialog.vue'

export default {
  components: {
  },
  name: 'CITaskHistoryList',
  props: ['taskID'],
  data() {
    return {
      tagLeft: '100px;',
      show: false,
      historyList: [],
      nextHistoryListUrl: null,
      selectStageHistory: null,
      showLoading: true,
      qrCodeShow: false,
      selectTaskQueueID: 0,
      selectTaskUUID: 0,
      packageDownloadlink: '无效链接'
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapGetters('citask', ['ciTaskStart']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },
  },
  methods: {
    ...mapMutations('citask', ['setCITaskStart']),
    closeRunDetail: function (historyID, stageHistoryID) {
      for (let i = 0; i < this.historyList.length; i++) {
        if (this.historyList[i].id === historyID) {
          for (let j = 0; j < this.historyList[i].StageHistory.length; j++) {
            if (this.historyList[i].StageHistory[j].id === stageHistoryID)
              this.historyList[i].StageHistory[j].ViewFormat.Show = false
          }
        }
      }
    },
    showRunDetail: function (history, stageHistory, index) {
      //console.log(index)
      let stepWidth = document.getElementById('stepContainer_' + stageHistory.id).offsetLeft
      for (let i = 0; i < this.historyList.length; i++) {
        if (this.historyList[i].id === history.id) {
          for (let j = 0; j < this.historyList[i].StageHistory.length; j++) {
            this.historyList[i].StageHistory[j].ViewFormat.Show = false
            if (this.historyList[i].StageHistory[j].id === stageHistory.id)
              this.historyList[i].StageHistory[j].ViewFormat.Show = true
            this.historyList[i].StageHistory[j].ViewFormat.TagLeft = (stepWidth) + 'px'
          }
        }
      }
    },

    showLog: function (id) {
      this.$emit('showLogDetail', id)
    },

    delStageHistory: function (history, index) {
      this.$Modal.confirm({
        title: "删除确认",
        content: "您即将删除本次构建？",
        onOk: () => {
          this.$axios.delete('/api/ci/task_history/' + history.id + '/clean_history').then(response => {
            this.$Message.success({
              content: '删除成功',
              duration: 3,
              closable: true
            })
            this.historyList.splice(index, 1)
          }), response => {
            this.$Message.success({
              content: '删除失败',
              duration: 3,
              closable: true
            })
          }

        },
        onCancel: () => { },
      });
    },

    showTestResult: function (stageHistoryID, testResultID, purposeName, step_id) {
      this.$emit('showTestResult', stageHistoryID, testResultID, purposeName, step_id)
    },

    getHistoryList: function (taskID) {
      this.showLoading = true
      this.$axios.get('/api/ci/task/' + this.taskID + '/task_histories/?page_size=10').then(response => {
        this.showLoading = false
        this.historyList = response.data.result.all_histories.results
        this.nextHistoryListUrl = response.data.result.all_histories.next
        this.initHistoryList(this.historyList)
      }, response => {

      })
    },

    initHistoryList: function (historyList) {
      for (let i = 0; i < this.historyList.length; i++) {
        this.historyList[i].show = false
        for (let j = 0; j < historyList[i].StageHistory.length; j++) {
        }
      }
    },

    handleReachBottom: function () {
      if (this.nextHistoryListUrl !== null) {
        this.$axios.get(this.nextHistoryListUrl).then(response => {
          this.historyList.push(response.data.result.all_histories.results)
          this.nextHistoryListUrl = response.data.result.all_histories.next
        }, response => {

        })
      } else {
        this.$Message.warning({
          content: '没有更多历史记录了',
          duration: 10,
          closable: true
        })
      }
    },

    downloadPackage: function (outputID, productName) {
      let realUrl = '/api/ci/task/output/' + outputID + '/download'
      this.$axios({ url: realUrl, method: 'get', responseType: 'arraybuffer' }).then(response => {
        let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', productName)
        document.body.appendChild(link)
        link.click()
        link.remove()
      }, response => {
      })
    }
  },

  created: function () {
    this.getHistoryList(this.taskID)
  },

  watch: {
    taskID: function (value) {
      this.getHistoryList(value)
    },
    ciTaskStart: function (value) {
      if (value) {
        this.getHistoryList(this.taskID)
        this.setCITaskStart(false)
      }
    }
  },
  componets: {
    CILogShowDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.history-card {
  /*margin-left:10px;*/
  /*margin-right: 10px;*/
  /*margin-top:1px;*/
  min-height: 80px;
}

.tag {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d3d7d4;
  position: relative;
  margin-top: 20px;
  background-color: #FFF;
  border-radius: 5px;
  padding: 10px;
  font-size: 12px;
}

.tag-em {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -40px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #d3d7d4;
  font-size: 0;
  line-height: 0;
}

.tag-span {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -33px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #FFF;
  font-size: 0;
  line-height: 0;
}

.product-container {
  padding: 10px;
  display: inline-block;
  border: 1px solid #f5f7f9;
  border-radius: 5px;
  margin-bottom: 15px;
}
</style>
