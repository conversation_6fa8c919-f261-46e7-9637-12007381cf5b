<template>
  <div :style="'height:' + containerHeight + 'px; overflow-y:scroll;'">
    <Card :bordered="false" dis-hover class="member-card cursor-hand">
      <Form ref="editGroup" :model="pGroup" :label-width="80">

        <Card :bordered="false" dis-hover>
          <div slot="title"> 基本信息</div>
          <FormItem label="ID">
            <span>{{ pGroup.id }}</span>
          </FormItem>
          <FormItem label="标题" prop="Title">
            <Input v-model="pGroup.group_name" placeholder="任务标题" />
          </FormItem>
          <FormItem label="设置为默认">
            <i-switch v-model="pGroup.is_default" />
          </FormItem>
          <FormItem label="描述:">
            <Input v-model="pGroup.description" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
              placeholder="Enter something..." />
          </FormItem>
        </Card>


        <Card :bordered="false" dis-hover>
          <div slot="title">参数管理</div>
          <span slot="extra" @click="addNewParameter">
            <Icon :size="24" color="#202d40" type="ios-add-circle" />
          </span>
          <div v-for="(item, index) in pGroup.parameters"
            style="border: 1px solid #e8eaec;padding: 5px 16px 16px 16px;width: 100%;margin-left: auto;margin-bottom:16px;margin-right: auto;border-radius: 10px;">
            <div style="height: 30px;margin-bottom: 10px;">
              <span>{{ index + 1 }}</span>
              <span @click="removeParameter(index)" style="float: right;">
                <Icon type="ios-close" :size="20" />
              </span>
            </div>
            <FormItem label="Key:">
              <Input v-model="item.key" type="text" placeholder="Enter something..." />
            </FormItem>
            <FormItem label="Value:">
              <Input v-model="item.value" type="text" placeholder="Enter something..." />
            </FormItem>
            <FormItem label="描述:">
              <Input v-model="item.description" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                placeholder="Enter something..." />
            </FormItem>
          </div>
        </Card>

        <Card :bordered="false" dis-hover>
          <div slot="title">插件设置</div>
          <Row v-for="step_setting in pGroup.step_settings" :key="step_setting.id" style="padding: 16px;">
            <Col span="12">
            <Icon type="ios-navigate-outline"></Icon>
            <span style="min-width: 100px; display: inline-block;">{{ step_setting.stage_title }} </span>
            <span>{{ step_setting.step_name }}</span>
            </Col>
            <Col span="8">
            <span style="min-width: 200px; display: inline-block;">
              {{ step_setting.desc }}
            </span>
            </Col>
            <Col span="4" style="text-align: right;padding-right: 20px;">
            <i-switch v-model="step_setting.is_on"></i-switch>
            </Col>
          </Row>
        </Card>

      </Form>
    </Card>
    <div style="position: fixed; bottom: 30px; left: 75%;">
      <span @click="saveParameterGroup">
        <Icon class="cursor-hand" type="ios-checkmark-circle" color="#32be77" size="50" />
      </span>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import ISwitch from '../../../../node_modules/view-design/src/components/switch/switch.vue'

export default {
  name: 'ParameterGroupDetail',
  props: ['parameterGroup'],
  data() {
    return {
      pGroup: {},
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapGetters('citask', ['ciTaskConfigChanged']),
    containerHeight: function () {
      return this.appBodyMainHeight
    }

  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),
    ...mapMutations('citask', ['setCITaskConfigChanged']),

    saveParameterGroup: function () {
      if (this.pGroup.group_name.trim() !== '') {
        this.$axios.patch('/api/ci/task/parameter_group/' + this.pGroup.id + '/', this.pGroup).then(response => {
          this.$emit('saveGroup', this.pGroup)
          this.$Message.success({
            content: '参数组保存成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '参数组保存失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    addNewParameter: function (value) {
      let temp = { key: '', value: '', description: '' }
      this.pGroup.parameters.push(temp)
    },

    removeParameter: function (index) {
      this.pGroup.parameters.splice(index, 1)
    },

    getParameterGroup: function (groupID) {
      this.$axios.get('/api/ci/task/parameter_group/' + groupID).then(response => {
        this.pGroup = response.data.result

      }, response => {

      })
    }

  },

  created: function () {
    this.pGroup = this.parameterGroup
  },

  mounted: function () {
  },

  watch: {
    parameterGroup: function (value) {
      this.getParameterGroup(value.id)
    },

    ciTaskConfigChanged: function (value) {
      if (value) {
        this.getParameterGroup(this.parameterGroup.id)
        this.setCITaskConfigChanged(false)
      }
    }
  },

  components: {
    ISwitch,
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  margin-bottom: 10px;
}
</style>
