<template>
  <div :style="'overflow-y:auto;height:'+ containerHeight+'px'" >
    <div class="info-body">
      <Card :bordered="false" dis-hover class="member-card" style="background-color: #f0f0f0;">
        <Row style="font-size: 14px;">
          <Col span="6" >
            <span>名称</span>
          </Col>
          <Col span="6" >
            <span>用法</span>
          </Col>
          <Col span="12">
            <span>说明</span>
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            WORKSPACE
          </Col>
          <Col span="6" >
            ${WORKSPACE}
          </Col>
          <Col span="12">
            代表当前任务在Agent上的工作目录
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BUILDTOOL
          </Col>
          <Col span="6" >
            ${BUILDTOOL}
          </Col>
          <Col span="12">
            Agent上的构建工具目录
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BUILDVERSION
          </Col>
          <Col span="6" >
            ${BUILDVERSION}
          </Col>
          <Col span="12">
            代表当前任务的构建版本，根据构建次数自增
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            COMMONSPACE
          </Col>
          <Col span="6" >
            ${COMMONSPACE}
          </Col>
          <Col span="12">
            各构建任务可以共享的一个目录
          </Col>
        </Row>
      </Card>

      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BUILDBACKUPSPACE
          </Col>
          <Col span="6" >
            ${BUILDBACKUPSPACE}
          </Col>
          <Col span="12">
            根据构建版本自动创建的目录，不会随任务执行清除
          </Col>
        </Row>
      </Card>

      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            TASKID
          </Col>
          <Col span="6" >
            ${TASKID}
          </Col>
          <Col span="12">
            构建任务ID
          </Col>
        </Row>
      </Card>

      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            TASKNAME
          </Col>
          <Col span="6" >
            ${TASKNAME}
          </Col>
          <Col span="12">
            构建任务名称
          </Col>
        </Row>
      </Card>

      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            HISTORYID
          </Col>
          <Col span="6" >
            ${HISTORYID}
          </Col>
          <Col span="12">
            构建任务记录ID
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BRANCHNAME
          </Col>
          <Col span="6" >
            ${BRANCHNAME}
          </Col>
          <Col span="12">
            从代码分支触发任务执行时的分支名称
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BRANCHCOMMITID
          </Col>
          <Col span="6" >
            ${BRANCHCOMMITID}
          </Col>
          <Col span="12">
            从代码分支触发任务执行时的分支提交ID
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            BRANCHTYPE
          </Col>
          <Col span="6" >
            ${BRANCHTYPE}
          </Col>
          <Col span="12">
            分支类型 1 分支 2 Tag
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            GITREPOHTTPURL
          </Col>
          <Col span="6" >
            ${GITREPOHTTPURL}
          </Col>
          <Col span="12">
            构建代码仓库地址(HTTP)
          </Col>
        </Row>
      </Card>
      <Card :bordered="false" dis-hover class="member-card">
        <Row>
          <Col span="6" >
            GITREPOSSHURL
          </Col>
          <Col span="6" >
            ${GITREPOSSHURL}
          </Col>
          <Col span="12">
            构建代码仓库地址(SSH)
          </Col>
        </Row>
      </Card>
    </div>
  </div>
</template>

<script>
  import { mapState, mapGetters, mapMutations } from 'vuex'


  export default {
    name: 'ProjectInfo',
    props: ['project'],
    data () {
      return {
      }
    },
    computed: {
      ...mapState(['appBodyMainHeight']),
      ...mapGetters('projectglobal',['createDocumentType']),
      ...mapGetters('document',['breadNav']),
      containerHeight: function () {
        return this.appBodyMainHeight
      }

    },

    methods: {
      ...mapMutations('projectglobal',['setCreateDocumentType','setCreateDialogShow']),

    },

    created: function() {
      this.$axios.get('/api/common/dicconfig/5/dicconfigs').then(response => {
        this.platforms=response.data.result
      }, response => {
        // error callback
      })

      this.$axios.get('/api/common/users/list').then(response => {
        this.userList=response.data.result
      }, response => {
        // error callback
      })

      this.$axios.get('/api/project/products').then(response => {
        this.products=response.data.result
      }, response => {
        // error callback
      })

      this.projectInfo = this.project
    },

    mounted: function () {
    },

    watch: {

      project: function () {
        this.projectInfo = this.project
      }

    },

    components: {
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

.info-body {
  width: 95%;
  padding-top:16px;
  margin-left: auto;
  margin-right: auto;
}

.member-card {
  width: 100%;
  height: 45px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

</style>
