<template>
  <div :style="'overflow-y:auto;height:'+ containerHeight+'px'" >
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import ProjectInfo from './CIAgent.vue'
import ProjectMember from './CICredentials.vue'
import ProjectMileStone from './CITag.vue'

export default {
  name: 'CISettings',
  data () {
    return {
      projectInfo: {}
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter',['userInfo']),
    ...mapGetters('projectglobal',['createDocumentType']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },
  },

  methods: {
    ...mapMutations('projectglobal',['setCreateDocumentType','setCreateDialogShow', 'setHeadMenu']),


  },

  created: function() {
  },

  mounted: function () {
  },

  watch: {

  },

  components: {
    labelEditorInput,
    ProjectInfo,
    ProjectMember,
    ProjectMileStone
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

.document-card {
  width: 200px;
  height: 150px;
  margin:10px;
  float:left;
}

</style>
