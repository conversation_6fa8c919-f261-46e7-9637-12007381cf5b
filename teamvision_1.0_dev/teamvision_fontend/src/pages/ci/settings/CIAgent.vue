<template>
  <div :style="'overflow-y:auto;height:' + containerHeight + 'px;'">
    <div style="padding-top: 16px;">
      <Card v-for="agent in agents" :key="agent.id" :bordered="false" dis-hover class="member-card cursor-hand">
        <Row>
          <Col :xl="20" :lg="20" :md="18" :sm="12">
          <Row>
            <Col span="6">
            <span style="font-weight: bold; display: inline-block">
              <span style="padding-right: 20px;">
                <Icon v-if="agent.Status === 2" type="md-checkmark-circle-outline" :size="14" style="color:#388e8e">
                </Icon>
                <Icon v-if="agent.Status !== 2" type="ios-checkmark-circle-outline" :size="14"></Icon>
              </span>
              <span style="padding-right: 20px;">{{ agent.id }}</span>
              <span @click="showAgentForm(agent.id)" style="text-decoration: underline;">{{ agent.Name }}</span>
            </span>
            </Col>
            <Col span="16">
            <span style="margin-left: 15px;">
              <span style="display: inline-block">
                <Tag v-for="tag in agent.TagFormat" :key="tag.id" :color="tag.TagColor" class="member-tag">{{
    tag.TagName }}</Tag>
              </span>
            </span>
            </Col>
          </Row>
          </Col>
          <Col :xl="4" :lg="4" :md="8" :sm="12">
          <span v-if="userInfo.system_permision < 2 || userInfo.is_space_admin">
            <Dropdown transfer @on-click="deleteAgent">
              <a href="javascript:void(0)" style="color: inherit;">
                <span>
                  <Icon :size="24" type="ios-more" />
                </span>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem :name="agent.id + ':' + agent.Name">
                  <Icon type="ios-trash-outline" />
                  <span>删除</span>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
          </Col>
        </Row>
      </Card>
      <Modal :value="ciNewObject" title="添加Agent" :mask-closable="false" :width="600" @on-cancel="cancel"
        :styles="{ bottom: '20px', top: '50px' }">
        <div :style="'height:' + modalHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
          <Form ref="ciAgentCreate" :model="formItem" :label-width="80" :rules="ruleCustom">
            <FormItem label="名称" prop="Name">
              <Input v-model="formItem.Name" placeholder="Agent名称20个字符以内！" />
            </FormItem>
            <FormItem label="标签" prop="AgentTags">
              <Select v-model="formItem.AgentTags" multiple placeholder="为Agent添加标签" @on-change="onProjectChange">
                <Option v-for="tag in agentTagList" :key="tag.id" :value="tag.id" :label="tag.TagName">
                  <span style="margin-right: 10px;">
                    <Avatar shape="square" :style="{ background: tag.TagColor }" size="small" />
                  </span>
                  <span>{{ tag.TagName }}</span>
                </Option>
              </Select>
            </FormItem>
            <FormItem label="工作目录" prop="AgentWorkSpace">
              <Input v-model="formItem.AgentWorkSpace" placeholder="任务工作目录" />
            </FormItem>
            <FormItem label="执行器数量" prop="Executors">
              <InputNumber :max="20" :min="1" :step="1" v-model="formItem.Executors"></InputNumber>
            </FormItem>
            <FormItem label="工具目录" prop="BuildToolsDir">
              <Input v-model="formItem.BuildToolsDir" placeholder="Agent上存放通用工具目录" />
            </FormItem>
          </Form>
        </div>

        <div slot="footer">
          <Button type="success" style="width: 80px; height:30px;" @click="addAgent('ciAgentCreate')" shape="circle">添加
          </Button>
          <Button type="default" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
        </div>
      </Modal>
    </div>
    <Drawer :value="showAgentDetail" class-name="fdsfds" @on-close="onCloseDetailPanel" :inner="true" :transfer="false"
      :width="40" :mask="false">
      <div slot style=""></div>
      <Form ref="editAgent" :model="editItem" :label-width="80" :rules="ruleCustom">
        <FormItem label="名称" prop="Name">
          <Input v-model="editItem.Name" placeholder="Agent名称20个字符以内！" />
        </FormItem>
        <FormItem label="标签" prop="AgentTags">
          <Select v-model="editItem.AgentTags" multiple placeholder="为Agent添加标签" @on-change="onProjectChange">
            <Option v-for="tag in agentTagList" :key="tag.id" :value="tag.id" :label="tag.TagName">
              <span style="margin-right: 10px;">
                <Avatar shape="square" :style="{ background: tag.TagColor }" size="small" />
              </span>
              <span>{{ tag.TagName }}</span>
            </Option>
          </Select>
        </FormItem>
        <FormItem label="工作目录" prop="AgentWorkSpace">
          <Input v-model="editItem.AgentWorkSpace" placeholder="任务工作目录" />
        </FormItem>
        <FormItem label="执行器数量" prop="Executors">
          <InputNumber :max="20" :min="1" :step="1" v-model="editItem.Executors"></InputNumber>
        </FormItem>
        <FormItem label="工具目录" prop="BuildToolsDir">
          <Input v-model="editItem.BuildToolsDir" placeholder="Agent上存放通用工具目录" />
        </FormItem>
      </Form>
      <Button type="success" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
        @click="editAgent('editAgent')">保存</Button>
    </Drawer>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import { agentValidateRules } from './CISettingsCreate'

export default {
  name: 'CIAgent',
  data() {
    return {
      agents: [],
      showAgentDetail: false,
      formItem: {
        Name: '',
        AgentWorkSpace: '',
        AgentTags: [],
        Executors: 1,
        BuildToolsDir: ''
      },
      editItem: {
        Name: '',
        AgentWorkSpace: '',
        AgentTags: [],
        Executors: 1,
        BuildToolsDir: ''
      },
      agentTagList: [],
      ruleCustom: {
        ...agentValidateRules
      }

    }
  },
  computed: {
    ...mapState(['appBodyMainHeight', 'appBodyHeight']),
    ...mapGetters('ciglobal', ['ciNewObject']),
    ...mapState('usercenter', ['userInfo']),

    containerHeight: function () {
      return this.appBodyHeight
    },

    modalHeight: function () {
      return this.appBodyMainHeight - 200
    }

  },

  methods: {
    ...mapMutations('ciglobal', ['setCINewObject']),

    onCloseDetailPanel: function () {
      this.showAgentDetail = false
    },

    showAgentForm: function (agentID) {
      this.showAgentDetail = true
      this.editItem = this.findAgent(agentID).entry
    },

    findAgent: function (agentID) {
      let result = { index: -1, entry: null }
      for (let i = 0; i < this.agents.length; i++) {
        if (agentID === this.agents[i].id) {
          result.entry = this.agents[i]
          result.index = i
          break
        }
      }
      return result
    },

    addAgent(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCINewObject(false)
          let successMessage = 'Agent添加成功'
          let failMessage = 'Agent添加失败，请联系管理员或者重试'
          this.$axios.post('/api/common/agents', this.formItem).then(response => {
            this.agents.push(response.data.result)
            this.$Message.success({
              content: successMessage,
              duration: 10
            })
          }, response => {
            this.setCINewObject(false)
            this.$Message.error({
              content: failMessage,
              duration: 10
            })
          })
          this.$refs[name].reset()
        }
      })
    },

    editAgent(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCINewObject(false)
          let successMessage = 'Agent保存成功'
          let failMessage = 'Agent保存失败，请联系管理员或者重试'
          this.$axios.put('/api/common/agent/' + this.editItem.id, this.editItem).then(response => {
            this.getAgents()
            this.$Message.success({
              content: successMessage,
              duration: 3
            })
          }, response => {
            this.$Message.error({
              content: failMessage,
              duration: 3
            })
          })
          this.$refs[name].reset()
        }
      })
    },

    deleteAgent: function (value) {
      let deleteValues = value.split(':')
      this.$Modal.confirm({
        title: '确认删除?',
        content: '您即将删除Agent[' + deleteValues[1] + ']',
        onOk: () => {
          this.$axios.delete('/api/common/agent/' + deleteValues[0]).then(response => {
            this.getAgents()
            this.$Message.success({
              content: 'Agent已经成功移除',
              duration: 3,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: 'Agent移除失败',
              duration: 3,
              closable: true
            })
          })
        }
      })
    },

    cancel() {
      this.setCINewObject(false)
    },

    getAgentTags: function () {
      this.$axios.get('/api/project/tags').then(response => {
        this.agentTagList = response.data.result
      }, response => {

      })
    },

    getAgents: function () {
      this.$axios.get('/api/common/agents').then(response => {
        this.agents = response.data.result
      }, response => {

      })
    },

    setAgentTag: function (value) {

    },



  },

  created: function () {
    this.getAgentTags()
    this.getAgents()
  },

  mounted: function () {

  },

  watch: {},

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 45px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

.member-avatar {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  padding-bottom: 2px;
  margin-right: 15px;
}

.member-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
}
</style>
