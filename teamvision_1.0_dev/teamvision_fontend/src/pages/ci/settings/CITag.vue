<template>
  <Tabs value="2">
    <TabPane label="全部" name="0">
      <ci-task-list :tagType="0"></ci-task-list>
    </TabPane>
    <TabPane label="任务分类" name="2">
      <ci-task-list :tagType="2"></ci-task-list>
    </TabPane>
    <TabPane label="Agent" name="3">
      <ci-task-list :tagType="3"></ci-task-list>
    </TabPane>
  </Tabs>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import CiTaskList from './CITagList.vue'

export default {
  name: 'CITag',
  data() {
    return {
      tags: [],
      formData: {
        TagName: '',
        TagprojectID: 0,
        TagVisableLevel: 1,
        TagColor: '',
        TagOwner: 0,
        TagType: 1
      }
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
  },

  methods: {
    updateTagTitle: function (value, id) {
      let parameters = { TagName: value }
      if (value !== '') {
        this.$axios.patch('/api/project/tag/' + id, parameters).then(response => {
          for (let i = 0; i < this.tags.length; i++) {
            if (this.tags[i].id === parseInt(id)) {
              this.tags.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '标题更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '标题更新失败',
            duration: 10,
            closable: true
          })
        })
      }

    },

    createTag: function () {
      this.formData.TagOwner = this.userInfo.id
      this.formData.TagColor = this.randomColor()
      if (this.formData.TagName.trim() !== '') {
        this.$axios.post('/api/project/tags', this.formData).then(response => {
          this.tags.push(response.data.result)
          this.$Message.success({
            content: '模块添加成功',
            duration: 10,
            closable: true
          })
          this.formData.TagName = ''
        }, response => {
          this.$Message.error({
            content: '模块添加失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    randomColor: function () {
      var r = Math.floor(Math.random() * 256);
      var g = Math.floor(Math.random() * 256);
      var b = Math.floor(Math.random() * 256);
      var color = '#' + r.toString(16) + g.toString(16) + b.toString(16);
      return color
    },

    getTags: function () {
      this.$axios.get('/api/project/tags').then(response => {
        this.tags = response.data.result

      }, response => {
      })
    },

    deleteTag: function (value) {
      let moduleInfo = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除标签[' + moduleInfo[1] + ']',
        onOk: () => {
          this.$axios.delete('/api/project/tag/' + moduleInfo[0]).then(response => {
            for (let i = 0; i < this.tags.length; i++) {
              if (this.tags[i].id === parseInt(moduleInfo[0])) {
                this.tags.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '标签删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '标签删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })

    }

  },

  created: function () {
    this.getTags()
  },

  mounted: function () {
  },

  watch: {
    moduleList: function () {
      this.modules = this.moduleList
    }
  },

  components: {
    labelEditorInput,
    CiTaskList
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 50px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}
</style>
