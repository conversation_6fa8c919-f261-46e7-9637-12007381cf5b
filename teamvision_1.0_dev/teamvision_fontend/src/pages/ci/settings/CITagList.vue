<template>
  <div>
    <div v-if="tagType !== 0" style="width: 60%;margin-left: auto;margin-right: auto; margin-bottom: 16px;">
      <Input v-model="formData.TagName" search enter-button="添加" @on-search="createTag" icon="ios-clock-outline"
        :maxlength="50" placeholder="输入Tag名称，回车创建标签" style="width: 100%" />
    </div>
    <div :style="'padding-top:16px;max-height:' + containerHeight + 'px;overflow:scroll'">
      <Card v-for="tag in tags" :padding="10" :key="tag.id" :bordered="false" dis-hover class="member-card cursor-hand">
        <Row>
          <Col :xl="20" :lg="20" :md="18" :sm="18">
          <span style="font-weight: bold;">
            <span style="float: left;margin-right: 10px;">
              <!--<Tag  :color="tag.TagColor"></Tag>-->
              <Icon type="ios-pricetag" :color="tag.TagColor" :size="24" />
            </span>
            <label-editor-input style="max-width: 400px;float: left;" @updateValue="updateTagTitle" placeHolder="模块名称"
              :id="tag.id" :displayText="tag.TagName"></label-editor-input>
          </span>
          </Col>
          <Col :xl="4" :lg="4" :md="6" :sm="6">
          <span>
            <Dropdown transfer @on-click="deleteTag">
              <a href="javascript:void(0)" style="color: inherit;">
                <span>
                  <Icon :size="24" type="ios-more" />
                </span>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem :name="tag.id + ':' + tag.TagName">
                  <Icon type="ios-trash-outline" />
                  <span>删除</span>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
          </Col>
        </Row>
      </Card>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'

export default {
  name: 'CITag',
  props: ['tagType'],
  data() {
    return {
      tags: [],
      formData: {
        TagName: '',
        TagprojectID: 0,
        TagVisableLevel: 1,
        TagColor: '',
        TagOwner: 0,
        TagType: 1
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
    containerHeight: function () {
      return this.appBodyMainHeight - 130
    }

  },

  methods: {

    updateTagTitle: function (value, id) {
      let parameters = { TagName: value }
      if (value !== '') {
        this.$axios.patch('/api/project/tag/' + id, parameters).then(response => {
          for (let i = 0; i < this.tags.length; i++) {
            if (this.tags[i].id === parseInt(id)) {
              this.tags.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '标题更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '标题更新失败',
            duration: 10,
            closable: true
          })
        })
      }

    },

    createTag: function () {
      this.formData.TagOwner = this.userInfo.id
      if (this.tagType === 0) {
        this.formData.TagType = 1
      } else {
        this.formData.TagType = this.tagType
      }
      this.formData.TagColor = this.randomColor()
      if (this.formData.TagName.trim() !== '') {
        this.$axios.post('/api/project/tags', this.formData).then(response => {
          this.tags.push(response.data.result)
          this.$Message.success({
            content: '模块添加成功',
            duration: 10,
            closable: true
          })
          this.formData.TagName = ''
        }, response => {
          this.$Message.error({
            content: '模块添加失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    randomColor: function () {
      let colorList = ['#339933', '#99cc66', '#ffff99', '#669966', '#66cc99', '#999966', '#666699,', '#339933', '#66cc66', '#99b3ff', '#a6bcff', '#00b2b3', '#c299ff', '#e0ccff', '#ecb3ff', '#009999', '#b2b300', '#cccc8f', '#9494b8', '#c2c2f0']
      let index = Math.floor(Math.random() * 20)
      if (this.tags.length > 0) {
        index = (this.tags.length + 1) % 20
      }
      return colorList[index]
    },

    getTags: function () {
      let filter = ''
      if (this.tagType !== 0) {
        filter = '?TagType=' + this.tagType
      }
      this.$axios.get('/api/project/tags' + filter).then(response => {
        this.tags = response.data.result
      }, response => {
      })
    },

    deleteTag: function (value) {
      let moduleInfo = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除标签[' + moduleInfo[1] + ']',
        onOk: () => {
          this.$axios.delete('/api/project/tag/' + moduleInfo[0]).then(response => {
            for (let i = 0; i < this.tags.length; i++) {
              if (this.tags[i].id === parseInt(moduleInfo[0])) {
                this.tags.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '标签删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '标签删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })

    }

  },

  created: function () {
    this.getTags()
  },

  mounted: function () {
  },

  watch: {
    moduleList: function () {
      this.modules = this.moduleList
    },
    tagType: function () {
      this.getTags()
    }
  },

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 50px;
  /*margin-right: 16px;*/
  /*margin-left: 16px;*/
  margin-bottom: 2px;
}
</style>
