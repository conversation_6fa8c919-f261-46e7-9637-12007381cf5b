
let getCITaskList = (filters) => {
    let initPromise = new Promise(function (resolve, reject) {
        let url = getCITaskListApi + filters
        axios.get(url).then(response => {
            let initData = response
            resolve(initData)
        }, response => {

            reject(response)
        })
    })
    return initPromise
}

let getCITaskHistoryList = (taskid, filters) => {
    let initPromise = new Promise(function (resolve, reject) {
        let url = getCITaskHistoryListApi.replace('{TASKID}', taskid) + filters
        axios.get(url).then(response => {
            let initData = response
            resolve(initData)
        }, response => {

            reject(response)
        })
    })
    return initPromise
}

export {
    getCITaskList,
    getCITaskHistoryList
}
