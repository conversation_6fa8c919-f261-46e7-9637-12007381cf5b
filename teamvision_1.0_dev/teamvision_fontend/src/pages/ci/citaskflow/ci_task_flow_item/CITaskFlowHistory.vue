<template>
  <div :style="'height:'+historyContainerHeight+'px'+'; overflow-y:scroll;padding-left:10px;padding-right:10px;'">
    <flow-history-item :flowID="flowID"></flow-history-item>
  </div>
</template>

<script>
  import { mapState, mapGetters, mapMutations} from 'vuex'
  import flowHistoryItem from './CITaskFlowHistoryItem.vue'
  export default {
    name: 'flowHistory',
    props: ['flowID'],
    data () {
      return {
        msg: 'Welcome to Your Vue.js App'
      }
    },
    computed: {
    ...mapState(['appBodyMainHeight']),
    historyContainerHeight: function () {
        return  this.appBodyMainHeight-33
      },
    },
    components: {
      flowHistoryItem
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

</style>
