<template>
  <div class="toolbar-container">
    <div class="toolbar-container-left">
      <div class="toolbar-project">
        <el-dropdown trigger="click" @command="handleChangeProject">
          <el-tag style="margin-left: 10px;">
            <Avatar :src="currentProject.PBAvatar" />
            <span style="padding-left:4px;">{{ currentProject.PBTitle }}</span>
            <Icon type="ios-arrow-down"></Icon>
          </el-tag>
          <el-dropdown-menu slot="dropdown" style="max-height: 400px; overflow-y: scroll; color:#5e5e5e;">
            <el-dropdown-item v-for="project in projectList" :key="project.id" :name="project.id" :command="project.id">
              <Avatar :src="project.PBAvatar" />
              <span style="padding-left:4px;">{{ project.PBTitle }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <ul class="toolbar-menus">
        <!--<router-link :to="'/project/'+ projectID +'/summary'" :exact="false" tag="li" active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">-->
        <!--<a href=""><i class="fa fa-fw  fa-bug"></i> 概要</a>-->
        <!--</router-link>-->
        <router-link :to="'/project/' + projectID + '/requirement'" :exact="false" tag="li"
          active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">
          <a href="">
            <Icon type="ios-flask"></Icon> 需求
          </a>
        </router-link>
        <router-link :to="'/project/' + projectID + '/task'" tag="li" active-class="app-body-head-menu-item-active"
          class="project-body-head-menu-item">
          <a :href="'/project/' + projectID + '/task'">
            <Icon type="md-list" /> 任务
          </a>
        </router-link>
        <!-- <router-link :to="'/project/'+projectID+'/mindmap'" tag="li" active-class="app-body-head-menu-item-active"
                class="project-body-head-menu-item">
                <a :href="'/project/'+projectID+'/mindmap'">
                  <span>
                    <Icon type="ios-key" />思维导图
                  </span>
                </a>
              </router-link> -->
        <router-link :to="'/project/' + projectID + '/fortesting'" tag="li"
          active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">
          <a :href="'/project/' + projectID + '/fortesting'">
            <Icon type="ios-bus" /> 提测
          </a>
        </router-link>
        <router-link :to="'/project/' + projectID + '/test/'" :exact="false" tag="li"
          active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">
          <a href="#">
            <Icon type="ios-bus" /> 测试
          </a>
        </router-link>
        <router-link :to="'/project/' + projectID + '/issue/all'" :exact="false" tag="li"
          active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">
          <a href=""><i class="fa fa-fw  fa-bug"></i> 问题</a>
        </router-link>
        <router-link :to="'/project/' + projectID + '/statistics'" :exact="false" tag="li"
          active-class="app-body-head-menu-item-active" class="project-body-head-menu-item">
          <a :href="'/project/' + projectID + '/statistics/issue'">
            <Icon type="ios-stats" /> 统计
          </a>
        </router-link>
        <!-- <router-link :to="'/project/'+projectID+'/documents'" tag="li" active-class="app-body-head-menu-item-active"
                class="project-body-head-menu-item">
                <a :href="'/project/'+projectID+'/documents'">
                  <Icon type="md-albums" /> 文件
                </a>
              </router-link> -->
        <router-link v-if="routerName === 'projectSettings'"
          :to="{ name: routerName, params: { projectID: projectID, page: settingsViewName } }" tag="li"
          class="app-body-head-menu-item-active project-body-head-menu-item">
          <a :href="'/project/' + projectID + '/documents'">
            <Icon :size="24" type="ios-settings" />
            {{ settingsName }}
          </a>
        </router-link>
      </ul>
    </div>
    <div class="toolbar-container-middle">
      <span v-if="headMenu.versionBox">
        <Select v-model="lastestVersion" @on-change="onVersionChange" :transfer="true" style="width:160px"
          :filterable="true">
          <Option v-for="item in versionList" :value="item.id" :key="item.id" :label="item.VersionLabel">
            {{ item.VersionLabel }}
          </Option>
        </Select>
      </span>
      <span v-if="headMenu.dateRangeBox">
        <DatePicker v-model="statisticsDateRange" transfer type="daterange" @on-change="selectDateRange"
          :options="dateRangeOption" placement="bottom-end" placeholder="选择日期" style="width: 200px"></DatePicker>
      </span>
      <div class="app-body-header-rightbar-default pull-right">
        <span @click="newObject" v-if="headMenu.newObject">
          <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
        </span>
        <span @click="importMindFile" v-if="headMenu.importMindFile">
          <Tooltip content="导入Xmind文件" transfer>
            <Avatar style="background-color: #5578aa;" class="cursor-hand" icon="md-log-in" />
          </Tooltip>
          <Divider type="vertical" />
        </span>
        <span v-if="headMenu.reqNew">
          <Dropdown transfer @on-click="createReqirement">
            <span>
              <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
              <Icon type="ios-arrow-down"></Icon>
            </span>
            <DropdownMenu slot="list">
              <DropdownItem name="1">
                <Icon type="ios-flask"></Icon> 需求
              </DropdownItem>
              <DropdownItem name="2">
                <Icon type="ios-bus" /> 提测
              </DropdownItem>
              <!--<DropdownItem name="3"><Icon type="ios-cloud-upload" />模块</DropdownItem>-->
            </DropdownMenu>
          </Dropdown>
          <Divider type="vertical" />
        </span>
        <span v-if="headMenu.documentNew">
          <Dropdown transfer @on-click="createDocument">
            <span>
              <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
              <Icon type="ios-arrow-down"></Icon>
            </span>
            <DropdownMenu slot="list">
              <DropdownItem name="1">
                <Icon type="ios-folder" /> 文件夹
              </DropdownItem>
              <DropdownItem name="2">
                <Icon type="ios-document" /> Excel
              </DropdownItem>
              <DropdownItem name="3">
                <Icon type="ios-cloud-upload" />上传文件
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        <span v-if="headMenu.issueExport" style="padding-left: 10px">
          <Divider type="vertical" />
          <Tooltip content="导出issue" transfer>
            <span class="cursor-hand" @click="exportIssueExcel">
              <Icon type="md-log-out" :size="20" color="#5578aa" />
            </span>
          </Tooltip>
          <Divider type="vertical" />
        </span>
        <span v-if="headMenu.taskViewMode" style="padding-left: 10px;color:#5578aa" class="cursor-hand">
          <Divider type="vertical" />
          <Dropdown transfer @on-click="taskViewSwitch">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon type="md-apps" :size="24" />
              </span>
              <Icon type="ios-arrow-down"></Icon>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem name="board">
                <Icon type="md-podium" /> 看板
              </DropdownItem>
              <DropdownItem name="gannt">
                <Icon type="ios-paw" /> 甘特图
              </DropdownItem>
              <DropdownItem name="list">
                <Icon type="ios-paw" />列表
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <span v-if="taskViewMode === 'board'">
            <Divider type="vertical" />
            <Tooltip content="筛选" transfer v-if="taskViewMode === 'board'">
              <span @click="showRightPanel">
                <Icon type="ios-funnel-outline" :size="24" />
              </span>
            </Tooltip>
          </span>
        </span>
      </div>
    </div>
    <div class="toolbar-container-right">
      <Dropdown transfer @on-click="settingsViewSwitch">
        <a href="javascript:void(0)" style="color: inherit;">
          <Icon :size="24" type="ios-settings" color="#5578aa" />
          <Icon type="ios-arrow-down"></Icon>
        </a>
        <DropdownMenu slot="list">
          <DropdownItem name="1:项目信息:info">
            <Icon type="md-podium" />
            <router-link :to="'/project/' + projectID + '/settings/info'" tag="span">项目信息</router-link>
          </DropdownItem>
          <DropdownItem name="2:版本规划:version">
            <Icon type="ios-paw" />
            <router-link :to="'/project/' + projectID + '/settings/version'" tag="span">版本规划</router-link>
          </DropdownItem>
          <DropdownItem name="3:成员:member">
            <Icon type="md-people" />
            <router-link :to="'/project/' + projectID + '/settings/member'" tag="span">成员</router-link>
          </DropdownItem>
          <DropdownItem name="4:模块:module">
            <Icon type="ios-cube" />
            <router-link :to="'/project/' + projectID + '/settings/module'" tag="span">模块</router-link>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapState, mapActions } from "vuex";
import merge from 'webpack-merge';

export default {
  name: "ProjectHead",
  inject: ['reload'],
  props: ["projectID", "page"],
  components: {
  },
  data() {
    return {
      versionList: [],
      projectPermision: 0,
      lastestVersion: 0,
      statisticsDateRange: "",
      taskViewMode: "board",
      settingsView: 1,
      settingsName: "",
      settingsViewName: "info",
      dateRangeOption: {
        shortcuts: [
          {
            text: "最近一星期",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return [start, end];
            },
          },
          {
            text: "最近一个月",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return [start, end];
            },
          },
          {
            text: "最近3个月",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              return [start, end];
            },
          },
        ],
      },
    };
  },

  computed: {
    ...mapState('project', ['projectList', 'currentProject', 'projectVersions', 'projectLatestVersion']),
    ...mapGetters("issue", ["issueFilters", "searchKeyword"]),
    ...mapGetters("projectglobal", ["projectRole", "headMenu"]),

    routerName: function () {
      return this.$route.name;
    },

    latestMonth: function () {
      const end = this.formatDate(new Date(), "yyyy-MM-dd");
      let start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      let startFormat = this.formatDate(start, "yyyy-MM-dd");
      return [startFormat, end];
    },
  },
  methods: {
    ...mapMutations("task", ["setTaskGanntMaxSize"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("projectglobal", ["setCreateDialogShow", "setProjectVersion", "setRightPanelShow", "setTaskViewMode", "setHeadMenu",
      "setCreateDocumentType", "setCreateReqType", "setDateRange", "setImportXmindFile",]),
    ...mapMutations("project", ["setMyProjectList", "setShowLoadingProjectList"]),
    ...mapActions('project', ['loadProjectInfo']),

    handleChangeProject(command) {
      this.$router.push({ params: merge(this.$route.params, { 'projectID': command }) })
      this.loadProjectInfo(command)
      this.reload();
    },

    newObject() {
      this.setCreateDialogShow(true);
    },

    onVersionChange(value) {
      this.setProjectVersion(value);
    },

    importMindFile: function () {
      this.setImportXmindFile(true);
    },

    selectDateRange: function () {
      let start = "";
      let end = "";
      if (this.statisticsDateRange[0] !== "") {
        start = this.formatDate(this.statisticsDateRange[0], "yyyy-MM-dd");
        end = this.formatDate(this.statisticsDateRange[1], "yyyy-MM-dd");
      }
      this.setDateRange([start, end]);
    },

    showRightPanel() {
      this.setRightPanelShow(true);
    },

    taskViewSwitch(value) {
      this.setItemViewMode(value);
      this.taskViewMode = value;
    },

    settingsViewSwitch(value) {
      let viewNames = value.split(":");
      this.settingsView = parseInt(viewNames[0]);
      this.settingsName = viewNames[1];
      this.settingsViewName = viewNames[2];
    },

    createDocument: function (value) {
      this.setCreateDocumentType(parseInt(value));
    },

    createReqirement: function (value) {
      this.setCreateReqType(parseInt(value));
    },

    formatDate: function (date, fmt) {
      let o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(
          RegExp.$1,
          (date.getFullYear() + "").substring(4 - RegExp.$1.length)
        );
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substring(("" + o[k]).length)
          );
      return fmt;
    },

    exportIssueExcel: function () {
      this.$Message.info({
        content: "导出中。。。",
        duration: 6
      })
      let realUrl = "/api/project/issue/export?";
      if (this.searchKeyword !== "") {
        realUrl = realUrl + "search=" + this.searchKeyword + "&";
      }

      if (this.issueFilters !== "") {
        realUrl = realUrl + this.issueFilters;
      }
      this.$axios({ url: realUrl, method: "get", responseType: "arraybuffer", }).then((response) => {
        let url = window.URL.createObjectURL(
          new Blob([response.data], { type: "application/vnd.ms-excel" })
        );
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        let disposition = response.headers["content-disposition"];
        let fileName = decodeURIComponent(disposition.split("filename=")[1]);
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        link.remove();
      },
        (response) => { }
      );
    },

    loadProjectVersions: function () {
      let defaultVersion = {
        id: 0,
        VersionLabel: "无",
        VprojectID: 0,
        VVersion: "无",
        VStartDate: null,
        VReleaseDate: null,
        VDescription: "",
      };

      this.versionList = [];
      this.lastestVersion = this.projectLatestVersion;
      this.setProjectVersion(this.lastestVersion);
      if (this.projectVersions.length > 0) {
        this.versionList.push(defaultVersion);
        this.versionList.push(...this.projectVersions);
      }
    },

    initSettingsMenu: function (value) {
      if (value) {
        if (value.trim().toLowerCase() === "info") {
          this.settingsView = 1;
          this.settingsName = "项目信息";
          this.settingsViewName = "info";
        }
        if (value.trim().toLowerCase() === "version") {
          this.settingsView = 2;
          this.settingsName = "版本规划";
          this.settingsViewName = "version";
        }
        if (value.trim().toLowerCase() === "member") {
          this.settingsView = 3;
          this.settingsName = "成员";
          this.settingsViewName = "info";
        }
        if (value.trim().toLowerCase() === "module") {
          this.settingsView = 4;
          this.settingsName = "模块";
          this.settingsViewName = "module";
        }
      }
    },

    initHeadMenu: function (to) {
      if (to.params.page === "info" || to.params.page === "module") {
        let headMenu = {
          newObject: false,
          searchBox: false,
          versionBox: false,
          dateRangeBox: false,
          documentNew: false,
          reqNew: false,
          issueExport: false,
          taskViewMode: false,
          taskFilter: false,
        };
        this.setHeadMenu(headMenu);
      }

      if (to.params.page === "member" || to.params.page === "version") {
        let headMenu = {
          newObject: true,
          searchBox: false,
          versionBox: false,
          dateRangeBox: false,
          documentNew: false,
          reqNew: false,
          issueExport: false,
          taskViewMode: false,
          taskFilter: false,
        };
        this.setHeadMenu(headMenu);
      }

      if (to.name === "projectStatistics") {
        let headMenu = {
          newObject: false,
          searchBox: false,
          versionBox: true,
          dateRangeBox: true,
          documentNew: false,
          reqNew: false,
          issueExport: false,
          taskViewMode: false,
          taskFilter: false,
        };
        this.setHeadMenu(headMenu);
      }
    },
  },

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.initHeadMenu(to);
    });
  },

  beforeRouteUpdate(to, from, next) {
    if (to.params.page === "info" || to.params.page === "module") {
      let headMenu = {
        newObject: false,
        searchBox: false,
        versionBox: false,
        dateRangeBox: false,
        documentNew: false,
        reqNew: false,
        issueExport: false,
        taskViewMode: false,
        taskFilter: false,
      };
      this.setHeadMenu(headMenu);
    }

    if (to.params.page === "member" || to.params.page === "version") {
      let headMenu = {
        newObject: true,
        searchBox: false,
        versionBox: false,
        dateRangeBox: false,
        documentNew: false,
        reqNew: false,
        issueExport: false,
        taskViewMode: false,
        taskFilter: false,
      };
      this.setHeadMenu(headMenu);
    }

    if (to.name === "projectStatistics") {
      let headMenu = {
        newObject: false,
        searchBox: false,
        versionBox: true,
        dateRangeBox: true,
        documentNew: false,
        reqNew: false,
        issueExport: false,
        taskViewMode: false,
        taskFilter: false,
      };
      this.setHeadMenu(headMenu);
    }

    this.statisticsDateRange = this.latestMonth;
    this.setDateRange(this.statisticsDateRange);
    next();
  },

  created: function () {
    this.loadProjectInfo(this.projectID)
    this.loadProjectVersions();
    this.initSettingsMenu(this.page);
    this.statisticsDateRange = this.latestMonth;
    this.setDateRange(this.statisticsDateRange);
  },

  watch: {
    projectID: function (value) {
      this.loadProjectVersions();
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped lang="less">
.toolbar-container {
  width: 100%;
  min-width: 1280px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 24px 0 8px;
  height: 48px;
  line-height: 48px;
  margin: 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.toolbar-container-left {
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
}

.toolbar-project {
  height: 100%;
  margin-right: 8px;
  position: relative;
  float: left;
}

.toolbar-menus {
  width: auto;
  height: 100%;
  font-size: 0;
  float: left;
}

@import "../../layout/appBody";
@import "../../layout/appHead";
@import "../../assets/teamvision/global/less/global";
</style>
