<template>
  <div :style="'height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;height:200px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />测试用例统计
        </div>
        <div>
          <Row type="flex" justify="space-between">
            <Col span="6" style="padding: 5px;">
            <Card :padding="5" style="height: 110px;color: #45be95;" dis-hover>
              <div class="summary-number-title">总数:</div>
              <div class="summary-number-content">
                <span style="color: inherit">{{ testCaseSummary.total }}</span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #5bc0de;" dis-hover>
              <div class="summary-number-title">今日新增:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">{{ testCaseSummary.today }}</span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #f1494e;" dis-hover>
              <div class="summary-number-title">本周新增:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">{{ testCaseSummary.this_week }}</span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #bedad3;" dis-hover>
              <div class="summary-number-title">本月新增:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">{{ testCaseSummary.this_month }}</span>
              </div>
            </Card>
            </Col>
          </Row>
        </div>
      </Card>
      </Col>
    </Row>
    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />每日新增用例趋势
        </div>
        <div id="OpenedIssueToday" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>
    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />测试用例总体趋势
        </div>
        <div id="OpenedIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'TestStatistics',
  props: ['projectID'],
  data() {
    return {
      testCaseSummary: {},
    }
  },

  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['projectVersion', 'dateRange']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),
    ...mapMutations('document', ['setbreadNav', 'setEditDocument']),

    loadTestCaseSummary: function () {
      this.$axios.get('/api/project/' + this.projectID + '/testcase/statistics')
        .then(response => {
          this.testCaseSummary = response.data.result
        })
    },

    //生成趋势线图
    createTrendLineChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_title']
        },
        xAxis: {
          categories: data['xaxis']
        },
        yAxis: {
          title: {}
        },
        plotOptions: {
          line: {
            dataLabels: {
              enabled: true          // 开启数据标签
            },
            enableMouseTracking: true // 关闭鼠标跟踪，对应的提示框、点击事件会失效
          }
        },
        series: data['series_data']
      }
      return option
    },

    //生成简单柱状图
    createColumnChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_type']
        },
        xAxis: {
          categories: data['xaxis'],
          crosshair: true
        },
        yAxis: {
          min: 0,
          title: {
            text: '问题数量 (个)'
          }
        },
        tooltip: {
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            colorByPoint: true,
          }
        },
        series: data['series_data']
      }
      return option
    },

    //生成饼图
    createPieChart: function (data) {
      let option = {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false
        },
        title: {
          text: data['chart_title']
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: false
            },
            showInLegend: true
          }
        },
        series: [{
          type: data['chart_type'],
          name: data['chart_title'],
          data: data['series_data']
        }]
      }
      return option
    },

    initTestCaseChart: function () {
      this.loadTestCaseSummary()
    }

  },

  created: function () {
    this.initTestCaseChart()
    //console.log(this.dateRange)
  },

  mounted: function () {
  },

  watch: {
    projectID: function () {
      this.initTestCaseChart()
    },

    projectVersion: function () {
      this.initTestCaseChart()
    },

    dateRange: function () {
      this.initTestCaseChart()
    }
  },

  components: {}
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.chart-trend {
  max-height: 500px;
}

.summary-number-title {

  font-size: 18px;

}

.summary-number-content {
  font-size: 36px;
  padding-left: 40%;
}
</style>
