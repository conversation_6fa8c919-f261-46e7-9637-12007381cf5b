<template>
  <div>
    <!--<Collapse simple>-->
    <!--<Panel name="1">-->
    <!--<Icon type="ios-funnel" :size="24" />-->
    <!--<div slot="content" style="margin-top: 20px; margin-bottom: 0px;">-->
    <!--<Row>-->
    <!--<Col span="8">-->
    <!--<span>日期</span>-->
    <!--<DatePicker type="daterange" :options="options2" placement="bottom-end" placeholder="Select date" style="width: 200px"></DatePicker>-->
    <!--</Col>-->
    <!--</Row>-->
    <!--</div>-->
    <!--</Panel>-->
    <!--</Collapse>-->
    <Tabs value="name1" style="padding-left:16px;padding-right:16px;">
      <TabPane label="问题" name="name1">
        <issue-statistics :projectID="projectID"></issue-statistics>
      </TabPane>
      <TabPane label="任务" name="name2">
        <Alert show-icon>持续开发中,敬请期待</Alert>
      </TabPane>
      <TabPane label="需求" name="name3">
        <Alert show-icon>持续开发中，敬请期待</Alert>
      </TabPane>
      <TabPane label="测试" name="teststaistics">
        <test-statistics :projectID="projectID"></test-statistics>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import IssueStatistics from './IssueStatistics.vue'
import TestStatistics from './TestStatistics.vue'

export default {
  name: 'ProjectStatistics',
  props: ['projectID'],
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['createDocumentType']),

    folder: function () {
      if (this.folderID) {
        return this.folderID
      } else {
        return ''
      }
    }
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),
    ...mapMutations('document', ['setbreadNav', 'setEditDocument']),
  },

  created: function () {
  },

  mounted: function () {
  },

  watch: {},

  components: {
    IssueStatistics,
    TestStatistics
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.document-card {
  width: 200px;
  height: 150px;
  margin: 10px;
  float: left;
}
</style>
