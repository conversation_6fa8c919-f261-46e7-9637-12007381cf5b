<template>
  <div :style="'height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;height:200px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          问题状态统计
        </div>
        <div>
          <Row type="flex" justify="space-between">
            <Col span="6" style="padding: 5px;">
            <Card :padding="5" style="height: 110px;color: #45be95;" dis-hover>
              <div class="summary-number-title">新建:</div>
              <div class="summary-number-content">
                <span style="color: inherit">
                  {{ issueStatusSummary.newIssueCount }}
                </span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #5bc0de;" dis-hover>
              <div class="summary-number-title">已解决:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">
                  {{ issueStatusSummary.reslovedIssueCount }}
                </span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #f1494e;" dis-hover>

              <div class="summary-number-title">重新打开:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">{{ issueStatusSummary.reopenedIssueCount }}</span>
              </div>
            </Card>
            </Col>
            <Col span="6" style="padding: 5px">
            <Card :padding="5" style="height: 110px;color: #bedad3;" dis-hover>

              <div class="summary-number-title">已关闭:</div>
              <div class="summary-number-content">
                <span style="color: inherit;">{{ issueStatusSummary.closedIssueCount }}</span>
              </div>
            </Card>
            </Col>
          </Row>
        </div>
      </Card>
      </Col>
    </Row>

    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          剩余问题分布
        </div>
        <div id="unclosed_issue_column" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>

    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          每日新增问题趋势
        </div>
        <div id="OpenedIssueToday" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>

    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          问题总体趋势
        </div>
        <div id="OpenedIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>


    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          各版本问题分布
        </div>
        <div id="versionIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>

    <Row>
      <Col span="24">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          各模块问题分布
        </div>
        <div id="moduleIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>

    <Row>
      <Col span="8">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px; margin-right: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          问题严重性
        </div>
        <div id="SeverityIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
      <Col span="8">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;margin-right: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          问题分类
        </div>
        <div id="CategoryIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
      <Col span="8">
      <Card :bordered="false" dis-hover class="cursor-hand" style="margin-bottom: 20px;">
        <div style="font-weight: 500" slot="title">
          <Icon type="ios-stats" />
          问题解决结果
        </div>

        <div id="ResolveResultIssueTotal" class="chart-trend"></div>
      </Card>
      </Col>
    </Row>

  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import HighCharts from 'highcharts'

export default {
  name: 'IssueStatistics',
  props: ['projectID'],
  data() {
    return {
      issueStatusSummary: {},
      pieChartOption: {
        chart: {
          type: 'pie',
        },
        title: {
          text: '任务状态分布'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: false
            },
            showInLegend: true
          }
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
        },
        series: [{
          name: '任务占比',
          data: []
        }]

      },
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['projectVersion', 'dateRange']),
    containerHeight: function () {
      return this.appBodyMainHeight - 20
    },
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),
    ...mapMutations('document', ['setbreadNav', 'setEditDocument']),

    loadIssueStatusSummary: function () {
      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/status_summary' + createDate).then(response => {
        this.issueStatusSummary = response.data.result
      }, response => {

      })
    },

    createOpenedIssueTotal: function () {

      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_trend_total' + createDate).then(response => {
        let option = this.createTrendLineChart(response.data.result)
        HighCharts.chart('OpenedIssueTotal', option)
      }, response => {

      })
    },

    createOpenedIssueToday: function () {
      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_trend_new' + createDate).then(response => {
        let option = this.createTrendLineChart(response.data.result)
        HighCharts.chart('OpenedIssueToday', option)
      }, response => {

      })
    },

    createVersionIssueTotal: function () {

      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/statistics/version_total_issue' + createDate).then(response => {
        let option = this.createColumnChart(response.data.result)
        HighCharts.chart('versionIssueTotal', option)
      }, response => {

      })
    },


    createModuleIssueTotal: function () {

      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_count_per_module' + createDate).then(response => {
        let option = this.createColumnChart(response.data.result)
        HighCharts.chart('moduleIssueTotal', option)
      }, response => {

      })
    },

    createSeverityIssueTotal: function () {

      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_count_by_severity' + createDate).then(response => {
        let option = this.createPieChart(response.data.result)
        HighCharts.chart('SeverityIssueTotal', option)
      }, response => {

      })
    },


    createCategoryIssueTotal: function () {
      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_count_by_category' + createDate).then(response => {
        let option = this.createPieChart(response.data.result)
        HighCharts.chart('CategoryIssueTotal', option)
      }, response => {

      })
    },

    createResolveResultIssueTotal: function () {
      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/issue_count_by_resolveresult' + createDate).then(response => {
        let option = this.createPieChart(response.data.result)
        HighCharts.chart('ResolveResultIssueTotal', option)
      }, response => {

      })
    },





    //生成趋势线图
    createTrendLineChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_title']
        },
        xAxis: {
          categories: data['xaxis']
        },
        yAxis: {
          title: {}
        },
        plotOptions: {
          line: {
            dataLabels: {
              enabled: true          // 开启数据标签
            },
            enableMouseTracking: true // 关闭鼠标跟踪，对应的提示框、点击事件会失效
          }
        },
        series: data['series_data']
      }
      return option
    },

    //生成简单柱状图
    createColumnChart: function (data) {
      let option = {
        chart: {
          type: data['chart_type']
        },
        title: {
          text: data['chart_title']
        },
        subtitle: {
          text: data['chart_sub_type']
        },
        xAxis: {
          categories: data['xaxis'],
          crosshair: true
        },
        yAxis: {
          min: 0,
          title: {
            text: '问题数量 (个)'
          }
        },
        tooltip: {
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            colorByPoint: true,
          }
        },
        series: data['series_data']
      }
      return option
    },

    //生成饼图
    createPieChart: function (data) {
      let option = {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false
        },
        title: {
          text: data['chart_title']
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: false
            },
            showInLegend: true
          }
        },
        series: [{
          type: data['chart_type'],
          name: data['chart_title'],
          data: data['series_data']
        }]
      }
      return option
    },

    createIssueByPeople: function () {
      let createDate = '?CreationTime=' + this.dateRange
      if (this.dateRange[0] === '') {
        createDate = ''
      }

      this.$axios.get('/api/project/' + this.projectID + '/' + this.projectVersion + '/statistics/unclosed_issue' + createDate).then(response => {
        let data = response.data.result
        HighCharts.chart('unclosed_issue_column', {
          chart: {
            type: data['chart_type']
          },
          title: {
            text: data['chart_title']
          },
          xAxis: {
            categories: data['xaxis']
          },
          yAxis: {
            min: 0,
            title: {
              text: ''
            },
            stackLabels: {
              enabled: true,
              style: {
                fontWeight: 'bold',
                color: (HighCharts.theme && HighCharts.theme.textColor) || 'gray'
              }
            }
          },
          legend: {
            align: 'right',
            x: -30,
            verticalAlign: 'top',
            y: 25,
            floating: true,
            backgroundColor: (HighCharts.theme && HighCharts.theme.background2) || 'white',
            borderColor: '#CCC',
            borderWidth: 1,
            shadow: false
          },
          tooltip: {
            formatter: function () {
              return '<b>' + this.x + '</b><br/>' +
                this.series.name + ': ' + this.y + '<br/>' +
                '总数: ' + this.point.stackTotal
            }
          },
          plotOptions: {
            column: {
              stacking: 'normal',
              dataLabels: {
                enabled: true,
                color: (HighCharts.theme && HighCharts.theme.dataLabelsColor) || 'white',
                style: {
                  textShadow: '0 0 3px black'
                }
              }
            }
          },
          series: data['series_data']
        })
      }, response => {

      })
    },

    initIssueChart: function () {
      this.createOpenedIssueToday()
      this.createIssueByPeople()
      this.createOpenedIssueTotal()
      this.createVersionIssueTotal()
      this.createModuleIssueTotal()
      this.createSeverityIssueTotal()
      this.createCategoryIssueTotal()
      this.createResolveResultIssueTotal()
      this.loadIssueStatusSummary()
    }

  },

  created: function () {
    this.initIssueChart()
    //console.log(this.dateRange)
  },

  mounted: function () {
  },

  watch: {
    projectID: function () {
      this.initIssueChart()
    },

    projectVersion: function () {
      this.initIssueChart()
    },

    dateRange: function () {
      this.initIssueChart()
    }
  },

  components: {}
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.chart-trend {
  max-height: 500px;
}

.summary-number-title {

  font-size: 18px;

}

.summary-number-content {
  font-size: 36px;
  padding-left: 40%;
}
</style>
