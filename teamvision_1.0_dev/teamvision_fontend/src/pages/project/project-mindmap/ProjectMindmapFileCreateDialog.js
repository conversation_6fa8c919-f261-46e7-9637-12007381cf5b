//初始化项目以及版本信息

let initProjectVersions = function (tempData) {
    let projectVersions=[]
    for (let i=0;i<tempData.length;i++)
    {
        let tempProject={}
        tempProject.value=tempData[i].id
        tempProject.label=tempData[i].PBTitle
        tempProject.children=[]
        for (let j=0;j<tempData[i].Versions.length;j++)
        {
            let tempChild={}
            tempChild.label=tempData[i].Versions[j].VVersion
            tempChild.value=tempData[i].Versions[j].id
            tempProject.children.push(tempChild)
        }
        projectVersions.push(tempProject)
    }
    return projectVersions
}


let mindmapFileValidateRules = {

  FileName: [
    { type: 'string', required: true, min: 1, max: 50, message: '文件名称长度必须在1-50个字符之间！', trigger: 'blur' }
  ],
  ProjectVersion: [
    { type: 'array', required: true,len: 2, message: '请选择版本或者创建版本后再创建测试点！' }
  ],
}

export {
  mindmapFileValidateRules,
  initProjectVersions
}
