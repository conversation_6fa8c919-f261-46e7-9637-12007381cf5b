<template>
  <Modal :value="importXmindFile" :title="dialogTitle" :width="700" @on-cancel="cancel"
    :styles="{ bottom: '20px', top: '50px' }">
    <div>
      <Form ref="importXmindFile" :model="formItem" :label-width="80" :rules="ruleCustom">
        <FormItem label="项目" prop="ProjectVersion">
          <Cascader v-model="formItem.ProjectVersion" :data="projectVersions" :filterable="true"></Cascader>
        </FormItem>
        <FormItem :error="uploadFileError">
          <Upload ref="upload" :show-upload-list="true" :default-file-list="formItem.attachments.defaultList"
            :on-success="handleSuccess" :format="['xmind']" :max-size="102400" :on-format-error="handleFormatError"
            :on-exceeded-size="handleMaxSize" :before-upload="handleBeforeUpload" type="drag"
            action="/api/project/fortesting/upload_files">
            <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>点击或拖拽Xmind文件</p>
            </div>
          </Upload>
          <span style="font-size: 12px;">
            <Icon type="ios-alert" color="orange" :size="16" /> 导入时间较长，请在5分钟后刷新页面查看!
          </span>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="ok('importXmindFile')">导入
      </Button>
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { mindmapFileValidateRules, initProjectVersions } from './ProjectMindmapFileCreateDialog'

import { VueEditor } from 'vue2-editor'

export default {
  name: 'ProjectXmindImportDialog',
  props: ['projectID', 'versionID'],
  data() {
    return {
      projectVersions: [],
      dialogTitle: '导入Xmind文件',
      uploadFileError: '',
      formItem: {
        projectID: 0,
        VersionID: 0,
        ProjectVersion: [],
        attachments: {
          defaultList: [],
          imgName: '',
          visible: false,
          uploadList: []
        }
      },
      ruleCustom: {
        ...mindmapFileValidateRules
      }
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'project', 'importXmindFile']),
    ...mapState(['appBodyHeight']),
    ...mapState('project', ['projectList']),
    containerHeight: function () {
      return this.appBodyHeight - 100
    }
  },
  methods:
  {
    ...mapMutations('projectglobal', ['setImportXmindFile']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (this.formItem.attachments.uploadList.length <= 0) {
          this.uploadFileError = '请选择要导入的文件'
        }
        if (this.formItem.attachments.uploadList.length > 1) {
          this.uploadFileError = '仅支持一次导入一个文件'
        }
        if (valid) {

          this.$axios.post('/api/project/' + this.formItem.ProjectVersion[0] + '/version/' + this.formItem.ProjectVersion[1] + '/mind_file/import', this.formItem).then(response => {
            this.$emit('createMindmap', response.data.result)
            this.setImportXmindFile(false)
            this.formItem.attachments.uploadList = []
            this.$Message.warning({
              content: '文件导入正在处理中，请在5分钟后手动刷新页面查看。',
              duration: 20,
              closable: true
            })
          }, response => {
            this.formItem.attachments.uploadList = []
            this.setImportXmindFile(false)
            this.$Message.warning({
              content: '文件导入失败',
              duration: 10,
              closable: true
            })

          })
        }
      })
    },
    cancel() {
      this.setImportXmindFile(false)
    },
    handleView(name) {
      this.formItem.attachments.imgName = name
      this.formItem.attachments.visible = true
    },
    handleRemove(file) {
      const fileList = this.formItem.attachments.uploadList
      this.formItem.attachments.uploadList.splice(fileList.indexOf(file), 1)
      this.removeFile(file.id)
      //          this.updateAttachments()
    },
    handleSuccess(res, file) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.formItem.attachments.uploadList = this.$refs.upload.fileList
      //console.log(this.formItem.attachments.uploadList)
    },
    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：xmind',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过100M限制',
        duration: 10,
        closable: true
      })
    },
    handleBeforeUpload() {
    },

    removeFile: function (file_id) {
      this.$axios.delete('/api/project/fortesting/delete_file/' + file_id).then(response => {
      }, response => { })
    },
  },
  created() { },
  mounted() {
    this.formItem.attachments.uploadList = this.$refs.upload.fileList
    this.projectVersions = initProjectVersions(this.projectList)
  },
  watch: {
    'formItem.attachments.uploadList': function (value) {
      if (value.length === 1) {
        this.uploadFileError = ''
      }
    }

  },
  components: {

    VueEditor
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.demo-upload-list {
  display: inline-block;
  width: 100px;
  height: 100px;
  text-align: center;
  margin: 5px;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
</style>
