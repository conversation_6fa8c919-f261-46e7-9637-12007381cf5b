<template>
  <div>
    <div style="margin-bottom: 20px;">
      <div style="padding: 5px;font-size: 16px;">
        <Icon type="ios-pricetags-outline" />标签：
      </div>
      <div style="padding: 20px 5px 5px 25px;">
        <div style="padding: 5px;">
          <Tag v-for="tag in projectTopicTags" :key="tag.id" @on-close="removeTag" @on-change="removeResource"
            :closable="tag.TagType === 3" style="float: left;" :name="tag.TagName" checkable :checked="tag.TagChecked"
            color="success">{{ tag.TagName }}</Tag>
        </div>
        <div style="padding-top:15px;">
          <Input search enter-button="+" @on-search="addResource" placeholder="添加新标签" />
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import * as jsonpatch from 'fast-json-patch/index';
import { applyOperation } from 'fast-json-patch/index';
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'

export default {
  name: 'NodeTag',
  components: {
  },
  props: ['projectID', 'nodeTags'],
  data() {
    return {
      projectTopicTags: [],
      selectTags: []
    }
  },
  computed: {

  },
  mounted() {

  },
  destroyed() {
  },
  created() {
    this.getTopicTags()
    this.selectTags = this.nodeTags

  },

  methods: {

    getTopicTags: function () {
      this.$axios.get('/api/ci/case_tags?projectID__in=0,' + this.projectID + '&TagType__in=2,3').then(response => {
        this.projectTopicTags = response.data.result
      }, response => {

      })
    },

    addResource: function (value) {
      if (this.tagExists(value) === 0) {
        let tempTag = { projectID: 1, TagType: 3, TagName: value }
        this.$axios.post('/api/ci/case_tags', tempTag).then(response => {
          this.$emit('addTag', response.data.result)
        }, response => {

        })
      } else {
        this.$Message.error({
          content: '标签已经存在',
          duration: 10,
        })
      }
    },

    tagExists: function (value) {
      let result = 0
      for (let i = 0; i < this.projectTopicTags.length; i++) {
        if (value === this.projectTopicTags[i].TagName) {
          result = this.projectTopicTags[i].id
        }
      }
      return result
    },

    removeTag: function (e, value) {
      let tagID = this.tagExists(value)
      this.$axios.delete('/api/ci/case_tag/' + tagID + '/').then(response => {
        for (let i = 0; i < this.projectTopicTags.length; i++) {
          if (tagID === this.projectTopicTags[i].id) {
            this.projectTopicTags.splice(i, 1)
          }
        }
      }, response => {

      })
    },

    removeResource: function (checked, name) {
      let tagID = this.tagExists(name)
      if (checked) {
        if (this.selectTags) {
          this.selectTags.push(name)
        } else {
          this.selectTags = [name]

        }
        this.$emit('addResource', tagID)
        this.$emit('updateProperty', 'Resource', this.selectTags)
      } else {
        if (this.selectTags) {
          for (let i = 0; i < this.selectTags.length; i++) {
            if (this.selectTags[i] === name) {
              this.selectTags.splice(i, 1)
              break
            }
          }
          this.$emit('updateProperty', 'Resource', this.selectTags)
          this.$emit('removeResource', tagID)
        }
      }
    },

    initProjectTags: function (value) {
      if (value) {
        //console.log(value)
        for (let j = 0; j < this.projectTopicTags.length; j++) {
          this.projectTopicTags[j].TagChecked = false
        }
        for (let i = 0; i < value.length; i++) {
          for (let j = 0; j < this.projectTopicTags.length; j++) {
            if (value[i] === this.projectTopicTags[j].TagName) {
              this.projectTopicTags[j].TagChecked = true
              break
            }
          }
        }
      }
    }




    // https://github.com/fex-team/kityminder-core/wiki/command
    // https://github.com/fex-team/kityminder-core/wiki/api
  },

  watch: {
    nodeTags: function (value) {
      this.selectTags = value
      this.initProjectTags(value)
    }
  }


}
</script>

<style lang="less">
#mindContainer .minder-editor-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: inherit;
  top: 54px;
  font-family: Arial, "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

.top {
  display: none;
}

.toolBar {
  position: fixed;
  top: 115px;
  width: 100%;
}

.toolRightPanel {
  position: fixed;
  top: 169px;
  width: 350px;
  right: 0px;
  height: 100%;
}

.toolRightPanelIcon {
  position: fixed;
  top: 200px;
  width: 40px;
  right: 100px;
  box-shadow: 0px 10px 5px #888888;
  border-radius: 25px;
  cursor: pointer;
  /*border: 2px solid #50C28B;*/
}

.mind-head-item {
  width: 60px;
  display: inline-block;
  cursor: pointer;
}

#mindContainer .minder-editor-container .tabBar {
  display: none;
}

.mindmap-comments-container {
  position: absolute;
  background: #FFD;
  padding: 5px 15px;
  border-radius: 5px;
  max-width: 400px;
  max-height: 200px;
  overflow: auto;
  z-index: 10;
  box-shadow: 0 0 15px rgba(0, 0, 0, .5);
  word-break: break-all;
  white-space: normal;
  font-size: 12px;
  color: #333;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
