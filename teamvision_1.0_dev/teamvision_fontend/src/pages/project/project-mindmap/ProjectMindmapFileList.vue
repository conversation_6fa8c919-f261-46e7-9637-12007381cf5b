<template>
  <div style="padding: 16px;color: #5578aa">
    <Row class="mindmapFileItem" v-for="file in mindMapFileList" :key="file.id">
      <Col span=10>
      <span>
        <tooltip v-if="file.FileType === 1" content="测试用例">
          <Avatar shape="square" style="color: #fff;background-color: #5578aa;">用例</Avatar>
        </tooltip>

        <tooltip v-if="file.FileType === 2" content="BVT测试记录">
          <Avatar shape="square" style="color: #fff;background-color: #22aaaa">BVT</Avatar>
        </tooltip>

        <tooltip v-if="file.FileType === 3" content="用例执行记录">
          <Avatar shape="square" style="color: #fff;background-color: #22aaaa">测试</Avatar>
        </tooltip>
      </span>
      <span style="width: 200px;">
        <span style="font-size:12px;padding: 10px;color:#8c96a0;width: 50px;display: inline-block;">#{{ file.id
          }}</span>
        <Tooltip content="更新日期">
          <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
            <Icon type="ios-calendar" color="#19be6b" /> {{ file.view_data.UpdateTime }}
          </i>
        </Tooltip>
        <Tooltip content="版本">
          <i style="padding-right: 10px;">[{{ file.view_data.VersionName }}]</i>
        </Tooltip>
      </span>

      <span style="padding: 10px; font-size: 16px;text-decoration: underline;">
        <label-editor-input-external-trigger :url="'/project/' + projectID + '/mindmap/' + file.id"
          style="display: inline-block;" :clickEdit="renameFile" @cancelUpdate="cancelUpdateFileTitle"
          @updateValue="updateFileTitle" placeHolder="阶段标题" :id="file.id" :displayWidth="200"
          :displayText="file.FileName"></label-editor-input-external-trigger>
      </span>
      </Col>
      <Col span="10">
      <span style="font-size:12px;">
        <span v-if="file.FileType === 1" style="color:#8c96a0; width: 150px;display: inline-block">
          <Tooltip content="自动化用例数/用例数">
            <span style="border: 1px solid #2f271d;padding:3px 5px 3px 5px;border-radius: 3px; font-size: 12px;">
              <span style="color:#19be6b;">{{ file.view_data.AutoPointCounts }}</span>/
              <span>{{ file.view_data.PointCounts }}</span>
            </span>
          </Tooltip>
        </span>
        <span v-if="file.FileType === 2 || file.FileType === 3"
          style="color:#8c96a0;width: 150px;display: inline-block">
          <span style="border: 1px solid #2f271d;padding:3px 5px 3px 5px;border-radius: 3px; font-size: 12px;">
            <tooltip content="成功/失败/待确认">
              <span style="color:#19be6b;">{{ file.view_data.FinishedPointCounts[0] }}/</span>
              <span style="color:brown;">{{ file.view_data.FinishedPointCounts[1] }}/</span>
              <span style="color:orange;">{{ file.view_data.FinishedPointCounts[2] }}</span>
            </tooltip>
          </span>
        </span>
        <span style="padding: 0px;color:#8c96a0;width: 125px;display: inline-block">
          <Tooltip content="创建人" transfer>
            <Avatar shape="square" style="background-color:#629333">
              {{ file.view_data.Owner }}
            </Avatar>
          </Tooltip>
        </span>
      </span>
      </Col>
      <Col span="4">
      <span>
        <Dropdown @on-click="fileOperation">
          <a href="javascript:void(0)">
            <Icon type="ios-more-outline" :size="36" />
          </a>
          <DropdownMenu slot="list">
            <DropdownItem :name="'rename:' + file.id + ':' + file.FileName">
              <Icon type="ios-redo-outline" /> 重命名
            </DropdownItem>
            <DropdownItem :name="'copy:' + file.id + ':' + file.FileName">
              <Icon type="ios-copy-outline" /> 复制
            </DropdownItem>
            <DropdownItem :name="'export:' + file.id + ':' + file.FileName">
              <Icon type="ios-cloud-download-outline" /> 导出
            </DropdownItem>
            <DropdownItem :name="'delete:' + file.id + ':' + file.FileName">
              <Icon type="ios-trash-outline" /> 删除
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </span>
      </Col>
    </Row>
    <project-mindmap-file-create-dialog :dialogTitle="dialogTitle" :fileID="copyFileID" :versionID="versionID"
      :projectID="projectID" @createMindmap="createMindmapFile"></project-mindmap-file-create-dialog>
    <project-xmind-file-import-dialog :versionID="versionID" :projectID="projectID"
      @importMindmap="createMindmapFile"></project-xmind-file-import-dialog>
    <span v-if="loading">
      <Spin fix size="large"></Spin>
    </span>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import labelEditorInputExternalTrigger from '../../../components/common/LabelEditorExternalTigger-Input.vue'
import ProjectMindmapFileCreateDialog from './ProjectMindmapFileCreateDialog.vue'
import ProjectXmindFileImportDialog from './ProjectImportXmindDialog.vue'

export default {
  name: 'projectMindmapFileList',
  props: ['projectID'],
  data() {
    return {
      data: [99, 71, 78, 25, 36, 92],
      line: '',
      renameFile: 0,
      loading: false,
      mindMapFileList: [],
      copyFileID: 0,
      dialogTitle: '新建测试点'

    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapGetters(['appBodyHeight', 'appBodyWidth']),
    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion
      }
      if (this.$route.params.versionID) {
        return this.$route.params.versionID
      }
      return 0
    },
    containerHeight: function () {
      return this.appBodyHeight
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange']),

    updateFileTitle: function (value, id) {
      this.renameFile = 0
      if (value && value !== '') {
        let parameters = { FileName: value }
        this.$axios.patch('/api/project/mindmap_file/' + id + '/', parameters).then(response => {
        }, response => {

        })

      }
    },

    loadMindmapFiles: function (projectID, versionID) {
      this.loading = true
      this.$axios.get('/api/project/' + projectID + '/version/' + versionID + "/mindmap_files").then(response => {
        this.loading = false
        //console.log(response.data.result)
        this.mindMapFileList = response.data.result
      }, response => {

      })

    },

    cancelUpdateFileTitle: function (value, id) {
      this.renameFile = 0
    },

    fileOperation: function (name) {
      let commands = name.split(":")
      if (commands[0] === 'rename') {
        this.renameFile = commands[1]
      }
      if (commands[0] === 'copy') {
        this.copyFileID = commands[1]
        this.setCreateDialogShow(true)
        this.dialogTitle = '复制文件'
      }

      if (commands[0] === 'export') {
        this.export2XmindFile(commands[1], commands[2])
      }

      if (commands[0] === 'delete') {
        this.deleteMindmapFile(commands[1], commands[2])
      }
    },


    downloadXminFile: function (file_id, file_name) {
      this.$axios.post('/api/project/mindmap_file/' + file_id + '/export').then(response => {
        let result = response.data.result
        if (result) {
          this.export2XmindFile(result, file_name)
        }
      }, response => { })

    },

    export2XmindFile: function (file_id, file_name) {
      let realUrl = '/api/project/mindmap_file/' + file_id + '/export'
      this.$axios({ url: realUrl, method: 'post', responseType: 'arraybuffer' }).then(response => {
        let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', file_name + '.xmind')
        document.body.appendChild(link)
        link.click()
        link.remove()
      }, response => {
      })
    },



    createMindmapFile: function (value) {
      if (value.VersionID === this.versionID || this.versionID === 0) {
        let tempList = []
        tempList.push(value)
        tempList.push(...this.mindMapFileList)
        this.mindMapFileList = tempList
        //            this.mindMapFileList.push(value)
      }
    },


    deleteMindmapFile: function (fileID, fileName) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除文件[' + fileName + ']',
        onOk: () => {
          this.$axios.delete('/api/project/mindmap_file/' + fileID + '/').then(response => {
            for (let i = 0; i < this.mindMapFileList.length; i++) {
              if (this.mindMapFileList[i].id === parseInt(fileID)) {
                this.mindMapFileList.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '文件删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '文件删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })
    }
  },

  created: function () {
    //console.log('created')
    this.loadMindmapFiles(this.projectID, this.versionID)

  },

  watch: {

    projectID: function () {
      this.loadMindmapFiles(this.projectID, this.versionID)
    },

    versionID: function () {
      //console.log('version')
      this.loadMindmapFiles(this.projectID, this.versionID)
    }

  },

  components: {
    labelEditorInputExternalTrigger,
    ProjectMindmapFileCreateDialog,
    ProjectXmindFileImportDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.mindmapFileItem {
  height: 60px;
  box-shadow: 0 1px 0 0 #f0f0f0;
  padding-left: 16px;
  padding-top: 20px;

}
</style>
