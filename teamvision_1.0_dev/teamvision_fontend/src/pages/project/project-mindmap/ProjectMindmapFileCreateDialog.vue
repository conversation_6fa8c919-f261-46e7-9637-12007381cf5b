<template>
  <Modal :value="createDialogShow" :title="dialogTitle" :width="700" @on-cancel="cancel"
    :styles="{ bottom: '20px', top: '50px' }">
    <div style="height:400px;overflow-y: scroll;overflow-x: hidden">
      <Form ref="createMindmapFile" :model="formItem" :label-width="80" :rules="ruleCustom">
        <FormItem label="项目" prop="ProjectVersion">
          <Cascader v-model="formItem.ProjectVersion" :data="projectVersions" :filterable="true"></Cascader>
        </FormItem>

        <FormItem label="名称" prop="FileName">
          <Input v-model="formItem.FileName" placeholder="文件名称" />
        </FormItem>
        <div v-if="fileID !== 0">
          <FormItem label="文件类型" prop="FileType">
            <Select v-model="formItem.FileType" style="width:200px">
              <Option :value="1">测试点</Option>
              <Option :value="2">BVT验证</Option>
              <Option :value="3">测试执行</Option>
            </Select>
          </FormItem>
          <FormItem label="过滤项" prop="Priority">
            <Select v-model="formItem.Priority" multiple style="width:200px">
              <Option :value="0">全部</Option>
              <Option :value="1">P1</Option>
              <Option :value="2">P2</Option>
              <Option :value="3">P3</Option>
              <Option :value="4">P4</Option>
              <Option :value="5">P5</Option>
            </Select>
          </FormItem>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <Button v-if="createDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
        @click="ok('createMindmapFile')">添加
      </Button>
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { mindmapFileValidateRules, initProjectVersions } from './ProjectMindmapFileCreateDialog'


export default {
  name: 'ProjectMindmapFileCreateDialog',
  props: ['projectID', 'dialogTitle', 'fileID', 'versionID'],
  data() {
    return {
      projectVersions: [],
      formItem: {
        FileName: '新建测试点',
        VersionID: 0,
        projectID: 0,
        FileType: 1,
        Priority: [0],
        ProjectVersion: []
      },
      ruleCustom: {
        ...mindmapFileValidateRules
      }
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'project']),
    ...mapState(['appBodyHeight']),
    ...mapState('projectglobal', ['createDialogShow']),

    containerHeight: function () {
      return this.appBodyHeight - 100
    },
  },
  methods:
  {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setViewDialogShow', 'setObjectChange']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCreateDialogShow(false)
          this.formItem.projectID = this.formItem.ProjectVersion[0]
          this.formItem.VersionID = this.formItem.ProjectVersion[1]
          if (!this.fileID || this.fileID === 0) {
            this.newMindmapFile()
          } else {
            this.copyMindmapFile()
          }
        }
      })
    },

    newMindmapFile: function () {
      this.$axios.post('/api/project/' + this.projectID + '/version/' + this.versionID + "/mindmap_files", this.formItem).then(response => {
        this.$emit('createMindmap', response.data.result)
        this.$Message.success({
          content: '用例文件创建成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.setCreateDialogShow(false)
        this.$Message.error({
          content: '用例文件创建失败',
          duration: 10,
          closable: true
        })

      })
    },

    copyMindmapFile: function () {
      this.$axios.post('/api/project/mindmap_file/' + this.fileID + '/copy', this.formItem).then(response => {
        this.$emit('createMindmap', response.data.result)
        this.fileID = 0
        this.setCreateDialogShow(false)
        this.$Message.success({
          content: '用例文件复制成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.setCreateDialogShow(false)
        this.$Message.error({
          content: '用例文件复制失败',
          duration: 10,
          closable: true
        })

      })
    },

    cancel() {
      this.setCreateDialogShow(false)
      this.setViewDialogShow(false)
    },

    removeFile: function (file_id) {
      this.$axios.delete('/api/project/fortesting/delete_file/' + file_id).then(response => {
      }, response => { })
    },

  },
  created() {
  },
  mounted() {
    this.projectVersions = initProjectVersions(this.projectList)
  },
  watch: {

  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.demo-upload-list {
  display: inline-block;
  width: 100px;
  height: 100px;
  text-align: center;
  margin: 5px;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
</style>
