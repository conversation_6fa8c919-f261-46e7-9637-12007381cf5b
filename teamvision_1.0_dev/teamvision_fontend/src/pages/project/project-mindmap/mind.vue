<template>
  <div style="">
    <div class="toolBar">
      <Card :padding="0">
        <div style=" padding: 16px; border-bottom: 1px solid #f5f7f9">
          <span>
            <span style="margin-right: 20px;">{{ fileName }}</span>
          </span>
          <Divider type="vertical" />
          <span class="mind-head-item" @click="saveData">
            <tooltip content="保存" transfer>
              <i class="fa fa-floppy-o" style="font-size: 20px;" aria-hidden="true"></i>
            </tooltip>
          </span>
          <span class="mind-head-item">
            <Dropdown @on-click="addNode" transfer>
              <span>
                <Icon type="ios-add" :size="24" />
              </span>
              <DropdownMenu slot="list">
                <DropdownItem name="AppendChildNode">
                  <Icon type="ios-disc-outline" /> 子节点 (Tab|Insert)
                </DropdownItem>
                <DropdownItem name="AppendParentNode">
                  <Icon type="ios-disc-outline" /> 父节点
                </DropdownItem>
                <DropdownItem name="AppendSiblingNode">
                  <Icon type="ios-disc-outline" /> 同级节点 (Enter)
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
          <span class="mind-head-item" @click="removeNode">
            <Tooltip content="删除节点" transfer>
              <Icon type="ios-trash-outline" :size="24" />
            </Tooltip>
          </span>
          <Divider type="vertical" />
          <span class="mind-head-item">
            <Dropdown @on-click="setTheme" transfer>
              <span>
                <Tooltip content="设置主题" transfer>
                  <Icon type="ios-color-wand-outline" :size="24" />
                </Tooltip>
              </span>
              <DropdownMenu slot="list">
                <Row style="padding: 5px;" v-for="rowItem in themeList " :key="rowItem[0].name">
                  <Col span="12">
                  <DropdownItem :name="rowItem[0].name">
                    <div
                      style="height: 30px;width: 70px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">
                      {{ rowItem[0].title }}
                    </div>
                  </DropdownItem>
                  </Col>
                  <Col span="12">
                  <DropdownItem :name="rowItem[1].name">
                    <div
                      style="height: 30px;width: 70px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">
                      {{ rowItem[1].title }}
                    </div>
                  </DropdownItem>
                  </Col>
                </Row>
              </DropdownMenu>
            </Dropdown>
          </span>
          <span class="mind-head-item">
            <Dropdown @on-click="setTemplate" transfer>
              <span>
                <Tooltip content="设置模板" transfer>
                  <Icon type="ios-snow-outline" :size="24" />
                </Tooltip>
              </span>
              <DropdownMenu slot="list">
                <DropdownItem v-for="rowItem in templateList " :name="rowItem.name" :key="rowItem.name">
                  <div style="height: 30px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">
                    {{ rowItem.title }}
                  </div>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
          <span @click="resetLayout" class="mind-head-item">
            <Tooltip content="重置布局(Ctrl+Shift+l)" transfer>
              <Icon type="ios-redo-outline" :size="24" />
            </Tooltip>
          </span>
          <span v-if="autoSave">
            <Spin fix>
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>保存中.....</div>
            </Spin>
          </span>
          <Divider type="vertical" />
          <span>
            30%<Slider :value="100" transfer show-tip="hover" :max="200" :min="30" :step="10" @on-change="zoomMindmap"
              style="display: inline-block;height: 24px;width: 200px;margin-left: 10px;margin-right: 10px;"></Slider>
            200%
          </span>
        </div>
      </Card>
    </div>
    <div id="mindContainer" v-if="importData.length > 0" :style="{ right: toolBarWidth }">
      <minder ref="minder" :importData="importData" @exportData="exportData"></minder>
    </div>
    <div v-if="showDetailSetting" class="toolRightPanel" :style="'overflow-y:scroll;height:' + containerHeight + 'px'">
      <Card :disHover="true">
        <p slot="title">
          <Icon type="ios-film-outline"></Icon>
          节点编辑
        </p>
        <span @click="closeSettingPanel" style="cursor: pointer" slot="extra">
          <Icon type="md-close" :size="24" />
        </span>

        <div>
          <div style="padding: 5px;"><span style="width: 40px;display: inline-block;font-size: 16px;">ID:
            </span><span>{{
              selectNodeData.id }}</span></div>
          <div style="padding: 5px;"><span style="width: 40px;display: inline-block;font-size: 16px;">Key:
            </span><span>{{
              selectNodeData.OriginalID }}</span></div>
          <div style="padding: 5px;font-size: 16px;">备注: </div>
          <div style="padding: 5px 5px 5px 25px;">
            <Input @on-blur="addNote" v-model="selectNodeData.note" type="textarea" :rows="4" placeholder="节点备注信息" />
          </div>

        </div>
        <Divider orientation="left"></Divider>
        <div style="margin-bottom: 20px;">
          <div style="padding: 5px; font-size: 16px;">
            <Icon type="ios-happy-outline" />优先级:
          </div>
          <div style="padding: 20px 5px 5px 25px;">
            <span @click="addPriority(0)" style="cursor: pointer;">
              <Avatar icon="md-close" size="small"
                style="color: #f56a00;background-color: white; border: 1px solid black" />
            </span>
            <span @click="addPriority(1)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(255, 18, 0);">1</Avatar>
            </span>
            <span @click="addPriority(2)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(1, 70, 127);">2</Avatar>
            </span>
            <span @click="addPriority(3)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(0, 175, 0);">3</Avatar>
            </span>
            <span @click="addPriority(4)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(255, 150, 46);">4</Avatar>
            </span>
            <span @click="addPriority(5)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(71, 32, 196);">5</Avatar>
            </span>
            <span @click="addPriority(6)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(81, 81, 81);;">6</Avatar>
            </span>
            <span @click="addPriority(7)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(81, 81, 81);;">7</Avatar>
            </span>
            <span @click="addPriority(8)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(81, 81, 81);;">8</Avatar>
            </span>
            <span @click="addPriority(9)" style="cursor: pointer;">
              <Avatar size="small" style="color:white;background-color: rgb(81, 81, 81);;">9</Avatar>
            </span>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <div style="padding: 5px;font-size: 16px;">
            <Icon type="ios-pie-outline" />进度:
          </div>
          <div style="padding: 20px 5px 5px 25px;">
            <span @click="addProgress(0)" style="cursor: pointer"> <i-circle :percent="0" stroke-color="#ff5500"
                :size="40">
                <span class="demo-Circle-inner">
                  <Icon type="ios-close" size="25" style="color:#ff5500"></Icon>
                </span>
              </i-circle>
            </span>
            <span @click="addProgress(1)" style="cursor: pointer">
              <i-circle :percent="0" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">12.5</span>
              </i-circle>
            </span>
            <span @click="addProgress(2)" style="cursor: pointer">
              <i-circle :percent="12.5" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">12.5</span>
              </i-circle>
            </span>
            <span @click="addProgress(3)" style="cursor: pointer">
              <i-circle :percent="25" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">25</span>
              </i-circle>
            </span>
            <span @click="addProgress(4)" style="cursor: pointer">
              <i-circle :percent="37.5" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">37.5</span>
              </i-circle>
            </span>
            <span @click="addProgress(5)" style="cursor: pointer">
              <i-circle :percent="50" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">50</span>
              </i-circle>
            </span>
          </div>
          <div style="padding: 5px 5px 5px 25px;">
            <span @click="addProgress(6)" style="cursor: pointer">
              <i-circle :percent="62.5" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">62.5</span>
              </i-circle>
            </span>
            <span @click="addProgress(7)" style="cursor: pointer">
              <i-circle :percent="75" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">75</span>
              </i-circle>
            </span>
            <span @click="addProgress(8)" style="cursor: pointer">
              <i-circle :percent="87.5" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">87.5</span>
              </i-circle>
            </span>
            <span @click="addProgress(9)" style="cursor: pointer">
              <i-circle :percent="100" stroke-color="#5cb85c" :size="40">
                <span style="font-size:12px">100</span>
              </i-circle>
            </span>
          </div>
        </div>

        <!--<node-tag :projectID="projectID" :nodeTags="selectNodeData.resource"></node-tag>-->
        <div style="margin-bottom: 20px;">
          <div style="padding: 5px;font-size: 16px;">
            <Icon type="ios-pricetags-outline" />标签：
          </div>
          <div style="padding: 20px 5px 5px 25px;">
            <div v-if="projectTopicTags.length > 0" style="padding: 5px;">
              <Tag v-for="tag in projectTopicTags" :key="tag.id" @on-close="removeTag" @on-change="removeResource"
                :closable="tag.TagType === 3" style="float: left;" :name="tag.TagName" checkable
                :checked="tag.TagChecked" color="success">{{ tag.TagName }}</Tag>
            </div>
            <div style="padding-top:15px;">
              <Input search enter-button="+" @on-search="addResource" placeholder="添加新标签" />
            </div>
          </div>
        </div>

        <div>
          <Divider orientation="left"></Divider>
          <div style="padding: 10px;font-size: 16px;">
            <Select v-model="fontFamily" style="width:200px" @on-change="setFontFamily">

              <Option style="font-family: Verdana" value="Verdana">Verdana</Option>
              <Option style="font-family: Georgia" value="Georgia">Georgia</Option>
              <Option style="font-family: Arial" value="Arial">Arial</Option>
              <Option style="font-family: Times New Roman" value="VerdTimes New Romanana">Times New Roman</Option>
              <Option style="font-family: Courier New" value="Courier New">Courier New</Option>
              <Option style="font-family: Impact" value="Impact">Impact</Option>
              <Option style="font-family: Comic Sans MS" value="Comic Sans MS">Comic Sans MS</Option>
              <Option style="font-family: Tahoma" value="Tahoma">Tahoma</Option>
              <Option style="font-family: Garamond" value="Garamond">Garamond</Option>
              <Option style="font-family: Lucida Console" value="Lucida Console">Lucida Console</Option>
              <Option style="font-family: 宋体" value="宋体">宋体</Option>
              <Option style="font-family: 微软雅黑" value="微软雅黑">微软雅黑</Option>
              <Option style="font-family: 黑体" value="黑体">黑体</Option>
              <Option style="font-family: 楷体" value="楷体">楷体</Option>

            </Select>
            <InputNumber @on-change="setFontSize" :max="100" v-model="fontSize"></InputNumber>
          </div>

          <div style="padding: 10px;font-size: 16px;">
            <Tooltip content="节点背景色">
              <ColorPicker v-model="nodeBackgroundColor" transfer size="small" recommend
                @on-change="setBackgroundColor" />
            </Tooltip>
            <Tooltip content="节点字体颜色">
              <ColorPicker v-model="nodeFontColor" transfer size="small" recommend @on-change="setFontColor" />
            </Tooltip>
          </div>

        </div>

        <div>
          <Divider orientation="left"></Divider>
          <div style="padding: 10px;font-size: 16px;">
            <Icon type="ios-image" />图片：
            <div style="padding-left: 15px;padding-top: 15px;">
              <Input v-model="selectNodeData.image" search enter-button="+" placeholder="http/https开头的图片连接"
                @on-search="addImage" />
              <div style="margin-top:5px; color: #c5c8ce; font-size: 12px;">添加空链接，删除已有图片</div>
            </div>
          </div>
          <div style="padding: 10px;font-size: 16px;">
            <Icon type="ios-link" />超级连接：
            <div style="padding-left: 15px;padding-top: 15px;   ">
              <Input v-model="selectNodeData.hyperlink" search enter-button="+" placeholder="http/https开头的超级连接地址"
                @on-search="addLink" />
              <div style="margin-top: 5px;color: #c5c8ce;font-size: 12px;">添加空链接，删除已有链接</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
    <div v-if="showDetailSettingIcon" class="toolRightPanelIcon">
      <span @click="expandDetailSettingBar">
        <Avatar icon="ios-expand" size="large" />
      </span>
    </div>
    <div v-if="showCommentsContainer" class="mindmap-comments-container"
      :style="{ left: selectNodePosition.x, top: selectNodePosition.y }">
      {{ selectNodeData.note }}
    </div>
    <span v-if="loading">
      <Spin fix size="large"></Spin>
    </span>

    <span v-if="loading">
      <Spin fix size="large"></Spin>
    </span>

  </div>
</template>



<script>
import * as jsonpatch from 'fast-json-patch/index';
//    import { applyOperation } from 'fast-json-patch/index';
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import NodeTag from './TopicProperty.vue'

export default {
  components: {
    NodeTag,
  },
  props: ['projectID', 'mindFileID'],
  data() {
    return {
      minder: null,
      paper: null,
      showDetailSetting: false,
      showDetailSettingIcon: false,
      toolbarExtend: 0,
      selectNodeData: {},
      selectNodePosition: { x: '0px', y: '0px' },
      showCommentsContainer: false,
      projectTopicTags: [],
      originalTags: [],
      fontSize: 14,
      fileName: '',
      autoSave: false,
      loading: false,
      copyNode: false,
      saveFileMenuly: false,
      fontFamily: '微软雅黑',
      nodeBackgroundColor: '#50c28b',
      nodeFontColor: 'black',
      updatedChildren: [],
      templateList: [{ name: 'default', title: '思维导图' },
      { name: 'tianpan', title: '天盘图' },
      { name: 'structure', title: '组织结构图' },
      { name: 'filetree', title: '目录组织图' },
      { name: 'right', title: '逻辑结构图' },
      { name: 'fish-bone', title: '鱼骨头图' }
      ],
      themeList: [[{ name: 'classic', title: '经典脑图' }, { name: 'classic-compact', title: '经典紧凑' }],
      [{ name: 'snow', title: '温柔冷光' }, { name: 'snow-compact', title: '紧凑冷光' }],
      [{ name: 'fish', title: '鱼骨图' }, { name: 'wire', title: '线框图' }],
      [{ name: 'fresh-red', title: '清新红' }, { name: 'fresh-red-compat', title: '紧凑红' }],
      [{ name: 'fresh-green', title: '文艺绿' }, { name: 'fresh-green-compat', title: '紧凑绿' }],
      [{ name: 'fresh-purple', title: '浪漫紫' }, { name: 'fresh-purple-compat', title: '紧凑紫' }],
      [{ name: 'fresh-blue', title: '天空蓝' }, { name: 'fresh-blue-compat', title: '紧凑蓝' }],
      [{ name: 'fresh-pink', title: '脑残粉' }, { name: 'fresh-pink-compat', title: '紧凑粉' }],
      [{ name: 'fresh-soil', title: '你土黄' }, { name: 'fresh-soil-compat', title: '紧凑黄' }],
      [{ name: 'tianpan', title: '清新红' }, { name: 'tianpan-compact', title: '紧凑天盘' }],
      ],
      importData: [],
      exportDatas: [],
      loading: false
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyHeight - 65
    },
    toolBarWidth: function () {
      return this.toolbarExtend + 'px'
    }
  },
  mounted() {
    this.loading = true
    this.$axios.get('/api/project/kitymind_file/' + this.mindFileID + '/').then(response => {
      this.loading = false
      this.importData = response.data.result.data
      this.fileName = response.data.result.FileName
      setTimeout(() => {
        this.minder = this.$refs.minder.minder
        this.bindevent()
        this.setTemplate(response.data.result.Template)
        this.setTheme(response.data.result.Theme)
        this.minder.execCommand('ExpandToLevel', 2)
      }, 20)

      //                setInterval(() => {
      //                    this.saveData()
      //                }, 300000)


    }, response => {
      this.loading = false

    })
    window.onbeforeunload = function (e) {
      e = e || window.event;
      // 兼容IE8和Firefox 4之前的版本
      if (e) {
        e.returnValue = '关闭提示';
      }
      // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
      return '关闭提示';
    }
  },
  destroyed() {
    window.onbeforeunload = null
  },
  created() {
    this.getTopicTags()
  },

  beforeRouteLeave(to, from, next) {
    const answer = window.confirm('离开前请确认保存了数据，确定要离开？')
    if (answer) {
      next()
    } else {
      next(false)
    }
  },

  methods: {


    exportData(value) {
    },


    tagChecked: function (tagName) {
      let result = false
      if (this.selectNodeData.resource) {
        for (let i = 0; i < this.selectNodeData.resource.length; i++) {
          if (tagName === this.selectNodeData.resource[i]) {
            result = true
            break
          }
        }
      }
      return result
    },


    saveData: function () {
      this.autoSave = true
      let parameters = { mind_tree: JSON.stringify(this.minder.exportJson()) }
      this.$axios.post('/api/project/mindmap_file/' + this.mindFileID + '/save', parameters).then(response => {
        let rootNode = this.minder.getRoot()
        this.updateNodeID(rootNode, response.data.result)
        this.autoSave = false
      }, response => {
        this.autoSave = false
        this.$Message.error({
          content: '网络连接异常，保存可能失败',
          duration: 10,
        })

      })
    },



    findNodeWithId: function (id) {
      let count = 0
      let allNodes = this.minder.getAllNode()
      for (let i = 0; i < allNodes.length; i++) {
        if (allNodes[i].data.id === id) {
          count = count + 1
        }
      }
      return count
    },

    copyNodeData: function (node) {
      if (this.copyNode) {
        node.data.id = this.uuid(12)
        for (let i = 0; i < node.children.length; i++) {
          this.updateNodeOriginalID(node.children[i])
        }
      }
    },

    updateNodeOriginalID: function (node) {
      if (node) {
        node.data.id = this.uuid(12)
        let children = node.children
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeOriginalID(node.children[i])
          }

        } else {
          return
        }
      }
      else {
        return
      }


    },

    updateNodeID: function (node, idMaps) {
      if (node) {
        if (idMaps[node.data.id]) {
          let id_keys = idMaps[node.data.id].split(':')
          node.data.id = id_keys[0]
          node.data.OriginalID = id_keys[1]
        }
        let children = node.children
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeID(node.children[i], idMaps)
          }

        } else {
          return
        }
      }
      else {
        return
      }


    },


    uuid: function (len, radix) {
      let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
      let uuid = [],
        i;
      radix = radix || chars.length;

      if (len) {
        // Compact form
        for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
      } else {
        // rfc4122, version 4 form
        let r;

        // rfc4122 requires these characters
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
        uuid[14] = '4';

        // Fill in random data.  At i==19 set the high bits of clock sequence as
        // per rfc4122, sec. 4.1.5
        for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
            r = 0 | Math.random() * 16;
            uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
          }
        }
      }

      return uuid.join('')
    },
    getTopicTags: function () {
      this.$axios.get('/api/ci/case_tags?projectID__in=0,' + this.projectID + '&TagType__in=2,3').then(response => {
        this.projectTopicTags = response.data.result
        this.originalTags = response.data.result
      }, response => {

      })
    },

    closeSettingPanel: function () {
      this.showDetailSetting = false
      this.toolbarExtend = 0
      this.showDetailSettingIcon = true
    },

    expandDetailSettingBar: function () {
      this.showDetailSetting = true
      this.toolbarExtend = 350
      this.showDetailSettingIcon = false
    },

    showDetailSettingBar: function (isShow) {
      if (!this.showDetailSettingIcon) {
        if (isShow) {
          this.showDetailSetting = isShow
          this.toolbarExtend = 350

        } else {
          this.showDetailSetting = false
          this.toolbarExtend = 0
        }
      }
    },

    addNode(command) {
      this.minder.execCommand(command, '分支主题')
    },


    addResource: function (value) {
      if (this.tagExists(value) === 0) {
        let tempTag = { projectID: 1, TagType: 3, TagName: value }
        this.$axios.post('/api/ci/case_tags', tempTag).then(response => {
          response.data.result.TagChecked = true
          this.projectTopicTags.push(response.data.result)
          if (this.selectNodeData.resource) {
            this.selectNodeData.resource.push(value)
          } else {
            this.selectNodeData.resource = [value]

          }
        }, response => {

        })
      } else {
        this.$Message.error({
          content: '标签已经存在',
          duration: 10,
        })
      }
    },

    tagExists: function (value) {
      let result = 0
      for (let i = 0; i < this.projectTopicTags.length; i++) {
        if (value === this.projectTopicTags[i].TagName) {
          result = this.projectTopicTags[i].id
        }
      }
      return result
    },

    removeTag: function (e, value) {
      let tagID = this.tagExists(value)
      this.$axios.delete('/api/ci/case_tag/' + tagID + '/').then(response => {
        for (let i = 0; i < this.projectTopicTags.length; i++) {
          if (tagID === this.projectTopicTags[i].id) {
            this.projectTopicTags.splice(i, 1)
          }
        }
      }, response => {

      })
    },

    removeResource: function (checked, name) {
      let resource = this.selectNodeData.resource
      if (checked) {
        if (resource) {
          if (resource.indexOf(name) < 0) {
            resource.push(name)
          }
        } else {
          resource = [name]

        }
        this.minder.execCommand('Resource', resource)
      } else {
        if (resource) {
          for (let i = 0; i < resource.length; i++) {
            if (resource[i] === name) {
              resource.splice(i, 1)
              break
            }
          }
          this.minder.execCommand('Resource', resource)
        }
      }
    },



    resetLayout() {
      this.minder.execCommand('ResetLayout')
    },

    setLayout(layoutName) {
      this.minder.execCommand('Layout', layoutName)
    },

    setTheme(theme) {
      this.minder.execCommand('Theme', theme)
      this.updateFileProperty('Theme', theme, this.mindFileID)
    },

    setFontFamily: function (family) {
      this.minder.execCommand('FontFamily', family)
      this.updateAllNodeData('FontFamily', family)
    },

    setFontSize: function (size) {
      this.minder.execCommand('FontSize', size)
      this.updateAllNodeData('FontSize', size)
    },

    setBackgroundColor: function (color) {
      this.minder.execCommand('Background', color)
      this.updateAllNodeData('BackgroundColor', color)


    },

    setFontColor: function (color) {
      this.minder.execCommand('ForeColor', color)
      this.updateAllNodeData('FontColor', color)
    },


    setTemplate(name) {
      this.minder.execCommand('Template', name)
      this.updateFileProperty('Template', name, this.mindFileID)
    },

    zoomMindmap: function (value) {
      this.minder.execCommand('Zoom', value)
    },

    updateFileProperty: function (fieldName, value, fileID) {
      let parameters = {}
      parameters[fieldName] = value
      this.$axios.patch('/api/project/kitymind_file/' + fileID + '/', parameters).then(response => {

      }, response => {

      })

    },

    removeNode() {
      this.minder.execCommand('RemoveNode')
    },

    addPriority(priority) {
      this.minder.execCommand('Priority', priority)
    },

    addProgress(progress) {
      this.minder.execCommand('Progress', progress)
    },

    addImage() {
      this.minder.execCommand('Image', this.selectNodeData.image, '')
    },

    addLink() {
      this.minder.execCommand('HyperLink', this.selectNodeData.hyperlink, '');
    },


    addNote() {
      this.value1 = true
      this.toolbarExtend = 80
      this.minder.execCommand('Note', this.selectNodeData.note)
    },


    initProjectTags: function () {

      for (let y = 0; y < this.projectTopicTags.length; y++) {
        this.projectTopicTags[y].TagChecked = false
      }
    },

    showComments: function (e) {
      let shapeName = e.kityEvent.targetShape.container.__KityClassName
      if (shapeName && shapeName === 'NoteIcon') {
        // console.log(e)
        // console.log(e.kityEvent.originEvent.y)
        this.showCommentsContainer = true
        this.selectNodePosition.y = e.kityEvent.originEvent.y - 85 + 'px'
        this.selectNodePosition.x = e.kityEvent.originEvent.x - 100 + 'px'
      } else {
        this.showCommentsContainer = false
      }
    },

    bindevent() {


      this.minder.on('click', (e) => {
        let node = this.minder.getSelectedNode()
        if (node) {
          this.showDetailSettingBar(true)
          this.showComments(e)
          this.selectNodeData = node.data
          if (node.data['font-size']) {
            this.fontSize = node.data['font-size']
          } else {
            this.fontSize = 14
          }

          if (node.data['font-family']) {
            this.fontFamily = node.data['font-family']
          } else {
            this.fontFamily = '微软雅黑'
          }
        } else {
          this.showDetailSettingBar(false)
          this.selectNodeData = {}
          this.showCommentsContainer = false
        }
      })


      this.minder.on('keydown', (e) => {
        //console.log(e)
        if (e.originEvent) {
          if (e.originEvent.key === 'v' && (e.originEvent.metaKey || e.originEvent.ctrlKey)) {
            let node = this.minder.getSelectedNode()
            if (node) {
              this.copyNode = true
            }
          }
        }
      })

      this.minder.on('mousedown', (e) => {
        //console.log(e)
        if (e.originEvent) {
          if (e.originEvent.button === 2 || e.originEvent.button === 0) {
            let node = this.minder.getSelectedNode()
            if (node) {
              this.showDetailSettingBar(true)
              this.showComments(e)
              this.selectNodeData = node.data
              this.initProjectTags()
              if (this.selectNodeData.resource) {
                for (let i = 0; i < this.selectNodeData.resource.length; i++) {
                  //console.log(this.selectNodeData.resource[i])
                  for (let y = 0; y < this.projectTopicTags.length; y++) {
                    //console.log(this.projectTopicTags[y])
                    if (this.selectNodeData.resource[i] === this.projectTopicTags[y].TagName) {
                      this.projectTopicTags[y].TagChecked = true
                      break
                    }
                  }
                }
              }
              if (node.data['font-size']) {
                this.fontSize = node.data['font-size']
              } else {
                this.fontSize = 14
              }

              if (node.data['font-family']) {
                this.fontFamily = node.data['font-family']
              } else {
                this.fontFamily = '微软雅黑'
              }
            } else {
              this.showDetailSettingBar(false)
              this.showCommentsContainer = false
            }
          }
        }
      })

      this.minder.on('selectionchange', (e) => {
        var node = this.minder.getSelectedNode()
        if (node) {
          this.selectNodeData = node.data
          this.copyNodeData(node)
          this.copyNode = false
          //console.log('You selected: "%s"', node.getText());
        }
      });
    },

    // https://github.com/fex-team/kityminder-core/wiki/command
    // https://github.com/fex-team/kityminder-core/wiki/api
  },

  watch: {
    'selectNodeData.resource': function (value) {
      this.initProjectTags()
      if (value) {
        for (let i = 0; i < value.length; i++) {
          //console.log(value[i])
          for (let y = 0; y < this.projectTopicTags.length; y++) {
            //console.log(this.projectTopicTags[y])
            if (value[i] === this.projectTopicTags[y].TagName) {
              this.projectTopicTags[y].TagChecked = true
              break
            }
          }
        }
      }
    },

    //            mindFileID: function (value) {
    //                if (value !==0) {
    //                    this.importData = []
    //                    this.$axios.get('/api/project/kitymind_file/'+value +'/').then(response => {
    //                        this.importData = response.data.result.data
    //                    }, response => {
    //
    //                    })
    //                }
    //            }
  }


}
</script>

<style lang="less">
#mindContainer .minder-editor-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: inherit;
  top: 67px;
  font-family: Arial, "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

.top {
  display: none;
}

.toolBar {
  position: fixed;
  top: 115px;
  width: 100%;
  left: 0px;
}

.toolRightPanel {
  position: fixed;
  top: 179px;
  width: 350px;
  right: 0px;
  height: 100%;
}

.toolRightPanelIcon {
  position: fixed;
  top: 200px;
  width: 40px;
  right: 100px;
  box-shadow: 0px 10px 5px #888888;
  border-radius: 25px;
  cursor: pointer;
  /*border: 2px solid #50C28B;*/
}

.mind-head-item {
  width: 60px;
  display: inline-block;
  cursor: pointer;
}

#mindContainer .minder-editor-container .tabBar {
  display: none;
}

.mindmap-comments-container {
  position: absolute;
  background: #FFD;
  padding: 5px 15px;
  border-radius: 5px;
  max-width: 400px;
  max-height: 200px;
  overflow: auto;
  z-index: 10;
  box-shadow: 0 0 15px rgba(0, 0, 0, .5);
  word-break: break-all;
  white-space: normal;
  font-size: 12px;
  color: #333;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
