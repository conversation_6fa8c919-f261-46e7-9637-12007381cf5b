<template>
  <div>
    <!--<minder :projectID="projectID"></minder>-->
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
//import minder from './mind.vue'

export default {
  name: 'projectTestCase',
  props: ['projectID'],
  data() {
    return {
      data: [99, 71, 78, 25, 36, 92],
      line: '',
    }
  },
  mounted() {
    this.calculatePath();
  },
  methods: {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),

  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapGetters(['appBodyHeight', 'appBodyWidth']),
    containerHeight: function () {
      return this.appBodyHeight
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

  },
  created: function () {

  },
  watch: {

  },

  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;

}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}
</style>
