<template>
  <div :style="'height:' + appBodyHeight + 'px;' + ' overflow-y: scroll'">
    <div v-loading="showLoading" element-loading-text="Loading ..." element-loading-background="rgba(238, 242, 246)"
      class="layout-content portal-list-container">
      <div v-for="project in myProjectList" :key="project.id">
        <Card v-if="project.Display" style="width:230px;height:180px;float:left;margin:20px;" :bordered="false">
          <div slot="title">
            <Dropdown>
              <span>
                <Icon type="ios-more" />
              </span>
              <DropdownMenu slot="list">
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/requirement'" :exact="false" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <Icon type="ios-flask"></Icon> 需求
                  </router-link>
                </DropdownItem>
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/task'" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <Icon type="md-list" /> 任务
                  </router-link>
                </DropdownItem>
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/test/test-case'" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <span>
                      <Icon type="ios-key" /> 测试点
                    </span>
                  </router-link>
                </DropdownItem>
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/fortesting'" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <Icon type="ios-bus" /> 提测
                  </router-link>
                </DropdownItem>
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/issue/all'" :exact="false" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <i class="fa fa-fw  fa-bug"></i> 问题
                  </router-link>
                </DropdownItem>
                <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/statistics'" :exact="false" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <Icon type="ios-stats" /> 统计
                  </router-link>
                </DropdownItem>
                <!-- <DropdownItem>
                  <router-link :to="'/project/' + project.id + '/documents'" tag="li"
                    active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                    <Icon type="md-albums" /> 文件
                  </router-link>
                </DropdownItem> -->
              </DropdownMenu>
            </Dropdown>
          </div>
          <div slot="extra">
            <Dropdown>
              <span>
                <Icon type="ios-settings-outline" />
              </span>
              <DropdownMenu slot="list">
                <DropdownItem name="1:项目信息:info">
                  <Icon type="md-podium" />
                  <router-link :to="'/project/' + project.id + '/settings/info'" tag="span">项目信息</router-link>
                </DropdownItem>
                <DropdownItem name="2:版本规划:version">
                  <Icon type="ios-paw" />
                  <router-link :to="'/project/' + project.id + '/settings/version'" tag="span">版本规划</router-link>
                </DropdownItem>
                <DropdownItem name="3:成员:member">
                  <Icon type="md-people" />
                  <router-link :to="'/project/' + project.id + '/settings/member'" tag="span">成员</router-link>
                </DropdownItem>
                <DropdownItem name="4:模块:module">
                  <Icon type="ios-cube" />
                  <router-link :to="'/project/' + project.id + '/settings/module'" tag="span">模块</router-link>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div style="text-align:center;height:90px;">
            <router-link :to="'/project/' + project.id + '/test/test-case'" tag="div" class="cursor-hand">
              <img :src="'' + project.PBAvatar" class="project_icon">
            </router-link>
          </div>
          <div>
            <div style="padding-top: 0px;color:#5578aa;text-align: center">{{ project.PBTitle }}</div>
          </div>
        </Card>
      </div>

      <project-create-dialog></project-create-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState, mapActions } from "vuex";
import ProjectCreateDialog from "./ProjectCreateDialog.vue";

export default {
  name: "projectList",
  data() {
    return {
    };
  },
  computed: {
    ...mapState(['appBodyHeight', 'appBodyMainHeight']),
    ...mapGetters("project", ["projectSearchKey", "getFilterProjectList", "projectList"]),
    ...mapState({
      showLoading: state => state.project.showLoadingProjectList
    }),
    myProjectList: function () {
      return this.getFilterProjectList;
    },
  },
  methods: {
    ...mapMutations("project", ["setProjectAdded", "setShowLoadingProjectList"]),
    ...mapActions('project', ['loadMyProjectList']),
    filterProjectList: function (value) {
      for (let i = 0; i < this.myProject.length; i++) {
        let temp = this.myProject[i];
        if (temp.PBTitle.toUpperCase().indexOf(value.toUpperCase()) < 0) {
          temp.Display = false;
        }
      }
    },

    initProjectDisplayStatus: function () {
      for (let i = 0; i < this.myProject.length; i++) {
        this.myProject[i].Display = true;
      }
    },
  },

  created: function () {
    this.loadMyProjectList();
  },
  components: { ProjectCreateDialog },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
/*@import "../../assets/teamvision/global/less/common_controlls";*/

.project_icon {
  width: 80%;
  height: 80px;
}
</style>
