<template>
  <div style="padding: 4px;" :style="'overflow-y:scroll;height:' + containerHeight + 'px'">
    <!--
    <Row>
      <Col span="16" style="padding-right: 2px;">
        <Card  dis-hover>
          <p slot="title">自动化用例排行</p>
          <div style="height: 200px;" id="autoCaseStatistics" >
          </div>
        </Card>
      </Col>
      <Col span="8" style="padding-right: 0px;">
        <Card  dis-hover  >
          <p slot="title">用例数量</p>
          <Row>
            <Col span="24">
              <div style="height: 200px; text-align: center;" >
                <i-circle :percent="100" stroke-color="#585eaa" :size="180">
                  <div class="home-project-circle">
                    <h1>{{ case_total_count.AutoCount }}</h1>
                    <p>自动化用例总数</p>
                    <span>
                    </span>
                  </div>
                </i-circle>
              </div>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    -->
    <Card dis-hover>
      <span slot="title" style="height:24px;">
        <!--
        <span style="margin-right: 10px;">
          <Select v-model="projectID" style="width:200px" size="small" filterable clearable placeholder="选择项目" @on-change="filterAutoCase">
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </span>
        -->
        <RadioGroup v-model="caseType" type="button" size="small" @on-change="filterAutoCase">
          <Radio :label="0">全部</Radio>
          <Radio :label="1">API</Radio>
          <Radio :label="2">WebUI</Radio>
        </RadioGroup>
        <Input v-model="caseName" style="width: 300px; margin-left: 20px;" size="small"
          placeholder="用例名称（TestCaseName）, Enter搜索" search @on-search="filterAutoCase" />
        <span v-if="autoCaseList.results" style="padding-left: 20px; size: 12px;">项目 [{{ projectName }}] 符合条件的自动化用例共计：
          <i>{{ autoCaseList.count }}
          </i>个</span>
      </span>
      <span slot="extra">
        <span style="margin-right: 4px;" @click="clearProjectCase">
          <Tooltip content="删除项目全部用例, 谨慎操作">
            <Button size="small" type="dashed">
              <Icon type="ios-trash-outline" :size="14" /> 删除项目用例
            </Button>
          </Tooltip>
        </span>
      </span>
      <div :style="'overflow-y:scroll;height:' + tableHeight + 'px'">
        <Table size="small" highlight-row border ref="selection" :columns="columns1" :data="autoCaseList.results" stripe
          :loading="loadingCaseResult" @on-select-all="getSelectItem" @on-selection-change="getSelectItem">
          <template slot-scope="{ row, index }" slot="action">
            <Button @click="removeCase(row)" shape="circle" icon="ios-trash"></Button>
          </template>
        </Table>
        <div style="margin: 10px;overflow: hidden">
          <div style="float: right;">
            <Page :total="autoCaseList.count" :page-size="20" :current="1" @on-change="changePage"></Page>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from "vuex";
import HighCharts from "highcharts";

export default {
  name: "ProjectAutoCaseList",
  props: ["projectID"],
  data() {
    return {
      case_total_count: {},
      columns1: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "ID",
          key: "id",
          width: 80,
          sortable: true,
        },
        {
          title: "描述",
          key: "Desc",
        },
        {
          title: "AutoCaseName",
          key: "AutoCaseName",
        },
        {
          title: "用例类型",
          key: "CaseTypeName",
          width: 120,
        },
        {
          title: "TestCaseKey",
          key: "TestCaseKey",
          width: 120,
        },
        {
          title: "操作",
          slot: "action",
          width: 120,
          align: "center",
        },
      ],
      autoCaseList: {},
      projectList: [],
      //projectID: 0,
      caseType: 0,
      caseName: "",
      selectItemId: [],
      projectName: "",
    };
  },
  computed: {
    ...mapState(["appBodyMainHeight", 'appBodyHeight']),
    ...mapState('usercenter', ["userInfo"]),
    ...mapGetters("citask", ["ciTaskStart"]),

    containerHeight: function () {
      return this.appBodyHeight;
    },
    tableHeight: function () {
      return this.appBodyHeight - 100;
    }
  },
  methods: {
    ...mapMutations("citask", ["setCITaskStart"]),
    getAutoCaseList: function (projectID, caseType, caseName) {
      let resultFilter = this.getFilter(projectID, caseType, caseName);

      this.loadingCaseResult = true;
      this.$axios.get("/api/ci/auto_cases?page_size=20" + resultFilter).then(
        (response) => {
          this.autoCaseList = response.data.result;
          this.loadingCaseResult = false;
        },
        (response) => { }
      );
    },

    loadProjectList: function () {
      this.$axios.get("/api/project/list").then(
        (response) => {
          this.projectList = response.data.result;
          for (let i = 0; i < this.projectList.length; i++) {
            if (this.projectList[i]["id"] == this.projectID) {
              //console.log(this.projectList[i]["id"], this.projectList[i]["PBTitle"])
              this.projectName = this.projectList[i]["PBTitle"];
            }
          }
        },
        (response) => { }
      );
    },

    clearProjectCase: function () {
      if (this.projectID !== 0) {
        //let selectProject = null;
        //for (let i = 0; i < this.projectList.length; i++) {
        //  if (this.projectList[i].id === this.projectID) {
        //    selectProject = this.projectList[i];
        ///  }
        //}
        this.$Modal.confirm({
          title: "删除确认",
          content:
            "您即将删除项目 [" + this.projectName + "] 的全部自动化用例",
          onOk: () => {
            this.$axios
              .delete(
                "/api/ci/auto_cases/delete/" +
                this.projectID +
                "?CaseType=" +
                this.caseType
              )
              .then(
                (response) => {
                  this.autoCaseList = [];
                  this.$Message.success({
                    content: "用例删除成功",
                    duration: 10,
                    closable: true,
                  });
                },
                (response) => {
                  this.$Message.error({
                    content: "用例删除失败",
                    duration: 10,
                    closable: true,
                  });
                }
              );
          },
          onCancel: () => { },
        });
      } else {
        this.$Message.warning({
          content: "请选择项目，完成项目用例清除。",
          duration: 10,
          closable: true,
        });
      }
    },

    changePage: function (value) {
      let resultFilter = this.getFilter(
        this.projectID,
        this.caseType,
        this.caseName
      );
      this.loadingCaseResult = true;
      this.$axios.get("/api/ci/auto_cases?page_size=20" + resultFilter + "&page=" + value).then(
        (response) => {
          this.autoCaseList = response.data.result;
          this.loadingCaseResult = false;
        },
        (response) => { }
      );
    },

    filterAutoCase: function (value) {
      this.getAutoCaseList(this.projectID, this.caseType, this.caseName);
    },

    removeCase: function (row) {
      var content = ''

      if (this.selectItemId.find((item) => { item === row.id })) {
        content = "CaseName[" + row.CaseName + "]</br>CaseID[" + this.selectItemId + "]"
      } else {
        content = "CaseName[" + row.CaseName + "]</br>CaseID[" + row.id + ',' + this.selectItemId + "]"
      }


      this.$Modal.confirm({
        title: "删除确认",
        content: content,
        onOk: () => {
          this.$axios.delete("/api/ci/auto_case/" + row.id).then((response) => {
            for (let i = 0; i < this.autoCaseList.results.length; i++) {
              if (this.autoCaseList.results[i].id === row.id) {
                this.autoCaseList.results.splice(i, 1);
                break;
              }
            }
            this.$Message.success({
              content: "用例删除成功",
              duration: 3,
              closable: true,
            });
          }, (response) => {
            this.$Message.error({
              content: "用例删除失败",
              duration: 3,
              closable: true,
            });
          }
          );
          if (this.selectItemId.length > 0) {
            this.clearSelectCase()
          }
          this.selectItemId = []
        },
        onCancel: () => { },
      });
    },

    clearSelectCase: function () {
      this.$axios.post("/api/project/autocase/delete", { ids: this.selectItemId }).then((response) => {
        for (let i = 0; i < this.autoCaseList.results.length; i++) {
          for (let j = 0; j < this.selectItemId.length; j++) {
            if (this.autoCaseList.results[i].id === this.selectItemId[j]) {
              this.autoCaseList.results.splice(i, 1);
              //console.log("del=", this.autoCaseList.results[i].id)
              break;
            }
          }
        }
      });
    },

    getFilter: function (projectID, caseType, caseName) {
      let resultFilter = "";
      if (projectID !== 0) {
        resultFilter = resultFilter + "&ProjectID=" + projectID;
      }
      if (caseType !== 0) {
        resultFilter = resultFilter + "&CaseType=" + caseType;
      }
      if (caseName.trim() !== "") {
        resultFilter = resultFilter + "&CaseName__icontains=" + caseName;
      }

      return resultFilter;
    },

    loadProjectAutoCaseStatistics: function () {
      this.$axios.get("/api/home/<USER>/project_autocase_column").then(
        (response) => {
          //console.log(response.data.result);
          let option = this.createColumnChart(response.data.result);
          HighCharts.chart("autoCaseStatistics", option);
        },
        (response) => { }
      );
    },

    loadCaseTotalCount: function () {
      this.$axios.get("/api/home/<USER>/case_total_count").then(
        (response) => {
          this.case_total_count = response.data.result;
        },
        (response) => { }
      );
    },

    //生成简单柱状图
    createColumnChart: function (data) {
      let option = {
        chart: {
          type: data["chart_type"],
        },
        title: {
          text: data["chart_title"],
        },
        subtitle: {
          text: data["chart_sub_type"],
        },
        legend: {
          layout: data["legend_layout"],
          align: data["legend_align"],
        },
        xAxis: {
          categories: data["xaxis"],
          crosshair: true,
        },
        yAxis: {
          min: 0,
          title: {
            text: data["series_name"],
          },
        },
        tooltip: {
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat:
            '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
            '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
          footerFormat: "</table>",
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            borderWidth: 0,
            colorByPoint: true,
            showInLegend: data["show_legend"],
          },
        },
        series: data["series_data"],
      };
      return option;
    },
    getSelectItem: function (selection) {
      this.selectItemId = [];
      for (var i = 0; i < selection.length; i++) {
        this.selectItemId.push(selection[i]["id"]);
      }
      //console.log("selection id list =====", this.selectItemId);
    },
  },

  created: function () {
    //this.loadProjectAutoCaseStatistics()
    this.getAutoCaseList(this.projectID, 0, "");
    this.loadProjectList();
    //this.loadCaseTotalCount()
  },

  mounted: function () { },

  watch: {},
  components: {
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.history-card {
  /*margin-left:10px;*/
  /*margin-right: 10px;*/
  /*margin-top:1px;*/
  min-height: 80px;
}

.tag {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d3d7d4;
  position: relative;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 5px;
  padding: 10px;
  font-size: 12px;
}

.tag-em {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -40px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #d3d7d4;
  font-size: 0;
  line-height: 0;
}

.tag-span {
  display: block;
  border-width: 20px;
  position: absolute;
  top: -33px;
  border-style: solid dashed dashed;
  border-color: transparent transparent #fff;
  font-size: 0;
  line-height: 0;
}

.product-container {
  padding: 10px;
  display: inline-block;
  border: 1px solid #f5f7f9;
  border-radius: 5px;
  margin-bottom: 15px;
}
</style>
