<template>
  <div>
    <div v-if="newWebPart">
      <Form ref="formInline" :model="formItem" :label-width="80">
        <FormItem prop="user" label="用途">
          <Input v-model="formItem.Title" style="width: 70%;" placeholder="报告用途"></Input>
        </FormItem>
        <FormItem prop="user" label="自动化任务">
          <Select v-model="formItem.taskID" @on-change="loadCITaskHistories" style="width:70%">
            <Option v-for="task in autoTestTaskList" :value="task.id" :key="task.id">{{ task.TaskName }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="password" label="执行记录">
          <Select v-model="formItem.historyID" style="width: 70%;">
            <Option :value="history.id" :key="history.id" v-for="history in autoTestTaskHistoryList">#{{
              history.BuildVersion }}--{{ history.StartTime }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="user" label="结论">
          <Input v-model="formItem.comments" style="width: 70%;" placeholder="自动化执行结果结论"></Input>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="saveWebPart('formInline')">保存</Button>
        </FormItem>
      </Form>
    </div>
    <div v-else>

    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { getCITaskList, getCITaskHistoryList } from '../../../../ci/api/CIApiService'
// import { getTestReportWebPartList } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'TestReportAutoResultWebpart',
  props: ['projectID', 'reportID', 'newWebPart', 'reportData'],
  data() {
    return {
      pluginSelectColor: '#c5c8ce',
      autoTestTaskList: [],
      autoTestTaskHistoryList: [],
      formItem: {
        Title: '',
        taskID: 0,
        historyID: 0,
        comments: '',
        ReportID: this.reportID
      }
    }

  },
  computed: {
    ...mapGetters(['appBodyHeight', 'userInfo']),
    containerHeight: function () {
      if (this.appBodyHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyHeight - 100
      }
    },
  },
  methods: {
    saveWebPart: function () {
      this.newWebPart = false
    },
    loadProjectCITask: function () {
      let filter = 'Project=' + this.projectID + '&&page_size=10000&IsSample=1'
      getCITaskList(filter).then(response => {
        this.autoTestTaskList = response.data.result.results
      }, response => {

      })
    },
    loadCITaskHistories: function (taskID) {
      let filter = '&page_size=10000&IsSample=1'
      getCITaskHistoryList(taskID, filter).then(response => {
        this.autoTestTaskHistoryList = response.data.result.all_histories.results
      }, response => {

      })
    }
  },
  created() {
    this.loadProjectCITask()
  },
  mounted() {
  },
  watch: {

  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less"></style>
