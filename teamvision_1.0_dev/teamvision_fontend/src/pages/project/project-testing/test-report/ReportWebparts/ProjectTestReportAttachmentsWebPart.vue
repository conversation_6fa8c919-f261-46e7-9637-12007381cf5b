<template>
  <div>
    <div>
      <List footer="Footer" border>
        <ListItem>This is a piece of text.</ListItem>
        <ListItem>This is a piece of text.</ListItem>
        <ListItem>This is a piece of text.</ListItem>
        <div slot="footer">
          <Upload ref="upload" :show-upload-list="false" :default-file-list="formItem.attachments.defaultList"
            :on-success="handleSuccess" :format="['jpg', 'jpeg', 'png', 'pdf', 'txt', 'sql', 'docx', 'doc', 'xlsx', 'xmind']"
            :max-size="10240" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize"
            :before-upload="handleBeforeUpload" multiple type="drag" action="/api/project/fortesting/upload_files"
            style="display: inline-block;width:100px;">
            <div style="width: 100px;height:100px;line-height: 100px;">
              <Icon type="ios-camera-outline" :size="20" />
            </div>
          </Upload>
        </div>
      </List>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
// import { getTestReportWebPartList } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'TestReportAttachmentsWebpart',
  props: ['projectID', 'reportID', 'webpartData', 'newWebPart'],
  data() {
    return {
      pluginSelectColor: '#c5c8ce',
      formItem: {
        attachments: {
          defaultList: [],
          imgName: '',
          visible: false,
          uploadList: []
        }
      },
    }

  },
  computed: {
    ...mapGetters(['appBodyHeight', 'userInfo']),
    containerHeight: function () {
      if (this.appBodyHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyHeight - 100
      }
    },
  },
  methods: {
    handleView(name) {
      this.formItem.attachments.imgName = name
      this.formItem.attachments.visible = true
    },
    handleRemove(file) {
      const fileList = this.formItem.attachments.uploadList
      this.formItem.attachments.uploadList.splice(fileList.indexOf(file), 1)
      this.removeFile(file.id)
    },
    handleSuccess(res, file) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.formItem.attachments.uploadList = this.$refs.upload.fileList
    },
    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\',\'pdf\',\'txt\',\'sql\',\'docx\',\'doc\',\'xlsx\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过10M限制',
        duration: 10,
        closable: true
      })
    },
    handleBeforeUpload() {
    },

  },
  created() {
  },
  mounted() {
  },
  watch: {

  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less"></style>
