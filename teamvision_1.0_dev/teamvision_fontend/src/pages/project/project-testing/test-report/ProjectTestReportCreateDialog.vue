<template>
  <div>
    <Modal :value="createDialogShow" :title="'创建' + reportDialogTitle + '测试报告'" :mask-closable="false"
      class-name="vertical-center-modal" :width="480" @on-cancel="cancel">
      <div style="height:400px;overflow-y: scroll;overflow-x: hidden">
        <Form ref="createTestReport" :model="formItem" :rules="ruleCustom">
          <FormItem label="报告名称" prop="Title" style="width: 100%">
            <Input v-model="formItem.Title" :maxlength="50" show-word-limit placeholder="报告标题" />
          </FormItem>
          <FormItem label="测试计划" prop="TestPlan">
            <Select transfer prefix="ios-person" v-model="formItem.TestPlan" :filterable="true" multiple
              placeholder="报告包含的计划">
              <Option v-for="plan in projectTestPlans" :key="plan.id" :value="plan.id">[{{ plan.id }}]{{ plan.Title }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="版本" prop="Version">
            <Select transfer v-model="formItem.Version" @on-change="onVersionChange" :filterable="true"
              placeholder="报告所属版本">
              <Option v-for="version in projectVersions" :key="version.id" :value="version.id">[{{ version.id }}]{{
                version.VersionLabel }}
              </Option>
            </Select>
          </FormItem>
          <Divider orientation="left">报告内容</Divider>
          <div>
            测试结果：
            测试用例状态统计；用例测试次数排名；用例测试失败次数排名；缺陷分布
          </div>
        </Form>
      </div>
      <div slot="footer">
        <Button v-if="createDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="ok('createTestReport')">添加
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { VueEditor } from 'vue2-editor'
import { testReportValidateRules } from '../business-service/ProjectTestReportCreateDialog.js'
import { createTestReport } from '../business-service/ProjectTestCaseApiService'
import { loadProjectTestPlan, loadProjectVersions } from '../../business-service/ProjectApiService'

export default {
  name: 'ProjectTestReportCreateDialog',
  props: ['projectID', 'testReportType'],
  data() {
    return {
      projectVersions: [],
      projectTestPlans: [],
      formItem: {
        Title: '',
        Project: 0,
        Version: 0,
        Creator: 0,
        ReportType: 2,
        Status: 0,
        TestPlan: [],
        CopyID: 0
      },
      ruleCustom: {
        ...testReportValidateRules
      }
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['createDialogShow', 'viewDialogShow', 'projectVersion']),
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapState('projectglobal', ['createDialogShow']),

    containerHeight: function () {
      if (this.appBodyHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyHeight - 100
      }
    },

    project: function () {
      return parseInt(this.projectID)
    },

    reportDialogTitle: function () {
      if (this.testReportType === '2') {
        return '冒烟'
      }
      if (this.testReportType === '1') {
        return '进度'
      }
      if (this.testReportType === '3') {
        return '完成'
      }
    }

  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setCaseSelectDialogShow', 'setTaskChange', 'setCreateReqType']),
    ...mapMutations('issue', ['setIssueChange']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.formItem.Creator = this.userInfo.id
          this.formItem.Project = this.projectID
          this.formItem.ReportType = this.testReportType
          createTestReport(this.formItem).then(response => {
            this.setCreateDialogShow(false)
            this.$emit("createReport", response)
          },
            response => {
              this.setCreateDialogShow(false)
            })
          this.$refs[name].resetField()
        }
      })
    },
    cancel() {
      this.setCreateDialogShow(false)
    },

    getProjectVersions: function (projectID) {
      loadProjectVersions(projectID).then(response => {
        //console.log(response)
        this.projectVersions = response.data.result.all_versions
        //this.formItem.Version = response.data.result.latest_version
        this.getProjectTestPlans(this.projectID, this.formItem.Version)
      }, response => { })

    },

    getProjectTestPlans: function (projectID, versionID) {
      let filter = ''
      if (versionID !== 0) {
        filter = '?Version=' + versionID
      }
      loadProjectTestPlan(projectID, filter).then(response => {
        this.projectTestPlans = response.data.result
      }, response => { })

    },

    onVersionChange: function (versionID) {
      // console.log(versionID)
      this.getProjectTestPlans(this.projectID, versionID)
    }

  },
  created() {
    if (this.projectID) {
      this.getProjectVersions(this.projectID)
    }
  },

  mounted() {
  },

  watch: {
    projectID: function (value) {
      if (value) {
        this.getProjectVersions(this.projectID)
        this.getProjectTestPlans(this.projectID, this.formItem.Version)
      }
    }
  },
  components: {
    VueEditor,
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }
}
</style>
