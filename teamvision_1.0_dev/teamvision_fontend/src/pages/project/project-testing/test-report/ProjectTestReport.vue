<template>
  <div :style="'height:' + containerHeight + 'px'">
    <Card :bordered="false" :dis-hover="true" :padding="0" style="margin: 0px 24px 8px;">
      <div class="TitleSection">
        <div class="CaseReviewTitle">
          <h4 class="Title">报告管理</h4>
        </div>
      </div>
    </Card>
    <Card :bordered="false" :dis-hover="true" :padding="16" style="margin: 0px 24px 8px;">
      <div>
        <span>
          <Input style="width: 300px;" type="text" search placeholder="输入报告名称,回车搜索" @on-search="searchReport" />
        </span>
        <span style="padding-left: 16px;">
          <Button shape="circle" icon="ios-refresh" @click="refreshReportList"></Button>
        </span>
      </div>
      <span slot="extra">
        <Dropdown @on-click="newObject" :transfer="true">
          <!--<Button shape="circle" size="small" href="javascript:void(0)" style="font-size:12px;">-->
          <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
          <!--<Icon type="ios-arrow-down"></Icon>-->
          <!--</Button>-->
          <DropdownMenu slot="list">
            <DropdownItem name="2">
              <Icon type="ios-trending-down" style="padding-right: 10px" />
              <a style="color: inherit" href="#">冒烟测试</a>
            </DropdownItem>
            <DropdownItem name="1">
              <Icon type="ios-trending-up" style="padding-right: 10px" />
              <a style="color: inherit" href="#">进度报告</a>
            </DropdownItem>
            <DropdownItem name="3">
              <Icon type="ios-unlock-outline" style="padding-right: 10px" />
              <a style="color: inherit" href="#">完成报告</a>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </span>
    </Card>
    <Card :bordered="false" :dis-hover="true" :padding="0"
      :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden;margin: 0px 24px 8px;'">
      <el-table header-row-class-name="table-header" @cell-mouse-enter="mouseInOperationCell"
        @cell-mouse-leave="mouseOutOperationCell" :data="tableData.results" style="width: 100%">
        <el-table-column label="类型" width="60">
          <template slot-scope="scope">
            <span v-if="scope.row.ReportType === 1" style="display: inline-block;">
              <tooltip transfer content="进度报告">
                <Avatar style="background-color:#a6bcff" size="small">进</Avatar>
              </tooltip>
            </span>
            <span v-if="scope.row.ReportType === 2" style="display: inline-block;">
              <tooltip transfer content="冒烟报告">
                <Avatar style="background-color:#66cc66" size="small">冒</Avatar>
              </tooltip>
            </span>
            <span v-if="scope.row.ReportType === 3" style="display: inline-block;">
              <tooltip transfer content="完成报告">
                <Avatar style="background-color:#00ae9d" size="small">完</Avatar>
              </tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="报告名称">
          <template slot-scope="scope">
            <span class="TableContent">
              <span style="margin-left: 10px">
                <router-link :to="'/project/' + projectID + '/test/report/' + scope.row.id">#{{ scope.row.id }} {{
                  scope.row.Title }}
                </router-link>
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <div class="TableContent">
              {{ scope.row.view_data.status_name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            <div class="TableContent">
              {{ scope.row.CreationTime }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建人" width="160">
          <template slot-scope="scope">
            <div class="TableContent">
              {{ scope.row.view_data.creator_name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-link :underline="false">
              <router-link :to="'/project/' + projectID + '/test/report/' + scope.row.id">查看</router-link>
            </el-link>
            <el-divider direction="vertical"></el-divider>
            <el-dropdown>
              <span>更多<i class="el-icon-arrow-down el-icon--right"></i></span>
              <el-dropdown-menu slot="dropdown">
                <!-- <el-dropdown-item @click.native="copyReport(scope.row.id)">复制</el-dropdown-item> -->
                <el-dropdown-item @click.native="deleteReport(scope.row.id, scope.$index)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px; margin-bottom: 20px; height: 40px; float:right">
        <el-pagination small background layout="prev, pager, next, total" :total=tableData.count
          :current-page.sync="currPage" :page-size=pageSize @current-change="currClick" @next-click="nextClick"
          @prev-click="prevClick">
        </el-pagination>
      </div>
    </Card>
    <project-test-report-create-dialog :projectID="projectID" @createReport="createTestReport"
      :testReportType="testReportType">
    </project-test-report-create-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import ProjectTestReportCreateDialog from './ProjectTestReportCreateDialog.vue'
import { loadProjectTestReports, CopyProjectTestReport, deleteProjectTestReport } from '../../business-service/ProjectApiService'

export default {
  name: 'projectTestPlanExecute',
  props: ['projectID', 'version'],
  data() {
    return {
      tableData: [],
      formInline: {
      },
      testReportType: 'BVT',
      rowOperationShow: 0,
      currPage: 1,
      pageSize: 10,
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange', 'createDialogShow']),
    ...mapState(['appBodyMainHeight', 'appBodyWidth']),
    ...mapState('projectglobal', ['createDialogShow']),

    containerHeight: function () {
      return this.appBodyMainHeight
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    versionID: function () {
      if (this.$route.params.version) {
        return this.$route.params.version
      }
      return 0
    },

    project: function () {
      let result = 0
      if (this.projectID) {
        result = this.projectID
      }
      return result
    }

  },
  methods:
  {
    ...mapMutations('projectglobal', ['setCreateDialogShow']),

    createTestReport: function (response) {
      this.tableData.results.splice(0, 0, response.data.result)
    },

    newObject(value) {
      this.testReportType = value
      this.setCreateDialogShow(true)
    },

    getTestReports: function (filter) {
      loadProjectTestReports(this.projectID, filter).then(response => {
        this.tableData = response.data.result
      })
    },

    prevClick: function () {
      this.currPage = this.currPage - 1
      let currPageFilter = "?page=" + this.currPage + "&page_size=" + this.pageSize
      this.getTestReports(currPageFilter)
    },

    nextClick: function () {
      this.currPage = this.currPage + 1
      let currPageFilter = "?page=" + this.currPage + "&page_size=" + this.pageSize
      this.getTestReports(currPageFilter)
    },

    currClick: function () {
      let currPageFilter = "?page=" + this.currPage + "&page_size=" + this.pageSize
      this.getTestReports(currPageFilter)
    },

    refreshReportList: function () {
      this.getTestReports('')
    },

    searchReport: function (searchWord) {
      let filter = ''
      if (searchWord.trim().length !== 0) {
        filter = '?Title__icontains=' + searchWord
      }
      this.getTestReports(filter)
    },
    mouseInOperationCell: function (row, colummn, cell, event) {
      this.rowOperationShow = row.id
    },
    mouseOutOperationCell: function (row, colummn, cell, event) {
      this.rowOperationShow = 0
    },

    copyReport: function (reportID) {
      CopyProjectTestReport(reportID).then(response => {
        this.tableData.splice(0, 0, response.data.result)
      }, response => {

      })
    },

    deleteReport: function (reportID, index) {
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除测试报告[#' + reportID + ']',
        onOk: () => {
          deleteProjectTestReport(reportID).then(response => {
            this.$Message.success({
              content: '删除成功',
              duration: 3,
              closable: true
            })
            this.tableData.results.splice(index, 1)
          }, response => {
            this.$Message.error({
              content: '删除失败',
              duration: 3,
              closable: true
            })
          })
        },
        onCancel: () => {
        }
      })
    }
  },

  created: function () {
    if (this.projectID) {
      this.getTestReports('')
    }
  },

  mounted: function () {
  },

  watch: {
    projectID: function (value) {
      if (this.projectID) {
        this.getTestReports('')
      }
    }

  },

  components: {
    ProjectTestReportCreateDialog
  }
}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.TitleSection {
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding: 18px 0px 5px 0px;
  background-color: #f5f7f9;
}

.ivu-table th {
  height: 40px;
  white-space: nowrap;
  overflow: hidden;
  background-color: red;
}

.Title {
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  color: rgb(32, 45, 64);
}

.InputDom-mediumSize {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  outline: none;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.07);
  border-image: initial;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  padding: 6px 40px 6px 12px;
}

.DateTo {
  font-size: 14px;
  color: rgb(145, 153, 163);
  margin: 0px 10px;
}

.FilterInputWrapper {
  width: 160px;
  font-size: 13px;
}

.ReportList {
  -webkit-box-flex: 1;
  flex-grow: 1;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 4px 0px;
  box-sizing: border-box;
  position: relative;
  margin: 0px 24px 38px;
  background: rgb(255, 255, 255);
  padding: 14px 24px 24px;
  border-radius: 3px;
}

.Table {
  width: 100%;
  text-align: left;
  table-layout: fixed;
}

FooterBarWrapper {
  height: 57px;
}

.TableTh {
  box-sizing: border-box;
}

.columnStyle>div {
  padding-top: 18px;
  padding-bottom: 18px;
  height: 56px;
}

.TableContent {
  font-weight: normal;
  font-family: "PingFang SC", "Helvetica Neue", "Hiragino Sans GB", "Segoe UI", "Microsoft YaHei", "微软雅黑", "sans-serif";
  color: #202D40;
  font-size: 12px;
}

.table-header {
  font-size: 12px;
}
</style>
