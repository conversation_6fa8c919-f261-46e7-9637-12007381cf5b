<template>
  <div>
    <Modal :value="dialogShow" title="添加报告内容" :mask-closable="false" class-name="vertical-center-modal" :width="800"
      @on-cancel="cancel">
      <div style="height:400px;overflow-y: scroll;overflow-x: hidden">
        <Row :gutter="16">
          <i-col v-for="webpart in webPartList" :key="webpart.id" span="8">
            <Card class="pluginContainer">
              <span slot="title">{{ webpart.Title }}</span>
              <span slot="extra">
                <Icon type="md-checkmark-circle-outline" :color="webpart.selectedColor" :size="24" />
              </span>
              <div @click="selectPlugin(webpart)"
                style="height: 80px;display: flex;align-items: center;justify-content: center;">
                <span style="padding: 10px;">
                  <Avatar style="background-color: #808695;" icon="md-trending-up" size="small" />
                </span>
                <span style="font-size: 12px;">
                  {{ webpart.Desc }}
                </span>
              </div>
            </Card>
          </i-col>
        </Row>
      </div>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="addReportWebpart">添加
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { getTestReportWebPartList } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'ProjectTestReportPluginDialog',
  props: ['projectID', 'reportID', 'dialogShow'],
  data() {
    return {
      webPartList: [],
      selectedWebParts: []
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion']),
    ...mapGetters(['appBodyHeight', 'userInfo']),
    containerHeight: function () {
      if (this.appBodyHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyHeight - 100
      }
    },

    project: function () {
      return parseInt(this.projectID)
    },
  },
  methods: {

    selectPlugin: function (webpart) {
      let webPartIndex = this.selectedWebParts.indexOf(webpart.id)
      if (webPartIndex >= 0) {
        this.selectedWebParts.splice(webPartIndex, webpart.id)
        webpart.selectedColor = ''
      } else {
        this.selectedWebParts.push(webpart.id)
        webpart.selectedColor = '#19be6b'
      }
      this.webPartList.sort()
    },

    cancel: function () {
      this.$emit('closeDialog')
      this.selectedWebParts = []
      for (let i = 0; i < this.webPartList.length; i++) {
        this.webPartList[i]['selectedColor'] = ''
      }

    },

    addReportWebpart: function () {
      let result = []
      let webPartIDs = []
      for (let i = 0; i < this.webPartList.length; i++) {
        this.webPartList[i]['selectedColor'] = ''
        webPartIDs.push(this.webPartList[i].id)
      }
      for (let i = 0; i < this.selectedWebParts.length; i++) {
        let index = webPartIDs.indexOf(this.selectedWebParts[i])
        if (index >= 0) {
          result.push(this.webPartList[index])
        }
      }
      this.$emit('addReportWebpart', result)
      this.selectedWebParts = []
    },

    loadTestReportWebPartList: function (projectID) {
      getTestReportWebPartList(projectID).then(response => {
        // console.log("loadTestReportWebPartList=", projectID)
        this.webPartList = response.data.result
        for (let i = 0; i < this.webPartList.length; i++) {
          this.webPartList[i]['selectedColor'] = ''
        }
      }, response => {

      })
      // console.log(this.webPartList)
    }
  },
  created() {
    this.loadTestReportWebPartList(this.projectID)
  },
  mounted() {
  },
  watch: {
    projectID: function (value) {
      this.loadTestReportWebPartList(value)
    }
  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.pluginContainer {
  cursor: pointer;
  margin-bottom: 10px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }
}
</style>
