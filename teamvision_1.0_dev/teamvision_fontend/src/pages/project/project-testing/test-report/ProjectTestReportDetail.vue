<template>
  <div :style="{ overflow: 'scroll' }">
    <div style="height: 100%; width: 100%;">
      <div ref="imageTofile" style="padding: 10px;">
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" class="SectionWrapper">
            <Tag color="primary">#{{ reportDetail.id }}</Tag>
            <label-editor-input v-if="reportDetail.Status != 1" :displayText="reportDetail.Title" :displayWidth="300"
              :search="false">
            </label-editor-input>
            <div v-if="reportDetail.Status == 1" v-html="reportDetail.Title"></div>
          </div>
          <div slot="extra" class="SectionWrapper">
            <span class="CreaterName">{{ reportDetail.view_data.creator_name }}</span>
            <span class="CreatorOperation">创建于</span>
            <span class="CreateTime" v-if="reportDetail.CreationTime">{{ reportDetail.CreationTime }}</span>
          </div>
          <div style="padding-left:16px;">
            <Row>
              <Col span="8">
              <div style="padding: 8px;">
                <span class="report-project-item-label">项目：</span>
                <span>{{ reportDetail.view_data.project_title }}</span>
              </div>
              <div style="padding: 8px;" v-if="reportDetail.view_data.version">
                <span class="report-project-item-label">版本：</span>
                <span>{{ reportDetail.view_data.version }}</span>
              </div>
              <div v-if="reportMembers.length > 0">
                <div style="padding: 8px;">
                  <span class="report-project-item-label">产品：</span>
                  <span> {{ reportMembers[0].PM }} </span>
                </div>
                <div style="padding: 8px;">
                  <span class="report-project-item-label">开发：</span>
                  <span> {{ reportMembers[0].Dev }} </span>
                </div>
                <div style="padding: 8px;">
                  <span class="report-project-item-label">测试：</span>
                  <span> {{ reportMembers[0].Tester }} </span>
                </div>
              </div>
              </Col>
              <Col span="16">
              <div style="padding-left: 16px;padding-right: 16px;">
                <div class="report-project-item-label" style="padding-bottom: 8px;">
                  <Icon type="ios-document" /> 报告总结
                </div>
                <div v-if="reportDetail.Status != 1">
                  <label-editor-vue-editor placeHolder="评审结论" @updateValue="updateReportComment"
                    :displayText="reportDetail.Summary">
                  </label-editor-vue-editor>
                </div>
                <div v-if="reportDetail.Status == 1" v-html="reportDetail.Summary"></div>
              </div>
              </Col>
            </Row>
          </div>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">1: 概要信息</div>
          <div style="padding-left:16px;">
            <Row type="flex" justify="start" style="height: 40px; color: #000000;font-weight: 500">
              <Col span="4">需求总数: {{ Summary.RequirementCount }}</Col>
              <Col span="4">用例总数: {{ Summary.CaseCount }}</Col>
              <Col span="4">缺陷总数: {{ Summary.IssueCount }}</Col>
            </Row>
            <Row type="flex" justify="start" style="height: 40px;color: #000000;font-weight: 500">
              <Col span="4">用例执行率: {{ Summary.CaseExecRate }}%</Col>
              <Col span="4">用例通过率: {{ Summary.CasePassRate }}%</Col>
            </Row>
            <Row type="flex" justify="start" style="height: 40px;color: #000000;font-weight: 500">
              <Col span="4">问题修复率: {{ Summary.IssueFixedRate }}%</Col>
              <Col span="4">重新激活率: {{ Summary.IssueReopenRate }}%</Col>
              <Col span="4">平均关闭时长: {{ Summary.IssueCloseAvgTime }}小时</Col>
              <Col span="4">平均解决时长: {{ Summary.IssueFixedAvgTime }}小时</Col>
            </Row>
          </div>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">2: 测试计划</div>
          <List v-if="reportDetail.view_data.test_plans" size="small" style="padding-left:16px;">
            <ListItem v-for="plan in reportDetail.view_data.test_plans" :key="plan.id">
              <Row style="width: 100%">
                <Col span="10">
                <span style="width: 50px;">#{{ plan.id }}</span>
                <span style="padding-right: 10px"> {{ plan.Title }} </span>
                </Col>
                <Col span="6">
                <span v-if="plan.StartTime">{{ plan.StartTime }}</span>
                <span v-else>未开始</span>
                <span>-</span>
                <span v-if="plan.FinishTime">{{ plan.FinishTime }}</span>
                <span v-else></span>
                </Col>
                <Col span="2">用例数:{{ plan.CaseCount }}</Col>
                <Col span="2">
                <Tag color="primary">{{ plan.view_data.status_name }}</Tag>
                </Col>
                <Col span="2" style="justify-content: center;align-items: center;display: flex;">
                <Avatar size="small" style="background-color: #87d068">{{ plan.view_data.creator_name }}</Avatar>
                </Col>
              </Row>
            </ListItem>
          </List>
          <div v-else>报告未关联测试计划</div>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">3: 需求列表</div>
          <List v-if="reportRequirements.length > 0" size="small" style="padding-left:16px;">
            <ListItem v-for="req in reportRequirements" :key="req.Requirement">
              <row style="  width: 100%">
                <Col span="18">
                <span style="width: 50px;">#{{ req.Requirement }}</span>
                <span> {{ req.Title }} </span>
                </Col>
                <Col span="6" style="justify-content: center;align-items: center;display: flex;">
                <span>
                  <Switch :disabled="reportDetail.Status == 1" v-model="req.Status" @on-change="updateReqStatus"
                    size="large" true-color="#19be6b" :true-value="7" :false-value="6">
                    <span slot="open">完成</span>
                    <span slot="close">进行</span>
                  </Switch>
                </span>
                </Col>
              </row>
            </ListItem>
          </List>
          <div v-else style="padding-left: 10px">报告关联计划中未关联任何提测需求</div>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 10px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">4: 测试结果</div>
          <Row style="padding-bottom: 10px;">
            <Col span="12" style="padding-right: 16px;">
            <Card :bordered="true" :dis-hover="true" :padding="16">
              <div slot="title">测试用例结果分布</div>
              <div style="height: 300px;" id="reportCaseResultPieChart"> </div>
            </Card>
            </Col>
            <Col span="12" style="padding-right: 16px;">
            <Card :bordered="true" :dis-hover="true" :padding="16">
              <div slot="title">问题状态分布</div>
              <div style="height: 300px;" id="issueStatusPieChart"> </div>
            </Card>
            </Col>
          </Row>
          <Row>
            <Col v-for="webPart in reportWebPartData" :key="'webpart' + webPart.id" span="12"
              style="margin-bottom: 10px;padding-right: 16px;">
            <Card :bordered="true" :dis-hover="true" :padding="16">
              <div slot="title">{{ webPart.Title }}</div>
              <span slot="extra">
                <Icon type="md-trash" color="#ed4014" :size="24" />
              </span>
              <test-report-auto-result-webpart v-if="webPart.id === 1" :projectID="projectID" :reportID="reportID"
                :reportData="webPart.WebPartData" :newWebPart="false"></test-report-auto-result-webpart>
            </Card>
            </Col>
            <Col v-for="webPart in reportWebParts" :key="'webpart' + webPart.id + webPart.time" span="12"
              style="margin-bottom: 10px;padding-right: 16px;">
            <Card :bordered="true" :dis-hover="true" :padding="16">
              <div slot="title">{{ webPart.Title }}</div>
              <span slot="extra">
                <Icon type="md-trash" color="#ed4014" :size="24" />
              </span>
              <test-report-auto-result-webpart v-if="webPart.id === 1" :projectID="projectID" :reportID="reportID"
                :newWebPart="true"></test-report-auto-result-webpart>
              <test-report-attachments-web-part v-if="webPart.id === 2" :projectID="projectID" :reportID="reportID"
                :newWebPart="true"></test-report-attachments-web-part>
            </Card>
            </Col>
            <Col span="12" style="padding-right: 16px;">
            <!-- <Card :bordered="true" :dis-hover="true" :padding="16">
            <div slot="title">添加更多结果报告</div>
            <div @click="openReportPluginDialog" class="newReportResult">
              <Icon type="md-add-circle" :size="64" />
            </div>
          </Card> -->
            </Col>
          </Row>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">5: 测试过程</div>
          <Card :bordered="true" :dis-hover="true" :style="{ 'margin-bottom': '10px' }" :padding="16">
            <div slot="title">测试用例列表</div>
            <List v-if="testReportCases.length > 0" size="small" style="padding-left:8px;">
              <ListItem v-for="testcase in testReportCases" :key="testcase.id">
                <Row style="width: 100%">
                  <Col span="16">
                  <span style="width: 50px;">#{{ testcase.id }}</span>
                  <span style="width: 50px;">{{ testcase.title }}</span>
                  <span style="width: 50px;">{{ testcase.Desc }}</span>
                  </Col>
                  <Col span="2">{{ testcase.result }} </Col>
                  <Col span="2">{{ testcase.owner_name }} </Col>
                </Row>
              </ListItem>
            </List>
          </Card>
          <Card :bordered="true" :dis-hover="true" :padding="16" :style="{ 'margin-bottom': '10px' }">
            <div slot="title">问题列表</div>
            <List v-if="testReportIssues.length > 0" size="small" style="padding-left:8px;">
              <ListItem v-for="issue in testReportIssues" :key="issue.id">
                <Row style="width: 100%">
                  <Col span="13">#{{ issue.id }} {{ issue.Title }} </Col>
                  <Col span="3">{{ issue.category_name }}</Col>
                  <Col span="2">{{ issue.priority_name }}</Col>
                  <Col span="2"> {{ issue.status_name.Name }} </Col>
                  <Col span="2"> {{ issue.processor_name }} </Col>
                  <Col span="2">{{ issue.creator_name }} </Col>
                </Row>
              </ListItem>
            </List>
          </Card>
          <Card :bordered="true" :dis-hover="true" :padding="16">
            <div slot="title">性能测试</div>
            <div style="padding-left: 16px;padding-right: 16px;">
              <div v-if="reportDetail.Status != 1">
                <label-editor-vue-editor placeHolder="性能测试报告" @updateValue="updateReportPerformance"
                  :displayText="reportDetail.Performance">
                </label-editor-vue-editor>
              </div>
              <div v-if="reportDetail.Status == 1" v-html="reportDetail.Performance ? reportDetail.Performance : '无'">
              </div>
            </div>
          </Card>
        </Card>
        <Card :bordered="false" :dis-hover="true" style="padding: 12px;">
          <div slot="title" style="color: #000000;font-weight: 700;font-size: 15px; padding-left: 4px;">6: 其他及附件</div>
          <Card :bordered="true" :dis-hover="true" :padding="16">
            <div slot="title">附件列表</div>
            <div>
              <div v-if="reportDetail.attachments_detail.length > 0">
                <Row v-for="attachment in reportDetail.attachments_detail" :key="attachment.id" class="attachment-item">
                  <Col :span="16">
                  <span class="cursor-hand"
                    @click="onViewAttachment(attachment.FileName, attachment.id, attachment.FileSuffixes)">
                    <Icon type="ios-attach" :size="15" />{{ attachment.FileName }}
                  </span>
                  </Col>
                  <Col :span="4">
                  <a :href="'/api/project/testreport/' + reportID + '/attachment/' + attachment.id">
                    <Icon type="ios-cloud-download" :size="20" />
                  </a>
                  <span v-if="reportDetail.Status == 0" class="cursor-hand"
                    @click="delectAttachment(attachment.id, attachment.FileName)">
                    <Icon type="ios-trash" :size="20" />
                  </span>
                  </Col>
                  <Col :span="4">
                  <Time :time="attachment.CreationTime" />
                  </Col>
                </Row>
              </div>
              <div v-else>
                无附件
              </div>
              <Upload v-if="reportDetail.Status == 0" ref="upload" multiple type="drag" paste
                :action="'/api/project/testreport/' + reportID + '/attachments'" :format="[]" :max-size="10240"
                :on-success="handleSuccess" :on-remove="handleRemove" :default-file-list="defaultList"
                :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
                <Icon type="ios-cloud-upload" size="18" style="color: #3399ff"></Icon>
                点击、拖拽、粘贴上传附件
              </Upload>
            </div>
          </Card>
        </Card>
        <test-report-plugin-dialog :projectID="projectID" :reportID="reportID" :dialogShow="showReportPluginDialog"
          @addReportWebpart="addReportWebpart" @closeDialog="closeDialog">
        </test-report-plugin-dialog>
      </div>
    </div>
    <Card dis-hover shadow style="margin-top: 20px;">
      <div style="width: 100%; text-align: right;">
        <span>
          <Button shape="circle" size="large" @click="sendTestReport" type="success">发送测试报告</Button>
        </span>
        <span>
          <Button shape="circle" size="large" @click="sendTestReportImg">发送测试报告(图片)</Button>
        </span>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import * as echarts from 'echarts';
import { createCirclePieChart } from '../../../../utils/EchartsUtil'
import LabelEditorTextArea from '../../../../components/common/LabelEditor-TextArea.vue'
import LabelEditorInput from '../../../../components/common/LabelEditor-Input.vue'
import labelEditorVueEditor from '../../../../components/common/LabelEditor-VUEEditor.vue'
import TestReportPluginDialog from './ProjectTestReportPluginAddDialog.vue'
import TestReportAutoResultWebpart from './ReportWebparts/ProjectTestReportAutoResultWebPart.vue'
import TestReportAttachmentsWebPart from './ReportWebparts/ProjectTestReportAttachmentsWebPart.vue'
import html2canvas from "html2canvas";
import {
  loadProjectTestReport,
  loadProjectRequirements,
  loadProjectTestReportTeamMates,
  updateProjectTestReport,
  loadProjectTestReportCaseResultStatistics,
  getProjectIssueCountStatistics,
  loadTestReportIssues,
  loadTestReportCases,
  sendTestReportApi
} from '../../business-service/ProjectApiService'

export default {
  name: 'projectTestReportDetail',
  props: ['projectID', 'reportID'],
  data() {
    return {
      reportComments: '点击添加总结',
      reportDetail: {
        attachments_detail: [],
        view_data: { createor_name: '' }
      },
      reportRequirements: [],
      reportMembers: [],
      reportWebParts: [],
      reportWebPartData: [],
      showReportPluginDialog: false,
      Summary: {
        RequirementCount: 0,
        CaseCount: 0,
        IssueCount: 0,
        CaseExecRate: 0,
        CasePassRate: 0,
        IssueFixedRate: 0,
        IssueReopenRate: 0,
        IssueCloseAvgTime: 0,
        IssueFixedAvgTime: 0,
      },
      testReportIssues: [],
      testReportCases: [],
      uploadList: [],
      defaultList: [],
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapState(['appBodyMainHeight', 'appBodyWidth']),

    containerHeight: function () {
      return this.appBodyMainHeight - 10
    },
  },
  methods:
  {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),
    ...mapMutations(['setItemViewMode']),

    getTestReport: function (reportID) {
      loadProjectTestReport(reportID).then(response => {
        this.reportDetail = response.data.result
        this.Summary.RequirementCount = response.data.result.view_data.requirement_count
        let CaseStatistics = JSON.parse(response.data.result.CaseStatistics)
        this.loadCaseResultPieChart(CaseStatistics)
        this.Summary.CaseCount = CaseStatistics.case_count
        this.Summary.CaseExecRate = CaseStatistics.case_exec_rate
        this.Summary.CasePassRate = CaseStatistics.pass_rate
        let IssueStatistics = JSON.parse(response.data.result.IssueStatistics)
        this.loadIssueStatusPieChart(IssueStatistics)
        this.Summary.IssueCount = IssueStatistics.issue_count
        this.Summary.IssueReopenRate = IssueStatistics.reopen_rate
        this.Summary.IssueFixedRate = IssueStatistics.fixed_rate
        this.Summary.IssueCloseAvgTime = IssueStatistics.closed_average_time
        this.Summary.IssueFixedAvgTime = IssueStatistics.fixed_average_time
        this.getReportMembers(this.reportDetail.id)
        this.reportRequirements = this.reportDetail.view_data.requirements
        this.testReportCases = this.reportDetail.view_data.test_cases
        this.testReportIssues = this.reportDetail.view_data.issues
      }, response => { })
    },

    getReportMembers: function (reportID) {
      loadProjectTestReportTeamMates(reportID).then(response => {
        this.reportMembers = response.data.result
      }, response => { })
    },

    // getTestReportIssues: function (reportID) {
    //   loadTestReportIssues(reportID).then(response => {
    //     this.testReportIssues = response.data.result.results
    //   }, response => { })
    // },
    // getTestReportCases: function (reportID) {
    //   loadTestReportCases(reportID).then(response => {
    //     this.testReportCases = response.data.result.results
    //   }, response => { })
    // },

    updateReportComment: function (value) {
      let parameters = { Summary: value }
      updateProjectTestReport(this.reportID, parameters).then(response => {
        this.$Message.success({
          content: '总结保存成功',
          duration: 3,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '总结保存失败',
          duration: 3,
          closable: true
        })
      })
    },

    updateReportPerformance: function (value) {
      let parameters = { Performance: value }
      updateProjectTestReport(this.reportID, parameters).then(response => {
        this.$Message.success({
          content: '性能测试保存成功',
          duration: 3,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '性能测试保存失败',
          duration: 3,
          closable: true
        })
      })
    },
    updateReqStatus: function (value) {
      // console.log(value)
    },

    // getCaseResultStatisticsData: function (reportID) {
    //   loadProjectTestReportCaseResultStatistics(reportID).then(response => {
    //     this.loadCaseResultPieChart(response.data.result)
    //   }, response => { })
    // },

    getCaseResultCountSeriesData: function (caseData) {
      let seriesData = { series_data: [] }
      let statusCount = { name: '测试结果分布', data: [] }
      statusCount.data.push({ name: '通过', value: caseData.pass })
      statusCount.data.push({ name: '失败', value: caseData.fail })
      statusCount.data.push({ name: '未执行', value: caseData.blocked })
      statusCount.data.push({ name: '重新执行', value: caseData.re_exec })
      seriesData.series_data.push(statusCount)
      return seriesData
    },

    loadCaseResultPieChart: function (caseData) {
      let myChart = echarts.init(document.getElementById('reportCaseResultPieChart'))
      let seriesData = this.getCaseResultCountSeriesData(caseData)
      let option = createCirclePieChart(seriesData, '')
      myChart.setOption(option)
      window.onresize = function () {
        myChart.resize();
      }
    },

    loadIssueStatusPieChart: function (issueData) {
      let myChart = echarts.init(document.getElementById('issueStatusPieChart'))
      let seriesData = this.getIssueStatusCountSeriesData(issueData)
      let option = createCirclePieChart(seriesData, '')
      myChart.setOption(option)
      window.onresize = function () {
        myChart.resize();
      }
    },

    getIssueStatusCountSeriesData: function (issueData) {
      let seriesData = { series_data: [] }
      let statusCount = { name: '问题状态分布', data: [] }
      statusCount.data.push({ name: '新增', value: issueData.issue_count })
      statusCount.data.push({ name: '解决', value: issueData.fixed_count })
      statusCount.data.push({ name: '重新打开', value: issueData.reopen_count })
      statusCount.data.push({ name: '关闭', value: issueData.closed_count })
      seriesData.series_data.push(statusCount)
      return seriesData
    },

    openReportPluginDialog: function () {
      this.showReportPluginDialog = true
    },

    closeDialog: function () {
      this.showReportPluginDialog = false
    },

    addReportWebpart: function (value) {
      let selectWebparts = JSON.parse(JSON.stringify(value))
      this.showReportPluginDialog = false
      for (let i = 0; i < selectWebparts.length; i++) {
        selectWebparts[i]['time'] = Date.now()
        this.reportWebParts.push(selectWebparts[i])
      }
    },

    sendTestReport: function () {
      if (this.reportDetail.id > 0) {
        sendTestReportApi(this.reportDetail.id).then(response => {
          this.$Message.success('发送成功');
        }, error => {
          let errorMsg = error.response.data.result
          this.$Message.warning(
            {
              content: errorMsg,
              duration: 6,
              closable: true
            }
          );
        })
      }
    },

    sendTestReportImg: function () {
      if (this.reportDetail.id > 0) {
        this.toImage2()
        //console.log(imgData)
      }
    },

    toImage: function () {
      const canvas = document.createElement("canvas")
      // 获取父标签，意思是这个标签内的 DOM 元素生成图片
      // imageTofile是给截图范围内的父级元素自定义的ref名称
      let canvasBox = this.$refs.imageTofile
      const width = parseInt(window.getComputedStyle(canvasBox).width)
      const height = parseInt(window.getComputedStyle(canvasBox).height)
      // 宽高 * 2 并放大 2 倍 是为了防止图片模糊
      canvas.width = width * 2
      canvas.height = height * 2
      canvas.style.width = width + 'px'
      canvas.style.height = height + 'px'
      const context = canvas.getContext("2d");
      context.scale(2, 2);
      const options = {
        backgroundColor: null,
        canvas: canvas,
        useCORS: true
      }
      html2canvas(canvasBox, options).then((canvas) => {
        // toDataURL 图片格式转成 base64
        let dataURL = canvas.toDataURL("image/png")
        this.downloadImage(dataURL)
        return dataURL
      })
    },

    toImage2: function () {
      const BaseScreenshot = this.$refs.imageTofile
      // 保存滚动位置
      const [x, y] = [window.scrollX, window.scrollY]
      // 滚动的初始位置
      window.scrollTo(0, 0)
      // 生成截图
      html2canvas(BaseScreenshot, {
        scrollY: 0,
        scrollX: 0,
        // 开启跨域配置
        useCORS: true,
        // canvas高
        height: BaseScreenshot.scrollHeight,
        // canvas宽
        width: BaseScreenshot.scrollWidth
      }).then((canvas) => {
        const imgData = canvas.toDataURL('image/jpeg', 1.0)
        window.scrollTo(x, y)

        sendTestReportApi(this.reportDetail.id, { reportImg: imgData }).then(response => {
          this.$Message.success('发送成功');
        }, response => {
          this.$Message.warning('发送失败');
        })

        this.downloadImage(imgData)
      });
    },

    downloadImage(imgUrl) {
      let a = document.createElement('a')
      a.href = imgUrl
      a.download = '截图'
      a.click()
    },

    delectAttachment: function (fileID, fileName) {
      this.$Modal.confirm({
        title: '附件删除确认',
        content: '即将删除附件' + fileName,
        onOk: () => {
          this.$axios.delete('/api/project/testreport/' + this.reportID + '/attachment/' + fileID).then(response => {
            this.getTestReport(this.reportID)
          }, response => {
          })
        },
        onCancel: () => {
        }
      })
    },

    handleSuccess(res, file, fileList) {
      this.defaultList = []
      this.getTestReport(this.reportID)
    },
    handleRemove(file, fileList) {
      this.uploadList = fileList
      this.getTestReport(this.reportID)
      // console.log(this.uploadList)
    },
    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过20M限制',
        duration: 10,
        closable: true
      })
    },



  },

  created: function () {
    if (!this.reportID) {
      this.reportID = this.$route.params.reportID
    }
    this.getTestReport(this.reportID)
  },

  mounted: function () {
  },

  watch: {
    reportID: function (value) {
      this.getTestReport(this.reportID)
    }
  },

  components: {
    LabelEditorTextArea,
    LabelEditorInput,
    labelEditorVueEditor,
    TestReportPluginDialog,
    TestReportAutoResultWebpart,
    TestReportAttachmentsWebPart
  }
}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.ReportTilteContainer {
  height: 50px;
  margin-bottom: 1px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
  box-shadow: rgba(216, 221, 228, 0.5) 0px 1px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 4px 0px;
  z-index: 20;
  flex: 0 0 50px;
  padding: 0px 24px 0px 0px;
}

.ReportTitle {
  margin-left: -12px;
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
}

.SectionWrapper {
  padding-left: 4px;
  padding-right: 10px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.ReportEditIcon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-left: 8px;
  cursor: pointer;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUxLjEgKDU3NTAxKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29uX2VkaXQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iU3ltYm9scyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Imljb25fZWRpdCIgZmlsbD0iI0M5Q0ZENyIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPGcgaWQ9IlNoYXBlIj4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xNSw3IEMxNC40NDcsNyAxNCw3LjQ0NyAxNCw4IEwxNCwxNCBMMiwxNCBMMiwyIEw4LDIgQzguNTUzLDIgOSwxLjU1MyA5LDEgQzksMC40NDcgOC41NTMsMCA4LDAgTDEsMCBDMC40NDcsMCAwLDAuNDQ3IDAsMSBMMCwxNSBDMCwxNS41NTMgMC40NDcsMTYgMSwxNiBMMTUsMTYgQzE1LjU1MywxNiAxNiwxNS41NTMgMTYsMTUgTDE2LDggQzE2LDcuNDQ3IDE1LjU1Myw3IDE1LDcgWiI+PC9wYXRoPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTE0LjI5MywwLjI5MyBMNi4yOTMsOC4yOTMgQzUuOTAyLDguNjg0IDUuOTAyLDkuMzE2IDYuMjkzLDkuNzA3IEM2LjQ4OCw5LjkwMiA2Ljc0NCwxMCA3LDEwIEM3LjI1NiwxMCA3LjUxMiw5LjkwMiA3LjcwNyw5LjcwNyBMMTUuNzA3LDEuNzA3IEMxNi4wOTgsMS4zMTYgMTYuMDk4LDAuNjg0IDE1LjcwNywwLjI5MyBDMTUuMzE2LC0wLjA5OCAxNC42ODQsLTAuMDk4IDE0LjI5MywwLjI5MyBaIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==) center center / cover no-repeat;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.AvatarWrapper {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  width: 24px;
  height: 24px;
  color: rgb(255, 255, 255);
  font-size: 12px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 1px 3px -1px, rgba(0, 0, 0, 0.1) 0px 2px 8px -1px;
  border-radius: 100%;
  background: url(https://coding-net-production-static-ci.codehub.cn/WM-TEXT-AVATAR-rcTfTjMRDQUTanBVLMCV.jpg?imageView2/1/w/48/h/48) center center / cover no-repeat rgb(255, 255, 255);
}

.RoleIcon {
  display: none;
  position: absolute;
  width: 14px;
  height: 14px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 1px 2px 0px, rgba(0, 0, 0, 0.1) 0px 2px 10px -1px;
  border-radius: 100%;
  background: center center / 12px no-repeat rgb(255, 255, 255);
}

.CreaterName {
  flex-shrink: 0;
  font-weight: bold;
  color: rgb(32, 45, 64);
  margin-left: 8px;
  margin-right: 4px;
}

.CreatorOperation {
  flex-shrink: 0;
  color: rgb(145, 153, 163);
  margin-right: 4px;
}

.CreateTime {
  flex-shrink: 0;
  color: rgb(145, 153, 163);
}

.report-project-item-label {
  width: 80px;
  display: inline-flex;
  font-size: 14px;
}

.newReportResult {
  height: 300px;
  border: 1px dashed gainsboro;
  display: flex;
  /* 针对子元素的垂直方向对齐方式 */
  align-items: center;
  /* 对子元素的水平方向对齐方式 */
  justify-content: center;
  cursor: pointer;
}
</style>
../../../../utils/EchartsUtil