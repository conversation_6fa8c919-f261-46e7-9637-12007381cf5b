<template>
  <div>
    <Modal :value="isShow" :title="'用例评审, 发起人: ' + userInfo.name" :mask-closable="false" showClose="false"
      class-name="vertical-center-modal" :width="800" @on-cancel="cancel">
      <div :style="'height:' + containerHeight - 100 + 'px;overflow-y: hidden;overflow-x: hidden'">
        <Form ref="createCaseReview" :model="formItem" :rules="ruleCustom">
          <Row>
            <Col :span="12">
            <FormItem label="用例评审" prop="Title" required>
              <Input v-model="formItem.Title" :maxlength="60" placeholder="输入用例评审标题" size="small"
                style="width: 280px" />
            </FormItem>
            </Col>
            <Col :span="12">
            <FormItem label="截止时间" prop="Deadline">
              <DatePicker type="date" v-model="formItem.Deadline" placeholder="选择截止日期" size="small"></DatePicker>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="评审人员" prop="Reviewer">
              <Select v-model="formItem.Reviewer" style="width:280px" size="small" multiple clearable placeholder="请选择">
                <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember"> {{ member.name
                  }}
                </Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="关联需求" prop="Requirement">
              <Select v-model="formItem.Requirement" filterable transfer size="small" multiple placeholder="请选择关联需求"
                style="width:280px">
                <Option v-for="require in projectRequirements" :key="require.id" :value="require.id">{{
                  "[" + require.id + "][" + require.ViewData.Owner + "] " + require.Title }}</Option>
              </Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <!-- <FormItem>
              <span prop="Creator">
                <span>发起人 </span>
                <Avatar size="small" :src="this.userInfo.extend_info.avatar"></Avatar>
                <span>{{ this.userInfo.name }}</span>
              </span>
            </FormItem> -->
            </Col>
            <Col span="8">
            </Col>
          </Row>
          <FormItem label="" prop="Desc" style="margin-top: 15px;">
            <Input v-model="formItem.Desc" maxlength="200" type="textarea" placeholder="点击编辑描述(可选)" />
          </FormItem>
          <FormItem label="">
            <div style="height:120px; font-size: 12px;">
              <span style="font-size:medium; font-weight: 600;">勾选需要评审的用例</span>
              <span style="font-size: 12px;">
                <Card>
                  <span>已选择用例{{ selectCaseCount }}条</span>
                  <Divider type="vertical" />
                  <span>
                    <Button type="text" @click="showCaseTree">
                      <span style="color: #0066ff;font-size: 14px;">
                        选择用例
                        <Icon type="md-arrow-round-forward" />
                      </span>
                    </Button>
                  </span>
                </Card>
              </span>
            </div>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button style="width: 80px; height:30px;" @click="cancel">取消</Button>
        <Button type="success" style="width: 80px; height:30px;" @click="ok('createCaseReview')">保存</Button>
      </div>
    </Modal>
    <case-review-test-case-select-dialog ref="case-review-test-case-dialog" @selectCase="selectCaseGroup"
      :projectID="projectID"></case-review-test-case-select-dialog>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import CaseReviewTestCaseSelectDialog from './CaseReviewTestCaseSelectDialog'
import { createCaseReview } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'CaseReviewCreateDialog',
  props: ['projectID',],
  data() {
    return {
      selectCaseCount: 0,
      formItem: {
        Title: '',
        Desc: '',
        Project: 0,
        Creator: 0,
        CaseCount: 0,
        SelectCaseGroup: [],
        HalfCaseGroup: [],
        Deadline: '',
        Reviewer: [],
        CaseCount: 0,
        Version: 0,
        Requirement: []
      },
      ruleCustom: {
        Title: [
          { type: 'string', required: true, min: 1, max: 50, message: '评审标题长度在1-50个字符之间', trigger: 'blur', }
        ],
        Reviewer: [
          { type: 'array', required: true, message: '请选择参与评审的人员', trigger: 'change', }
        ],
        Deadline: [
          { type: 'date', required: true, message: '请选择截止时间', trigger: 'change', }
        ],
        Requirement: [
          // { type: 'array', required: true, message: '请选择相关需求', trigger: 'change', }
        ]
      },
      isShow: false
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['viewDialogShow', 'projectVersion']),
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapState('project', ['projectMembers', 'projectCaseCount', 'projectFortestings', 'projectRequirements']),

    containerHeight: function () {
      if (this.appBodyMainHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyMainHeight - 100
      }
    },

    dialogShow: function () {
      return this.createReqType === 1
    },
  },

  methods: {
    ...mapMutations('projectglobal', ['setTaskChange', 'setCreateReqType']),
    ...mapActions('project', ['loadProjectReqirements']),
    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.formItem.Creator = this.userInfo.id
          this.formItem.Project = this.projectID
          createCaseReview(this.projectID, this.formItem).then(response => {
            this.setIsShow(false)
            this.$emit("createReview", response)
            this.$parent.getCaseReviewList(this.projectID, '')
          }, response => {
            this.setIsShow(false)
          })
        }
      })
    },

    cancel() {
      this.setIsShow(false)
    },

    setIsShow: function (bol) {
      this.isShow = bol
    },

    selectCaseGroup(caseCount, groupIDs, halfCheckCaseGroup) {
      this.selectCaseCount = caseCount
      this.formItem.CaseCount = caseCount
      this.formItem.SelectCaseGroup = groupIDs
      this.formItem.HalfCaseGroup = halfCheckCaseGroup
    },

    showCaseTree: function () {
      this.$refs['case-review-test-case-dialog'].setDialogShow(true)
    },

    caseFilterChange: function (value) {
      if (value === 1) {
        this.formItem.CaseCount = this.projectCaseCount
        //console.log(value)
      }
    },

  },

  created() {

  },

  mounted() {
    if (this.projectRequirements.length < 0) {
      this.$store.dispatch('project/loadProjectReqirements', { projectID: this.projectID, urlArgs: 'Status=3,4&page_size=100&ordering=-id' })
    }
  },

  watch: {
  },

  components: {
    CaseReviewTestCaseSelectDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.case-review-tile {
  border: none;
}

.add-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  box-shadow: rgba(4, 83, 180, 0.1) 0px 2px 6px 0px;
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  background: rgb(157, 175, 202);
}

.input {
  border-radius: 0px;
  border-top-width: 0px;
  border-left-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-style: none;
}

.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }

  #requireNumId .ivu-input {
    height: 34px;
    padding: 7px 26px;
  }

  #requireNumId .ivu-input-prefix i,
  .ivu-input-suffix i {
    font-size: 16px;
    line-height: 32px;
    color: #5578aa;
  }

}
</style>
