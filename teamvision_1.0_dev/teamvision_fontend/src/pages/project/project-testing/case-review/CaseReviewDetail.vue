<template>
  <div>
    <Layout>
      <!-- <Sider hide-trigger :width="360" :style="{background: '#fff','border-left':'1px solid #fff',}">
          <case-review-panel :casereviewID="casereview" :projectID="projectID" @updateCasereview="updateCasereviewID"></case-review-panel>
        </Sider> -->
      <Content>
        <case-review-content :casereviewID="casereview" :projectID="projectID"></case-review-content>
      </Content>
    </Layout>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import CaseReviewPanel from "./CaseReviewPanel.vue"
import CaseReviewContent from "./CaseReviewContent.vue";

export default {
  name: "CaseReviewDetail",
  props: ["projectID", "version", "casereviewID"],

  data() {
    return {
      columnItemHeight: 200,
      viewMode: "board",
      casereview: ''

    };
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(["appBodyMainHeight", "appBodyWidth"]),

    containerHeight: function () {
      return this.appBodyMainHeight - 10;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },

    versionId: function () {
      if (this.$route.params.version) {
        return this.$route.params.version;
      }
      return 0;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },

  },

  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),

    updateCasereviewID: function (CasereviewID) {
      this.casereview = CasereviewID
    },


  },

  created: function () {
    this.projectID = parseInt(this.$route.params.projectID)
    this.casereview = this.$route.params.casereviewID
  },

  mounted: function () { },
  watch: {

  },

  components: {
    CaseReviewPanel,
    CaseReviewContent,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less"></style>