<template>
  <div class="minder-content" :style="{ height: containerHeight + 'px' }">
    <div id="mindContainer" v-loading="loading">
      <kityMinder v-if="importData != null" ref="minder" :importJson="importData" @exportData="exportData"></kityMinder>
    </div>
    <div v-show="showDetailSetting" class="testcase-toolRightPanel">
      <div class="title">
        <span v-if="selectNodeData.id >= 0"> # {{ selectNodeData.id }} {{ selectNodeData.text }} </span>
        <span v-else>Key: {{ selectNodeData.OriginalID }}</span>
        <span @click="closeSettingPanel">
          <Icon type="md-close" :size="20" />
        </span>
      </div>
      <div class="content">
        <div>用例描述: </div>
        <div style="padding: 4px 2px 4px 2px;">
          <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.note" type="textarea"
            :rows="6" placeholder="用例描述信息" />
        </div>
        <div>前置条件: </div>
        <div style="padding: 4px 2px 4px 2px;">
          <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.Precondition"
            type="textarea" :rows="6" placeholder="用例前置条件" />
        </div>
        <div>预期结果: </div>
        <div style="padding: 4px 2px 4px 2px;">
          <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.ExpectResult"
            type="textarea" :rows="6" placeholder="用例预期结果" />
        </div>
      </div>
    </div>
    <div @click="expandDetailSettingBar" class="test-review-case-toolRightPanelIcon">
      <Avatar v-show="showDetailSettingIcon" icon="ios-expand" size="large" />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";

export default {
  components: {
  },
  props: ["projectID", "mindFileID"],
  data() {
    return {
      minder: null,
      paper: null,
      topToolbarLeft: 596,
      showDetailSetting: false,
      showDetailSettingIcon: false,
      toolbarExtend: 0,
      selectNodeData: {},
      selectNodePosition: { x: "0px", y: "0px" },
      showCommentsContainer: false,
      projectTopicTags: [],
      originalTags: [],
      fontSize: 14,
      autoSave: false,
      loading: false,
      copyNode: false,
      saveFileMenuly: false,
      fontFamily: "微软雅黑",
      nodeBackgroundColor: "#50c28b",
      nodeFontColor: "black",
      updatedChildren: [],
      templateList: [
        { name: "default", title: "思维导图" },
        { name: "tianpan", title: "天盘图" },
        { name: "structure", title: "组织结构图" },
        { name: "filetree", title: "目录组织图" },
        { name: "right", title: "逻辑结构图" },
        { name: "fish-bone", title: "鱼骨头图" },
      ],
      themeList: [
        [
          { name: "classic", title: "经典脑图" },
          { name: "classic-compact", title: "经典紧凑" },
        ],
        [
          { name: "snow", title: "温柔冷光" },
          { name: "snow-compact", title: "紧凑冷光" },
        ],
        [
          { name: "fish", title: "鱼骨图" },
          { name: "wire", title: "线框图" },
        ],
        [
          { name: "fresh-red", title: "清新红" },
          { name: "fresh-red-compat", title: "紧凑红" },
        ],
        [
          { name: "fresh-green", title: "文艺绿" },
          { name: "fresh-green-compat", title: "紧凑绿" },
        ],
        [
          { name: "fresh-purple", title: "浪漫紫" },
          { name: "fresh-purple-compat", title: "紧凑紫" },
        ],
        [
          { name: "fresh-blue", title: "天空蓝" },
          { name: "fresh-blue-compat", title: "紧凑蓝" },
        ],
        [
          { name: "fresh-pink", title: "脑残粉" },
          { name: "fresh-pink-compat", title: "紧凑粉" },
        ],
        [
          { name: "fresh-soil", title: "你土黄" },
          { name: "fresh-soil-compat", title: "紧凑黄" },
        ],
        [
          { name: "tianpan", title: "清新红" },
          { name: "tianpan-compact", title: "紧凑天盘" },
        ],
      ],
      importData: null,
      loading: false,
      selectNodeID: 0
    };
  },
  computed: {
    ...mapState(["appBodyMainHeight",]),
    ...mapState('usercenter', ['userInfo']),

    containerHeight: function () {
      return this.appBodyMainHeight - 190;
    },
    toolBarWidth: function () {
      return this.toolbarExtend + "px";
    },
  },

  created() {

  },

  mounted() {
    this.getCaseReviewMind()

    //console.log("mount mind.vue");
    window.onbeforeunload = function (e) {
      e = e || window.event;
      // 兼容IE8和Firefox 4之前的版本
      if (e) {
        e.returnValue = "关闭提示";
      }
      // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
      return "关闭提示";
    };
  },

  destroyed() {
    window.onbeforeunload = null;
  },


  // beforeRouteLeave(to, from, next) {
  //   const answer = window.confirm("离开前请确认保存了数据，确定要离开？");
  //   if (answer) {
  //     next();
  //   } else {
  //     next(false);
  //   }
  // },

  methods: {
    ...mapMutations("testcase", ["setTestCaseViewMode"]),
    exportData(value) { },

    getMinderData: function () {
      return this.minder.exportJson()
    },

    getCaseReviewMind: function () {
      this.loading = true;
      this.importData = null;
      this.$axios.get("/api/project/casereview/mindmap/" + this.mindFileID).then((response) => {
        this.loading = false;
        this.importData = response.data.result.data[0];
        setTimeout(() => {
          this.minder = this.$refs.minder.minder;
          this.setTheme("fresh-blue-compat");
          this.setTemplate("default");
          this.bindevent();
          this.minder.execCommand("ExpandToLevel", 3);
        }, 100);
        //setInterval(() => {
        //this.saveData()
        //}, 300000)

      }, (response) => {
        this.loading = false;
      }
      );
    },

    copyNodeData: function (node) {
      if (this.copyNode) {
        node.data.id = this.uuid(12);
        for (let i = 0; i < node.children.length; i++) {
          this.updateNodeOriginalID(node.children[i]);
        }
      }
    },

    updateNodeOriginalID: function (node) {
      if (node) {
        node.data.id = this.uuid(12);
        let children = node.children;
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeOriginalID(node.children[i]);
          }
        } else {
          return;
        }
      } else {
        return;
      }
    },

    updateNodeID: function (node, idMaps) {
      if (node) {
        if (idMaps[node.data.id]) {
          let id_keys = idMaps[node.data.id].split(":");
          node.data.id = id_keys[0];
          node.data.OriginalID = id_keys[1];
        }
        let children = node.children;
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeID(node.children[i], idMaps);
          }
        } else {
          return;
        }
      } else {
        return;
      }
    },

    uuid: function (len, radix) {
      let chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
      let uuid = [], i;
      radix = radix || chars.length;

      if (len) {
        // Compact form
        for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
      } else {
        // rfc4122, version 4 form
        let r;

        // rfc4122 requires these characters
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
        uuid[14] = "4";

        // Fill in random data.  At i==19 set the high bits of clock sequence as
        // per rfc4122, sec. 4.1.5
        for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
            r = 0 | (Math.random() * 16);
            uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
          }
        }
      }
      return uuid.join("");
    },

    closeSettingPanel: function () {
      this.showDetailSetting = false;
      this.toolbarExtend = 0;
      this.showDetailSettingIcon = true;
    },

    expandDetailSettingBar: function () {
      let node = this.minder.getSelectedNode();
      if (node.children.length == 0) {
        // console.log("node.children.length=", node.children.length);
        this.showDetailSetting = true;
        this.toolbarExtend = 328;
        this.showDetailSettingIcon = false;
      }
    },

    showDetailSettingBar: function (isShow) {
      if (!this.showDetailSettingIcon) {
        if (isShow) {
          this.showDetailSetting = isShow;
          this.toolbarExtend = 328;
        } else {
          this.showDetailSetting = false;
          this.toolbarExtend = 0;
        }
      }
    },

    addNode(command) {
      if (command == "AppendChildNode") {
        let node = this.minder.getSelectedNode();
        // console.log("select node=", node);
      }
      this.minder.execCommand(command, "分支主题");
    },

    editNode() {
      this.minder.execCommand('text')
    },

    tagExists: function (value) {
      let result = 0;
      for (let i = 0; i < this.projectTopicTags.length; i++) {
        if (value === this.projectTopicTags[i].TagName) {
          result = this.projectTopicTags[i].id;
        }
      }
      return result;
    },

    removeResource: function (checked, name) {
      let resource = this.selectNodeData.resource;
      if (checked) {
        if (resource) {
          if (resource.indexOf(name) < 0) {
            resource.push(name);
          }
        } else {
          resource = [name];
        }
        this.minder.execCommand("Resource", resource);
      } else {
        if (resource) {
          for (let i = 0; i < resource.length; i++) {
            if (resource[i] === name) {
              resource.splice(i, 1);
              break;
            }
          }
          this.minder.execCommand("Resource", resource);
        }
      }
    },

    resetLayout() {
      this.minder.execCommand("ResetLayout");
    },

    setLayout(layoutName) {
      this.minder.execCommand("Layout", layoutName);
    },

    setTheme(theme) {
      this.minder.execCommand("Theme", theme);
      //                this.updateFileProperty('Theme',theme,this.mindFileID)
    },

    setFontFamily: function (family) {
      this.minder.execCommand("FontFamily", family);
      this.updateAllNodeData("FontFamily", family);
    },

    setFontSize: function (size) {
      this.minder.execCommand("FontSize", size);
      this.updateAllNodeData("FontSize", size);
    },

    setBackgroundColor: function (color) {
      this.minder.execCommand("Background", color);
      this.updateAllNodeData("BackgroundColor", color);
    },

    setFontColor: function (color) {
      this.minder.execCommand("ForeColor", color);
      this.updateAllNodeData("FontColor", color);
    },

    setTemplate(name) {
      this.minder.execCommand("Template", name);
      //                this.updateFileProperty('Template',name,this.mindFileID)
    },

    zoomMindmap: function (value) {
      this.minder.execCommand("Zoom", value);
      this.minder.execCommand;
    },

    setCamera: function () {
      this.minder.execCommand("camera", this.minder.getRoot(), 800);
    },

    removeNode() {
      this.minder.execCommand("RemoveNode");
    },

    addPriority(priority) {
      this.minder.execCommand("Priority", priority);
    },

    addProgress(progress) {
      this.minder.execCommand("Progress", progress);
    },

    addImage() {
      this.minder.execCommand("Image", this.selectNodeData.image, "");
    },

    addLink() {
      this.minder.execCommand("HyperLink", this.selectNodeData.hyperlink, "");
    },

    unDo() {
      //this.minder.history.unDo()
    },

    reDo() {
      //this.minder.history.reDo()
    },

    addNote() {
      this.value1 = true;
      this.toolbarExtend = 80;
      this.minder.execCommand("Note", this.selectNodeData.note);
      //this.selectNodeData.Desc = this.selectNodeData.note;
    },

    initProjectTags: function () {
      for (let y = 0; y < this.projectTopicTags.length; y++) {
        this.projectTopicTags[y].TagChecked = false;
      }
    },

    showComments: function (e) {
      let shapeName = e.kityEvent.targetShape.container.__KityClassName;
      if (shapeName && shapeName === "NoteIcon") {
        this.showCommentsContainer = true;
        this.selectNodePosition.y = e.kityEvent.originEvent.y - 110 + "px";
        this.selectNodePosition.x =
          e.kityEvent.originEvent.x - this.topToolbarLeft + "px";
      } else {
        this.showCommentsContainer = false;
      }
    },

    bindevent() {
      this.minder.on("click", (e) => {
        let node = this.minder.getSelectedNode();
        if (node) {
          if (node.children.length == 0) {
            this.showDetailSettingBar(true);
            this.showComments(e);
            this.selectNodeData = node.data;
            //this.selectNodeData.Desc = this.selectNodeData.note

            if (node.data["font-size"]) {
              this.fontSize = node.data["font-size"];
            } else {
              this.fontSize = 14;
            }

            if (node.data["font-family"]) {
              this.fontFamily = node.data["font-family"];
            } else {
              this.fontFamily = "微软雅黑";
            }
          } else {
            this.showDetailSettingBar(false);
            this.selectNodeData = {};
            this.showCommentsContainer = false;
          }
          this.$emit('isDisableDeleteButton', false)
          this.selectNodeID = node.data.id
        } else {
          this.showDetailSettingBar(false);
          this.selectNodeData = {};
          this.showCommentsContainer = false;
          this.$emit('isDisableDeleteButton', true)
        }
      });

      this.minder.on("keydown", (e) => {
        // console.log(e);
        if (e.originEvent) {
          if (
            e.originEvent.key === "v" && (e.originEvent.metaKey || e.originEvent.ctrlKey)) {
            let node = this.minder.getSelectedNode();
            if (node) {
              this.copyNode = true;
            }
          }
        }
      });

      this.minder.on("mousedown", (e) => {
        //console.log(e);
        if (e.originEvent) {
          if (e.originEvent.button === 2 || e.originEvent.button === 0) {
            let node = this.minder.getSelectedNode();
            if (node) {
              if (node.children.length == 0) {
                this.showDetailSettingBar(true);
                this.showComments(e);
                this.selectNodeData = node.data;
                this.initProjectTags();
                if (this.selectNodeData.resource) {
                  for (let i = 0; i < this.selectNodeData.resource.length; i++) {
                    // console.log(this.selectNodeData.resource[i]);
                    for (let y = 0; y < this.projectTopicTags.length; y++) {
                      // console.log(this.projectTopicTags[y]);
                      if (this.selectNodeData.resource[i] === this.projectTopicTags[y].TagName) {
                        this.projectTopicTags[y].TagChecked = true;
                        break;
                      }
                    }
                  }
                }
                if (node.data["font-size"]) {
                  this.fontSize = node.data["font-size"];
                } else {
                  this.fontSize = 14;
                }

                if (node.data["font-family"]) {
                  this.fontFamily = node.data["font-family"];
                } else {
                  this.fontFamily = "微软雅黑";
                }
              }
            } else {
              this.showDetailSettingBar(false);
              this.showCommentsContainer = false;
            }
          }
        }
      });

      this.minder.on("selectionchange", (e) => {
        var nodes = this.minder.getSelectedNodes();
        if (nodes) {
          for (let i = 0; i < nodes.length; i++) {
            this.selectNodeData = nodes[i].data;
            this.copyNodeData(nodes[i]);
            //console.log('You selected: "%s"', nodes[i].getText());
          }
          this.copyNode = false;
        }
      });
    },

    // https://github.com/fex-team/kityminder-core/wiki/command
    // https://github.com/fex-team/kityminder-core/wiki/api
  },

  watch: {
    "selectNodeData.resource": function (value) {
      this.initProjectTags();
      if (value) {
        for (let i = 0; i < value.length; i++) {
          // console.log(value[i]);
          for (let y = 0; y < this.projectTopicTags.length; y++) {
            // console.log(this.projectTopicTags[y]);
            if (value[i] === this.projectTopicTags[y].TagName) {
              this.projectTopicTags[y].TagChecked = true;
              break;
            }
          }
        }
      }
    },

    //            mindFileID: function (value) {
    //                if (value !==0) {
    //                    this.importData = []
    //                    this.$axios.get('/api/project/kitymind_file/'+value +'/').then(response => {
    //                        this.importData = response.data.result.data
    //                    }, response => {
    //
    //                    })
    //                }
    //            }
  },
};
</script>

<style lang="less" scoped>
.case-review-minder {
  // .ivu-card-head {
  //   border-bottom: 1px solid #e8eaec;
  //   //padding: 2px 6px;
  //   //line-height: 1;
  //   align-content: center;
  //   -webkit-align-content: center;

  //   .mind-header {
  //     display: inline-block;
  //     width: 100%;
  //     height: 46px;
  //     line-height: 20px;
  //     font-size: 12px;
  //     color: #5578aa;
  //     font-weight: 500;
  //     overflow: hidden;
  //     text-overflow: ellipsis;
  //     white-space: nowrap;
  //   }
  // }
}

.minder-content {
  overflow: auto;
  /* 启用溢出滚动 */
}

#caseReviewMindContainer .minder-editor-container {
  overflow-y: auto;
  position: absolute;
  bottom: 0;
  left: 0;
  right: inherit;
  top: 60px;
  font-family: Arial, "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;

  .tools {
    display: none
  }

  .tabBar {
    display: none;
  }

  .ivu-card {
    position: relative;
  }
}

.top {
  display: none;
}

.testcase-toolRightPanel {
  background: #FFFFFF;
  border: 1px solid #E6E9ED;
  position: fixed;
  overflow-y: auto;
  bottom: 100px;
  width: 320px;
  right: 16px;
  z-index: 2;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #E6E9ED;
    padding: 4px;

    .span {
      text-align: left;
    }
  }

  .content {
    padding: 4px;
  }
}

.testcase-toolBar {
  /*position: fixed;*/
  position: relative;
  top: 110px;
  width: 100%;
  /*left: 596px;*/
}

.test-review-case-toolRightPanel {
  position: absolute;
  overflow-y: auto;
  //overflow-y:scroll;
  //top: 340px;
  width: 320px;
  right: 0px;
  //height: 100%;
}

.test-review-case-toolRightPanelIcon {
  position: fixed;
  top: 360px;
  width: 40px;
  right: 100px;
  box-shadow: 0px 10px 5px #888888;
  border-radius: 25px;
  cursor: pointer;
  /*border: 2px solid #50C28B;*/
}

.testcase-mind-head-item {
  display: inline-block;
  cursor: pointer;
  padding-left: 10px;
  //padding-right: 6px;
  align-items: center;

  .ivu-avatar-small {
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
  }
}

.mindmap-comments-container {
  position: absolute;
  background: #ffd;
  padding: 5px 15px;
  border-radius: 5px;
  max-width: 400px;
  max-height: 200px;
  overflow: auto;
  z-index: 10;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  word-break: break-all;
  white-space: normal;
  font-size: 12px;
  color: #333;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
