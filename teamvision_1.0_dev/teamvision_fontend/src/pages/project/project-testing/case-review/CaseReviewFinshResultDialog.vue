<template>
  <el-dialog title="输入评审结论" :visible.sync="dialogFormVisible">
    <el-form :model="descForm" ref="descForm" :rules="rules">
      <el-form-item label="" prop="desc">
        <el-input v-model="descForm.desc" maxlength="1000" placeholder="输入评审结论内容" show-word-limit type="textarea"
          :rows="10" />
      </el-form-item>
      <el-button size="mini" @click="dialogFormVisible = false">取 消</el-button>
      <el-button size="mini" type="success" @click="upadteCaseReviewStatus('descForm')">确 定</el-button>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  props: ["projectID", "casereviewID"],
  data() {
    return {
      dialogFormVisible: false,
      descForm: {
        desc: "",
      },
      rules: {
        desc: [
          { required: true, message: '输入评审结论', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    open() {
      this.dialogFormVisible = true
    },

    upadteCaseReviewStatus: function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let reqdata = { "Status": 2, "ResultDesc": this.descForm.desc }
          this.$axios.patch('/api/project/' + this.projectID + '/casereview/' + this.casereviewID, reqdata).then((response) => {
            this.$Message.success({
              content: '用例评审已完成',
              duration: 3,
              closable: true
            })
            this.$parent.getCaseReviewList(this.projectID, 0, 0, 0, "")
            this.dialogFormVisible = false
          }, (response) => {
            this.$Message.error({
              content: '用例评审完成失败',
              duration: 3,
              closable: true
            })
          },
          );
        } else {
          return false;
        }

      })

    },
  }
};
</script>

<style></style>