<template>
  <div :style="'height:' + containerHeight + 'px'">
    <Card :bordered="false" :dis-hover="true" :padding="0" style="margin: 0px 24px 8px;">
      <div class="TitleSection">
        <div class="CaseReviewTitle">
          <h4 class="Title">用例评审</h4>
        </div>
      </div>
    </Card>
    <Card :bordered="false" :dis-hover="true" :padding="16" style="margin: 0px 24px 8px;">
      <span style="margin-left: 2px;">
        <span>状态：</span>
        <Select clearable v-model="formItem.Status" style="width:120px" size="small" filterable>
          <Option v-for="status in CaseReviewStatus" :key="status.id" :value="status.id">{{ status.status }}
          </Option>
        </Select>
      </span>
      <span style="margin-left: 12px;">
        <span>发起人：</span>
        <Select clearable v-model="formItem.Creator" style="width:120px" size="small" filterable>
          <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{ member.name
            }}</Option>
        </Select>
      </span>
      <!-- <span style="margin-left: 12px;">
                <span>评审人：</span>
                <Select clearable v-model="formItem.Reviewer" style="width:120px" size="small"  filterable>
                    <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{ member.name }}</Option>
                </Select>
            </span> -->
      <span style="margin-left: 12px;">
        <span>评审名称：</span>
        <Input v-model="formItem.Title" style="width: 120px;" type="text" size="small" placeholder="请输入关键字" />
      </span>
      <span style="margin-left: 12px;">
        <Button size="small" type="primary" @click="getCaseReviewList">查询</Button>
      </span>
      <span style="margin-left: 2px;">
        <Button size="small" @click="resetFilter(formItem)">重置</Button>
      </span>
      <span style="margin-top: 10%;float: right;" slot="extra" @click="setCreateCaseReviewShow(true)">
        <Button style="position: relative;" type="primary" size="small">
          <!-- <Icon type="el-icon-plus" :size="14" /> -->
          <i class="el-icon-plus" icon="md-add"> 创建评审</i>
        </Button>
      </span>
    </Card>
    <Card :bordered="false" :dis-hover="true" :padding="0" style="margin: 0px 24px 8px;">
      <el-table header-row-class-name="table-header" :data="projectCaseReviews.results" style="width: 100%;">
        <el-table-column label="ID" width="60">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.id }}</div>
          </template>
        </el-table-column>
        <el-table-column label="名称">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.Title }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <div class="StatusContent" :style="{ backgroundColor: selectcolor(scope.row.view_data.status_name) }">
              <span>{{ scope.row.view_data.status_name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="发起人" width="100">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.view_data.creator_name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="评审人" width="160">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.view_data.reviewer_name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="发起时间" width="160">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.CreationTime.substring(0, 19).replace('T', ' ') }}</div>
          </template>
        </el-table-column>
        <el-table-column label="截止日期" width="160">
          <template slot-scope="scope">
            <div class="TableContent">{{ scope.row.Deadline.substring(0, 19).replace('T', ' ') }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-link :underline="false">
              <router-link :to="'/project/' + projectID + '/test/case-reviews/' + scope.row.id">查看</router-link>
            </el-link>
            <el-divider direction="vertical"></el-divider>
            <el-dropdown>
              <span>更多<i class="el-icon-arrow-down el-icon--right"></i></span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="scope.row.Status == 1"
                  @click.native="openFinshResultDialog(scope.row.id)">完成评审</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.Status == 2" disabled>完成评审</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.Status == 2"
                  @click.native="openCreateDialog(scope)">创建测试计划</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.Status == 2" @click.native="updateStatus(scope)">归档</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.Status == 3" @click.native="updateStatus(scope)"
                  disabled>归档</el-dropdown-item>
                <el-dropdown-item @click.native="deleteCaseReview(scope)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px; margin-bottom: 20px; height: 40px; float:right">
        <el-pagination small background layout="prev, pager, next, total" :total=projectCaseReviews.count
          :current-page.sync="currPage" :page-size=pageSize @current-change="currClick" @next-click="nextClick"
          @prev-click="prevClick">
        </el-pagination>
      </div>
    </Card>
    <CaseReviewFinshResultDialog ref="finsh" :projectID="projectID" :casereviewID="currentCasereviewID">
    </CaseReviewFinshResultDialog>
    <case-review-create-dialog ref="createCaseReviewDialog" :projectID="projectID" @createReview="createCaseReview">
    </case-review-create-dialog>
    <project-test-plan-create-dialog :projectID="projectID" :caseReviewID="currentCasereviewID">
    </project-test-plan-create-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import CaseReviewCreateDialog from './CaseReviewCreateDialog.vue'
import CaseReviewFinshResultDialog from './CaseReviewFinshResultDialog.vue'
import ProjectTestPlanCreateDialog from '../test-plan/ProjectTestPlanCreateDialog.vue';


export default {
  name: 'CaseReview',
  props: ["projectID", "version", "casereviewID"],
  data() {
    return {
      CaseReviewStatus: [{ id: "1", status: "进行中" }, { id: "2", status: "已完成" }],
      Creaters: [],
      Reviewer: [],
      formItem: {
        Creator: '',
        Reviewer: '',
        Status: '',
        Title: ''
      },
      currentCasereviewID: '',
      currPage: 1,
      pageSize: 10,
    }
  },

  computed: {
    ...mapState(['appBodyWidth', 'appBodyHeight']),
    ...mapState('projectglobal', ['createDialogShow']),
    ...mapState('project', ['projectVersions', 'projectMembers', 'projectCaseCount', 'projectCaseReviews']),

    containerHeight: function () {
      return this.appBodyHeight
    },

    containerWidth: function () {
      return this.appBodyWidth
    },
  },

  methods: {
    ...mapMutations("projectglobal", ["setCreateDialogShow", "setViewDialogShow",]),
    ...mapMutations('project', ['updateProjectCaseReviews']),
    ...mapActions('project', ['loadProjectTestCaseReviews']),

    openCreateDialog: function (caseReview) {
      this.currentCasereviewID = caseReview.row.id
      this.setCreateDialogShow(true);
    },

    setCreateCaseReviewShow: function (bol) {
      this.$refs['createCaseReviewDialog'].setIsShow(bol)
    },

    openFinshResultDialog: function (casereview_id) {
      this.currentCasereviewID = casereview_id
      this.$refs.finsh.open();
    },

    createCaseReview: function (response) {
      if (response) {
        this.updateProjectCaseReviews(response.data.result)
      }
    },

    getCaseReviewList: function () {
      let resultFilter = this.getFilter();
      this.loadProjectTestCaseReviews({ projectID: this.projectID, urlArgs: resultFilter })
    },

    prevClick: function () {
      this.currPage = this.currPage - 1
      this.getCaseReviewList()
    },

    nextClick: function () {
      this.currPage = this.currPage + 1
      this.getCaseReviewList()
    },

    currClick: function () {
      this.getCaseReviewList()
    },

    getFilter: function () {
      let resultFilter = "";
      if (this.formItem.Status > 0) {
        resultFilter = resultFilter + "Status=" + this.formItem.Status + "&";
      }
      if (this.formItem.Creator > 0) {
        resultFilter = resultFilter + "Creator=" + this.formItem.Creator + "&";
      }
      if (this.formItem.Reviewer > 0) {
        resultFilter = resultFilter + "Title__icontains=" + this.formItem.Reviewer + "&";
      }
      if (this.formItem.Title.trim() !== "") {
        resultFilter = resultFilter + "Title__icontains=" + this.formItem.Title + "&";
      }
      resultFilter = resultFilter + "page=" + this.currPage + "&"
      resultFilter = resultFilter + "page_size=" + this.pageSize
      resultFilter = resultFilter + '&sort=-id'
      return resultFilter;
    },

    resetFilter: function (form) {
      this.formItem.Status = '请选择',
        this.formItem.Creator = '请选择',
        this.formItem.Reviewer = '请选择',
        this.formItem.Title = '请输入关键字'
      this.getCaseReviewList()
    },

    deleteCaseReview: function (caseReview) {
      this.$axios.delete('/api/project/' + this.projectID + '/casereview/' + caseReview.row.id).then((response) => {
        this.$Message.success({
          content: '删除成功',
          duration: 3,
          closable: true
        })
        this.getCaseReviewList()
      }, (response) => {
        this.$Message.error({
          content: '删除失败',
          duration: 3,
          closable: true
        })
      },
      );
    },

    updateStatus: function (caseReview) {
      let requestData = {
        Status: 3
      }
      this.$axios.patch('/api/project/' + this.projectID + '/casereview/' + caseReview.row.id, requestData).then((response) => {
        this.$Message.success({
          content: '归档成功',
          duration: 3,
          closable: true
        })
        this.getCaseReviewList()
      }, (response) => {
        this.$Message.error({
          content: '归档失败',
          duration: 3,
          closable: true
        })
      },
      );
    },

    selectcolor: function (status_name) {
      let background_color = ''
      switch (status_name) {
        case "已完成":
          background_color = '#ccff99'
          break
        case "进行中":
          background_color = '#ffee99'
          break
        case "已归档":
          background_color = '#cccccc'
          break
      }
      return background_color
    },
  },

  created: function () {
    if (this.projectID) {
      this.getCaseReviewList()
    }
  },

  mounted: function () {
  },

  watch: {
    projectID: function (value) {
      if (this.projectID) {
        this.getCaseReviewList()
      }
    }
  },

  components: {
    CaseReviewCreateDialog,
    CaseReviewFinshResultDialog,
    ProjectTestPlanCreateDialog
  }
}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->

<style scoped lang="less">
.FeiE {
  display: flex;
  height: 100%;
}

.ivu-table th {
  height: 40px;
  white-space: nowrap;
  overflow: hidden;
  background-color: red;
}

.TopWrapper {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0px;
  left: 50%;
  z-index: 10000;
  transform: translateX(-50%);
  transition: height 500ms ease 0s;
}

.BottomRightWrapper {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 10px;
  right: 16px;
  z-index: 10000;
  transition: height 500ms ease 0s;
}

.ViewProjects {
  display: flex;
  -webkit-box-align: stretch;
  align-items: stretch;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.ProjectRouter {
  height: 100%;
  background-color: rgb(243, 244, 246);
  flex: 1 1 auto;
  overflow: auto;
}

.TestView {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgb(243, 244, 246);
}

.TitleSection {
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding: 18px 0px 5px 0px;
  background-color: #f5f7f9;
}

.CaseReviewTitle {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.Title {
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  color: rgb(32, 45, 64);
}

.ButtonGroup {
  display: flex;
}

.CreateButton {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 13px;
}

.button-default {
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(218, 223, 230);
  border-image: initial;
}

.button {
  box-sizing: border-box;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  min-width: 88px;
  height: 36px;
  text-align: center;
  color: rgb(32, 45, 64);
  font-size: 14px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 10px 12px;
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.ButtonGroup .button+.button {
  margin-left: 12px;
}

.CreateButton {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 13px;
}

.button-primary {
  color: rgb(255, 255, 255);
  background-color: rgb(32, 45, 64);
  box-shadow: rgba(32, 45, 64, 0.08) 0px 1px 4px 0px;
  border-color: rgb(32, 45, 64);
}

.FilterWrapper {
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 4px 0px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
  margin: 0px 24px 8px;
  background: rgb(255, 255, 255);
  border-radius: 3px;
  padding: 20px 24px;
}

.FormItem:first-of-type {
  margin-left: 0px;
}

.FormItem {
  flex-shrink: 0;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-left: 24px;
}



.FormLabel {
  font-size: 13px;
  line-height: 16px;
  white-space: nowrap;
}

.FormInput {
  width: 150px;
  flex-shrink: 0;
}

.Root {
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.Root .react-datepicker-wrapper,
.Root .react-datepicker__input-container {
  width: 100%;
}

.react-datepicker__input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.InputWrapper {
  display: block;
  position: relative;
}

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.InputDom-mediumSize {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  outline: none;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.07);
  border-image: initial;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  padding: 6px 40px 6px 12px;
}

.DateTo {
  font-size: 14px;
  color: rgb(145, 153, 163);
  margin: 0px 10px;
}

.FilterInputWrapper {
  width: 160px;
  font-size: 13px;
}

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.InputDom-mediumSize {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  outline: none;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.07);
  border-image: initial;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  padding: 6px 12px;
}

.switchStyle-SwitchButton {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.ReportListWrapper {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  flex: 1 0 0px;
}

.ReportList {
  -webkit-box-flex: 1;
  flex-grow: 1;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 4px 0px;
  box-sizing: border-box;
  position: relative;
  margin: 0px 24px 38px;
  background: rgb(255, 255, 255);
  padding: 14px 24px 24px;
  border-radius: 3px;
}

.Table {
  width: 100%;
  text-align: left;
  table-layout: fixed;
}

FooterBarWrapper {
  height: 57px;
}

.TableTh {
  box-sizing: border-box;
}

.TableHeader-colHeaderStyle {
  box-sizing: border-box;
  height: 32px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 24px;
  position: sticky;
  top: 0px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  overflow: hidden;
}

.SimpleTd-textEllipsis-columnStyle {
  vertical-align: top;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.SimpleTd-textEllipsis-columnStyle div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.columnStyle>div {
  padding-top: 18px;
  padding-bottom: 18px;
  height: 56px;
}

.SimpleWrapper {
  position: relative;
  box-sizing: border-box;
  -webkit-box-flex: 1;
  flex-grow: 1;
  width: 100%;
  height: 100%;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 24px 10px 0px;
}

.SimpleTd-textEllipsis-columnStyle div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.TableContent {
  font-weight: normal;
  font-family: "PingFang SC", "Helvetica Neue", "Hiragino Sans GB", "Segoe UI", "Microsoft YaHei", "微软雅黑", "sans-serif";
  color: #0d0d0d;
  font-size: 12px;
}

.StatusContent {
  font-weight: normal;
  font-family: "PingFang SC", "Helvetica Neue", "Hiragino Sans GB", "Segoe UI", "Microsoft YaHei", "微软雅黑", "sans-serif";
  color: #0d0d0d;
  font-size: 12px;
  text-align: center;
}

.table-header {
  font-size: 12px;
  height: 10px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
</style>