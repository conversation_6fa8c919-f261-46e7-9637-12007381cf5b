<template>
  <div :style="'height:' + containerHeight + 'px; width: 100%'">
    <div class="css-CaseReviewCaseTree-SectionTreeView">
      <Spin fix size="large" v-if="isLoading"></Spin>
      <el-tree ref="caseReviewCaseTree" :data="CaseReviewCaseTreeData" node-key="id" icon-class="el-icon-caret-right"
        :filter-node-method="filterNode" :highlight-current="true" draggable empty-text="评审未包含任何用例"
        :expand-on-click-node="false" :props="defaultProps" default-expand-all>
        <span class="CaseReviewCaseTree-node" slot-scope="{ node, data }">
          <span v-if="data.IsGroup">
            <span class="css-1bcfq0a-folderIcon-SectionIcon"></span>
            <span class="css-CaseReviewCaseTree-SectionName-textEllipsis">{{ node.label }}</span>
          </span>
          <span v-if="!data.IsGroup" style="display: inline-flex">
            <span>
              <Icon :size="16" type="ios-document-outline" />
            </span>
            <span class="css-CaseReviewCaseTree-SectionName-textEllipsis">
              <Poptip trigger="hover" transfer :content="data.Title" placement="top-start" padding="2px 4px 2px 4px"
                word-wrap width="200">
                {{ data.Title }}
              </Poptip>
            </span>
          </span>
          <span class="testplan-SectionControls">
            <span v-if="!data.IsGroup" style="display: block; padding: 0px 2px 0px 2px">
              <Tag color="blue">{{ data.Priority }}</Tag>
            </span>
            <span style="display: block;padding:0px 2px 0px 30px">
              <Button type="info" ghost @click="deleteTestReviewCase(data.TestCase)">Del</Button>
            </span>
          </span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";

export default {
  props: ["projectID", "casereviewID", "refresh"],
  data() {
    return {
      isLoading: false,
      defaultProps: {
        label: "Title",
        children: "children",
        isLeaf: false,
      },
      CaseReviewCaseTreeData: []
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapState('testcase', ['testCaseTagList']),
    ...mapState(["appBodyHeight", "appBodyMainHeight",]),
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters("testcase", [
      "testCaseGroupTitle",
      "caseFilters",
      "refreshCase",
    ]),

    containerHeight: function () {
      return this.appBodyHeight - 300;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },
    tagList: function () {
      return this.getCaseTags()
    },
  },
  methods: {
    ...mapGetters('testcase', ['getCaseTags']),
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", ["setTestCaseGroupTitle", "setSearchKeyword", "setRefreshCase", "setShowCaseID",]),

    setLoading: function (bol) {
      this.isLoading = bol
    },

    loadCaseReviewCaseTree: function (casereviewID) {
      this.setLoading(true)
      this.$axios.get('/api/project/casereview/case_tree/' + casereviewID).then(response => {
        this.CaseReviewCaseTreeData = response.data.result
        this.setLoading(false)
      }, response => { })
    },

    filterNode(value, data) {
      if (value.type === 1) {
        if (!value.filters[0]) return true;
        return data.Title.indexOf(value.filters[0]) !== -1;
      }
      if (value.type === 2) {
        if (value.filters.length === 0) return true;
        return value.filters.indexOf(data.Priority) > -1;
      }
      if (value.type === 3) {
        if (value.filters.length === 0) return true;
        return value.filters.indexOf(data.accessTest) > -1;
      }
    },

    deleteTestReviewCase(testcase) {
      console.log(testcase)
      this.$axios.delete('/api/project/casereview/case_tree/' + this.casereviewID + "?id=" + testcase).then(response => {
        this.loadCaseReviewCaseTree(this.casereviewID);
        this.$Message.success({
          content: '删除成功',
          duration: 3,
          closable: true
        })

      }, response => {
        this.$Message.error({
          content: '删除失败',
          duration: 3,
          closable: true
        })
      })
    }
  },

  created: function () {
    if (this.casereviewID) {
      this.loadCaseReviewCaseTree(this.casereviewID);
    }
  },

  mounted: function () { },

  watch: {
    casereviewID: function (value) {
      if (this.casereviewID) {
        this.loadCaseReviewCaseTree(this.casereviewID);
      }
    },
    caseFilters: function (value) {
      this.$refs.caseReviewCaseTree.filter(value);
    },

  },

  components: {},
};

</script>

<style lang="less">
.css-CaseReviewCaseTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 10px;
  position: relative;
}

.CaseReviewCaseTree-node {
  //flex: 1;
  display: flex;
  height: 26px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  //padding-right: 8px;

  -webkit-box-align: center;

  flex: 1 1 auto;
  padding-left: 0px;
  min-width: 0px;
  //max-width:80%;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 8px;
}

.css-1bcfq0a-folderIcon-SectionIcon {
  display: inline-block;
  width: 14px;
  height: 14px;
  flex: 0 0 12px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-CaseReviewCaseTree-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 4px 0px 4px;
  overflow: hidden;
  color: #606266;
  //width: 50%;
  width: 400px;
  margin: 0px 4px 0px 4px;
}

.css-142qn3c-CaseReviewCaseTree-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px;
  cursor: pointer;
  padding: 4px;
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  -webkit-appearance: none;
  -webkit-user-select: text;
  outline-color: transparent;
  box-shadow: none;
}

.add-new-case:hover {
  color: #0066ff;
}

.testplan-SectionControls {
  flex: 0 0 auto;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding-left: 6px;
  padding-right: 2px;
  margin-right: 33px;
  position: relative;
  //width: 52px;
}
</style>