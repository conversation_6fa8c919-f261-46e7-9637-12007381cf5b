<template>
  <div :style="{ background: '#fff', 'border-left': '1px solid #fff' }">
    <div class="SectionPanelTop ">
      <label class="SectionTitle ">
        <h1 class="heading">用例评审</h1>
      </label>
      <label class="SectionFilter">
        <Input v-model="filterText" clearable placeholder="搜索用例评审" style="width: 100%;" />
      </label>
      <div class="SectionControls">
        <span class="RootSection">
          <span>
            <Icon type="ios-folder-open" :size="16" color="#0066ff" />
          </span>
          <span class="css-1d4bcnu-SectionName-SectionName">全部分组</span>
        </span>
      </div>
      <div class="css-CaseGroupTree-SectionTreeView" :style="{ height: containerHeight + 'px' }">
        <el-tree ref="versionCaseReviewGroupTree" :data="versionCaseReviewGroup" :props="defaultProps" lazy
          :load="loadNode" icon-class="el-icon-caret-right" highlight-current :expand-on-click-node="false"
          :filter-node-method="filterNode" node-key="id" :default-expanded-keys="defaultTree"
          @node-click="handleNodeClick">
          <span class="css-TestCaseGroupTree-node" slot-scope="{ node, data }">
            <el-badge v-if="data.leaf == false" id="elbadge" style="margin-top: auto" :value="node.length" :max="9999">
              <span class="css-folderIcon-SectionIcon"></span>
            </el-badge>
            <span :class="isDelLine(data)">{{ data.Title }}</span>
          </span>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import { loadProjectVersionCaseReviewList } from '../../business-service/ProjectApiService'

export default {
  name: "caseReviewPanel",
  props: ["projectID", "checkbox", "checkStrictly", "casereviewID"],
  data() {
    return {
      showNodeToolID: 0,
      showTestCaseImportXindFile: true,
      spinShow: true,
      defaultTree: [],
      Tree: [],
      filterText: '',
      defaultProps: {
        id: "id",
        label: "Title",
        children: "children",
        isLeaf: 'leaf',
      },
      versionCaseReviewGroup: [],
    }
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters("testcase", ["testCaseGroupTitle", "searchKeyword",]),
    ...mapState(["appBodyMainHeight", "appBodyWidth"]),

    containerHeight: function () {
      return this.appBodyMainHeight - 152;
    },

  },
  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations("testcase", ["setSearchKeyword", "setShowTestCaseImportXmindDialog",]),
    ...mapMutations(["setItemViewMode"]),

    changeViewMode: function (value) {
      //console.log(value);
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.Title.indexOf(value) !== -1;
    },

    loadDefaultTree: function (casereviewID, projectID) {
      this.$axios.get('/api/project/' + projectID + '/casereview/list?id=' + casereviewID).then(response => {
        this.defaultTree.push(response.data.result[0].HalfCaseGroup)
      }, response => { })
    },

    loadNode(node, resolve) {
      // console.log(node); //如果展开第一级节点，从后台加载一级节点列表
      if (node.level == 0) {
        this.node = node
        this.resolveFunc = resolve
        this.loadRootNode(node, resolve);
      } //如果展开其他级节点，动态从后台加载下一级节点列表
      if (node.level == 1) {
        this.loadChildNode(node, node.data.id, resolve);
      }
    },

    //加载第一级节点
    loadRootNode(node, resolve) {
      this.$axios.get("/api/project/" + this.projectID + "/testcase/lazygrouptree?Root=0")
        .then((response) => {
          response.data.result.forEach(function (item) {
            item['leaf'] = false
          })
          this.versionCaseReviewGroup = response.data.result;
          //console.log(response.data.result)

          return resolve(this.versionCaseReviewGroup);
        });
    },
    //加载节点的子节点集合
    loadChildNode(node, parent_id, resolve) {
      this.$axios
        .get("/api/project/" + this.projectID + "/casereview/list?case_group_id=" + parent_id)
        .then((response) => {
          response.data.result.forEach(function (item) {
            item['leaf'] = true
          })
          let data = response.data.result;
          return resolve(data);
        });
    },

    isDelLine(data) {
      if (data.Status == 1) {
        return 'css-SectionName-textEllipsis-del-line'
      } else {
        return 'css-SectionName-textEllipsis'
      }
    },

    handleNodeClick(data) {
      this.$router.push({
        path: '/project/' + data.Project + '/test/case-reviews/' + data.id
      })
      this.$emit('updateCasereview', data.id)
    },


  },
  created: function () {
    this.loadDefaultTree(this.casereviewID, this.projectID)
  },

  mounted: function () {


  },

  watch: {
    'filterText': function (val) {
      val = val.replace(/(^\s*)|(\s*$)/g, "");
      this.$refs.versionCaseReviewGroupTree.filter(val);
    }

  },

  components: {
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.heading {
  font-size: 20px;
  font-weight: bold;
  color: #334866;
}

.SectionPanelTop {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 1px 8px 0px;
}

.SectionTitle {
  display: flex;
  flex-shrink: 0;
  -webkit-box-align: center;
  align-items: center;
  height: 80px;
  padding: 0px 24px;
}

.SectionFilter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 100px;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
  flex: 0 0 40px;
  //padding: 0px 24px;
}


.SectionControls {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 40px;
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
}

.RootSection {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 100%;
  cursor: default;
  flex: 1 0 76px;
  padding: 0px 24px;
}

.css-1d4bcnu-SectionName-SectionName {
  margin-left: 8px;
  font-size: 13px;
  /*font-weight: bold;*/
}

.css-TestPlanCaseTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 52px;
  position: relative;
}

.css-CaseGroupTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 56px;
  position: relative;
}

.css-TestCaseGroupTree-node {
  //flex: 1;
  display: flex;
  height: 26px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  //padding-right: 8px;
  -webkit-box-align: center;
  flex: 1 1 auto;
  padding-left: 0px;
  min-width: 0px;
  //max-width:80%;
}

.css-folderIcon-SectionIcon {
  display: inline-block;
  width: 24px;
  height: 24px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-SectionName-textEllipsis-del-line {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  //padding: 0px 2px 0px 2px;
  //padding-left: 4px;
  overflow: hidden;
  color: #606266;
  margin: 0px 4px 0px 4px;

  .text-display {
    text-decoration: line-through;
  }
}

.css-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  //padding: 0px 2px 0px 2px;
  //padding-left: 4px;
  overflow: hidden;
  color: #606266;
}
</style>