<template>
  <div :style="'height:' + containerHeight + 'px'">
    <Card :bordered="false" :dis-hover="true" style="margin: 10px 24px 8px;">
      <p slot="title">用例评审: {{ this.caseReview.Title }} </p>
      <div>
        <h4>相关需求:
          <div v-if="requirements.length > 0">
            <li v-for="require in requirements" :key="require.id">
              <Tag color="primary"># {{ require.id }} </Tag>
              <router-link :to="'/project/' + projectID + '/requirement/' + require.id">
                <span> [{{ require.ViewData.Status }}]</span>
                <span> [{{ require.ViewData.Owner }}]</span>
                <span>{{ require.Title }} </span>
              </router-link>
            </li>
          </div>
          <div v-if="requirements.length == 0"> 暂无关联需求 </div>
        </h4>
      </div>
      <div v-if="caseReview.Status != 1" style="padding-top: 20px;">
        <h4>评审结果:
          <div>
            {{ caseReview.CaseReviewResult }}
          </div>
        </h4>
      </div>
      <div style="padding-top: 20px;">
        <h4>评审人员:
          <div>
            <li>{{ caseReview.view_data.reviewer }}</li>
          </div>
        </h4>
      </div>
    </Card>
    <Card :bordered="false" :dis-hover="true" style="margin: 0px 24px 8px;">
      <div slot="title">
        <el-button size="small" :disabled="this.caseReview.Status != 1" @click="showCaseTree">添加用例</el-button>
        <el-button size="small" v-if="caseViewType === true" :disabled="this.caseReview.Status != 1"
          @click="saveTestCase">保存</el-button>
        <el-button size="small" v-if="caseViewType === true"
          :disabled="disableDeleteButton || this.caseReview.Status != 1" @click="deleteTestCase">删除用例</el-button>
      </div>
      <div slot="extra" @click="changeCaseView(true)">
        <span class="SectionControls">
          <span v-if="selectViewType == false" class="css-1avq18a-actionGroup" style="cursor: pointer;">
            <Icon type="md-sync" :size="20" /> 脑图视图
          </span>
          <span v-if="selectViewType == true" class="css-1avq18a-actionGroup" style="cursor: pointer;">
            <Icon type="md-sync" :size="20" /> 树状视图
          </span>
        </span>
      </div>
      <div>
        <div v-if="caseViewType === false">
          <div style="position: relative;">
            <Row>
              <Col span="22">用例名称</Col>
              <Col span="1" style="position: relative;">等级</Col>
              <Col span="1" style="position: relative;">操作</Col>
            </Row>
          </div>
          <div style="margin-top: 30px;">
            <CaseReviewCaseTree ref='casetree' :projectID="projectID" :casereviewID="casereviewID">
            </CaseReviewCaseTree>
          </div>
        </div>
        <div v-if="caseViewType === true">
          <mind-case ref='casemind' :projectID="projectID" :mindFileID="casereviewID"
            @isDisableDeleteButton="isDisableDeleteButton"></mind-case>
        </div>
      </div>
    </Card>
    <CaseReviewTestCaseSelectDialog ref="case-review-test-case-dialog" @selectCase="selectCaseGroup"
      @refreshTestCase="refreshCaseReviewCaseTree" :projectID="projectID" :casereviewID="casereviewID">
    </CaseReviewTestCaseSelectDialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import CaseReviewCaseTree from './CaseReviewCaseTree'
import CaseReviewTestCaseSelectDialog from './CaseReviewTestCaseSelectDialog'
import mindCase from "./xmind/mind.vue";

export default {
  props: ["projectID", "casereviewID", "refresh"],
  data() {
    return {
      dropdownEditorTags: [],
      filterTagIds: [],
      caseReview: {
        Title: "",
      },

      caseViewType: false,
      selectViewType: false,
      disableDeleteButton: true,
      requirements: [],
      formItem: {
        Title: '',
        Desc: '',
        Project: 0,
        Creator: 0,
        CaseCount: 0,
        SelectCaseGroup: [],
        HalfCaseGroup: [],
        Deadline: '',
        Reviewer: [],
        Version: 0,
        Requirement: []
      },
    }
  },

  methods: {
    ...mapMutations("testcase", ["setCaseFilters", "setRefreshCase", "setShowCaseID", "setTestCaseViewMode",]),
    ...mapMutations('projectglobal', ['setCaseSelectDialogShow']),
    ...mapMutations("testplan", ["setUpdateTestPlanTestCaseButton"]),

    loadCaseReviewTitle: function () {
      this.$axios.get("/api/project/" + this.projectID + "/casereview/" + this.casereviewID).then(
        (response) => {
          this.caseReview = response.data.result
          this.requirements = response.data.result.view_data.requirements
        }, (response) => { }
      );
    },

    refreshCaseReviewCaseTree: function (casereviewID) {
      if (this.caseViewType == true) {
        this.$refs.casemind.getCaseReviewMind()
      } else {
        this.$refs.casetree.loadCaseReviewCaseTree(casereviewID)
      }
    },

    loadCaseTags: function () {
      for (let i = 0; i < this.testCaseTagList.length; i++) {
        let tempTag = { label: "", value: 0, color: "" };
        tempTag.label = this.testCaseTagList[i].TagName;
        tempTag.value = this.testCaseTagList[i].TagValue;
        tempTag.color = this.testCaseTagList[i].TagColor;
        this.dropdownEditorTags.push(tempTag);
      }
    },

    filterCaseTitle: function (keyword) {
      let value = { type: 1, filters: [keyword] };
      this.setCaseFilters(value);
    },

    filterCaseByTag: function (value) {
      let tagID = parseInt(value);
      let tagIndex = this.filterTagIds.indexOf(tagID);
      if (tagIndex > -1) {
        this.filterTagIds.splice(tagIndex, 1);
      } else {
        this.filterTagIds.push(tagID);
      }
      let filters = { type: 2, filters: this.filterTagIds };
      this.setCaseFilters(filters);
    },

    showCaseTree: function () {
      this.$refs['case-review-test-case-dialog'].setDialogShow(true)
      // this.setCaseSelectDialogShow(true)
      // this.setUpdateTestPlanTestCaseButton(true);
    },

    selectCaseGroup(caseCount, groupIDs, halfCheckCaseGroup) {
      this.addTestCaseTree(groupIDs, halfCheckCaseGroup)
    },

    updateTestCaseTree: function () {
      // console.log("@@@@this.casereviewID",this.casereviewID)
    },

    changeCaseView: function () {
      this.caseViewType = !this.caseViewType
      this.selectViewType = !this.selectViewType
      // this.setTestCaseViewMode("mindMap");
    },

    isDisableDeleteButton: function (value) {
      this.disableDeleteButton = value
    },

    deleteTestCase: function () {
      let testCaseID = 0
      if (this.caseViewType == true) {
        testCaseID = this.$refs.casemind.selectNodeID
      }
      this.$axios.delete('/api/project/casereview/case_tree/' + this.casereviewID + "?id=" + testCaseID).then(response => {
        this.$Message.success({
          content: '删除成功',
          duration: 3,
          closable: true
        })
        this.refreshCaseReviewCaseTree(this.casereviewID)
      }, response => {
        this.$Message.error({
          content: '删除失败',
          duration: 3,
          closable: true
        })
      })
    },

    addTestCaseTree: function (checkCaseGroup, halfCheckCaseGroup) {
      let UpdateTestCaseTree = {}
      UpdateTestCaseTree.SelectCaseGroup = checkCaseGroup
      UpdateTestCaseTree.halfCheckCaseGroup = halfCheckCaseGroup
      this.$axios.patch("/api/project/casereview/case_tree/" + this.casereviewID, UpdateTestCaseTree).then((response) => {
        this.$Message.success({
          content: '添加成功',
          duration: 3,
          closable: true
        })
        this.setCaseSelectDialogShow(false);
        this.refreshCaseReviewCaseTree(this.casereviewID)
      })
    },

    saveTestCase: function () {
      let testcase_minder = this.$refs['casemind'].getMinderData()
      //console.log("testcase_mindertestcase_minde====", testcase_minder)
      this.$axios.post('/api/project/casereview/' + this.casereviewID + '/mindmap/save', testcase_minder).then((response) => {
        this.$Message.success({
          content: '保存成功',
          duration: 3,
          closable: true
        })
        this.refreshCaseReviewCaseTree(this.casereviewID)
      }), response => {
        this.$Message.error({
          content: '保存失败',
          duration: 3,
          closable: true
        })
      }
    }
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(["appBodyHeight", "appBodyMainHeight", "appBodyWidth"]),
    ...mapState('testcase', ['testCaseTagList']),
    ...mapGetters("testcase", ["showCaseID", "testCaseViewMode"]),

    containerHeight: function () {
      return this.appBodyHeight;
    },

  },

  watch: {
  },

  components: {
    CaseReviewCaseTree,
    CaseReviewTestCaseSelectDialog,
    mindCase,
  },

  created: function () {
    this.casereviewID = this.$route.params.casereviewID
    this.loadCaseTags();
    this.loadCaseReviewTitle()
  },

}

</script>

<style>
.css-khpljl-SectionPanelTop {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 1px 8px 0px;
}

.css-1avq18a-actionGroup {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 52px;
  padding: 0px 8px;
}

.css-addcase-btn {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 10px;
  width: 20px;
}

.span {
  display: inline;
}

.SectionControls {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 40px;
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
  margin-left: 20px;
}

.casereview-SectionControls {
  /* flex: 0 0 auto;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between; */
  /* -webkit-box-align: center; */
  /* align-items: center; */
  /* padding-left: 2px;
  padding-right: -20px;
  margin-right: 2px;*/
  position: relative;
}
</style>