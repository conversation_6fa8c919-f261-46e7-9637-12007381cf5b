<template>
  <div class="projent-test-layout" :style="'height:' + containerHeight + 'px; width: 100%;'">
    <Layout :style="'height:' + containerHeight + 'px;'">
      <Sider hide-trigger collapsible :collapsed-width="60" v-model="isCollapse" width="180"
        :style="{ background: '#f5f7f8' }">
        <el-menu :default-active="menuSelect" mode="vertical" router class="el-menu-vertical-demo"
          :collapse="isCollapse" :style="{ background: '#f5f7f8' }" background-color="#f5f7f8">
          <el-menu-item index="test-case" :route="'/project/' + projectID + '/test/test-case'">
            <i class="el-icon-collection"></i>
            <span slot="title">测试用例</span>
          </el-menu-item>
          <el-menu-item index="case-reviews" :route="'/project/' + projectID + '/test/case-reviews'">
            <i class="el-icon-document-checked"></i>
            <span slot="title">用例评审</span>
          </el-menu-item>
          <el-menu-item index="test-plan" :route="'/project/' + projectID + '/test/test-plan'">
            <i class="el-icon-date"></i>
            <span slot="title">测试计划</span>
          </el-menu-item>
          <el-menu-item index="report" :route="'/project/' + projectID + '/test/report'">
            <i class="el-icon-document"></i>
            <span slot="title">测试报告</span>
          </el-menu-item>
          <el-menu-item index="test-autocase" :route="'/project/' + projectID + '/test/auto-case'">
            <i class="el-icon-paperclip"></i>
            <span slot="title">自动化用例</span>
          </el-menu-item>
        </el-menu>
        <Row type="flex" justify="center" align="middle">
          <p style="height: 80px"></p>
          <Col span="4">
          <Icon @click.native="collapsedSider" size="28" color="#5578AA" :type="iconname"></Icon>
          </Col>
        </Row>
      </Sider>
      <Content :style="{ background: '#f5f7f8' }">
        <router-view name="projectTest" :projectID="projectID"></router-view>
      </Content>
    </Layout>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";


export default {
  name: "projectTestingManagement",
  props: ["action", "testAction", "objID", "groupID", "caseID", "version", "planId"],
  data() {
    return {
      projectID: 0,
      columnItemHeight: 200,
      viewMode: "board",
      iconname: "ios-arrow-dropleft",
      isCollapse: false,
      autocaseShow: false,
    };
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState("testcase", ["testCaseViewMode"]),
    ...mapState(['appBodyHeight', "appBodyMainHeight", "appBodyWidth"]),

    containerHeight: function () {
      return this.appBodyHeight;
    },

    menuSelect: function () {
      switch (this.$route.name) {
        case 'testCase':
          return 'test-case'
        case 'testPlan':
          return 'test-plan'
        case 'testReport':
          return 'report'
        case 'projecttestReportDetail':
          return 'report'
        case 'AutoCase':
          return 'test-autocase'
        case 'CaseReviews':
          return 'case-reviews'
        case 'CaseReviewsDetail':
          return 'case-reviews'
        default:
          return 'test-case'
      }
    },

    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      }
      return 0;
    },

    testCaseExecute: function () {
      if (this.$route.params.testAction) {
        return this.$route.params.testAction;
      }
      return 0;
    },

    testReportID: function () {
      if (this.$route.params.groupID) {
        return this.$route.params.groupID;
      }
      return 0;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },

  },

  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange", 'setprojectID']),
    ...mapMutations('testcase', ['setTestCasePlanShow']),
    ...mapMutations(["setItemViewMode"]),
    ...mapActions('project', ['loadProjectInfo', 'getProjectFortestings']),


    changeViewMode: function (value) {
      //console.log(value);
    },

    collapsedSider() {
      if (this.isCollapse) {
        this.setTestCasePlanShow(true)
      } else {
        this.setTestCasePlanShow(false)
      }
      this.isCollapse = !this.isCollapse;
      if (this.iconname == "ios-arrow-dropleft") {
        this.iconname = "ios-arrow-dropright";
      } else {
        this.iconname = "ios-arrow-dropleft";
      }
    },
  },

  created: function () {
    //console.log('ProjectTesting.vue created')
    this.projectID = parseInt(this.$route.params.projectID)
    this.setprojectID(this.projectID)
    this.loadProjectInfo(this.projectID)
    this.$store.dispatch('project/getProjectFortestings', { projectID: this.projectID, versionId: 0 })
  },

  beforeRouteLeave: function (to, from, next) {
    //console.log("from==", from)
    if (from.name == "testCaseGroup") {
      this.$confirm('正在离开本页面，请确认是否已保存 ？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        // 如果取消跳转地址栏会变化，这时保持地址栏不变
        window.history.go(1)
      })
    } else {
      next()
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    // console.log("from==", from)
    if (from.name == "testCaseGroup") {
      //if(from.params.action == "test-case"){
      //if(from.params.action == "test-case" && this.testCaseViewMode == "mindMap"){
      this.$confirm('正在离开本页面，请确认是否已保存 ？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        window.history.go(1)
      })
    } else {
      next()
    }
  },

  mounted: function () { },

  watch: {
    versionID: function (value) { },
    projectID: function () {
      this.loadProjectInfo(this.projectID)
    },
  },

  components: {
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.testCaseSectionTitle {
  display: flex;
  flex-shrink: 0;
  -webkit-box-align: center;
  align-items: center;
  height: 72px;
  padding: 0px 24px;
}

.testCaseectionFilter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-top: -16px;
  height: 48px;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
  flex: 0 0 48px;
  padding: 0px 24px;
}

.testCaseSectionControls {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 40px;
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
}

.testCaseSectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.layout-header {
  //height: 35px;
  //background: #fff;
  //box-shadow: 0 1px 1px rgba(0,0,0,.1);
}
</style>
