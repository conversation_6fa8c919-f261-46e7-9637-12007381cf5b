<template>
  <Modal :value="caseSelectDialogShow" :z-index="1001" title="选择测试用例" :mask-closable="false" :mask="false"
    class-name="vertical-center-modal" :width="800" @on-cancel="cancel">
    <div style="height: 432px;overflow-y: scroll;overflow-x: hidden">
      <div class="css-142qn3c-SectionTreeView">
        <el-tree ref="caseTree" :data="testCaseGroup" lazy :load="loadNode" node-key="id" :show-checkbox="true"
          icon-class="el-icon-caret-right" :highlight-current="false" empty-text="请选择要关联的测试用例"
          :expand-on-click-node="true" @check-change="handleCheckChange" :props="defaultProps"
          :default-expand-all="false" :auto-expand-parent="true" :default-expanded-keys="defaultExpanded">
          <span slot-scope="{ node, data }">
            <span class="css-SectionName-textEllipsis" v-if="data.IsGroup">
              <el-badge id="elbadge" class="css-el-badge" :value=data.pd_count :max="999">
                <span class="css-folderIcon-SectionIcon">
                </span>
              </el-badge>
              <span style="width: 50px; text-overflow: ellipsis; white-space: nowrap;">{{ data.Title }}</span>
            </span>
            <span v-if="!data.IsGroup && data.id !== -1">
              <span style="width:100px; display:inline-flex">
                <span style="font-size: 13px;">
                  <Icon :size="16" type="ios-document-outline" />
                  <span>{{ data.id }}</span>
                  <span>{{ data.Title }}</span>
                </span>
              </span>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div slot="footer">
      <Button type="primary" @click="clearSelectCase">清空</Button>
      <Poptip confirm transfer @on-ok="ok" @on-cancel="" width="200">
        <div slot="title" style="text-align: left; margin: 0">
          确定添加测试用例 {{ selectCasesCount }} 条
        </div>
        <Button type="primary" @click="checkedCount">添加</Button>
      </Poptip>
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState } from "vuex";
import { VueEditor } from "vue2-editor";
import ProjectTestCaseGroupTree from "../test-case/ProjectTestCaseGroupTree.vue";
import { getTestCaseList } from "../business-service/ProjectTestCaseApiService.js";

export default {
  props: ["projectID"],
  data() {
    return {
      projectVersions: [],
      projectMembers: [],
      issueStatus: [],
      showLoading: false,
      checkCaseGroup: [],
      halfCheckCaseGroup: [],
      selectCasesCount: 0,
      selectGroupCount: 0,
      selectGroupCaseList: [],
      defaultExpanded: [],
      editorToolBar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ color: [] }, { background: [] }],
      ],
      testCaseGroup: [],
      defaultProps: {
        children: "children",
        label: "Title",
        isLeaf: function (data, node) {
          return !data.IsGroup;
        }
      },
      upDateTestPlanTestCase: {},
      checked: false,
      checkedId: 0,
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion"]),
    ...mapState(["appBodyMainHeight"]),
    ...mapState('projectglobal', ['caseSelectDialogShow',]),
    ...mapState('testplan', ['defTestPlan', 'UpdateTestPlanTestCaseButton']),

    containerHeight: function () {
      if (this.appBodyMainHeight - 100 > 600) {
        return 600;
      } else {
        return this.appBodyMainHeight - 600;
      }
    },

    project: function () {
      return parseInt(this.projectID);
    },

    projectVersion: function () {
      return parseInt(this.versionID);
    },

    dialogShow: function () {
      return this.createReqType === 1;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setCaseSelectDialogShow"]),
    ...mapMutations("testplan", ["setUpdateTestPlanTestCaseButton", "setRefreshPlanCase"]),

    ok: function () {
      // console.log("UpdateTestPlanTestCaseButton=", this.UpdateTestPlanTestCaseButton)
      if (this.UpdateTestPlanTestCaseButton == true) {
        this.upDateTestPlanTestCase.Owner = this.defTestPlan.Owner
        this.upDateTestPlanTestCase.SelectCaseGroup = this.checkCaseGroup
        this.upDateTestPlanTestCase.halfCheckCaseGroup = this.halfCheckCaseGroup
        this.$axios.patch("/api/project/testplan/testplancases/" + this.defTestPlan.id, this.upDateTestPlanTestCase).then((response) => {
          this.$Message.success({
            content: '添加成功',
            duration: 3,
            closable: true
          })
          this.setCaseSelectDialogShow(false);
          this.setRefreshPlanCase(true);
        }, (response) => {
          this.$Message.error({
            content: '添加失败',
            duration: 3,
            closable: true
          })
        },
          this.setUpdateTestPlanTestCaseButton(false)
        );
      } else {
        // console.log("UpdateTestPlanTestCaseButton=false")
        this.$emit("selectCase", this.selectCasesCount, this.checkCaseGroup, this.halfCheckCaseGroup
        );
        this.setCaseSelectDialogShow(false);
      }
    },
    cancel() {
      this.setCaseSelectDialogShow(false);
    },

    checkTestCaseGroup(data, node, el, selectKeys, halfSelectKeys, selectCasesCount) {
      this.checkCaseGroup = selectKeys;
      this.selectCasesCount = selectCasesCount;
      this.halfCheckCaseGroup = halfSelectKeys;
    },

    selectTestCaseGroup(data, node, el) {
      this.selectGroupCaseList = [];
      this.showLoading = true;
      getTestCaseList(data.id, 0).then(
        (response) => {
          this.showLoading = false;
          this.selectGroupCaseList = response.data.result;
        },
      );
    },

    checkedCount() {
      this.selectCasesCount = 0
      this.selectGroupCount = 0
      this.checkCaseGroup = []
      this.halfCheckCaseGroup = []

      this.checkedCase = this.$refs.caseTree.getCheckedNodes();
      for (var i = 0; i < this.checkedCase.length; i++) {
        if (this.checkedCase[i].IsGroup == false) {
          this.selectCasesCount = this.selectCasesCount + 1
        }
        if (this.checkedCase[i].IsGroup == true) {
          this.selectGroupCount = this.selectGroupCount + 1
        }
      }

      this.checkCaseGroup = this.$refs.caseTree.getCheckedKeys();
      this.halfCheckCaseGroup = this.$refs.caseTree.getHalfCheckedKeys();

      // var ConfirmInfo = "添加目录 " + this.selectGroupCount + " 个, 测试用例 " + this.selectCasesCount + " 条 ?";
      // this.$Modal.confirm({
      //   //title: '确认添加',
      //   content: ConfirmInfo,
      //   onOk: () => {
      //     this.ok()
      //   },
      //   onCancel: () => {
      //   }
      // });
    },

    clearSelectCase() {
      this.$refs.caseTree.setCheckedKeys([]);
      this.checkCaseGroup = [];
      this.halfCheckCaseGroup = [];
    },

    handleExpanded(data) {
      if (!this.defaultExpanded.includes(data.id)) {
        this.defaultExpanded.push(data.id)
        this.checkedId = data.id
      }
    },

    handleCheckChange(data, checked, indeterminate) {
      //console.log('this.defaultExpandedthis.defaultExpanded====', this.defaultExpanded)
      if (checked) {
        if (!this.defaultExpanded.includes(data.id)) {
          if (data.IsGroup == true) {
            this.defaultExpanded.push(data.id)
            this.checkedId = data.id
          }
        }
      } else {
        for (var i = 0; i < this.defaultExpanded.length; i++) {
          if (this.defaultExpanded[i] === data.id) {
            this.defaultExpanded.splice(i, 1)
          }
        }
      }
      this.checked = checked
    },

    loadNode(node, resolve) {
      if (node.level == 0) {
        this.loadRootNode(node, resolve);
      }
      if (node.level >= 1) {
        if (!this.defaultExpanded.includes(node.data.id)) {
          this.defaultExpanded.push(node.data.id)
        }
        this.loadChildNode(node, node.data.id, resolve);
      }
    },
    loadRootNode(node, resolve) {
      this.$axios.get("/api/project/" + this.projectID + "/testcase/lazyload?parent_id=0&is_group=1").then((response) => {
        this.testCaseGroup = response.data.result;
        this.spinShow = false;
        return resolve(this.testCaseGroup);
      });
    },
    loadChildNode(node, parent_id, resolve) {
      this.$axios.get("/api/project/" + this.projectID + "/testcase/lazyload?parent_id=" + parent_id).then((response) => {
        let data = response.data.result;
        if (this.checked) {
          // if(this.checkedId == parent_id){
          if (this.defaultExpanded.includes(parent_id)) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].IsGroup == true) {
                this.defaultExpanded.push(data[i].id)
                this.checkedId = data[i].id
              }
            }
          }
        }
        return resolve(data);
      });
    },
  },

  created() {
  },

  mounted() { },

  watch: {
  },

  components: {
    VueEditor,
    ProjectTestCaseGroupTree,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }

  .ivu-modal-body {
    padding: 0px 16px 0px 16px;
    font-size: 14px;
    line-height: 1.5;
  }
}

.demo-split {
  height: 100%;
  border: 1px solid #dcdee2;
}

.demo-split-pane {
  padding: 10px;
}

.css-folderIcon-SectionIcon {
  display: inline-block;
  width: 16px;
  height: 14px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

#elbadge .el-badge__content {
  transform: scale(0.75);
  top: 9px;
  right: -2px;
  border-radius: 10px;
  min-width: 20px;
  color: #606266;
  height: 12px;
  line-height: 1px;
  text-align: center;
  padding: 0px 0px 0px 0px;
  border-style: none;
  border: 0px solid transparent;
  background: transparent;
}


.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-1bcfq0a-folderIcon-SectionIcon {
  display: inline-block;
  width: 14px;
  height: 12px;
  flex: 0 0 12px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-1kjszj6-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 0px;
  overflow: hidden;
  color: #606266;
}

.css-142qn3c-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px;
  cursor: pointer;
  padding: 4px;
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  -webkit-appearance: none;
  -webkit-user-select: text;
  outline-color: transparent;
  box-shadow: none;
}

.add-new-case:hover {
  color: #0066ff;
}

.ivu-input {
  display: inline-block;
  width: 100%;
  height: 26px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 14px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #5578aa;
  background-color: #fff;
  background-image: none;
  position: relative;
  cursor: text;
}

.ivu-input-icon {
  width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 16px;
  text-align: center;
  color: #808695;
  position: absolute;
  right: 0;
  z-index: 3;
}

.text-display {
  padding: 0px 0px;
}

.ivu-dropdown-menu {
  min-width: 200px;
}
</style>
