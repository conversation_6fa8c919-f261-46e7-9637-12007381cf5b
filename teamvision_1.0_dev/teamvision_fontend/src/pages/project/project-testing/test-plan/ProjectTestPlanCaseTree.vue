<template>
  <div style="height: 100%; width: 100%">
    <div class="css-TestPlanCaseTree-SectionTreeView">
      <Spin size="large" fix v-if="spinShow"></Spin>
      <el-tree v-if="!isLazyLoad" ref="testPlanCaseTree" :data="testPlanCaseTreeData" :show-checkbox="checkbox"
        node-key="id" icon-class="el-icon-caret-right" :filter-node-method="filterNode" :highlight-current="true"
        draggable empty-text="计划未包含任何用例" :expand-on-click-node="false" :props="defaultProps" :default-expand-all="false"
        :default-expanded-keys="defaultExpandIds" @node-click="handleNodeClick" @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse" @check-change="handleCheckChange" @current-change="handleCurrentChange"
        :auto-expand-parent="true">
        <span class="TestPlanCaseTree-node" slot-scope="{ node, data }" @mouseover="onMouseOver(data.id)"
          @mouseout="onMouseOut(data.id)">
          <span v-if="data.IsGroup">
            <Icon v-if="data.TestResult !== 0" type="ios-folder" color="green" size="16" />
            <Icon v-if="data.TestResult === 0" type="ios-folder" color="gold" size="16" />
            <span class="css-TestPlanCaseTree-SectionName-textEllipsis">{{ node.label }} ({{ data.exec_count }})</span>
          </span>
          <span v-if="!data.IsGroup" style="display: inline-flex">
            <span>
              <Icon v-if="data.TestResult !== 0" color="green" :size="16" type="ios-document" />
              <Icon v-if="data.TestResult === 0" :size="16" type="ios-document-outline" />
            </span>
            <span style="font-size: 13px"> {{ data.id }}</span>
            <span class="css-TestPlanCaseTree-SectionName-textEllipsis">
              <Poptip trigger="hover" transfer :content="data.Title" placement="top-start" padding="2px 4px 2px 4px"
                word-wrap width="400">{{ data.Title }}
              </Poptip>
            </span>
          </span>
          <span class="testplan-SectionControls" v-if="!data.IsGroup">
            <span style="display: block; padding: 0px 2px 0px 2px">{{ data.view_data.Owner }}</span>
            <span style="display: block; padding: 0px 2px 0px 2px">
              <Tooltip :content="data.view_data.TestResult" placement="top" theme="light" :delay="500">
                <Icon size="18" v-if="data.TestResult === 0" type="ios-alert" />
                <Icon size="18" v-if="data.TestResult === 4" color="red" type="md-close-circle" />
                <Icon size="18" v-if="data.TestResult === 3" color="orange" type="ios-disc" />
                <Icon size="18" v-if="data.TestResult === 2" type="md-remove-circle" />
                <Icon size="18" v-if="data.TestResult === 1" color="green" type="md-checkmark-circle" />
              </Tooltip>
            </span>
            <span style="display: block; padding: 0px 2px 0px 2px">
              <Tag color="blue">{{ data.view_data.Priority }}</Tag>
            </span>
          </span>
        </span>
      </el-tree>
      <div v-if="reset">
        <el-tree v-if="isLazyLoad" ref="testPlanCaseTree" lazy :load="loadNode" :data="testPlanCaseTreeData"
          :props="defaultProps" :show-checkbox="checkbox" node-key="id" icon-class="el-icon-caret-right"
          :filter-node-method="filterNode" :highlight-current="true" draggable empty-text="计划未包含任何用例"
          :expand-on-click-node="false" :default-expand-all="false" :default-expanded-keys="defaultExpandIds"
          @node-click="handleNodeClick" @node-expand="handleNodeExpandLazyLoad" @node-collapse="handleNodeCollapse"
          @check-change="handleCheckChangeLazyLoad" @current-change="handleCurrentChange" :auto-expand-parent="true">
          <span class="TestPlanCaseTree-node" slot-scope="{ node, data }" @mouseover="onMouseOver(data.id)"
            @mouseout="onMouseOut(data.id)">
            <span v-if="data.IsGroup">
              <Icon v-if="data.TestResult === 0" type="ios-folder" color="gold" size="16" />
              <Icon v-if="data.TestResult !== 0" type="ios-folder" color="green" size="16" />
              <span class="css-TestPlanCaseTree-SectionName-textEllipsis">{{ node.label }}</span>
            </span>
            <span v-if="!data.IsGroup" style="display: inline-flex">
              <span>
                <Icon v-if="data.TestResult !== 0" color="green" :size="16" type="ios-document" />
                <Icon v-if="data.TestResult === 0" :size="16" type="ios-document-outline" />
              </span>
              <span style="font-size: 13px"> {{ data.id }}</span>
              <span class="css-TestPlanCaseTree-SectionName-textEllipsis">
                <Poptip trigger="hover" transfer :content="data.Title" placement="top-start" padding="2px 4px 2px 4px"
                  word-wrap width="400">{{ data.Title }}
                </Poptip>
              </span>
            </span>
            <span class="testplan-SectionControls" v-if="!data.IsGroup">
              <span style="display: block; padding: 0px 2px 0px 2px">{{ data.view_data.Owner }}</span>
              <span style="display: block; padding: 0px 2px 0px 2px">
                <Tooltip :content="data.view_data.TestResult" placement="top" theme="light" :delay="500">
                  <Icon size="18" v-if="data.TestResult === 0" type="ios-alert" />
                  <Icon size="18" v-if="data.TestResult === 4" color="red" type="md-close-circle" />
                  <Icon size="18" v-if="data.TestResult === 3" color="orange" type="ios-disc" />
                  <Icon size="18" v-if="data.TestResult === 2" type="md-remove-circle" />
                  <Icon size="18" v-if="data.TestResult === 1" color="green" type="md-checkmark-circle" />
                </Tooltip>
              </span>
              <span style="display: block; padding: 0px 2px 0px 2px">
                <Tag color="blue">{{ data.view_data.Priority }}</Tag>
              </span>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import labelEditorInputExternalTrigger from "../../../../components/common/LabelEditorExternalTigger-Input.vue";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";
import { updateTestPlanOwner } from "../business-service/ProjectTestCaseApiService";
import { getMemberDropdownItemData } from "../../business-service/ProjectUtilService";


export default {
  name: "ProjectTestPlanCaseTree",
  props: ["projectID", "checkbox",],
  data() {
    return {
      planId: 0,
      showNodeToolID: 0,
      asginMemberDropdownData: [],
      defaultProps: {
        children: "children",
        label: "Title",
        isLeaf: function (data, node) {
          return !data.IsGroup;
        }
      },
      spinShow: false,
      defaultExpandIds: [], // 这里存放要默认展开的节点 id
      checkedId: 0,
      caseResultDict: {
        //0,未开始,1通过，2 受阻碍 3 重测，4 失败
        '0': '未开始',
        '1': '通过',
        '2': '受阻碍',
        '3': '重测',
        '4': '失败',
      },
      maxCaseCount: 500,
      reset: true,
    };
  },

  computed: {
    ...mapGetters("projectglobal", ["objectChange"]),
    ...mapState(["appBodyMainHeight", "appBodyWidth"]),
    ...mapGetters("testcase", ["testCaseGroupTitle", "caseFilters",]),
    ...mapState('project', ['projectMembers']),
    ...mapState('testplan', ['testPlanCaseTree', 'testPlanCaseTreeData', 'refreshPlanCase', 'updateExecuteCaseTree',
      'currentCaseNode', 'defTestPlan', 'checkedTestCase', 'checkedTestCaseIdList']),

    containerHeight: function () {
      return this.appBodyMainHeight - 10;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },

    isLazyLoad: function () {
      return this.defTestPlan.CaseCount > this.maxCaseCount
    }
  },

  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testplan", ["setCurrentCaseNode", "setSelectCaseID", "setCheckedTestCase",
      "setRefreshCase", "setTestCaseTree", "setRefreshPlanCase", "initTestCaseTree"]),
    ...mapActions('testplan', ['getTestCaseTree']),

    onMouseOver(caseID) { },
    onMouseOut(caseID) { },

    updateAsginOwner: function (pid, owner) {
      let parameters = { owner: owner, ids: [pid] };
      updateTestPlanOwner(this.defTestPlan.id, parameters).then((response) => { });
    },

    getProjectMembers() {
      let tempMember = { label: "未分配", value: 0, color: "" };
      this.asginMemberDropdownData = getMemberDropdownItemData(this.projectMembers);
      this.asginMemberDropdownData.splice(0, 0, tempMember);
    },

    filterNode(value, data) {
      // console.log(value);
      if (value.type === 1) {
        if (!value.filters[0]) return true;
        return data.Title.indexOf(value.filters[0]) !== -1;
      } else {
        if (value.filters.length === 0) return true;
        // console.log(data.Priority);
        return value.filters.indexOf(data.Priority) > -1;
      }
    },

    loadTestCaseTree(planId) {
      if (this.defTestPlan.CaseCount < this.maxCaseCount) {
        this.getTestCaseTree(planId)
      }
      if (this.defTestPlan.CaseCount > this.maxCaseCount) {
        this.resetTreeRoot()
      }
    },

    // 树节点展开
    handleNodeExpand(data) {
      let flag = false;
      this.defaultExpandIds.some((item) => {
        if (item === data.id) {
          // 判断当前节点是否存在， 存在不做处理
          flag = true;
          return true;
        }
      });
      if (!flag) {
        // 不存在则存到数组里
        this.defaultExpandIds.push(data.id);
      }
    },

    // 树节点关闭
    handleNodeCollapse(node, data) {
      // 删除当前关闭的节点
      this.defaultExpandIds.some((item, i) => {
        if (item === data.id) {
          this.defaultExpandIds.splice(i, 1);
        }
      });
      this.removeChildrenIds(data); // 这里主要针对多级树状结构，当关闭父节点时，递归删除父节点下的所有子节点
      this.collapseAllChildNodes(node);
    },

    // 删除树子节点
    removeChildrenIds(data) {
      const ts = this;
      if (data.children) {
        data.children.forEach(function (item) {
          const index = ts.defaultExpandIds.indexOf(item.id);
          if (index > 0) {
            ts.defaultExpandIds.splice(index, 1);
          }
          ts.removeChildrenIds(item);
        });
      }
    },

    collapseAllChildNodes(node) {
      const childNodes = node.childNodes;
      if (childNodes) {
        childNodes.forEach(childNode => {
          this.$refs.testPlanCaseTree.store.collapseNode(childNode);
          this.collapseAllChildNodes(childNode); // 递归收缩子节点
        });
      }
    },

    handleNodeClick(data) {
      if (data.IsGroup == false) {
        // console.log(this.$refs.testPlanCaseTree.getCurrentKey())
        // this.setSelectCaseID(this.$refs.testPlanCaseTree.getCurrentKey());
        this.setCurrentCaseNode(data);
      }
    },

    // 获取已选择的测试caseid
    handleCheckChange() {
      this.setCheckedTestCase(this.$refs.testPlanCaseTree.getCheckedNodes());
    },

    handleCheckChangeLazyLoad(data, checked, indeterminate) {
      // console.log("handleCheckChangeLazyLoad", data, checked)
      if (checked) {
        if (!this.defaultExpandIds.includes(data.id)) {
          if (data.IsGroup == true) {
            this.defaultExpandIds.push(data.id)
            this.checkedId = data.id
          }
        }
      } else {
        for (var i = 0; i < this.defaultExpandIds.length; i++) {
          if (this.defaultExpandIds[i] === data.id) {
            this.defaultExpandIds.splice(i, 1)
          }
        }
      }
      this.checked = checked
      this.setCheckedTestCase(this.$refs.testPlanCaseTree.getCheckedNodes());
    },

    // 树节点展开
    handleNodeExpandLazyLoad(data) {
      // console.log("handleNodeExpandLazyLoad", data)
      if (!this.defaultExpandIds.includes(data.id)) {
        this.defaultExpandIds.push(data.id)
        this.checkedId = data.id
      }
      this.setCheckedTestCase(this.$refs.testPlanCaseTree.getCheckedNodes());
    },

    updateCaseTreeCaseResult() {
      // console.log("this.updateExecuteCaseTree=", this.updateExecuteCaseTree)
      if (this.updateExecuteCaseTree.id_list.length > 0) {
        for (var i = 0; i < this.updateExecuteCaseTree.id_list.length; i++) {
          var node_data = this.$refs.testPlanCaseTree.getNode(this.updateExecuteCaseTree.id_list[i]);
          node_data.data.TestResult = this.updateExecuteCaseTree.result
          node_data.data.TestResultName = this.caseResultDict[this.updateExecuteCaseTree.result]
          this.$refs.testPlanCaseTree.updateKeyChildren((this.updateExecuteCaseTree.id_list[i], node_data));
          // update parent node
          var parentID = node_data.parent.data.id
          var parentNodeData = this.$refs.testPlanCaseTree.getNode(parentID);
          parentNodeData.data.TestResult = this.updateExecuteCaseTree.result
          this.$refs.testPlanCaseTree.updateKeyChildren((parentID, parentNodeData));
        }
      }
      //this.updateExecuteCaseTree.refresh = false
    },

    handleCurrentChange(data) {
      // console.log("setCurrentCaseNode==", data)
      if (!this.defaultExpandIds.includes(data.id)) {
        this.defaultExpandIds.push(data.id)
        this.checkedId = data.id
      }
      this.setCurrentCaseNode(data);
    },

    resetTreeRoot() {
      this.reset = false
      this.initTestCaseTree([]);
      //this.loadNode(null, () => { });
      this.reset = true
    },
    loadNode(node, resolve) {
      // console.log("loadNode==================", node, "resolve==================", resolve);
      if (node.level == 0) {
        if (this.defTestPlan.id > 0) {
          this.loadRootNode(node, resolve);
        }
      } else if (node.level >= 1) {
        if (!this.defaultExpandIds.includes(node.data.id)) {
          this.defaultExpandIds.push(node.data.id)
        }
        this.loadChildNode(node, node.data.TestCase, resolve);
      }
    },
    loadRootNode(node, resolve) {
      // console.log("rootnode", node);
      this.$axios.get("/api/project/testplan/" + this.defTestPlan.id + "/case_tree/lazyload?case_id=0").then((response) => {
        let data = response.data.result
        this.initTestCaseTree(response.data.result);
        this.spinShow = false;
        // data.forEach(element => {
        //   this.defaultExpandIds.push(element.id)
        // });
        return resolve(data);
      });
    },
    loadChildNode(node, parent_id, resolve) {
      if (node.data.IsGroup == true) {
        this.$axios.get("/api/project/testplan/" + this.defTestPlan.id + "/case_tree/lazyload?case_id=" + parent_id).then((response) => {
          let data = response.data.result;
          if (this.checked) {
            // if(this.checkedId == parent_id){
            if (this.defaultExpandIds.includes(node.data.id)) {
              for (let i = 0; i < data.length; i++) {
                if (data[i].IsGroup == true) {
                  this.defaultExpandIds.push(data[i].id)
                  this.checkedId = data[i].id
                }
              }
            }
          }
          this.setCheckedTestCase(this.$refs.testPlanCaseTree.getCheckedNodes());
          return resolve(data);
        });
      } else {
        return resolve([]);
      }
    },
  },

  created: function () { },

  mounted: function () {
    if (this.projectID) {
      this.getProjectMembers();
    }

    // console.log("ProjectTestCaseTree.vue for created")
    if (this.defTestPlan.id > 0) {
      if (this.testPlanCaseTreeData.length == 0) {
        this.planId = this.$route.params.planId
        if (this.planId != undefined) {
          this.loadTestCaseTree(this.planId);
        }
      }
    }
  },
  destroyed() {
    this.setCheckedTestCase([]);
  },

  watch: {
    testPlanCaseTreeData() {
      if (this.testPlanCaseTreeData == null) {
        this.spinShow = true
      } else {
        this.spinShow = false
      }
    },

    caseFilters: function (value) {
      this.$refs.testPlanCaseTree.filter(value);
    },

    refreshPlanCase: function (value) {
      if (value) {
        this.setRefreshPlanCase(false);
        this.loadTestCaseTree(this.defTestPlan.id)
      }
    },

    projectID: function (value) {
      if (this.projectID) {
        this.getProjectMembers();
      }
    },

    currentCaseNode(data) {
      if (data) {
        this.$refs.testPlanCaseTree.setCurrentKey(data.id)
      }
    },

    defTestPlan: function () {
      this.loadTestCaseTree(this.defTestPlan.id)
    },

    updateExecuteCaseTree: {
      handler(new_value, lod_value) {
        if (new_value.refresh == true) {
          this.updateCaseTreeCaseResult()
        }
      },
      immediate: true,
      deep: true,
    },
  },

  components: {
    labelEditorInputExternalTrigger,
    labelEditorDropdwonItem,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.css-TestPlanCaseTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 52px;
  position: relative;
}

.TestPlanCaseTree-node {
  //flex: 1;
  display: flex;
  height: 24px;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  //padding-right: 8px;
  -webkit-box-align: center;
  flex: 1 1 auto;
  padding-left: 0px;
  min-width: 0px;
  //max-width:80%;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 8px;
}

.css-TestPlanCaseTree-SectionName-textEllipsis {
  min-width: 100px;
  max-width: 300px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 4px 0px 4px;
  overflow: hidden;
  color: #606266;
  //width: 50%;
  //width: 400px;
  margin: 0px 4px 0px 4px;
}

.css-142qn3c-TestPlanCaseTree-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px;
  cursor: pointer;
  padding: 4px;
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  outline-color: transparent;
  box-shadow: none;
}

.add-new-case:hover {
  color: #0066ff;
}

.testplan-SectionControls {
  //flex: 0 0 auto;
  //display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding-left: 6px;
  padding-right: 2px;
  width: 90px;
}
</style>
