<template>
  <div>
    <Modal :value="TestPlanEditDialogShow" title="编辑测试计划" :mask-closable="false" class-name="vertical-center-modal"
      :width="800" @on-cancel="cancel">
      <div :style="'height:' + containerHeight - 100 + 'px;overflow-y: scroll;overflow-x: hidden'">
        <i-form ref="createTestPlan" :model="formItem" :label-width="0" :rules="ruleCustom">
          <FormItem label="" prop="Title">
            <Input readonly disabled maxlength="50" v-model="formItem.Title" show-word-limit placeholder="计划标题" />
          </FormItem>
          <Row>
            <Col span="12" style="padding-right: 10px;">
            <FormItem label="" prop="Version">
              <Select transfer v-model="formItem.Version" :filterable="true" placeholder="计划所属版本">
                <Option v-for="version in projectVersions" :key="version.id" :value="version.id">{{ version.VersionLabel
                }}</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <Select transfer v-model="formItem.Owner" :filterable="true" placeholder="用例默认分配给">
              <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{ member.name
              }} </Option>
            </Select>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="" style="padding-right: 10px;" prop="fortestings_tmp">
              <Select filterable allow-create transfer multiple v-model="formItem.fortestings" placeholder="关联提测">
                <Option v-for="fortesting in projectFortestings" :key="fortesting.id" :value="fortesting.id">{{ '[' +
                  fortesting.id + ']' + fortesting.Topic }}</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem transfer label="" id="requireNumId" prop="requireNum_tmp">
              <Input type="number" v-model="formItem.requireNum" placeholder="需求个数" prefix="md-calculator" />
            </FormItem>
            </Col>
          </Row>
          <FormItem label="" prop="Desc">
            <Input v-model="formItem.Desc" maxlength="1000" :rows="6" show-word-limit type="textarea"
              placeholder="计划描述/提测链接" />
          </FormItem>
        </i-form>
      </div>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="ok('updateTestPlan')">完成
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from "vuex";
import { VueEditor } from "vue2-editor";
import { testplanValidateRules } from "../business-service/ProjectTestPlanCreateDialog.js";
import ProjectTestCaseSelectDialog from "./ProjectTestCaseSelectDialog.vue";
import { updateTestPlan } from "../business-service/ProjectTestCaseApiService";
import { loadProjectFortestings } from "../../business-service/ProjectApiService";

export default {
  name: "ProjectTestPlanEditDialog",
  props: ["projectID"],
  data() {
    return {
      projectFortestings: [{ id: 0, Topic: "手动输入" }],
      selectCasesCount: 0,
      formItem: {
        Title: "",
        Desc: "",
        Version: 0,
        Owner: 0,
        fortestings: [],
        requireNum: "",
      },
      ruleCustom: {
        ...testplanValidateRules,
      },
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["viewDialogShow", "projectVersion",]),
    ...mapState(["appBodyMainHeight"]),
    ...mapState('project', ['projectVersions', 'projectMembers', 'projectCaseCount']),
    ...mapState('testplan', ['defTestPlan', 'TestPlanEditDialogShow']),

    containerHeight: function () {
      if (this.appBodyMainHeight - 100 > 600) {
        return 600;
      } else {
        return this.appBodyMainHeight - 100;
      }
    },

    project: function () {
      return parseInt(this.projectID);
    },

    projectVersion: function () {
      return parseInt(this.versionID);
    },

    dialogShow: function () {
      return this.createReqType === 1;
    },

    fortestingDefLable: function () {
      let forTestingTopic = []
      for (let i = 0; i <= this.formItem.fortestings.length; i++) {
        for (let j = 0; j <= this.projectFortestings.length; j++) {
          if (this.formItem.fortestings[i] = this.projectFortestings[j]["id"]) {
            forTestingTopic.push(this.projectFortestings[j]["Topic"])
          }
        }
      }
      return forTestingTopic
    }
  },
  methods: {
    ...mapMutations("projectglobal", ["setCaseSelectDialogShow", "setTaskChange", "setCreateReqType",]),
    ...mapMutations("issue", ["setIssueChange"]),
    ...mapMutations('testplan', ['setTestPlanEditDialogShow']),

    ok(name) {
      updateTestPlan(this.formItem.id, this.formItem).then(
        (response) => {
          this.setTestPlanEditDialogShow(false);
          this.$emit("editPlan", response);
        },
        (response) => {
          this.setTestPlanEditDialogShow(false);
        }
      );
    },

    cancel() {
      this.setTestPlanEditDialogShow(false);
    },

    getProjectFortestings: function (projectID, versionID) {
      loadProjectFortestings(projectID, versionID).then(
        (response) => {
          this.projectFortestings = response.data.result.results;
        },
        (response) => { }
      );
    },

    searchPlan: function () { },

    caseFilterChange: function (value) {
      if (value === 1) {
        this.formItem.CaseCount = this.projectCaseCount;
      }
    },
  },

  created() {
    if (this.projectID) {
      this.getProjectFortestings(this.projectID, 0);
    }
  },
  mounted() { },
  watch: {
    projectID: function (value) {
      if (value) {
        this.getProjectFortestings(this.projectID, 0);
      }
    },

    defTestPlan: function (value) {
      this.formItem.id = this.defTestPlan.id;
      this.formItem.Title = this.defTestPlan.Title;
      this.formItem.Version = this.defTestPlan.Version;
      this.formItem.Desc = this.defTestPlan.Desc;
      this.formItem.Owner = this.defTestPlan.Owner;
      this.formItem.fortestings = this.defTestPlan.Fortestings;
      this.formItem.requireNum = this.defTestPlan.requireNum;
    },
  },
  components: {
    VueEditor,

    ProjectTestCaseSelectDialog,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }

  #requireNumId .ivu-input {
    height: 34px;
    padding: 7px 26px;
  }

  #requireNumId .ivu-input-prefix i,
  .ivu-input-suffix i {
    font-size: 16px;
    line-height: 32px;
    color: #5578aa;
  }
}
</style>
