<template>
  <div class="TestsWrapper">
    <div class="PlanDetailHeader">
      <div class="HeadingWrapper">
        <router-link class="NavBackButton"
          :to="'/project/' + projectID + '/test/test-plan/' + testPlan.id + '/detail'"></router-link>
        <div class="Divider"></div>
        <div class="IDTag ">#{{ testPlan.id }}</div>
        <div class="PlanTitle ">{{ testPlan.Title }}</div>
      </div>
      <div class="FilterWrapper">
        <div class="FliterSelectWrapper">
          <div class="Selection">全部状态</div>
        </div>
        <div class="Divider"></div>
        <div class="Root ">
          <div class="root">
            <div class="InputWrapper">
              <input type="text" class="InputDom-mediumSize-hasIconInput-withoutBorder " placeholder="搜索" value="" />
              <div class="InputIcon "></div>
            </div>
          </div>
          <div value="" class="ClearButton"></div>
        </div>
        <div class="ExtraFilterWrapper">
          <div class="Divider"></div>
          <span>只测我的</span>
          <span class="switchStyle-SwitchButton"></span>
        </div>
      </div>
    </div>
    <div class="PlanTestsContainer" style="position: relative;">
      <div
        :style="'height:' + containerHeight + 'px;' + 'position: relative; width: inherit; overflow: auto; will-change: transform; direction: ltr;'">
        <Card :bordered="false" dis-hover class="SectionTreeView" :padding="4">
          <project-test-plan-case-tree :projectID="projectID" :checkbox="true">
          </project-test-plan-case-tree>
        </Card>
      </div>
      <div class="resize-triggers">
        <div class="expand-trigger">
          <div style="width: 429px; height: 823px;"></div>
        </div>
        <div class="contract-trigger"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import ProjectTestPlanCaseTree from "./ProjectTestPlanCaseTree.vue";

export default {
  name: "ProjectTestPlanExecuteCaseList",
  props: ["projectID", "planId"],
  data() {
    return {
      testPlan: {
        "id": 0,
        "Title": ""
      }
    };
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(["appBodyMainHeight", "appBodyWidth"]),
    ...mapState('testplan', ['defTestPlan']),

    containerHeight: function () {
      return this.appBodyMainHeight - 70;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },
  },

  methods: {
    ...mapMutations(["setItemViewMode"]),
    ...mapActions('testplan', ['getDefTestPlan']),
  },

  created: function () {
    this.testPlan = this.defTestPlan
    // if(this.defTestPlan == null){
    //     this.planId = this.$route.params.planId
    //     this.getDefTestPlan(this.planId)
    //   }
  },

  mounted: function () { },
  watch: {
    defTestPlan: function () {
      this.testPlan = this.defTestPlan
    },
  },

  components: {
    ProjectTestPlanCaseTree,
  },
};
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.TestsWrapper {
  width: 100%;
  flex: 1 1 100px;
  display: flex;
  flex-direction: column;
}

.feie *,
.feie ::before,
.feie ::after {
  box-sizing: content-box;
}

.HeadingWrapper {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin: 18px 0px 14px;
}

.feie a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.feie a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.NavBackButton {
  display: block;
  width: 28px;
  height: 28px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 6px -1px,
    rgba(5, 35, 90, 0.1) 0px 5px 20px -2px;
  cursor: pointer;
  flex-shrink: 0;
  border-radius: 28px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUwLjIgKDU1MDQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9uYXYtYmFjazwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJUZXN0LVBsYW4tJmFtcDstUmVzdWx0cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9IkNvbXBvbmVudHMtLS1BZGQtUmVzbHV0IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMjM0My4wMDAwMDAsIC05NzcuMDAwMDAwKSI+CiAgICAgICAgICAgIDxnIGlkPSJuYXYtYmFjay1ob3ZlciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjMzNi4wMDAwMDAsIDk3MC4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJpY29ucy9uYXYtYmFjayIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNy4wMDAwMDAsIDcuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPGc+CiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBpZD0iU2hhcGUiIHN0cm9rZT0iIzEwNzFEMyIgc3Ryb2tlLXdpZHRoPSIyLjI4IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHBvaW50cz0iNy44NzUgNC41IDMuMzc1IDkgNy44NzUgMTMuNSI+PC9wb2x5bGluZT4KICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgaWQ9IlJlY3RhbmdsZS0xNSIgZmlsbD0iIzEwNzFEMyIgeD0iNC41IiB5PSI3Ljg3NSIgd2lkdGg9IjEwLjEyNSIgaGVpZ2h0PSIyLjI1IiByeD0iMS4xMjUiPjwvcmVjdD4KICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==) center center no-repeat rgb(255, 255, 255);
}

.Divider {
  height: 16px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.07);
  flex: 0 0 auto;
  margin: 0px 8px;
}

.IDTag {
  height: 20px;
  margin-right: 4px;
  background-color: rgb(36, 123, 233);
  color: rgb(255, 255, 255);
  font-size: 12px;
  line-height: 20px;
  padding: 0px 4px;
  border-radius: 2px;
}

.PlanTitle {
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  overflow: hidden;
}

.folderIcon-SectionIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.FilterWrapper {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 40px;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(9, 10, 23, 0.06) 0px 2px 4px -1px,
    rgba(0, 0, 0, 0.08) 0px 6px 24px -3px;
  padding: 0px 4px;
  border-radius: 3px;
}

.FliterSelectWrapper {
  flex: 0 0 90px;
}

.Selection {
  box-sizing: border-box;
  position: relative;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 8px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Divider {
  height: 16px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.07);
  flex: 0 0 auto;
  margin: 0px 12px 0px 4px;
}

.Root {
  -webkit-box-flex: 0;
  flex-grow: 0;
  display: flex;
  width: 100%;
  position: relative;
}

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.InputDom-mediumSize-hasIconInput-withoutBorder {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  box-shadow: none !important;
  outline: none;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  padding: 6px 12px 6px 24px;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
}

.InputIcon {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  display: block;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjEgKDUxMTQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5zZWFyY2g8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iVGVzdC1DYXNlLS0tQ3JlYXRlLSZhbXA7LUVkaXQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIG9wYWNpdHk9IjAuMzAwMDAwMDEyIj4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZciIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzkuMDAwMDAwLCAtMjAuMDAwMDAwKSIgZmlsbD0iIzM1MzUzNSIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPGcgaWQ9ImxlZnQtc2lkZSI+CiAgICAgICAgICAgICAgICA8ZyBpZD0ibGVmdHNpZGUtaGVhZCI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9Imljb25zL3NlYXJjaCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjM5LjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMTIuMTEyNSwxMC44MjYwODcgQzEyLjksOS43ODI2MDg3IDEzLjMzNzUsOC41NjUyMTczOSAxMy4zMzc1LDcuMTczOTEzMDQgQzEzLjMzNzUsMy43ODI2MDg3IDEwLjYyNSwxIDcuMjEyNSwxIEMzLjgsMSAxLDMuNzgyNjA4NyAxLDcuMTczOTEzMDQgQzEsMTAuNTY1MjE3NCAzLjgsMTMuMzQ3ODI2MSA3LjIxMjUsMTMuMzQ3ODI2MSBDOC42MTI1LDEzLjM0NzgyNjEgOS45MjUsMTIuOTEzMDQzNSAxMC44ODc1LDEyLjEzMDQzNDggTDEzLjUxMjUsMTQuNzM5MTMwNCBDMTMuNjg3NSwxNC45MTMwNDM1IDEzLjk1LDE1IDE0LjEyNSwxNSBDMTQuMywxNSAxNC41NjI1LDE0LjkxMzA0MzUgMTQuNzM3NSwxNC43MzkxMzA0IEMxNS4wODc1LDE0LjM5MTMwNDMgMTUuMDg3NSwxMy44Njk1NjUyIDE0LjczNzUsMTMuNTIxNzM5MSBMMTIuMTEyNSwxMC44MjYwODcgWiBNNy4yMTI1LDExLjUyMTczOTEgQzQuNzYyNSwxMS41MjE3MzkxIDIuNzUsOS42MDg2OTU2NSAyLjc1LDcuMTczOTEzMDQgQzIuNzUsNC43MzkxMzA0MyA0Ljc2MjUsMi43MzkxMzA0MyA3LjIxMjUsMi43MzkxMzA0MyBDOS42NjI1LDIuNzM5MTMwNDMgMTEuNjc1LDQuNzM5MTMwNDMgMTEuNjc1LDcuMTczOTEzMDQgQzExLjY3NSw5LjYwODY5NTY1IDkuNjYyNSwxMS41MjE3MzkxIDcuMjEyNSwxMS41MjE3MzkxIFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
  background-size: 16px;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background-repeat: no-repeat;
  background-position: center center;
}

.ClearButton {
  -webkit-box-flex: 1;
  flex-grow: 1;
  flex-shrink: 0;
  width: 16px;
  display: none;
  cursor: pointer;
  opacity: 0.5;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjMgKDUxMTY3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9pbnB1dC1jbGVhcjwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJUZXN0LUNhc2UtLS1DcmVhdGUtJmFtcDstRWRpdCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41Ij4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZct5pCc57SiIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMjAyLjAwMDAwMCwgLTIwLjAwMDAwMCkiIGZpbGw9IiNBNUIxQzIiIGZpbGwtcnVsZT0ibm9uemVybyI+CiAgICAgICAgICAgIDxnIGlkPSJsZWZ0LXNpZGUiPgogICAgICAgICAgICAgICAgPGcgaWQ9ImxlZnRzaWRlLWhlYWQiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJpY29ucy9pbnB1dC1jbGVhciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjAyLjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxIEM0LjE1LDEgMSw0LjE1IDEsOCBDMSwxMS44NSA0LjE1LDE1IDgsMTUgQzExLjg1LDE1IDE1LDExLjg1IDE1LDggQzE1LDQuMTUgMTEuODUsMSA4LDEgWiBNMTEuMDYyNSw5LjgzNzUgTDkuODM3NSwxMS4wNjI1IEw4LDkuMjI1IEw2LjE2MjUsMTEuMDYyNSBMNC45Mzc1LDkuODM3NSBMNi43NzUsOCBMNC45Mzc1LDYuMTYyNSBMNi4xNjI1LDQuOTM3NSBMOCw2Ljc3NSBMOS44Mzc1LDQuOTM3NSBMMTEuMDYyNSw2LjE2MjUgTDkuMjI1LDggTDExLjA2MjUsOS44Mzc1IFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+) center center / 16px 16px no-repeat;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.ExtraFilterWrapper {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex: 0 0 auto;
}

.Divider {
  height: 16px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.07);
  flex: 0 0 auto;
  margin: 0px 8px;
}

.ExtraFilterWrapper>span {
  margin-right: 8px;
}

.switchStyle-SwitchButton {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.PlanDetailHeader {
  padding: 0px 16px;
}

.PlanDetailHeader {
  position: relative;
  z-index: 1;
  flex: 0 0 100px;
  padding: 0px 12px;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
}

.PlanTestsContainer {
  scroll-behavior: smooth;
  flex: 1 1 auto;
  margin: 8px 12px;
}

.resize-triggers,
.resize-triggers>div,
.contract-trigger:before {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  z-index: -1;
}

.resize-triggers>div {
  background: #eee;
  overflow: auto;
}

.resize-triggers,
.resize-triggers>div,
.contract-trigger:before {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  z-index: -1;
}

.TestCaseHeaderWrapper {
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  width: 100%;
  background-color: rgb(255, 255, 255);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.DetailHeader {
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1060px;
  height: 100px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 12px;
  flex: 1 1 auto;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TestTitle-textEllipsis {
  margin-bottom: 12px;
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
}

.TestTitleBaseInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
}

.TestMainInfo {
  overflow-y: auto;
  width: 100%;
  max-width: 1060px;
  padding-top: 108px;
  flex: 1 1 auto;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Wrapper {
  position: relative;
  padding: 20px;
}

.Precondition {
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  margin-top: 41px;
  margin-bottom: 12px;
  position: relative;
  font-weight: bold;
}

.CasePreconds {
  width: 100%;
  color: rgb(201, 207, 215);
  font-size: 13px;
  line-height: 16px;
  margin-bottom: 40px;
  padding: 10px 0px;
}

.AttachmentWrapper {
  margin-top: 40px;
}

.SectionContent-descriptionContent {
  font-size: 14px;
  line-height: 28px;
  margin-bottom: 40px;
}

.SectionTitle {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
}

.SectionContent {
  font-size: 14px;
  line-height: 28px;
}

.OperationPanelWrapper {
  width: 100%;
  max-width: 1060px;
  flex: 0 0 264px;
}

.OperationPanel {
  display: flex;
  flex-direction: column;
  position: absolute;
  z-index: 1030;
  bottom: 0px;
  width: 100%;
  max-width: 1060px;
  min-height: 264px;
  max-height: calc(100% - 124px);
  height: 264px;
  box-shadow: rgba(0, 0, 0, 0.02) 0px -1px 6px 0px,
    rgba(10, 36, 70, 0.05) 0px -4px 24px 0px;
  border-radius: 3px 3px 0px 0px;
  background: rgb(255, 255, 255);
}

.Wrapper {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 16px 24px 0px;
}

.RecordWrapper {
  position: relative;
  padding-left: 30px;
  padding-bottom: 24px;
}

.TypeIcon {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 19px;
  height: 19px;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjE4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHdpZHRoPSIxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxjaXJjbGUgY3g9IjkiIGN5PSI5IiBmaWxsPSIjMzA4NmVjIiByPSI5Ii8+PHBhdGggZD0ibTUuNSA2aDdjLjI3NiAwIC41LjIyNC41LjV2NWMwIC4yNzYtLjIyNC41LS41LjVoLTIuNWwtMyAydi0yaC0xLjVjLS4yNzYgMC0uNS0uMjI0LS41LS41di01YzAtLjI3Ni4yMjQtLjUuNS0uNXoiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvZz48L3N2Zz4=) center center no-repeat;
}

.RecordTitle {
  color: rgb(32, 45, 64);
  font-size: 13px;

  line-height: 20px;
}

.RecordFooter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
}

.Divider {
  width: 1px;
  height: 12px;
  background-color: rgba(0, 0, 0, 0.07);
  margin: 0px 8px;
}

.ExpandArea {
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  width: 108px;
  height: 20px;
  cursor: ns-resize;
}

.Wrapper-wrapperClassName {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  flex: 0 0 51px;
  padding: 0px 24px;
}

ol,
ul {
  list-style: none;
}

.NoRecords {
  display: flex;
  flex-direction: column;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  background-image: linear-gradient(45deg,
      rgb(250, 250, 250) 0%,
      rgb(245, 246, 247) 100%);
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 1 1 auto;
  padding: 0px 24px;
}

.PanelPositionWrapper {
  z-index: 12;
  position: relative;
  flex: 0 0 56px;
}

.PanelContentWrapper {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.08) 0px -2px 12px 0px;
}

.PanelContentBody {
  display: flex;
  -webkit-box-align: stretch;
  align-items: stretch;
  height: 0px;
  opacity: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  transition: height 0.3s ease 0s, opacity 0.4s ease 0.2s;
}

.PanelContentFooter {
  box-sizing: border-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 56px;
  padding: 10px 24px;
}

.OperationTypeContainer {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.OperationTab {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-right: 32px;
  height: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.OperationTab {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-right: 32px;
  height: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.OperationActionContainer {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.OperatorButton {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(255, 255, 255);
  font-size: 14px;
  line-height: 16px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
  padding: 10px 32px 10px 10px;
  background: rgb(33, 202, 100);
  border-width: 1px;
  border-style: solid;
  border-color: rgb(31, 194, 96);
  border-image: initial;
  border-radius: 3px;
}

.TypeIcon {
  display: inline-block;
  margin-right: 6px;
  width: 16px;
  height: 16px;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHdpZHRoPSIxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtOCAxNmMtNC40MTgyNzggMC04LTMuNTgxNzIyLTgtOHMzLjU4MTcyMi04IDgtOCA4IDMuNTgxNzIyIDggOC0zLjU4MTcyMiA4LTggOHptNC44LTkuNi0xLjQtMS40LTQgNC0yLTItMS40IDEuNCAzLjQgMy40eiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+) center center no-repeat;
}

.MoreButton {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 32px;
  height: 36px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5ICg1MTAwMikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Q2hldnJvbi9tb3JlLWRvd248L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iQ29tcG9uZW50cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41IiBzdHJva2UtbGluZWNhcD0icm91bmQiPgogICAgICAgIDxnIGlkPSJGb3JtLUVsZW1lbnRzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNDcyLjAwMDAwMCwgLTY0NC4wMDAwMDApIiBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMiI+CiAgICAgICAgICAgIDxnIGlkPSJCdXR0b24iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDMzNi4wMDAwMDAsIDYzNC4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJDaGV2cm9uL2NoZXZyb24tZG93bi13aGl0ZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTM2LjAwMDAwMCwgMTAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9IkNoZXZyb24vbW9yZS13aGl0ZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBpZD0iY2hldnJvbiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOC4wMDAwMDAsIDQuNzUwMDAwKSBzY2FsZSgxLCAtMSkgdHJhbnNsYXRlKC04LjAwMDAwMCwgLTQuNzUwMDAwKSAiIHBvaW50cz0iMTEgMy41IDggNiA1IDMuNSI+PC9wb2x5bGluZT4KICAgICAgICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIGlkPSJjaGV2cm9uIiBwb2ludHM9IjExIDEwLjUgOCAxMyA1IDEwLjUiPjwvcG9seWxpbmU+CiAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) center center / 16px no-repeat;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TestSwtichOpeartor {
  position: relative;
  margin-left: 8px;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  width: 73px;
  height: 36px;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(208, 212, 217);
  border-image: initial;
  border-radius: 3px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TabItem {
  display: flex;
  font-size: 15px;
  line-height: 16px;
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
  color: rgb(32, 45, 64);
  padding: 16px 0px;
  border-bottom: 2px solid rgb(0, 102, 255);
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

wrapperClassName li+li {
  margin-left: 40px;
}

.DetailHeader {
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1060px;
  height: 100px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 16px;
  padding-bottom: 12px;
  flex: 1 1 auto;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.textEllipsis {
  margin-bottom: 12px;
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
}

.TestTitleBaseInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
}

.IDTag {
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: rgb(255, 255, 255);
  background-color: #0066ff;
  padding: 0px 5px;
  border-radius: 2px;
}

.BaseInfoItem {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 16px;
  padding-left: 17px;
  height: 24px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 0 0 auto;
}

.PathInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 16px;
  padding-left: 17px;
  height: 24px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 1 1 auto;
}

.Wrapper {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex: 1 1 auto;
}

.SectionIcon {
  display: block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.Path {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 8px;
  height: 16px;
  font-size: 13px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  flex: 1 1 auto;
}

.PathContent {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.Divider {
  color: rgb(201, 207, 215);
  padding: 0px 4px;
}

.Selection {
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 20px 0px 7px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Icon {
  margin-right: 8px;
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 24px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUwLjIgKDU1MDQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5hdmF0YXItZGVmYXVsdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJTeW1ib2xzIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iYXZhdGFycy9hdmF0YXItZGVmYXVsdCI+CiAgICAgICAgICAgIDxnIGlkPSJhdmF0YXItZGVmYXVsdCI+CiAgICAgICAgICAgICAgICA8Y2lyY2xlIGlkPSJPdmFsLTQiIGZpbGw9IiNFMUUzRTYiIGN4PSIxMiIgY3k9IjEyIiByPSIxMiI+PC9jaXJjbGU+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxMCBDOCw3Ljc5MSA5Ljg0NCw2IDEyLDYgQzE0LjE1Niw2IDE2LDcuNzkxIDE2LDEwIEwxNiwxMSBDMTYsMTMuMjA5IDE0LjE1NiwxNSAxMiwxNSBDOS44NDQsMTUgOCwxMy4yMDkgOCwxMSBMOCwxMCBaIiBpZD0iUGF0aCIgZmlsbD0iI0ZGRkZGRiI+PC9wYXRoPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTEyLDIyIEM5LjU2NywyMiA3LjMzNSwyMS4xMjQgNS41OTksMTkuNjc0IEM2LjQzOCwxOC4wOTEgOC4wODMsMTcgMTAsMTcgTDE0LDE3IEMxNS45MTcsMTcgMTcuNTYyLDE4LjA5MSAxOC40MDEsMTkuNjc0IEMxNi42NjUsMjEuMTI0IDE0LjQzMywyMiAxMiwyMiBaIiBpZD0iUGF0aCIgZmlsbD0iI0ZGRkZGRiI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) center center / 24px no-repeat rgb(240, 241, 242);
}

.PriorityWrapper {
  flex-shrink: 0;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 13px;
  line-height: 16px;
  color: rgb(32, 45, 64);
}

.PriorityIcon {
  flex-shrink: 0;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  margin-right: 8px;
  background: rgba(50, 116, 250, 0.1);
  border-width: 2px;
  border-style: solid;
  border-color: rgb(50, 116, 250);
  border-image: initial;
  border-radius: 50%;
}
</style>
