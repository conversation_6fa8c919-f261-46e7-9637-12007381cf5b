<template>
  <div>
    <div class="SectionPanel">
      <div class="SectionTitleWrapper">
        <div class="SectionTitle">测试计划
          <div class="OperationPanelWrapper" @click="openCreateDialog">
            <Avatar class="add-icon" icon="md-add" size="26" />
          </div>
        </div>
        <div class="SearchInputWrapper">
          <div class="Root">
            <div class="InputWrapper">
              <input type="text" @keydown="searchPlan" class="InputDom-hasIconInput-withoutBorder"
                placeholder="搜索测试计划(未实现)" v-model="searchWord">
              <div class="InputIcon"></div>
            </div>
          </div>
        </div>
        <div class="MenuWrapper">
          <div class="SelectWrapper">
            <div class="SelectContainer">
              <div value="全部测试计划" class="Selection">
                <span style="display:inline-flex;align-items: center">
                  <label-editor-dropdwon-item @updateValue="filterTestPlan" :showIcon="false" :itemID="0" :value="0"
                    :itemList="planStatusDropdwonData" displayColor="" displayText="全部测试计划">
                  </label-editor-dropdwon-item>
                </span>
              </div>
            </div>
          </div>
          <div class="ShowArchivedPlan">
            <span class="label">显示已归档计划</span>
            <span class="switchStyle-SwitchButton">
              <i-switch v-model="showFiledPlan" @on-change="showFiledTestPlan" size="default" true-color="#0066ff">
                <Icon type="md-checkmark" slot="open"></Icon>
                <Icon type="md-close" slot="close"></Icon>
              </i-switch>
            </span>
          </div>
        </div>
      </div>
      <div class="PlansSection" :style="{ height: planContainerHeight + 'px' }">
        <Spin size="large" fix v-if="spinShow"></Spin>
        <div v-for="version, index in versionsPlans" :key="version.id">
          <div v-if="version.view_data.test_plans" class="Root" style="display: block!important;">
            <div class="IterationTitleWrapper">
              <div style="display: inline-flex;align-items: center;justify-content:space-between; width: 100%;">
                <router-link class="MileStoneTitleLink"
                  :to="'/project/' + version.VProjectID + '/test/test-plan/version/' + version.id">
                  <div class="IterationTimeLine">
                    <span class="remaining-time remaining-time-overdue RemainingTime">{{
                      version.view_data.version_status }}</span>
                    <span class="Line"></span>
                    {{ version.VStartDate }} ~ {{ version.VReleaseDate }}
                  </div>
                  <div class="iteration-title IterationTitle" title="test">
                    <div class="normalSize">迭代 {{ index + 1 }}</div>
                    <div class="textEllipsis">{{ version.VVersion }}</div>
                  </div>
                </router-link>
                <a v-if="version.view_data.version_status == '已过期'" @click="loadVersionPlan(version.id)"
                  style="padding-right: 8px;">
                  <Tooltip transfer theme="light" content="加载已过期测试计划">
                    <Icon type="ios-code-download" size='28' />
                  </Tooltip>
                </a>
              </div>
              <div class="PlanList">
                <List>
                  <ListItem v-for="testplan in version.view_data.test_plans"
                    v-show="testplan.Status !== 4 || showFiledPlan" :key="testplan.id" style="padding: 0px;"
                    @click.native="handlerDefPlan(testplan)">
                    <ListItemMeta :class="{ archiveTestPlan: testplan.Status == 4 }">
                      <template slot="title">
                        <router-link class="SectionWrapper"
                          :to="'/project/' + testplan.Project + '/test/test-plan/' + testplan.id + '/detail'">
                          <div class="PlanTitle">
                            <div class="Tag">#{{ testplan.id }}</div>
                            <div class="Title-textEllipsis">{{ testplan.Title }}</div>
                          </div>
                          <div class="PlanDetail">
                            <span>{{ testPlanStatus[testplan.Status] }}</span> &nbsp;
                            <span class="Line"></span>
                            <span class="Percentage">通过 {{ testplan.view_data.pass_rate }}%</span> &nbsp;
                            <span class="Line"></span>
                            <span class="TimeContinue">执行 {{ testplan.view_data.plan_duration }} 天</span> &nbsp;
                            <span class="Line"></span>
                            <span>需求数 {{ testplan.requireNum }}</span>
                          </div>
                        </router-link>
                      </template>
                    </ListItemMeta>
                  </ListItem>
                </List>
              </div>
            </div>
            <!--<span class="add-icon add-icon-tiny yellow addPlanButton"></span>-->
          </div>
        </div>
      </div>
    </div>
    <project-test-plan-create-dialog :projectID="projectID" @createPlan="createPlanSuccess">
    </project-test-plan-create-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";
//import ProjectTestPlanCreateDialog from "./ProjectTestPlanCreateDialog.vue";
import { getProjectTaskStatus } from '../../business-service/ProjectApiService'
import { transferTaskStatusDropdownItemData } from '../business-service/ProjectTestCaseUtilService'

const ProjectTestPlanCreateDialog = (resolve) => {
  import('./ProjectTestPlanCreateDialog.vue').then((module) => {
    resolve(module)
  })
}

export default {
  name: "projectTestPlanPanel",
  props: ["projectID", "version"],
  data() {
    return {
      showFiledPlan: false,
      searchWord: "",
      planStatusDropdwonData: [],
      testPlanStatus: {
        1: "新建",
        2: "测试中",
        3: "已完成",
        4: "已归档",
        5: "已暂停",
      },
      bgcolor: "#FFFF00",
      spinShow: true,
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(['appBodyHeight', "appBodyMainHeight", "appBodyWidth"]),
    ...mapState('testplan', ['versionsPlans', 'refreshVersionPlans']),

    containerHeight: function () {
      return this.appBodyMainHeight - 10;
    },

    planContainerHeight: function () {
      return this.appBodyHeight - 144;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },

    versionId: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      }
      if (this.$route.params.versionId) {
        return this.$route.params.versionId;
      }
      return 0;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },

  },

  methods: {
    ...mapMutations("projectglobal", ["setCreateDialogShow", "setViewDialogShow",]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", ["setRefreshTestPlans",]),
    ...mapMutations('testplan', ['setVersionPlans']),
    ...mapActions('testplan', ['getVersionsPlans', 'getPlanVersions', 'getVersionTestPlans']),

    changeViewMode: function (value) {
      //console.log(value);
    },

    handleNodeClick(data) {
      //console.log(data);
    },

    searchPlan: function () {
      //console.log(this.searchWord);
    },

    filterTestPlan: function () { },

    showFiledTestPlan: function (value) {
      if (value) {
        this.showFiledPlan = true;
      } else {
        this.showFiledPlan = false;
      }
    },

    openCreateDialog: function () {
      this.setCreateDialogShow(true);
    },

    createPlanSuccess: function (response) {
      if (response) {
        let versionId = response.data.result.Version;
        for (let i = 0; i < this.versionsPlans.length; i++) {
          if (this.versionsPlans[i].id === versionId) {
            this.versionsPlans[i].view_data.test_plans.push(response.data.result);
          }
        }
      }
    },

    handlerDefPlan(testplan) {
      this.$store.dispatch('testplan/getDefTestPlan', testplan.id)
    },

    loadVersionPlan(versionId) {
      //console.log(versionId)
      this.getVersionTestPlans({ 'projectID': this.projectID, 'versionId': versionId })
    },

    loadPlanStatus: function () {
      getProjectTaskStatus(6).then((response) => {
        let statusList = transferTaskStatusDropdownItemData(response.data.result);
        let tempTag = { label: "全部计划", value: 0, color: "" };
        this.planStatusDropdwonData.push(tempTag);
        this.planStatusDropdwonData.push(...statusList);
      },
      );
    },
  },

  created: function () {
    //console.log('ProjectTestPlanPanel.vue for created')
    // if (this.versionsPlans[0].VprojectID != this.projectID){
    //   this.setVersionPlans(null);
    // }
    this.projectID = parseInt(this.$route.params.projectID)
    this.getVersionsPlans(this.projectID);
  },

  mounted: function () { },

  watch: {
    versionsPlans: function () {
      if (this.versionsPlans == null) {
        this.spinShow = true;
      } else {
        this.spinShow = false;
      }
    },

    versionId: function (value) { },

    refreshVersionPlans: function () {
      this.getVersionsPlans(this.projectID);
    },
  },

  components: {
    labelEditorDropdwonItem,
    ProjectTestPlanCreateDialog,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.SectionPanel {
  width: 360px;
  display: flex;
  flex-direction: column;
  flex: 0 0 360px;
  background: rgb(246, 247, 248);
  border-right: 1px solid rgba(0, 0, 0, 0.07);
  overflow: hidden;
}

.SectionTitleWrapper {
  flex-shrink: 0;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 1px 4px -1px, rgba(0, 0, 0, 0.1) 0px 2px 8px -3px;
  z-index: 1;
  padding: 0px 20px;
  background: rgb(255, 255, 255);
}

.SectionTitle {
  padding: 0px 0px;
  height: 72px;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
  color: rgb(32, 45, 64);
}

.OperationPanelWrapper {
  position: relative;
}

.SectionTitle .add-icon {
  margin-right: -8px;
}

.add-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  box-shadow: rgba(19, 111, 220, 0.1) 0px 2px 6px 0px;
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  background: rgb(0, 102, 255);
}

.SearchInputWrapper {
  margin-top: -16px;
  height: 48px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex: 0 0 48px;
}

.Root {
  -webkit-box-flex: 0;
  flex-grow: 0;
  display: flex;
  width: 100%;
  position: relative;
}

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.ClearButton {
  -webkit-box-flex: 1;
  flex-grow: 1;
  flex-shrink: 0;
  width: 16px;
  display: none;
  cursor: pointer;
  opacity: 0.5;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjMgKDUxMTY3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9pbnB1dC1jbGVhcjwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJUZXN0LUNhc2UtLS1DcmVhdGUtJmFtcDstRWRpdCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41Ij4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZct5pCc57SiIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMjAyLjAwMDAwMCwgLTIwLjAwMDAwMCkiIGZpbGw9IiNBNUIxQzIiIGZpbGwtcnVsZT0ibm9uemVybyI+CiAgICAgICAgICAgIDxnIGlkPSJsZWZ0LXNpZGUiPgogICAgICAgICAgICAgICAgPGcgaWQ9ImxlZnRzaWRlLWhlYWQiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJpY29ucy9pbnB1dC1jbGVhciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjAyLjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxIEM0LjE1LDEgMSw0LjE1IDEsOCBDMSwxMS44NSA0LjE1LDE1IDgsMTUgQzExLjg1LDE1IDE1LDExLjg1IDE1LDggQzE1LDQuMTUgMTEuODUsMSA4LDEgWiBNMTEuMDYyNSw5LjgzNzUgTDkuODM3NSwxMS4wNjI1IEw4LDkuMjI1IEw2LjE2MjUsMTEuMDYyNSBMNC45Mzc1LDkuODM3NSBMNi43NzUsOCBMNC45Mzc1LDYuMTYyNSBMNi4xNjI1LDQuOTM3NSBMOCw2Ljc3NSBMOS44Mzc1LDQuOTM3NSBMMTEuMDYyNSw2LjE2MjUgTDkuMjI1LDggTDExLjA2MjUsOS44Mzc1IFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+) center center / 16px 16px no-repeat;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.InputDom-hasIconInput-withoutBorder {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  height: 36px;
  line-height: 24px;
  font-size: 14px;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-shadow: none !important;
  outline: none;
  padding: 6px 12px 6px 24px;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
}

.InputIcon {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  display: block;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjEgKDUxMTQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5zZWFyY2g8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iVGVzdC1DYXNlLS0tQ3JlYXRlLSZhbXA7LUVkaXQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIG9wYWNpdHk9IjAuMzAwMDAwMDEyIj4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZciIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzkuMDAwMDAwLCAtMjAuMDAwMDAwKSIgZmlsbD0iIzM1MzUzNSIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPGcgaWQ9ImxlZnQtc2lkZSI+CiAgICAgICAgICAgICAgICA8ZyBpZD0ibGVmdHNpZGUtaGVhZCI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9Imljb25zL3NlYXJjaCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjM5LjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMTIuMTEyNSwxMC44MjYwODcgQzEyLjksOS43ODI2MDg3IDEzLjMzNzUsOC41NjUyMTczOSAxMy4zMzc1LDcuMTczOTEzMDQgQzEzLjMzNzUsMy43ODI2MDg3IDEwLjYyNSwxIDcuMjEyNSwxIEMzLjgsMSAxLDMuNzgyNjA4NyAxLDcuMTczOTEzMDQgQzEsMTAuNTY1MjE3NCAzLjgsMTMuMzQ3ODI2MSA3LjIxMjUsMTMuMzQ3ODI2MSBDOC42MTI1LDEzLjM0NzgyNjEgOS45MjUsMTIuOTEzMDQzNSAxMC44ODc1LDEyLjEzMDQzNDggTDEzLjUxMjUsMTQuNzM5MTMwNCBDMTMuNjg3NSwxNC45MTMwNDM1IDEzLjk1LDE1IDE0LjEyNSwxNSBDMTQuMywxNSAxNC41NjI1LDE0LjkxMzA0MzUgMTQuNzM3NSwxNC43MzkxMzA0IEMxNS4wODc1LDE0LjM5MTMwNDMgMTUuMDg3NSwxMy44Njk1NjUyIDE0LjczNzUsMTMuNTIxNzM5MSBMMTIuMTEyNSwxMC44MjYwODcgWiBNNy4yMTI1LDExLjUyMTczOTEgQzQuNzYyNSwxMS41MjE3MzkxIDIuNzUsOS42MDg2OTU2NSAyLjc1LDcuMTczOTEzMDQgQzIuNzUsNC43MzkxMzA0MyA0Ljc2MjUsMi43MzkxMzA0MyA3LjIxMjUsMi43MzkxMzA0MyBDOS42NjI1LDIuNzM5MTMwNDMgMTEuNjc1LDQuNzM5MTMwNDMgMTEuNjc1LDcuMTczOTEzMDQgQzExLjY3NSw5LjYwODY5NTY1IDkuNjYyNSwxMS41MjE3MzkxIDcuMjEyNSwxMS41MjE3MzkxIFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
  background-size: 16px;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background-repeat: no-repeat;
  background-position: center center;
}

.MenuWrapper {
  height: 40px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
  position: relative;
  background: rgb(255, 255, 255);
}

.SelectWrapper {
  margin-left: -16px;
}

.SelectContainer {
  position: relative;
  flex-shrink: 0;
}

.Selection {
  box-sizing: border-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  user-select: none;
  padding: 6px 12px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.SelectionDetail-textEllipsis {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.ShowArchivedPlan {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(145, 153, 163);
  //margin-right: -8px;
}

.ShowArchivedPlan .label {
  margin-right: 8px;
}

.switchStyle-SwitchButton {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 32px;
  height: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.PlansSection {
  -webkit-box-flex: 1;
  flex-grow: 1;
  overflow-y: auto;
  position: relative;
}

.Root {
  margin-top: 8px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 6px -3px;
  width: 100%;
  background: rgb(255, 255, 255);
}

.IterationTitleWrapper {
  position: relative;
  padding: 4px 4px 0px 4px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.feie a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.MileStoneTitleLink {
  display: inline-block;
  background-size: 50px;
  position: relative;
  color: rgb(32, 45, 64) !important;
  padding: 10px 16px;
  border-radius: 3px;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.IterationTimeLine {
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
}

.IterationTitle {
  width: 100%;
  font-size: 14px;
  line-height: 16px;
  margin-top: 8px;
  display: flex;
}

.RemainingTime {
  padding-right: 8px;
  margin-right: 8px;
  position: relative;
  font-weight: bold;
  color: rgb(250, 91, 69);
}

.IterationTitle {
  width: 100%;
  font-size: 14px;
  line-height: 16px;
  margin-top: 8px;
  display: flex;
}

.normalSize {
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  min-width: 0px;
  font-size: 12px;
  color: rgb(255, 255, 255);
  margin-right: 8px;
  height: 16px;
  padding-right: 2px;
  flex: 0 0 auto;
  border-radius: 2px;
  background: rgb(252, 149, 13);
}

.Title-textEllipsis {
  max-width: 240px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 0 0px;
  overflow: hidden;
}

.add-icon.yellow {
  box-shadow: rgba(245, 166, 35, 0.1) 0px 2px 4px 0px;
  background: rgb(252, 149, 13);
}

.add-icon.add-icon-tiny {
  width: 24px;
  height: 24px;
}

.addPlanButton {
  position: absolute;
  right: 12px;
  bottom: 12px;
  display: none;
  transition: display 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.PlanList {
  overflow: hidden;
  padding: 2px 0px 2px 2px;
}

.SectionWrapper {
  display: block;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
  margin-bottom: 2px;
  position: relative;
  color: rgb(32, 45, 64) !important;
  border-radius: 3px;
  padding: 12px 20px;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.SectionWrapper.active {
  background: rgba(16, 113, 211, 0.08);
}

.SectionWrapper:hover {
  background: rgba(16, 113, 211, 0.08);
}

.PlanTitle {
  display: flex;
  width: 100%;
  font-size: 13px;
  line-height: 16px;
  margin-bottom: 8px;
}

.PlanDetail {
  font-size: 12px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.Tag {
  display: inline-flex;
  min-width: 0px;
  height: 16px;
  line-height: 16px;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(255, 255, 255);
  font-size: 12px;
  margin-right: 8px;
  flex: 0 0 auto;
  padding: 0px 3px;
  border-radius: 2px;
  background: rgb(0, 102, 255);
  font-size: 12px;
}

.Percentage {
  //color: rgb(32, 45, 64);
  //font-weight: bold;
}

.Line {
  width: 1px;
  height: 10px;
  display: inline-block;
  margin: 0px 9px;
  background: rgba(0, 0, 0, 0.07);
}

.TimeContinue {
  display: block;
}

.addPlanButton {
  position: absolute;
  right: 12px;
  bottom: 12px;
  display: none;
  -webkit-transition: display 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  transition: display 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.add-icon {
  display: inline-block;
  width: 36px;
  height: 36px;
  -webkit-box-shadow: rgba(19, 111, 220, 0.1) 0px 2px 6px 0px;
  box-shadow: rgba(19, 111, 220, 0.1) 0px 2px 6px 0px;
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  background: #0066ff;
}

.MileStoneTitleLink.active {
  background-color: rgba(255, 170, 33, 0.2);
}

.MileStoneTitleLink:hover {
  background-color: rgba(255, 170, 33, 0.2);
}

.archiveTestPlan {
  background: #e1e1e1;
}
</style>
