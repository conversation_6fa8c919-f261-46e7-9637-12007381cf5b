<template>
  <div class="TestDetailInfo">
    <div :style="{ height: containerHeight + 'px' }" v-if="testPlanCase != undefined">
      <div class="TestCaseHeaderWrapper">
        <div class="DetailHeader">
          <div title="测试" class="TestTitle-textEllipsis">#{{ testPlanCase.test_case.id }} </div>
          <div class="TestTitleBaseInfo">
            <div class="IDTag">#{{ testPlanCase.id }}</div>
            <i class="el-icon-folder" style="padding-left: 10px;" v-if="testPlanCase.IsGroup"></i>
            <div style="padding-left: 10px;">{{ testPlanCase.test_case.Title }}</div>
            <div class="BaseInfoItem"><span style="margin-left:10px;margin-right:10px">等级</span>
              <div class="PriorityWrapper">
                <div class="PriorityIcon"></div>
                <span>{{ testPlanCase.test_case.view_data.priority }}</span>
              </div>
            </div>
            <div class="BaseInfoItem"><span style="margin-left:10px;margin-right:10px">分配给</span>
              <div class="PickerWrapper">
                <div type="arrowShow" class="Selection">{{ testPlanCase.owner_name }} </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="TestMainInfo">
        <div>
          <h4>用例描述</h4>
          <div class="descriptionContent"> {{ testPlanCase.test_case.Desc }}
          </div>
        </div>
        <div>
          <h4>前置条件</h4>
          <div class="descriptionContent">
            <div>{{ testPlanCase.test_case.Precondition }}</div>
          </div>
        </div>
        <div>
          <h4>预期结果</h4>
          <div class="descriptionContent">
            {{ testPlanCase.test_case.ExpectResult }}
          </div>
        </div>
      </div>
      <div class="OperationPanelWrapper">
        <div v-if="!drawer" class="OperationPanel" :style="{ height: containerHeight * 0.3 + 'px' }">
          <el-tabs value="first" :style="{ height: containerHeight * 0.3 - 56 + 'px', padding: '16px' }">
            <el-tab-pane label="测试结果" name="first">
              <div style="overflow: scroll;height: 160px;">
                <el-timeline :reverse="reverse">
                  <el-timeline-item v-for="(activity, index) in activities" :key="index"
                    :timestamp="activity.CreationTime">
                    {{ activity.content }}
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>
            <el-tab-pane label="问题" name="second">
              <div class="NoDefects" style="height: inherit">
                请在记录结果面板关联缺陷
              </div>
            </el-tab-pane>
          </el-tabs>
          <div class="PanelPositionWrapper">
            <div class="PanelContentWrapper">
              <div class="PanelContentBody"></div>
              <div class="PanelContentFooter">
                <div class="OperationTypeContainer">
                  <div type="TEST" @click="drawer = true" class="OperationTab">记录结果</div>
                  <!--<div type="COMMENT" @click="drawer = true" class="OperationTab">发表注释</div>-->
                </div>
                <div class="OperationActionContainer">
                  <div class="Wrapper">
                    <el-dropdown split-button type="primary" size="mini" @click="submitResult" @command="selectResult">
                      {{ resultType.value }} 并下一条
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item v-for="item in TestResultArray" :key="item.type" :command="item.type">{{
                          item.value }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
                <div class="TestSwtichOpeartor">
                  <el-button-group>
                    <el-button size="mini" icon="el-icon-arrow-left"></el-button>
                    <el-button size="mini"><i class="el-icon-arrow-right el-icon--right"></i></el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Drawer v-if="drawer" width="100" :mask="false" inner :transfer="false"
        style="box-shadow:0px -2px 5px #F2F2F2;position: absolute;bottom: 0px;height:280px;width: 100%;"
        :closable="true" v-model="drawer">
        <div>
          <Row>
            <Col span="16" style="border-right: 1px solid #f2f2f2">
            <div style="padding: 0px 8px 8px 0px; font-size: 16px;font-weight: bold;">
              <span>记录结果</span>
            </div>
            <el-radio-group size="small" v-model="caseResult">
              <el-radio :label="1" class="caseResult caseResultDefault">通过</el-radio>
              <el-radio :label="2" class="caseResult caseResultDefault">阻碍</el-radio>
              <el-radio :label="3" class="caseResult caseResultDefault">重测</el-radio>
              <el-radio :label="4" class="caseResult caseResultDefault">失败</el-radio>
            </el-radio-group>
            <div style="padding: 16px 16px 16px 0px">
              <Input type="textarea" v-model="ActualResult" :rows="5" placeholder="添加描述" maxlength="2048"
                show-word-limit />
            </div>
            <div style="padding-left: 0px;">
              <Button style="background-color: #0066ff;color:#ffffff" @click="setTestCaseResult">添加结果</Button>
              <Button type="default" @click="setTestCaseResult">记录结果并下一条</Button>
            </div>
            </Col>
            <Col span="8">
            <div style="padding-top: 20px;padding-left: 16px;">
              <Card :padding="0" :bordered="false" style="padding-left: 16px;">
                <el-collapse value="1" style="border-bottom: none;border-top:none;">
                  <el-collapse-item style="border-bottom: none;border-top:none" name="1">
                    <template slot="title" style="color: black;">
                      <Icon type="ios-bug" style="padding-right: 10px;" /> 关联缺陷
                    </template>
                    <div style="padding-right: 16px;">
                      <Input value="" placeholder="输入关联问题ID" />
                      <div>
                        <Icon type="md-add" /><el-button style="color:black" type="text">添加新问题</el-button>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </Card>
            </div>
            </Col>
          </Row>
          <!--<div class="PanelContentWrapper">-->
          <!--<div class="PanelContentBody">-->
          <!--</div>-->
          <!--<div class="PanelContentFooter">-->
          <!--<div class="OperationTypeContainer">-->
          <!--<div type="TEST" @click="drawer = true" class="OperationTab">记录结果</div>-->
          <!--&lt;!&ndash;<div type="COMMENT" @click="drawer = true" class="OperationTab">发表注释</div>&ndash;&gt;-->
          <!--</div>-->
          <!--</div>-->
          <!--</div>-->
        </div>
      </Drawer>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import { getTestCase, updateTestPlanCase, getTestPlanCaseApi } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'ProjectTestPlanExecuteCaseDetail',
  props: ['projectID'],
  data() {
    return {
      testPlanCase: undefined,
      drawer: false,
      direction: 'btt',
      caseResult: 1,
      reverse: true,
      activities: [
        {
          content: '',
          CreationTime: ''
        },
      ],
      TestResultArray: [{ type: 1, value: '通过' },
      { type: 2, value: '阻碍' },
      { type: 3, value: '重测' },
      { type: 4, value: '失败' }],
      resultType: { type: 1, value: "通过" },
      ActualResult: ''
    }
  },

  computed: {
    ...mapState(['appBodyHeight', 'appBodyWidth']),
    ...mapState('testplan', ['currentCaseNode', 'checkedTestCase']),

    containerHeight: function () {
      return this.appBodyHeight - 10
    },

    containerWidth: function () {
      return this.appBodyWidth
    },
  },

  methods:
  {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),
    ...mapMutations(['setItemViewMode']),
    ...mapMutations('testplan', ['setRefreshCase',]),
    ...mapActions('testplan', ['updateTestCaseResult',]),

    submitResult: function () {
      // if (this.checkedTestCase.length >= 20) {
      //   this.$Message.error({
      //     content: '批量提交最多不可以超过20个',
      //     duration: 3,
      //     closable: true
      //   })
      // } else {
      this.updateTestCaseResult(this.resultType.type)
      //}
    },

    setTestCaseResult: function () {
      this.req_data = {
        "TestResult": this.caseResult,
        "ActualResult": this.ActualResult
      }
      //console.log(this.testPlanCase.id)
      updateTestPlanCase(this.testPlanCase.id, this.req_data).then(response => {
        this.updateActivities(response.view_data)
      }, response => {
      })
      this.setRefreshCase(true)
    },

    selectResult(type) {
      switch (type) {
        case 1:
          this.resultType = this.TestResultArray[type - 1]
          break
        case 2:
          this.resultType = this.TestResultArray[type - 1]
          break
        case 3:
          this.resultType = this.TestResultArray[type - 1]
          break
        case 4:
          this.resultType = this.TestResultArray[type - 1]
          break
        default:
          this.resultType = this.TestResultArray[type - 1]
          break
      }
    },

    updateActivities: function (exec_result) {
      this.activities = []
      if (exec_result.length > 0) {
        for (let i = 0; i < exec_result.length; i++) {
          let tmp = {
            content: '',
            CreationTime: ''
          }
          tmp['CreationTime'] = exec_result[i]['CreationTime']
          tmp['content'] = '测试结果:' + exec_result[i]['Result'] + '--' + exec_result[i]['ActualResult']
          this.activities.push(tmp)
        }
      }
    },

    getTestPlanCase: function (caseID) {
      if (caseID > 0) {
        getTestPlanCaseApi(caseID).then(response => {
          this.testPlanCase = response
          this.updateActivities(response.exec_result)
        }, response => {
        }
        )
      }
    },
  },

  created: function () {
  },

  mounted: function () {
    if (this.currentCaseNode !== undefined) {
      this.getTestPlanCase(this.currentCaseNode.id)
    }
  },
  watch: {
    currentCaseNode: function (value) {
      if (value !== undefined) {
        this.getTestPlanCase(value.id)
      }
    },
  },

}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.TestDetailInfo {
  position: relative;
  // display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  background-color: rgb(255, 255, 255);
  flex: 1 auto;
  overflow: hidden;
}

.TestCaseHeaderWrapper {
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  width: 100%;
  background-color: rgb(255, 255, 255);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

/*.DetailHeader {*/
/*box-sizing: border-box;*/
/*position: relative;*/
/*display: flex;*/
/*flex-direction: column;*/
/*width: 100%;*/
/*max-width: 1060px;*/
/*height: 100px;*/
/*padding-left: 20px;*/
/*padding-right: 20px;*/
/*padding-top: 16px;*/
/*padding-bottom: 12px;*/
/*flex: 1 1 auto;*/
/*transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;*/
/*}*/

.TestTitle-textEllipsis {
  margin-bottom: 12px;
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 14px;
  line-height: 18px;
  text-overflow: ellipsis;
  //white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;

  display: inline-block;
  font-family: "微软雅黑";
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.TestTitleBaseInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
}

.TestMainInfo {
  overflow-y: auto;
  width: 100%;
  max-width: 1060px;
  padding-top: 108px;
  padding-right: 16px;
  padding-left: 16px;
  flex: 1 1 auto;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Wrapper {
  position: relative;
  padding: 20px;
}

.Precondition {
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  margin-top: 41px;
  margin-bottom: 12px;
  position: relative;
  font-weight: bold;
}

.CasePreconds {
  width: 100%;
  color: rgb(201, 207, 215);
  font-size: 14px;
  margin-bottom: 40px;
  padding: 10px 0px;
  display: inline-block;
  line-height: 1.6;
  font-family: "微软雅黑";
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  white-space: normal;
}

.AttachmentWrapper {
  margin-top: 40px;
}

.descriptionContent {
  font-size: 12px;
  line-height: 28px;
  margin-bottom: 40px;
  max-width: 800px;
  display: inline-block;
  line-height: 1.6;
  font-family: "微软雅黑";
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  white-space: normal;
  padding: 8px 8px 8px 8px;
}

.ExecuteSectionTitle {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
}

.OperationPanelWrapper {
  width: 100%;
  max-width: 1060px;
  flex: 0 0 264px;
}

.OperationPanel {
  display: flex;
  flex-direction: column;
  position: absolute;
  z-index: 1030;
  bottom: 0px;
  width: 100%;
  max-width: 1060px;
  min-height: 264px;
  max-height: calc(100% - 124px);
  height: 264px;
  box-shadow: rgba(0, 0, 0, 0.02) 0px -1px 6px 0px, rgba(10, 36, 70, 0.05) 0px -4px 24px 0px;
  border-radius: 3px 3px 0px 0px;
  background: rgb(255, 255, 255);
}

/*.Wrapper {*/
/*overflow-y: auto;*/
/*flex: 1 1 auto;*/
/*padding: 16px 24px 0px;*/
/*}*/

.RecordWrapper {
  position: relative;
  padding-left: 30px;
  padding-bottom: 24px;
}

.TypeIcon {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 19px;
  height: 19px;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjE4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHdpZHRoPSIxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxjaXJjbGUgY3g9IjkiIGN5PSI5IiBmaWxsPSIjMzA4NmVjIiByPSI5Ii8+PHBhdGggZD0ibTUuNSA2aDdjLjI3NiAwIC41LjIyNC41LjV2NWMwIC4yNzYtLjIyNC41LS41LjVoLTIuNWwtMyAydi0yaC0xLjVjLS4yNzYgMC0uNS0uMjI0LS41LS41di01YzAtLjI3Ni4yMjQtLjUuNS0uNXoiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvZz48L3N2Zz4=) center center no-repeat;
}

.RecordTitle {
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 20px;
}

.RecordFooter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
}

.Divider {
  width: 1px;
  height: 12px;
  background-color: rgba(0, 0, 0, 0.07);
  margin: 0px 8px;
}

.ExpandArea {
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  width: 108px;
  height: 20px;
  cursor: ns-resize;
}

.NoDefects {
  display: flex;
  flex-direction: column;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  background-image: linear-gradient(45deg, rgb(250, 250, 250) 0%, rgb(245, 246, 247) 100%);
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 1 1 auto;
  padding: 0px 24px;
}

.Wrapper-wrapperClassName {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  flex: 0 0 51px;
  padding: 0px 24px;
}

ol,
ul {
  list-style: none;
}

.NoRecords {
  display: flex;
  flex-direction: column;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  background-image: linear-gradient(45deg, rgb(250, 250, 250) 0%, rgb(245, 246, 247) 100%);
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 1 1 auto;
  padding: 0px 24px;
}

.PanelPositionWrapper {
  z-index: 12;
  /*position: relative;*/
  flex: 0 0 56px;
}

.PanelContentWrapper {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.08) 0px -2px 12px 0px;
}

.PanelContentBody {
  display: flex;
  -webkit-box-align: stretch;
  align-items: stretch;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  transition: height 0.3s ease 0s, opacity 0.4s ease 0.2s;
}

.PanelContentFooter {
  box-sizing: border-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 50px;
  padding: 10px 24px;
}

.OperationTypeContainer {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.OperationTab {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-right: 32px;
  height: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.OperationTab {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-right: 32px;
  height: 16px;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  color: rgb(32, 45, 64);
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.OperationActionContainer {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.OperatorButton {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(255, 255, 255);
  font-size: 14px;
  line-height: 16px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
  padding: 10px 32px 10px 10px;
  background: rgb(33, 202, 100);
  border-width: 1px;
  border-style: solid;
  border-color: rgb(31, 194, 96);
  border-image: initial;
  border-radius: 3px;
}

.TypeIcon {
  display: inline-block;
  margin-right: 6px;
  width: 16px;
  height: 16px;
  background: url(data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHdpZHRoPSIxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtOCAxNmMtNC40MTgyNzggMC04LTMuNTgxNzIyLTgtOHMzLjU4MTcyMi04IDgtOCA4IDMuNTgxNzIyIDggOC0zLjU4MTcyMiA4LTggOHptNC44LTkuNi0xLjQtMS40LTQgNC0yLTItMS40IDEuNCAzLjQgMy40eiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+) center center no-repeat;
}

.MoreButton {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 32px;
  height: 36px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5ICg1MTAwMikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Q2hldnJvbi9tb3JlLWRvd248L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iQ29tcG9uZW50cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41IiBzdHJva2UtbGluZWNhcD0icm91bmQiPgogICAgICAgIDxnIGlkPSJGb3JtLUVsZW1lbnRzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNDcyLjAwMDAwMCwgLTY0NC4wMDAwMDApIiBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMiI+CiAgICAgICAgICAgIDxnIGlkPSJCdXR0b24iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDMzNi4wMDAwMDAsIDYzNC4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJDaGV2cm9uL2NoZXZyb24tZG93bi13aGl0ZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTM2LjAwMDAwMCwgMTAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9IkNoZXZyb24vbW9yZS13aGl0ZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBpZD0iY2hldnJvbiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOC4wMDAwMDAsIDQuNzUwMDAwKSBzY2FsZSgxLCAtMSkgdHJhbnNsYXRlKC04LjAwMDAwMCwgLTQuNzUwMDAwKSAiIHBvaW50cz0iMTEgMy41IDggNiA1IDMuNSI+PC9wb2x5bGluZT4KICAgICAgICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIGlkPSJjaGV2cm9uIiBwb2ludHM9IjExIDEwLjUgOCAxMyA1IDEwLjUiPjwvcG9seWxpbmU+CiAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) center center / 16px no-repeat;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TestSwtichOpeartor {
  position: relative;
  margin-left: 8px;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(208, 212, 217);
  border-image: initial;
  border-radius: 3px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TabItem {
  display: flex;
  font-size: 15px;
  line-height: 16px;
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
  color: rgb(32, 45, 64);
  padding: 16px 0px;
  border-bottom: 2px solid rgb(0, 102, 255);
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

wrapperClassName li+li {
  margin-left: 40px;
}

.DetailHeader {
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1060px;
  height: 100%;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 16px;
  padding-bottom: 16px;
  flex: 1 1 auto;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.DetailHeader::after {
  content: "";
  display: block;
  position: relative;
  bottom: -14px;
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.07);
}

.textEllipsis {
  margin-bottom: 12px;
  color: rgb(32, 45, 64);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
}

.TestTitleBaseInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
}

.IDTag {
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: rgb(255, 255, 255);
  background-color: rgb(250, 91, 69);
  padding: 0px 5px;
  border-radius: 2px;
}

.BaseInfoItem {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 16px;
  //padding-left: 17px;
  height: 24px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 0 0 auto;
}

.PathInfo {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 16px;
  padding-left: 17px;
  height: 24px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  flex: 1 1 auto;
}

/*.Wrapper {*/
/*position: relative;*/
/*display: flex;*/
/*-webkit-box-align: center;*/
/*align-items: center;*/
/*flex: 1 1 auto;*/
/*}*/

.SectionIcon {
  display: block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.Path {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  margin-left: 8px;
  height: 16px;
  font-size: 13px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  flex: 1 1 auto;
}

.PathContent {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.Divider {
  color: rgb(201, 207, 215);
  padding: 0px 4px;
}

.PickerWrapper {
  position: relative;
  height: 32px;
}

.Selection {
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(145, 153, 163);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 30px 0px 7px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Icon {
  margin-right: 8px;
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 24px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUwLjIgKDU1MDQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5hdmF0YXItZGVmYXVsdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJTeW1ib2xzIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iYXZhdGFycy9hdmF0YXItZGVmYXVsdCI+CiAgICAgICAgICAgIDxnIGlkPSJhdmF0YXItZGVmYXVsdCI+CiAgICAgICAgICAgICAgICA8Y2lyY2xlIGlkPSJPdmFsLTQiIGZpbGw9IiNFMUUzRTYiIGN4PSIxMiIgY3k9IjEyIiByPSIxMiI+PC9jaXJjbGU+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxMCBDOCw3Ljc5MSA5Ljg0NCw2IDEyLDYgQzE0LjE1Niw2IDE2LDcuNzkxIDE2LDEwIEwxNiwxMSBDMTYsMTMuMjA5IDE0LjE1NiwxNSAxMiwxNSBDOS44NDQsMTUgOCwxMy4yMDkgOCwxMSBMOCwxMCBaIiBpZD0iUGF0aCIgZmlsbD0iI0ZGRkZGRiI+PC9wYXRoPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTEyLDIyIEM5LjU2NywyMiA3LjMzNSwyMS4xMjQgNS41OTksMTkuNjc0IEM2LjQzOCwxOC4wOTEgOC4wODMsMTcgMTAsMTcgTDE0LDE3IEMxNS45MTcsMTcgMTcuNTYyLDE4LjA5MSAxOC40MDEsMTkuNjc0IEMxNi42NjUsMjEuMTI0IDE0LjQzMywyMiAxMiwyMiBaIiBpZD0iUGF0aCIgZmlsbD0iI0ZGRkZGRiI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) center center / 24px no-repeat rgb(240, 241, 242);
}

.PriorityWrapper {
  flex-shrink: 0;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 13px;
  line-height: 16px;
  color: rgb(32, 45, 64);
}

.PriorityIcon {
  flex-shrink: 0;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  margin-right: 8px;
  background: rgba(50, 116, 250, 0.1);
  border-width: 2px;
  border-style: solid;
  border-color: rgb(50, 116, 250);
  border-image: initial;
  border-radius: 50%;
}


/*.caseResult:hover{*/
/*background-color: #F2F2F2;*/
/*}*/

.caseResultPass {
  border: 1px solid #21ca64;
  background-color: #21ca64;
  height: 30px;
  width: 86px;
  padding: 8px 15px;
  border-radius: 3px;
}

.caseResultDefault {
  color: #202d40;
  background-color: #f7f8f9;
  height: 30px;
  width: 86px;
  padding: 8px 15px;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}
</style>
