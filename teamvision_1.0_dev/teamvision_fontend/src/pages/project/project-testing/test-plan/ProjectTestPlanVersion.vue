<template>
  <div class="PlanDetailView">
    <div class="Root">
      <div class="FlexBox">
        <div class="TitleSection">
          <div class="PlanTitle">
            <div class="Tag-largeSize">
              <span></span>迭代
            </div>
            <div title="test" class="TitleContent-textEllipsis">
              <div>{{ planVersion.VVersion }}</div>
            </div>
          </div>
        </div>
        <div class="Root" v-if="planVersion.VDescription">
          <div class="PlanDescriptionWrapper" style="height: 20px;">
            <div class="PlanDescription">
              <div>{{ planVersion.VDescription }}</div>
            </div>
          </div>
        </div>
        <div class="UserSection">
          <div class="CountDownTime">
            <div class="Overdue">{{ planVersion.view_data.version_status }}</div>
          </div>
          <div class="TimeSection">
            <div class="SectionLabel">开始日期</div>
            <div class="SectionText">{{ planVersion.VStartDate }}</div>
          </div>
          <div class="TimeSection">
            <div class="SectionLabel">结束日期</div>
            <div class="SectionText">{{ planVersion.VReleaseDate }}</div>
          </div>
        </div>
        <div class="InfoPanel">
          <div class="InfoBarState">
            <div class="PlanCasesStatistics">
              <span class="PassPercentage">{{ planVersion.view_data.pass_rate }}%</span>测试通过
              <span class="DotSpan"></span>已测用例
              <span class="TestedCases">{{ planVersion.view_data.case_count - planVersion.view_data.no_run }}<i
                  class="AllCases"> / {{ planVersion.view_data.case_count }}</i></span>
            </div>
            <test-case-process-bar :pass="planVersion.view_data.pass_rate" :reTest="planVersion.view_data.retest_rate"
              :fail="planVersion.view_data.fail_rate" :blocked="planVersion.view_data.blocked_rate"
              :norun="planVersion.view_data.norun_rate"></test-case-process-bar>
          </div>
          <div class="Line"></div>
          <ul class="TestsStatistics">
            <li>
              <p class="StatisticsCount">2</p>
              <p class="StatisticsDescription">未完结计划</p>
            </li>
            <li>
              <p class="StatisticsCount">{{ planVersion.view_data.case_count - planVersion.view_data.no_run -
                planVersion.view_data.passed_case
                }}</p>
              <p class="StatisticsDescription">未通过任务</p>
            </li>
            <li>
              <p class="StatisticsCount">{{ planVersion.view_data.case_coverage }}%</p>
              <p class="StatisticsDescription">用例库覆盖</p>
            </li>
          </ul>
        </div>
        <div class="Tab-Root">
          <div class="PlanMenuPanel">
            <ul class="Wrapper-wrapperClassName">
              <li class="TabItem-TabItem"><span>未通过任务</span>
              </li>
            </ul>
            <div class="FilterWrapper">
              <div class="FliterSelectWrapper">
                <div class="PickerWrapper">
                  <div class="Selection">
                    <span style="display:inline-flex;align-items: center;width: 70px;">
                      <label-editor-dropdwon-item @updateValue="filterByCaseStatus($event)" :showIcon="false"
                        :itemID="0" :value="-1" :itemList="caseStatus" displayColor="" displayText="全部状态">
                      </label-editor-dropdwon-item>
                    </span>
                  </div>
                </div>
              </div>
              <div class="FliterUserWrapper">
                <div class="SelectContainer">
                  <span style="display:inline-flex;align-items: center;width: 70px;">
                    <label-editor-dropdwon-item :search="true" @updateValue="filterByCaseOwner" :showIcon="false"
                      :itemID="0" :value="0" :itemList="memberDropdownData" displayColor="" displayText="全部经办人">
                    </label-editor-dropdwon-item>
                  </span>
                </div>
              </div>
              <div class="FilterLine"></div>
              <div class="SearchInputWrapper">
                <div class="Case-Root">
                  <div class="root">
                    <div class="InputWrapper">
                      <input type="text" class="InputDom-hasIconInput-withoutBorder" placeholder="搜索" value="">
                      <div class="InputIcon"></div>
                    </div>
                  </div>
                  <div value="" class="ClearButton"></div>
                </div>
              </div>
            </div>
            <a class="Scale-scaleButtonStyle"></a>
          </div>
          <div class="ScrollWrapper">
            <div class="Case-Root">
              <el-card v-for="plan in planVersion.view_data.test_plans " v-if="plan.view_data.unpass_case.length > 0"
                :key="plan.id" style="margin-bottom: 6px;"
                :body-style="{ 'padding': '0px', 'max-height': caseTreeContainerHeight + 'px', overflow: 'scroll' }">
                <el-collapse @change="showCaseList">
                  <el-collapse-item :name="plan.id">
                    <template slot="title">
                      <div style="padding-left: 16px;">
                        <div class="PlanTitle">
                          <div class="Tag">#{{ plan.id }}</div>
                          <div class="Title-textEllipsis">{{ plan.Title }}</div>
                        </div>
                      </div>
                    </template>
                    <div style="padding-left: 16px;">
                      <el-table size="mini" :data="plan.view_data.unpass_case" style="width: 100%">
                        <el-table-column prop="Title" label="测试任务" :min-width="300">
                          <template slot-scope="scope">
                            <Icon v-if="scope.row.TestResult === 0" type="ios-alert" />
                            <Icon v-if="scope.row.TestResult === 4" color="red" type="md-close-circle" />
                            <Icon v-if="scope.row.TestResult === 3" color="orange" type="ios-disc" />
                            <Icon v-if="scope.row.TestResult === 2" type="md-remove-circle" />
                            <span style="margin-left: 0px">{{ scope.row.TestResultName }}</span>
                            <span style="margin-left: 10px">{{ scope.row.id }}</span>
                            <span style="margin-left: 10px">{{ scope.row.Title }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="id" label="关联问题" width="100">
                        </el-table-column>
                        <el-table-column prop="OwnerName" label="经办人" width="100">
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-card>
            </div>
            <div v-if="0 === 1" class="EmptyPage">
              <div class="EmptyIcon"></div>
              <div class="EmptySlogan">暂无未通过的测试任务</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import { getTestCaseResults, getPlanCaseResults, getVersionTestPlans } from '../business-service/ProjectTestCaseApiService'
import { transferTaskStatusDropdownItemData } from '../business-service/ProjectTestCaseUtilService'
import { getMemberDropdownItemData } from '../../business-service/ProjectUtilService'
import TestCaseProcessBar from '../../../../components/common/ProgressBar.vue'
import labelEditorDropdwonItem from '../../../../components/common/LabelEditor-DropdownItem.vue'

export default {
  name: 'projectTestPlanVersion',
  props: ['projectID', 'versionID'],
  data() {
    return {
      caseOwners: [],
      planVersion: {
        VDescription: '',
        VprojectID: 0,
        VReleaseDate: '',
        VStartDate: '',
        VVersion: '',
        VersionFiled: false,
        id: 0,
        view_data: { version_status: '状态' }
      },
      caseStatus: [],
      memberDropdownData: [],
    }
  },

  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapState(['appBodyMainHeight', 'appBodyWidth']),
    ...mapState('project', ['projectMembers']),
    containerHeight: function () {
      return this.appBodyMainHeight - 10
    },

    caseTreeContainerHeight: function () {
      return this.appBodyMainHeight - 400
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    project: function () {
      let result = 0
      if (this.projectID) {
        result = this.projectID
      }
      return result
    }

  },

  methods: {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),
    ...mapMutations(['setItemViewMode']),

    changeViewMode(value) {
      // console.log(value)
    },
    handleNodeClick(data) {
      // console.log(data)
    },
    append(data) {
      const newChild = { id: id++, label: 'testtest', children: [] }
      if (!data.children) {
        this.$set(data, 'children', [])
      }
      data.children.push(newChild)
    },

    filterByCaseStatus: function (e) {
    },

    filterByCaseOwner: function () {
    },

    loadPlanVersionInfo(versionID) {
      getVersionTestPlans(versionID).then(response => {
        this.planVersion = response.data.result
      },)
    },

    loadCaseStatus: function () {
      getTestCaseResults(7).then(response => {
        let statusList = transferTaskStatusDropdownItemData(response.data.result)
        let tempTag = { label: '全部状态', value: -1, color: '' }
        this.caseStatus.push(tempTag)
        this.caseStatus.push(...statusList)
      }, response => {
      })
    },

    getProjectMembers: function () {
      let tempTag = { label: '全部成员', value: 0, color: '' }
      this.memberDropdownData = getMemberDropdownItemData(this.projectMembers)
      this.memberDropdownData.splice(0, 0, tempTag)
    },


    showCaseList: function (value) {
      //          if(value.length>0) {
      //            getPlanCaseResults(value[0]).then(response=>{
      //              let caseResults = response.data.result
      //              for(let i =0; i<this.planVersion.view_data.test_plans; i++) {
      //                if(value === this.planVersion.view_data.test_plans[i].id) {
      //                  this.planVersion.view_data.test_plans[i]['caseResults'] = caseResults
      //                  break
      //                }
      //              }
      //            },response=>{})
      //          }
    }

  },

  created: function () {
    if (this.$route.params.versionID) {
      this.versionID = this.$route.params.versionID
    } else {
      if (this.versionID) {
        this.loadPlanVersionInfo(this.versionID)
      }
    }
    this.loadCaseStatus()
    this.getProjectMembers()
  },

  mounted: function () {
    this.versionID = this.$route.params.versionID
  },

  watch: {
    versionID: function (value) {
      this.loadPlanVersionInfo(this.versionID)
    }
  },

  components: {
    TestCaseProcessBar,
    labelEditorDropdwonItem
  }
}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.PlanDetailView {
  position: relative;
  display: flex;
  flex: 1 1 auto;
}

.Root {
  display: flex;
  flex: 1 1 auto;
  margin: 0px auto;
  padding: 0px 24px;
}

.FlexBox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  margin: 0px auto;
}

.TitleSection {
  flex-shrink: 0;
  position: relative;
  padding-top: 24px;
}

.PlanTitle {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 24px;
  font-size: 20px;
  line-height: 24px;
  color: rgb(32, 45, 64);
}

.PlanTitle {
  display: flex;
  width: 100%;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 8px;
}

.PlanDetail {
  font-size: 12px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.Tag {
  display: inline-flex;
  min-width: 0px;
  height: 16px;
  line-height: 16px;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(255, 255, 255);
  font-size: 12px;
  margin-right: 8px;
  flex: 0 0 auto;
  padding: 0px 3px;
  border-radius: 2px;
  background: rgb(0, 102, 255);
}

.Tag-largeSize {
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  min-width: 0px;
  font-size: 12px;
  color: rgb(255, 255, 255);
  margin-right: 8px;
  height: 20px;
  padding-right: 4px;
  flex: 0 0 auto;
  border-radius: 2px;
  background: rgb(252, 149, 13);
}

.TitleContent-textEllipsis {
  position: relative;
  -webkit-box-flex: 1;
  flex-grow: 1;
  margin-right: 9px;
}

/*.Root {*/
/*border-bottom: 1px solid rgba(0, 0, 0, 0.07);*/
/*}*/

.PlanDescriptionWrapper {
  display: flex;
  height: 20px;
  position: relative;
  margin: 8px 0px 9px;
  overflow: hidden;
  transition: height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.PlanDescription {
  display: flex;
  width: 100%;
  position: absolute;
  font-size: 13px;
  line-height: 20px;
  left: 0px;
  top: 0px;
  color: rgb(145, 153, 163);
}

.UserSection {
  flex-shrink: 0;
  display: flex;
  height: 82px;
  -webkit-box-align: center;
  align-items: center;
}

.CountDownTime {
  width: 133px;
  position: relative;
  margin-right: 40px;
  flex-shrink: 0;
}

.Overdue {
  flex-shrink: 0;
  width: 67px;
  height: 30px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  position: relative;
  font-weight: bold;
  color: rgb(145, 153, 163);
  box-sizing: border-box;
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(201, 207, 215);
  border-image: initial;
}

.TimeSection {
  flex-shrink: 0;
  width: 117px;
}

.SectionLabel {
  font-size: 12px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  margin-bottom: 6px;
}

.SectionText {
  font-size: 14px;
  line-height: 20px;
  color: rgb(145, 153, 163);
}

.InfoPanel {
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 6px -1px, rgba(0, 0, 0, 0.05) 0px 6px 28px -3px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding: 24px 24px 16px 16px;
  background: rgb(255, 255, 255);
  border-radius: 3px;
}

.InfoBarState {
  -webkit-box-flex: 1;
  flex-grow: 1;
}

.PlanCasesStatistics {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-weight: bold;
  line-height: 16px;
  font-size: 12px;
  margin-bottom: 16px;
}

.PassPercentage {
  font-size: 16px;
  padding-right: 4px;
}

.DotSpan {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  margin: 0px 16px;
  background: rgb(201, 207, 215);
}

.TestedCases {
  font-size: 14px;
  padding-left: 8px;
}

.AllCases {
  color: rgb(145, 153, 163);
}

.BarWrapper-testStatusBarStyle {
  display: inline-flex;
  position: relative;
  height: 16px;
  width: 100%;
}

.Bar {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  display: inline-flex;
  min-width: 160px;
  box-sizing: border-box;
  margin-right: -2px;
  border-radius: 3px;
  background: rgb(255, 255, 255);
}

.Bar li:first-of-type {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.BarSection-passStyle-HoverStyle {
  width: 33%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background: rgb(45, 211, 111);
}

.Bar li:last-child {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.BarSection-HoverStyle {
  width: 67%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  background: rgb(222, 223, 229);
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Line {
  width: 1px;
  height: 56px;
  margin: 0px 32px;
  background: rgba(0, 0, 0, 0.07);
}

.TestsStatistics {
  display: flex;
  text-align: center;
  flex: 0 0 240px;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.StatisticsCount {
  font-size: 24px;
  font-weight: bold;
  line-height: 28px;
  margin-bottom: 8px;
}

.StatisticsDescription {
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
}

li {
  display: list-item;
  text-align: -webkit-match-parent;
  list-style: none;
}

.Tab-Root {
  -webkit-box-flex: 1;
  flex-grow: 1;
  min-height: 0px;
  display: flex;
  flex-direction: column;
}

.PlanMenuPanel {
  flex-shrink: 0;
  width: 100%;
  margin-top: 16px;
  position: relative;
  border-radius: 3px;
  background: rgb(255, 255, 255);
}

.ScrollWrapper {
  padding-top: 8px;
  margin-bottom: 8px;
  flex: 1 1 auto;
  overflow: auto;
}


.Wrapper-wrapperClassName {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  padding: 0px 16px;
}

.TabItem-TabItem {
  display: flex;
  font-size: 15px;
  line-height: 16px;
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
  color: rgb(32, 45, 64);
  padding: 16px 0px;
  border-bottom: 2px solid rgb(0, 102, 255);
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.FilterWrapper {
  height: 48px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding: 0px 4px;
  background: rgb(255, 255, 255);
}

.FliterSelectWrapper {
  position: relative;
}

.PickerWrapper {
  position: relative;
  height: 32px;
  display: inline-flex;
}


.Selection-panelOpenStyle {
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  background-color: rgb(243, 244, 246);
  padding: 0px 8px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  border-color: rgb(243, 244, 246);
}


.PanelScroll {
  max-height: 224px;
  overflow: auto;
}

.FliterUserWrapper {
  flex: 0 0 120px;
}

.SelectContainer {
  position: relative;
  flex-shrink: 0;
}

.Selection {
  box-sizing: border-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  user-select: none;
  padding: 6px 12px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}


.FilterLine {
  flex-shrink: 0;
  width: 1px;
  height: 16px;
  background: rgba(0, 0, 0, 0.07);
  margin: 0px 24px 0px 8px;
}

.SearchInputWrapper {
  -webkit-box-flex: 1;
  flex-grow: 1;
  display: flex;
}

/*.Root {*/
/*-webkit-box-flex: 0;*/
/*flex-grow: 0;*/
/*display: flex;*/
/*width: 100%;*/
/*position: relative;*/
/*}*/

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.InputDom-hasIconInput-withoutBorder {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  height: 36px;
  line-height: 24px;
  font-size: 14px;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-shadow: none !important;
  outline: none;
  padding: 6px 12px 6px 24px;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
}

.InputIcon {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  display: block;
  background-image: url(data:image/svg+xml;base64,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);
  background-size: 16px;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background-repeat: no-repeat;
  background-position: center center;
}

.ScrollWrapper {
  padding-top: 8px;
  margin-bottom: 8px;
  flex: 1 1 auto;
  overflow: auto;
}

.Case-Root {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.EmptyPage {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  flex-direction: column;
  flex: 1 1 auto;
  background: rgb(255, 255, 255);
}

.EmptyIcon {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
  background: url(https://assets.codehub.cn/coding-testing-assets/static/img/no-undone-test-circle.91412a0.svg) center center / cover no-repeat;
}

.EmptySlogan {
  font-size: 13px;
  line-height: 18px;
  color: rgb(145, 153, 163);
}

.Wrapper-wrapperClassName {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  padding: 0px 16px;
}

.table-case-title {
  width: 50%;
}
</style>
