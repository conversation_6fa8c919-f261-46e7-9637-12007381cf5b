<template>
  <div>
    <Modal :value="createDialogShow" title="创建测试计划" :mask-closable="false" class-name="vertical-center-modal"
      :width="600" @on-cancel="cancel">
      <Spin fix v-show="isLoading">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>创建中 ...</div>
      </Spin>
      <div :style="'height:' + containerHeight - 100 + 'px;overflow-y: scroll;overflow-x: hidden'">
        <Form ref="createTestPlan" :model="formItem" :label-width="0" :rules="ruleCustom">
          <FormItem label="" prop="Title">
            <Input v-model="formItem.Title" :maxlength="50" show-word-limit placeholder="测试计划标题" />
          </FormItem>
          <Row>
            <Col span="12" style="padding-right: 10px;">
            <FormItem label="" prop="Version">
              <Select transfer v-model="formItem.Version" filterable placeholder="计划所属版本">
                <Option v-for="version in projectVersions" :key="version.id" :value="version.id">{{ version.VersionLabel
                }}</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="" prop="">
              <Select transfer v-model="formItem.Owner" filterable placeholder="默认分配给">
                <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{ member.name
                }}</Option>
              </Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12" style="padding-right: 10px;">
            <FormItem label="" prop="fortestings">
              <Select filterable allow-create transfer multiple v-model="formItem.fortestings" placeholder="关联提测">
                <Option v-for="fortesting in getFortestingsForStatus" :key="fortesting.id" :value="fortesting.id">{{
                  "[" + fortesting.id + "]" + fortesting.Topic }}</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem transfer label="" id="requireNumId" prop="requireNum">
              <Input type="number" v-model="formItem.requireNum" placeholder="需求个数" prefix="md-calculator" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24" style="padding-right: 10px;">
            <FormItem label="" prop="estimatedDate">
              <DatePicker :value="formItem.estimatedDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                transfer placeholder="预计开始 - 结束时间" style="width: 275px" @on-change="handleTestDateChange">
              </DatePicker>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12" v-if="showPlanEst" style="padding-right: 10px;">
            <FormItem label="" prop="writeTestCases">
              <DatePicker :value="formItem.writeTestCases" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                transfer placeholder="编写测试用例 开始 - 结束时间" style="width: 270px"
                @on-change="handleWriteTestCasesDateChange">
              </DatePicker>
            </FormItem>
            </Col>
            <Col span="12" v-if="showPlanEst">
            <FormItem label="" prop="testCaseReview">
              <DatePicker :value="formItem.testCaseReview" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                transfer placeholder="测试用例评审 开始 - 结束时间" style="width: 270px" @on-change="handleCaseReviewDateChange">
              </DatePicker>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12" v-if="showPlanEst" style="padding-right: 10px;">
            <FormItem label="" prop="execCase">
              <DatePicker :value="formItem.execCase" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                transfer placeholder="执行测试 开始 - 结束时间" style="width: 270px" @on-change="handleExecCaseDateChange">
              </DatePicker>
            </FormItem>
            </Col>
            <Col span="12" v-if="showPlanEst">
            <FormItem label="" prop="performanceTest">
              <DatePicker :value="formItem.performanceTest" format="yyyy-MM-dd" type="daterange" placement="bottom-end"
                transfer placeholder="性能测试 开始 - 结束时间" style="width: 270px" @on-change="handlePerformanceTestDateChange">
              </DatePicker>
            </FormItem>
            </Col>
          </Row>
          <FormItem label="" prop="Desc">
            <Input v-model="formItem.Desc" maxlength="1000" :rows="8" show-word-limit type="textarea"
              placeholder="计划描述/提测链接" />
          </FormItem>
          <FormItem v-if="!caseReviewID" label="" prop="IncludeAllCase">
            <RadioGroup v-model="formItem.IncludeAllCase" @on-change="caseFilterChange">
              <Radio style="padding-top: 0px;font-size: 12px;" :label="0" border>手动选择用例</Radio>
              <Radio style="padding-top: 0px;font-size: 12px;" :label="1" border>包含全部用例</Radio>
              <Radio style="padding-top: 0px;font-size: 12px;" :label="2" border>已评审测试用例</Radio>
            </RadioGroup>
            <div style="height:80px;padding-left: 10px; font-size: 12px;">
              <div v-if="formItem.IncludeAllCase === 0">
                <div>手动从用例库中圈选用例，如果用例库有新创建的用例，不会被同步到本计划。</div>
                <div style="font-size: 12px;border: 1px solid #dcdee2;padding: 8px;">
                  <span>{{ selectCasesCount }} 条用例已选择</span>
                  <Divider type="vertical" />
                  <span>
                    <Button type="text" @click="showCaseTree">
                      <span style="color: #0066ff;font-size: 14px;"> 选择用例
                        <Icon type="md-arrow-round-forward" />
                      </span>
                    </Button>
                  </span>
                </div>
              </div>
              <div v-if="formItem.IncludeAllCase === 1">
                覆盖本项目全部可用用例({{ projectCaseCount }})，如果用例库有新增的用例，会自动加入到本计划中
              </div>
              <div v-if="formItem.IncludeAllCase === 2">
                <div>从已通过测试用例评审中选择: </div>
                <Select clearable v-model="formItem.case_review_id" style="width:220px" filterable transfer>
                  <Option v-for="case_review in projectCaseReviews" :key="case_review.id" :value="case_review.id">[{{
                    case_review.id }}] {{ case_review.Title }}</Option>
                </Select>
              </div>
            </div>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button v-if="createDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="ok('createTestPlan')">创建</Button>
      </div>
    </Modal>
    <project-test-case-select-dialog @selectCase="selectCaseGroup" :projectID="projectID">
    </project-test-case-select-dialog>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import { testplanValidateRules } from '../business-service/ProjectTestPlanCreateDialog.js'
import ProjectTestCaseSelectDialog from './ProjectTestCaseSelectDialog.vue'
import { createTestPlan } from '../business-service/ProjectTestCaseApiService'

export default {
  name: 'ProjectTestPlanCreateDialog',
  props: ['projectID', 'caseReviewID'],
  data() {
    return {
      isLoading: false,
      selectCasesCount: 0,
      formItem: {
        Title: '',
        Desc: '',
        Project: 0,
        Version: 0,
        IncludeAllCase: 0,
        Creator: 0,
        Owner: 0,
        CaseCount: 0,
        fortestings: [],
        SelectCaseGroup: [],
        halfCheckCaseGroup: [],
        requireNum: '',
        case_review_id: 0,
        projectCaseReviews: [],
        estimatedDate: [],
        EstimatedStartTime: '',
        EstimatedFinishTime: '',
        writeTestCases: [],
        testCaseReview: [],
        execCase: [],
        performanceTest: [],
      },
      showPlanEst: false,
      ruleCustom: {
        ...testplanValidateRules
      },
    }
  },
  computed: {
    ...mapGetters('project', ['getFortestingsForStatus']),
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapState('projectglobal', ['createDialogShow',]),
    ...mapState('project', ['projectVersions', 'projectMembers', 'projectCaseCount', 'projectFortestings',]),

    containerHeight: function () {
      if (this.appBodyMainHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyMainHeight - 100
      }
    },

    dialogShow: function () {
      return this.createReqType === 1
    },
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setCaseSelectDialogShow', 'setTaskChange', 'setCreateReqType']),
    ...mapMutations('issue', ['setIssueChange']),
    ...mapMutations('testplan', ['setDefTestPlan']),
    ...mapActions('testplan', ['getTestCaseTree']),
    ...mapActions('project', ['getProjectFortestings']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.isLoading = true

          this.formItem.Creator = this.userInfo.id
          this.formItem.Project = this.projectID

          if (this.formItem.IncludeAllCase == 2) {
            this.formItem.IncludeAllCase = 0
          }
          if (this.caseReviewID) {
            this.formItem.case_review_id = this.caseReviewID
          }

          createTestPlan(this.formItem).then(response => {
            this.isLoading = false
            let success_info = '创建成功:' + response.data.result.Title
            this.$Message.success({
              content: success_info,
              duration: 3,
              closable: true
            })
            this.setCreateDialogShow(false)
            this.$emit("createPlan", response)
            this.setDefTestPlan(response.data.result)
            this.getTestCaseTree(response.data.result.id)
          }, response => {
            this.$Message.success({
              content: '创建失败',
              duration: 3,
              closable: true
            })
            this.isLoading = false
            this.setCreateDialogShow(false)
          })
          //this.$refs[name].resetField()
        }
      })
    },
    cancel() {
      this.setCreateDialogShow(false)
    },

    selectCaseGroup(caseCount, groupIDs, halfCheckCaseGroup) {
      this.selectCasesCount = caseCount
      this.formItem.CaseCount = caseCount
      this.formItem.SelectCaseGroup = groupIDs
      this.formItem.halfCheckCaseGroup = halfCheckCaseGroup
    },

    searchPlan: function () {
    },

    showCaseTree: function () {
      this.setCaseSelectDialogShow(true)
    },

    caseFilterChange: function (value) {
      if (value === 1) {
        this.formItem.CaseCount = this.projectCaseCount
      }
    },

    getProjectCaseReviews: function (value) {
      let reqUrl = "/api/project/" + this.projectID + "/casereview?page=1&page_size=20&Status=2&sort=-id"
      this.$axios.get(reqUrl).then((response) => {
        this.projectCaseReviews = response.data.result.results;
      }, (response) => { }
      );
    },

    handleTestDateChange: function (daterange) {
      this.formItem.estimatedDate = daterange;
      this.formItem.EstimatedStartTime = daterange[0];
      this.formItem.EstimatedFinishTime = daterange[1];
      const startDate = new Date(this.formItem.estimatedDate[0]);
      const endDate = new Date(this.formItem.estimatedDate[1]);
      const diffTime = endDate - startDate;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      this.showPlanEst = diffDays >= 3;
    },

    handleWriteTestCasesDateChange: function (dateRange) {
      this.formItem.writeTestCases = dateRange;
      let writeTestCasesStr = "";
      if (dateRange.length > 1 && dateRange[0] !== '' && dateRange[1] !== '') {
        writeTestCasesStr = "1.编写用例: " + dateRange[0] + " ~ " + dateRange[1];
        this.formItem.Desc = writeTestCasesStr + '\n' + this.formItem.Desc.split('\n').filter(line => !line.startsWith('1.编写用例')).join('\n');
      } else {
        this.formItem.Desc = this.formItem.Desc.split('\n').filter(line => !line.startsWith('1.编写用例')).join('\n');
      }
    },

    handleCaseReviewDateChange: function (dateRange) {
      this.formItem.testCaseReview = dateRange;
      this.handleDescChange(dateRange, '2.用例评审', '1.编写用例');
    },

    handleExecCaseDateChange: function (dateRange) {
      this.formItem.execCase = dateRange;
      this.handleDescChange(dateRange, '3.功能测试', '2.用例评审');
    },

    handlePerformanceTestDateChange: function (dateRange) {
      this.formItem.performanceTest = dateRange;
      this.handleDescChange(dateRange, '4.性能测试', '3.功能测试');
    },

    handleDescChange: function (dateRange, changeContent, insertLine) {
      if (dateRange.length > 1 && dateRange[0] !== '' && dateRange[1] !== '') {
        let writeTestCasesStr = "";
        writeTestCasesStr = changeContent + ": " + dateRange[0] + " ~ " + dateRange[1];
        let descLines = this.formItem.Desc.split('\n');
        let insertIndex = descLines.findIndex(line => line.startsWith(insertLine));

        if (insertIndex !== -1) {
          let changeContentIndex = descLines.findIndex((line, index) => line.startsWith(changeContent) && index === insertIndex + 1);

          if (changeContentIndex === -1) {
            insertIndex += 1;
            descLines.splice(insertIndex, 0, writeTestCasesStr);
          } else {
            insertIndex += 1;
            descLines.splice(insertIndex, 1, writeTestCasesStr);
          }
          this.formItem.Desc = [].concat(descLines).join('\n');
        } else {
          let changeContentIndex = descLines.findIndex((line) => line.startsWith(changeContent));

          if (changeContentIndex !== -1) {
            descLines.splice(changeContentIndex, 0, writeTestCasesStr);
          } else {
            descLines.splice(changeContentIndex + 2, 0, writeTestCasesStr);
          }
          this.formItem.Desc = [].concat(descLines).join('\n');
        }
      } else {
        this.formItem.Desc = this.formItem.Desc.split('\n').filter(line => !line.startsWith(changeContent)).join('\n');
      }
    },
  },

  created() {
    this.getProjectCaseReviews()
  },

  mounted() {
    this.getProjectFortestings({ projectID: this.projectID, versionId: 0 })
  },

  watch: {
    projectVersions: function (val, oldValue) {
    }
  },

  components: {
    ProjectTestCaseSelectDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .ivu-modal-header {
    /* border-bottom: 1px solid #e8eaec; */
    padding: 14px 16px;
    line-height: 1;
  }

  #requireNumId .ivu-input {
    height: 26px;
  }

  #requireNumId .ivu-input-prefix i,
  .ivu-input-suffix i {
    font-size: 16px;
    line-height: 26px;
    color: #5578aa;
  }

  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }

  .ivu-form-item {
    margin-bottom: 14px;
    vertical-align: top;
    zoom: 1;
  }

  .ivu-form-item-error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 0px;
    color: #ed4014;
  }

  .error-message {
    font-size: 8px;
    color: red;
  }
}
</style>
