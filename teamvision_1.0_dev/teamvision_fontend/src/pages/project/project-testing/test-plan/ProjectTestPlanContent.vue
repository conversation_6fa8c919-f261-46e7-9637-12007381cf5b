<template>
  <div>
    <div class="PlanDetailView">
      <div class="PlanDetailRoot">
        <Spin v-if="isLoading" fix size="large" style="height:800px;margin: 4px;"></Spin>
        <div v-else class="FlexBox">
          <div class="TitleSection">
            <div class="PlanTitle">
              <div class="NormalStateTag">{{ planInfo.view_data.Status }}</div>
              <div class="NormalStateTag">#{{ planInfo.id }}</div>
              <div class="TitleContent-textEllipsis">
                <div>{{ planInfo.Title }}</div>
              </div>
              <el-dropdown size="mini">
                <div class="StartTestRoot">
                  <router-link class="StartLink" to="" exact @click.native="StatusTestPlan(planInfo, 2)">
                    <div class="StartIcon"></div>开始测试
                  </router-link>
                  <div class="MenuButton"></div>
                </div>
                <el-dropdown-menu slot="dropdown" style="width: 140px;margin-top: 1px;">
                  <el-dropdown-item @click.native="editTestPlan(planInfo)">
                    <Icon type="ios-create" />编辑
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="StatusTestPlan(planInfo, 3)">
                    <Icon type="md-cloud-done" />完成
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="StatusTestPlan(planInfo, 4)">
                    <Icon type="ios-bookmark" />归档
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="StatusTestPlan(planInfo, 5)">
                    <Icon type="ios-pause" />暂停
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="deleteTestPlan(planInfo)">
                    <Icon type="md-trash" />删除
                  </el-dropdown-item>
                  <!--
                  <el-dropdown-item>
                    <Icon type="ios-copy" />复制此计划
                  </el-dropdown-item>
                  -->
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="Root">
            <div class="UserSection">
              <div class="UserDetail">
                <Avatar :size="22">{{ planInfo.view_data.creator_name }}</Avatar>
                <div class="UserName">{{ planInfo.view_data.creator_name }} 创建于 {{ planInfo.CreationTime }}, 预计开始时间:{{
                  planInfo.EstimatedStartTime }}, 完成时间:{{ planInfo.EstimatedFinishTime }}</div>
              </div>
            </div>
            <div class="PlanDescriptionWrapper">
              <div class="PlanDescription">
                <div class="PlanDescriptionTitle">
                  <span class="PassPercentage">描述</span>
                </div>
                <div>{{ planInfo.Desc ? planInfo.Desc : '无' }}</div>
              </div>
              <div class="PlanRequirement">
                <div class="PlanDescriptionTitle">
                  <span class="PassPercentage">关联需求</span>
                </div>
                <div v-if="planInfo.view_data.requirements.length == 0"> 暂无关联需求 </div>
                <div v-else>
                  <li v-for="require in planInfo.view_data.requirements" :key="require.id">
                    <span class="NormalStateTag">#{{ require.id }}</span>
                    <span>
                      <router-link :to="'/project/' + projectID + '/requirement/' + require.id">
                        <span> [{{ require.ViewData.Status }}]</span>
                        <span> [{{ require.ViewData.Owner }}]</span>
                        <span>{{ require.Title }} </span>
                      </router-link>
                    </span>
                  </li>
                </div>
              </div>
            </div>
            <div class="InfoPanel">
              <div class="InfoBarState">
                <div class="PlanCasesStatistics">
                  <span class="PassPercentage">{{ planInfo.view_data.pass_rate }}%</span>
                  测试通过
                  <span class="DotSpan"></span>
                  <span>
                    {{ planInfo.view_data.passed_case }} <i class="AllCases">/ {{ planInfo.view_data.case_count }}</i>
                  </span>
                </div>
                <test-case-process-bar :pass="planInfo.view_data.pass_rate" :reTest="planInfo.view_data.retest_rate"
                  :fail="planInfo.view_data.fail_rate" :blocked="planInfo.view_data.blocked_rate"
                  :norun="planInfo.view_data.norun_rate">
                </test-case-process-bar>
              </div>
              <div class="Line e1smntyr18"></div>
              <ul class="TestsStatistics">
                <li>
                  <p class="StatisticsCount">{{ planInfo.view_data.unpass_case.length }}</p>
                  <p class="StatisticsDescription">未通过任务</p>
                </li>
                <li>
                  <p class="StatisticsCount">0</p>
                  <p class="StatisticsDescription">打开的缺陷</p>
                </li>
                <li>
                  <p class="StatisticsCount">{{ planInfo.view_data.case_coverage }}%</p>
                  <p class="StatisticsDescription">用例库覆盖</p>
                </li>
              </ul>
            </div>
            <div class="Root">
              <div class="PlanMenuPanel">
                <el-tabs style="padding: 8px 16px 0px 16px;">
                  <el-tab-pane>
                    <span slot="label">用例</span>
                    <div class="FilterWrapper">
                      <div class="SelectContainer">
                        <span style="display:inline-flex;align-items: center;width: 100px;">
                          <label-editor-dropdwon-item @updateValue="filterByCaseStatus" :showIcon="false" :itemID="0"
                            :value="-1" :itemList="caseStatus" displayColor displayText="全部状态">
                          </label-editor-dropdwon-item>
                        </span>
                      </div>
                      <Divider type="vertical"></Divider>
                      <div class="SelectContainer">
                        <span style="display:inline-flex;align-items: center;width: 120px;">
                          <label-editor-dropdwon-item :search="true" @updateValue="filterByCaseOwner" :showIcon="false"
                            :itemID="0" :value="0" :itemList="memberDropdownData" displayColor displayText="全部经办人">
                          </label-editor-dropdwon-item>
                        </span>
                      </div>
                      <Divider type="vertical"></Divider>
                      <div class="SelectContainer">
                        <span v-if="showAsginOwner" style="display:inline-flex;align-items: center;width: 80px;">
                          <label-editor-dropdwon-item :search="true" @updateValue="distributionByCaseOwner"
                            :showIcon="false" :itemID="0" :value="0" :itemList="asginMemberDropdownData" displayColor
                            displayText="分配给">
                          </label-editor-dropdwon-item>
                        </span>
                        <el-link v-if="!showAsginOwner" disabled>分配给</el-link>
                      </div>
                      <Divider type="vertical"></Divider>
                      <div class="SelectContainer">
                        <span v-if="planInfo.Status === 2 || planInfo.Status === 1"
                          style="display:inline-flex;align-items: center;width: 80px;">
                          <Button type="text" @click="showCaseTree">添加用例</Button>
                        </span>
                      </div>
                    </div>
                  </el-tab-pane>
                  <!--<el-tab-pane label="活动">问题</el-tab-pane>-->
                  <!--<el-tab-pane label="问题">问题</el-tab-pane>-->
                </el-tabs>
              </div>
              <div class="Root">
                <div
                  style="position: relative; width: inherit; overflow: auto; will-change: transform; direction: ltr;margin-top: 8px;">
                  <Card :bordered="false" dis-hover class="SectionTreeView"
                    :style="{ height: caseTreeContainerHeight + 'px' }">
                    <project-test-plan-case-tree :projectID="projectID" :checkbox="true">
                    </project-test-plan-case-tree>
                  </Card>
                </div>
                <!-- <div class="resize-triggers">
                  <div class="expand-trigger">
                    <div style="width: 716px; height: 560px;"></div>
                  </div>
                  <div class="contract-trigger"></div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <project-test-plan-edit-dialog :projectID="projectID" @editPlan="editPlanSuccess"></project-test-plan-edit-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import TestCaseProcessBar from "../../../../components/common/ProgressBar.vue";
import ProjectTestPlanCaseTree from "./ProjectTestPlanCaseTree.vue";
import { transferTaskStatusDropdownItemData } from "../business-service/ProjectTestCaseUtilService";
import { getMemberDropdownItemData } from "../../business-service/ProjectUtilService";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";
import ProjectTestPlanEditDialog from "./ProjectTestPlanEditDialog.vue";
import { getTestCaseResults, updateTestPlanStatus, } from "../business-service/ProjectTestCaseApiService";

export default {
  name: "projectTestPlanContent",
  props: ["projectID", "version"],

  data() {
    return {
      isLoading: true,
      caseStatus: [],
      showAsginOwner: false,
      memberDropdownData: [],
      asginMemberDropdownData: [],
    };
  },

  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters("testcase", ["testCaseGroupTitle", "caseFilters",]),
    ...mapGetters('testplan', ['filterTestPlanCaseTreeDataByOwner', 'filterTestPlanCaseTreeDataByTestResult']),
    ...mapState(['appBodyWidth', 'appBodyMainHeight']),
    ...mapState('project', ['projectMembers']),
    ...mapState('testplan', ['versionsPlans', 'refreshVersionPlans', 'defTestPlan', 'defTestPlanId', 'checkedTestCase']),

    caseTreeContainerHeight: function () {
      return this.appBodyMainHeight - 345;
    },

    planInfo: function () {
      return this.defTestPlan
    }

  },

  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange", "setCreateDialogShow", "setCaseSelectDialogShow",]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", ["setRefreshPlanCase",]),
    ...mapMutations("testplan", ["setUpdateTestPlanTestCaseButton", "setTestPlanEditDialogShow", 'setRefreshVersionPlans', 'setDefTestPlanId']),
    ...mapActions('testplan', ['getDefTestPlan', 'updateTestPlanCaseOwner', 'delTestPlan']),

    loadCaseStatus: function () {
      getTestCaseResults(7).then((response) => {
        let statusList = transferTaskStatusDropdownItemData(
          response.data.result
        );
        let tempTag = { label: "全部状态", value: -1, color: "" };
        this.caseStatus.push(tempTag);
        this.caseStatus.push(...statusList);
      },
        (response) => { }
      );
    },

    getProjectMembers: function () {
      let tempTag = { label: "全部成员", value: 0, color: "" };
      let tempMember = { label: "未分配", value: 0, color: "" };
      this.memberDropdownData = getMemberDropdownItemData(this.projectMembers);
      this.asginMemberDropdownData = getMemberDropdownItemData(this.projectMembers);
      this.memberDropdownData.splice(0, 0, tempTag);
      this.asginMemberDropdownData.splice(0, 0, tempMember);
      //console.log(this.memberDropdownData);
    },

    filterByCaseStatus: function (newValue, oldValue, id) {
      this.filterTestPlanCaseTreeDataByTestResult(newValue)
      // oldValue = "_" + oldValue;
      // this.$axios.get("/api/project/testplan/" + this.planInfo.id + "/case_tree_info/" + oldValue + "/").then((response) => {
      //   this.setRefreshPlanCase(true);
      // }, (response) => { }
      // );
    },

    filterByCaseOwner: function (newValue, oldValue, id) {
      this.filterTestPlanCaseTreeDataByOwner()
      // this.$axios.get("/api/project/testplan/" + this.planInfo.id + "/case_tree_info/" + oldValue + "/").then((response) => {
      //   this.setRefreshPlanCase(true);
      // }, (response) => { }
      // );
    },

    distributionByCaseOwner: function (tmp, ownerId) {
      this.updateTestPlanCaseOwner(this.planInfo.id, ownerId)
      this.filterByCaseOwner(0, 0, 0);
      this.showAsginOwner = false;

    },

    deleteTestPlan: function (planInfo) {
      this.$Modal.confirm({
        title: "删除确认",
        content: "您即将删除测试计划[" + planInfo.Title + "]",
        onOk: () => {
          this.delTestPlan(planInfo.id)
          this.setDefTestPlanId()
          this.getDefTestPlan(this.defTestPlanId)
        },
        onCancel: () => { },
      });
    },

    StatusTestPlan: function (planInfo, statusType) {
      // console.log(planInfo);
      if (planInfo.Status == 3 && statusType == 2) {
        this.$Message.error({
          content: "归档状态的测试计划不允许再次操作",
          duration: 3,
          closable: true,
        });
        return
      }

      if (planInfo.Status == 4 && planInfo.Status == 2) {
        this.$Message.error({
          content: "完成状态的测试计划不允许修改为测试中",
          duration: 3,
          closable: true,
        });
        return
      }

      let push_router = "/project/" + this.projectID + "/test/test-plan/" + this.planInfo.id + "/tests"
      if (statusType == 2) {
        if (planInfo.Status == 2) {
          this.$router.push({ path: push_router });
        } else {
          this.$Modal.confirm({
            title: "开始确认",
            content: "【" + planInfo.Title + "】状态将变更为【测试中】",
            onOk: () => {
              updateTestPlanStatus(planInfo.id, statusType).then((response) => {
                this.setRefreshVersionPlans(true);
                this.$router.push({ path: push_router, });
              }
              );
            },
            onCancel: () => { },
          });
        }
      } else {
        updateTestPlanStatus(planInfo.id, statusType).then((response) => {
          this.setRefreshVersionPlans(true);
        });
      }
    },

    editTestPlan: function (planInfo) {
      this.setTestPlanEditDialogShow(true);
    },

    showCaseTree: function () {
      this.setCaseSelectDialogShow(true);
      this.setUpdateTestPlanTestCaseButton(true);
    },

    editPlanSuccess: function (response) {
      //
    },

  },

  created: function () {
    //console.log("TestPlanContent.vue for created");
    this.loadCaseStatus();
    if (this.planInfo.id == 0) {
      let planId = this.$route.params.planId
      this.getDefTestPlan(planId)
    }
  },

  mounted: function () {
    this.getProjectMembers();
    //console.log(this.planInfo)
  },

  watch: {
    checkedTestCase() {
      if (this.checkedTestCase.length > 0) {
        this.showAsginOwner = true
      } else {
        this.showAsginOwner = false
      }
    },
    defTestPlanId(oldValue, newValue) {
      this.getDefTestPlan(newValue)
    },
    planInfo: {
      handler(newVal) {
        // 监听 defTestPlan 的变化，数据加载完成后关闭加载状态
        if (newVal.id > 0) {

          this.isLoading = false;
        }
      },
      immediate: true,
    },

  },

  components: {
    TestCaseProcessBar,
    labelEditorDropdwonItem,
    ProjectTestPlanCaseTree,
    ProjectTestPlanEditDialog
  },
};
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.PlanDetailView {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  margin: 4px;
}

.PlanDetailRoot {
  display: flex;
  flex: 1 1 auto;
  padding: 12px;
  background: rgb(243, 244, 246);
}

.FlexBox {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  margin: 0px auto;
}

.TitleSection {
  padding: 6px;
  flex-shrink: 0;
  position: relative;
}

.PlanTitle {
  height: 24px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-size: 20px;
  line-height: 24px;
  color: rgb(32, 45, 64);
}

.NormalStateTag {
  font-size: 12px;
  flex-shrink: 0;
  height: 20px;
  line-height: 20px;
  color: rgb(255, 255, 255);
  font-weight: bold;
  margin-right: 4px;
  //display: flex;
  border-radius: 2px;
  padding: 0px 4px;
  background: rgb(0, 102, 255);
}

.TitleContent-textEllipsis {
  position: relative;
  -webkit-box-flex: 1;
  flex-grow: 1;
  margin-right: 9px;
}

.StartTestRoot {
  width: 140px;
  height: 32px;
  box-shadow: rgba(0, 28, 71, 0.05) 0px 4px 12px -2px;
  display: flex;
  position: relative;
  background: rgb(0, 102, 255);
  border-radius: 3px;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s,
    box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.StartLink {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding-left: 16px;
  font-size: 16px;
  color: rgb(255, 255, 255) !important;
  flex: 1 1 auto;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.StartIcon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background: url(data:image/svg+xml;base64,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) center center / cover no-repeat;
}

.MenuButton {
  height: 100%;
  cursor: pointer;
  position: relative;
  flex: 0 0 36px;
  background: url(data:image/svg+xml;base64,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) center center / 16px no-repeat;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  border-radius: 0px 3px 3px 0px;
}

.Root {
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.PlanDescriptionWrapper {
  height: auto;
  // max-height: none;
  // position: relative;
  // overflow: hidden;
  // flex-shrink: 0;
  // width: 100%;
  // box-sizing: border-box;
  // -webkit-box-align: center;
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.PlanDescription {
  padding: 12px;
  height: 100%;
  font-size: 12px;
  flex: 1;
  margin-right: 2px;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  background: rgb(255, 255, 255);
}

.PlanRequirement {
  padding: 12px;
  height: 100%;
  font-size: 12px;
  flex: 1;
  margin-left: 2px;
  //line-height: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  background: rgb(255, 255, 255);
}

.PlanDescriptionTitle {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-weight: bold;
  line-height: 16px;
  font-size: 12px;
  //margin-bottom: 8px;
}

.UserSection {
  padding: 6px;
  font-size: 12px;
  flex-shrink: 0;
  display: flex;
  height: 30px;
  -webkit-box-align: center;
  align-items: center;
}

.UserDetail {
  font-size: 13px;
  color: rgb(145, 153, 163);
  line-height: 16px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  margin-right: 56px;
}

.AvatarWrapper {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  width: 24px;
  height: 24px;
  color: rgb(255, 255, 255);
  font-size: 12px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 1px 3px -1px,
    rgba(0, 0, 0, 0.1) 0px 2px 8px -1px;
  border-radius: 100%;
  background: url(https://coding-net-production-static-ci.codehub.cn/WM-TEXT-AVATAR-rcTfTjMRDQUTanBVLMCV.jpg?imageView2/1/w/48/h/48) center center / cover no-repeat rgb(255, 255, 255);
}

.RoleIcon {
  display: none;
  position: absolute;
  width: 14px;
  height: 14px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 1px 2px 0px,
    rgba(0, 0, 0, 0.1) 0px 2px 10px -1px;
  border-radius: 100%;
  background: center center / 12px no-repeat rgb(255, 255, 255);
}

.UserName {
  color: rgb(32, 45, 64);
  margin: 0px 4px 0px 10px;
}

.InfoPanel {
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 6px -1px,
    rgba(0, 0, 0, 0.05) 0px 6px 28px -3px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding: 12px;
  background: rgb(255, 255, 255);
  border-radius: 3px;
  margin-top: 4px;
}

.InfoBarState {
  -webkit-box-flex: 1;
  flex-grow: 1;
}

.PlanCasesStatistics {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  font-weight: bold;
  line-height: 16px;
  font-size: 12px;
  margin-bottom: 16px;
}

.PassPercentage {
  font-size: 14px;
  padding-right: 4px;
}

.DotSpan {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  margin: 0px 16px;
  background: rgb(201, 207, 215);
}

.TestedCases {
  font-size: 14px;
  padding-left: 8px;
}

.AllCases {
  color: rgb(145, 153, 163);
}

.BarWrapper-TestStatusBarStyle {
  display: inline-flex;
  position: relative;
  height: 16px;
  width: 100%;
}

.Bar {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  display: inline-flex;
  min-width: 160px;
  box-sizing: border-box;
  margin-right: -2px;
  border-radius: 3px;
  background: rgb(255, 255, 255);
}

.child {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.Bar li:first-of-type {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.BarSection-passStyle-HoverStyle {
  width: 100%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background: rgb(45, 211, 111);
}

.BarSection-retestStyle-HoverStyle {
  width: 2%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background: rgb(255, 191, 0);
}

.BarSection-blockedStyle-HoverStyle {
  width: 2%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background: rgb(108, 110, 150);
}

.BarSection-failedStyle-HoverStyle {
  width: 8%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background: rgb(246, 77, 62);
}

.BarSection-HoverStyle {
  width: 61%;
  height: 100%;
  cursor: pointer;
  margin: 0px 2px 0px 0px;
  background: rgb(222, 223, 229);
  border-width: 0px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Line {
  width: 1px;
  height: 56px;
  margin: 0px 32px;
  background: rgba(0, 0, 0, 0.07);
}

.TestsStatistics {
  display: flex;
  text-align: center;
  -webkit-box-pack: justify;
  justify-content: space-between;
  flex: 0 0 240px;
}

.StatisticsCount {
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
  margin-bottom: 8px;
}

.StatisticsDescription {
  font-size: 12px;
  line-height: 16px;
  color: rgb(145, 153, 163);
}

.Root {
  -webkit-box-flex: 1;
  flex-grow: 1;
  min-height: 0px;
  display: flex;
  flex-direction: column;
}

.PlanMenuPanel {
  flex-shrink: 0;
  width: 100%;
  position: relative;
  margin-top: 4px;
  border-radius: 3px;
  background: rgb(255, 255, 255);
}

.Wrapper-wrapperClassName {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  padding: 0px 16px;
}

.FilterWrapper {
  height: 48px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  padding: 0px 0px;
  background: rgb(255, 255, 255);
}

.feie a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.feie *,
.feie ::before,
.feie ::after {
  box-sizing: content-box;
}

.feie a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.Scale-scaleButtonStyle {
  display: block;
  width: 24px;
  height: 24px;
  cursor: pointer;
  background-color: rgb(255, 255, 255);
  background-size: 16px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU0LjEgKDc2NDkwKSAtIGh0dHBzOi8vc2tldGNoYXBwLmNvbSAtLT4KICAgIDx0aXRsZT5tYXhpbWl6ZTwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJTeW1ib2xzIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iaWNvbnMvbWF4aW1pemUtcyI+CiAgICAgICAgICAgIDxnIGlkPSJtYXhpbWl6ZSI+CiAgICAgICAgICAgICAgICA8ZyBpZD0iR3JvdXAtNSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNS4wMDAwMDAsIDExLjAwMDAwMCkgcm90YXRlKC0xODAuMDAwMDAwKSB0cmFuc2xhdGUoLTUuMDAwMDAwLCAtMTEuMDAwMDAwKSB0cmFuc2xhdGUoMi4wMDAwMDAsIDguMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTAsMCBMNS41LDAgQzUuNzc2MTQyMzcsLTUuMDcyNjUzMTNlLTE3IDYsMC4yMjM4NTc2MjUgNiwwLjUgTDYsNiBMMCwwIFoiIGlkPSJSZWN0YW5nbGUtNSIgZmlsbD0iI0JFQzZEMSI+PC9wYXRoPgogICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik00LjAxNjc2MywyLjAzNDE5OTM4IEwxLjAzNTgyODEzLDUuMDE1MTM0MjUiIGlkPSJQYXRoLTIiIHN0cm9rZT0iI0JFQzZEMSIgc3Ryb2tlLXdpZHRoPSIyIj48L3BhdGg+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8ZyBpZD0iR3JvdXAtNiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTEuMDAwMDAwLCA1LjAwMDAwMCkgcm90YXRlKC0xODAuMDAwMDAwKSB0cmFuc2xhdGUoLTExLjAwMDAwMCwgLTUuMDAwMDAwKSB0cmFuc2xhdGUoOC4wMDAwMDAsIDIuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTAsMCBMNiw2IEwwLjUsNiBDMC4yMjM4NTc2MjUsNiAzLjM4MTc2ODc2ZS0xNyw1Ljc3NjE0MjM3IDAsNS41IEwwLDAgWiIgaWQ9IlJlY3RhbmdsZS0yMSIgZmlsbD0iI0JFQzZEMSI+PC9wYXRoPgogICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0yLjAzMDg4NTQyLDQuMDQ0NTgzMzMgTDQuOTg1MzcxOTUsMS4wODExODA2IiBpZD0iUGF0aC0zIiBzdHJva2U9IiNCRUM2RDEiIHN0cm9rZS13aWR0aD0iMiI+PC9wYXRoPgogICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=);
  position: absolute;
  right: 16px;
  top: 16px;
  border-radius: 2px;
  background-repeat: no-repeat;
  background-position: center center;
  transition: background 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TabItem-TabItem {
  display: flex;
  font-size: 15px;
  line-height: 16px;
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
  color: rgb(32, 45, 64);
  padding: 16px 0px;
  border-bottom: 2px solid rgb(0, 102, 255);
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.TabItem {
  display: flex;
  font-size: 15px;
  line-height: 16px;
  color: rgb(145, 153, 163);
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  padding: 16px 0px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Wrapper-wrapperClassName li+li {
  margin-left: 40px;
}


.PickerWrapper {
  position: relative;
  height: 32px;
  display: inline-flex;
}

.Selection {
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 8px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.FliterUserWrapper {
  flex: 0 0 auto;
}

.SelectContainer {
  position: relative;
  flex-shrink: 0;
}

.Selection {
  box-sizing: border-box;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  position: relative;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  user-select: none;
  padding: 6px 12px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.SelectionDetail-textEllipsis {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.PickerWrapper {
  position: relative;
  height: 32px;
  display: inline-flex;
}

.Selection {
  min-width: 84px;
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(32, 45, 64);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 8px;
  border-radius: 2px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.FilterLine {
  width: 1px;
  height: 16px;
  flex: 0 0 1px;
  background: rgba(0, 0, 0, 0.07);
  margin: 0px 24px 0px 8px;
}

.Root {
  -webkit-box-flex: 0;
  flex-grow: 0;
  display: flex;
  width: 100%;
  position: relative;
}

.root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.InputDom-hasIconInput-withoutBorder {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  height: 36px;
  line-height: 24px;
  font-size: 14px;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-shadow: none !important;
  outline: none;
  padding: 6px 12px 6px 24px;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
}

.InputIcon {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  display: block;
  background-image: url(data:image/svg+xml;base64,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);
  background-size: 16px;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background-repeat: no-repeat;
  background-position: center center;
}

.ClearButton {
  -webkit-box-flex: 1;
  flex-grow: 1;
  flex-shrink: 0;
  width: 16px;
  display: none;
  cursor: pointer;
  opacity: 0.5;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjMgKDUxMTY3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9pbnB1dC1jbGVhcjwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJUZXN0LUNhc2UtLS1DcmVhdGUtJmFtcDstRWRpdCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41Ij4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZct5pCc57SiIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMjAyLjAwMDAwMCwgLTIwLjAwMDAwMCkiIGZpbGw9IiNBNUIxQzIiIGZpbGwtcnVsZT0ibm9uemVybyI+CiAgICAgICAgICAgIDxnIGlkPSJsZWZ0LXNpZGUiPgogICAgICAgICAgICAgICAgPGcgaWQ9ImxlZnRzaWRlLWhlYWQiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJpY29ucy9pbnB1dC1jbGVhciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjAyLjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxIEM0LjE1LDEgMSw0LjE1IDEsOCBDMSwxMS44NSA0LjE1LDE1IDgsMTUgQzExLjg1LDE1IDE1LDExLjg1IDE1LDggQzE1LDQuMTUgMTEuODUsMSA4LDEgWiBNMTEuMDYyNSw5LjgzNzUgTDkuODM3NSwxMS4wNjI1IEw4LDkuMjI1IEw2LjE2MjUsMTEuMDYyNSBMNC45Mzc1LDkuODM3NSBMNi43NzUsOCBMNC45Mzc1LDYuMTYyNSBMNi4xNjI1LDQuOTM3NSBMOCw2Ljc3NSBMOS44Mzc1LDQuOTM3NSBMMTEuMDYyNSw2LjE2MjUgTDkuMjI1LDggTDExLjA2MjUsOS44Mzc1IFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+) center center / 16px 16px no-repeat;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.PickerWrapper {
  flex-shrink: 0;
  position: relative;
  height: 32px;
}

.Selection[disabled] {
  font-weight: normal;
  opacity: 0.3;
  color: rgb(32, 45, 64);
  pointer-events: none;
}

.Selection {
  font-weight: normal;
  box-sizing: border-box;
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  color: rgb(7, 95, 184);
  font-size: 13px;
  line-height: 16px;
  cursor: pointer;
  user-select: none;
  padding: 0px 30px 0px 7px;
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-image: initial;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.Name-textEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

li {
  list-style: none;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 8px;
}
</style>
