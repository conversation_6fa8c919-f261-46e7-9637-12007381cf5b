<template>
  <div class="TestPlanExec" :style="{ height: containerHeight + 'px', overflow: 'scroll' }">
    <project-test-plan-execute-case-list :projectID="projectID" :planId="planId"></project-test-plan-execute-case-list>
    <project-test-plan-execute-case-detail :projectID="projectID"></project-test-plan-execute-case-detail>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import ProjectTestPlanExecuteCaseList from './ProjectTestPlanExecuteCaseList.vue'
import ProjectTestPlanExecuteCaseDetail from './ProjectTestPlanExecuteCaseDetail.vue'

export default {
  name: 'ProjectTestPlanExecute',
  props: ['projectID', 'planId'],
  data() {
    return {
    }
  },

  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapState(['appBodyHeight', 'appBodyWidth']),
    ...mapState('testplan', ['defTestPlan']),

    containerHeight: function () {
      return this.appBodyHeight - 10
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    versionId: function () {
      if (this.$route.params.version) {
        return this.$route.params.version
      }
      return 0
    },

  },
  methods: {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),
    ...mapMutations(['setItemViewMode']),
    ...mapActions('testplan', ['getDefTestPlan']),

    onCheckNode: function () {
    }

  },

  created: function () {
    //console.log("ProjectTestPlanExcute.vue for created")
    if (this.defTestPlan.id == 0) {
      this.planId = this.$route.params.planId
      this.getDefTestPlan(this.planId)
    }
  },

  mounted: function () {
  },

  watch: {
    versionId: function (value) {
    }

  },

  components: {
    ProjectTestPlanExecuteCaseList,
    ProjectTestPlanExecuteCaseDetail
  }
}
</script>

<!-- Add"scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.TestPlanExec {
  display: flex;
  /*-webkit-box-align: stretch;*/
  /*align-items: stretch;*/
  height: 100%;
  background-color: rgb(243, 244, 246);
}
</style>
