<template>
  <div :style="'padding: 0px 10px 0px 10px;' + containerHeight + 'px;'">
    <Card v-if="caseViewType === false" :padding="0" :bordered="false" dis-hover style="background-color: #f5f7f8">
      <div class="SectionTitle">
        <h1 class="css-1z0o09y-heading">全部用例</h1>
      </div>
      <div slot="extra">
        <span class="SectionControls">
          <span class="css-1avq18a-actionGroup">
            <Dropdown @on-click="filterCaseByTag">
              <span>用例等级
                <Icon type="ios-arrow-down"></Icon>
              </span>
              <DropdownMenu slot="list">
                <DropdownItem v-for="item in testCaseTagList" :key="item.id" :name="item.TagValue">
                  <span style="display: inline-flex;align-items: center;width: 20px;">
                    <Icon :color="item.TagColor" type="md-disc" />
                  </span>
                  <span style="display: inline-flex;align-items: center;width: 40px;">
                    {{ item.TagName }}
                  </span>
                  <span v-if="filterTagIds.indexOf(item.TagValue) > -1"
                    style="display: inline-flex;align-items: center;width: 20px;">
                    <Icon type="md-checkmark" />
                  </span>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
          <Divider type="vertical" />
          <span class="css-1avq18a-actionGroup">
            <Radio v-model="filterAdmittanceTest" label="1" @click.native.prevent="filterCaseByAccessTest()">准入用例
            </Radio>
          </span>
          <Divider type="vertical" />
          <span class="css-1avq18a-actionGroup">
            <Input size="small" suffix="ios-search" search @on-search="filterCaseGroup" placeholder="搜索用例"></Input>
          </span>
          <Divider type="vertical" />
          <span class="css-1avq18a-actionGroup">
            <span class="css-pk8ir0-treeControls">
              <span @click="refreshCaseTree" class="testCasePanelButton">
                <Tooltip placement="bottom" content="刷新" theme="light">
                  <Icon type="ios-refresh-circle-outline" :size="18" />
                </Tooltip>
              </span>
              <span style="display: flex;align-items: center;">
                <Divider type="vertical" />
              </span>
              <span class="testCasePanelButton">
                <Dropdown trigger="click" style="margin-left: 8px">
                  <Tooltip placement="bottom" content="导出" theme="light">
                    <Icon type="ios-download-outline" :size="20" />
                  </Tooltip>
                  <DropdownMenu slot="list">
                    <DropdownItem @click.native="exportCaseFile(8)">Xmind 8</DropdownItem>
                    <DropdownItem @click.native="exportCaseFile(2020)">Xmind 2020以上版本</DropdownItem>
                    <DropdownItem @click.native="exportPytestFile">Pytest</DropdownItem>
                    <DropdownItem @click.native="exportNoseFile">GAT</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </span>
            </span>
            <Divider type="vertical" />
          </span>
          <span class="css-1avq18a-actionGroup" @click="changeCaseView(true)" style="cursor: pointer;">
            <Icon type="md-sync" :size="20" /> 脑图视图
          </span>
        </span>
      </div>
    </Card>

    <Card v-if="caseViewType === false" :bordered="false" dis-hover class="css-142qn3c-SectionTreeView"
      :style="{ minHeight: '280px', background: '#f5f7f8', height: containerHeight - 72 + 'px' }">
      <project-test-case-tree :projectID="projectID" :groupID="groupID" :caseID="caseID" :checkbox="false"
        :selectCaseTree="false">
      </project-test-case-tree>
    </Card>
    <div v-if="caseViewType === true" class="css-142qn3c-SectionTreeView"
      :style="{ height: appBodyHeight + 'px; width:100%' }">
      <mind-case :projectID="projectID" :mindFileID="groupID"></mind-case>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import ProjectTestCaseTree from "./ProjectTestCaseTree.vue";
import { export2XmindFile, exportPytestFile, } from "../business-service/ProjectTestCaseApiService";
import labelEditorInput from "../../../../components/common/LabelEditor-Input.vue";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";
import mindCase from "./xmind/mind.vue";

export default {
  name: "projectTestCaseContent",
  props: ["projectID", "groupID", "caseID"],
  data() {
    return {
      dropdownEditorTags: [],
      filterTagIds: [],
      caseViewType: false,
      filterAdmittanceTest: false,
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(['appBodyHeight', "appBodyWidth"]),
    ...mapState('testcase', ['testCaseTagList']),
    ...mapGetters("testcase", ["showCaseID", "testCaseViewMode"]),

    containerHeight: function () {
      return this.appBodyHeight
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", [
      "setCaseFilters",
      "setRefreshCase",
      "setShowCaseID",
      "setTestCaseViewMode",
    ]),

    filterCaseGroup: function (keyword) {
      let value = { type: 1, filters: [keyword] };
      this.setCaseFilters(value);
    },

    filterCaseByTag: function (value) {
      let tagID = parseInt(value);
      let tagIndex = this.filterTagIds.indexOf(tagID);
      if (tagIndex > -1) {
        this.filterTagIds.splice(tagIndex, 1);
      } else {
        //console.log(tagID);
        this.filterTagIds.push(tagID);
      }
      let filters = { type: 2, filters: this.filterTagIds };
      this.setCaseFilters(filters);
    },

    filterCaseByAccessTest: function () {
      this.filterAdmittanceTest = !this.filterAdmittanceTest;
      if (this.filterAdmittanceTest == true) {
        let filters = { type: 3, filters: [1] };
        this.setCaseFilters(filters);
      } else {
        let filters = { type: 3, filters: [] };
        this.setCaseFilters(filters);
      }
    },

    refreshCaseTree: function () {
      this.setRefreshCase(true);
    },

    loadCaseTags: function () {
      //console.log('this.testCaseTagList=============', this.testCaseTagList)
      for (let i = 0; i < this.testCaseTagList.length; i++) {
        let tempTag = { label: "", value: 0, color: "" };
        tempTag.label = this.testCaseTagList[i].TagName;
        tempTag.value = this.testCaseTagList[i].TagValue;
        tempTag.color = this.testCaseTagList[i].TagColor;
        this.dropdownEditorTags.push(tempTag);
      }
    },

    exportCaseFile: function (version) {
      export2XmindFile(this.groupID, "测试用例", version);
    },

    exportPytestFile: function () {
      exportPytestFile(this.groupID)
    },

    changeCaseView: function (value) {
      this.caseViewType = value;
      this.setTestCaseViewMode("mindMap");
    },
  },

  created: function () {
    this.loadCaseTags();
    this.caseViewType = false;
  },

  mounted: function () { },
  watch: {
    versionID: function (value) { },

    caseID: function (value) {
      if (value && value > 0) {
        this.setShowCaseID(value);
      }
    },

    groupID: function (value) {
      this.setTestCaseViewMode("");
      this.caseViewType = false;
    },

    testCaseViewMode: function (value) {
      if (value === "tree") {
        this.caseViewType = false;
      }
      if (value === "mindMap") {
        this.caseViewType = true;
      }
    },
  },

  components: {
    ProjectTestCaseTree,
    labelEditorInput,
    labelEditorDropdwonItem,
    mindCase,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.css-1z0o09y-heading {
  font-size: 20px;
  font-weight: bold;
}

.css-khpljl-SectionPanelTop {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 1px 8px 0px;
}

.css-1eb5082-Root {
  -webkit-box-flex: 0;
  flex-grow: 0;
  display: flex;
  width: 100%;
  position: relative;
}

.css-1udk0wm-root {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.css-tudja-InputWrapper {
  position: relative;
  flex: 1 1 0%;
}

.css-1n0v24l-InputDom-mediumSize-hasIconInput-withoutBorder {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  box-shadow: none !important;
  outline: none;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  overflow: hidden;
  padding: 6px 12px 6px 24px;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
}

.css-1avq18a-actionGroup .ivu-input {
  display: inline-block;
  width: 100%;
  height: 32px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 14px;
  border: none;
  border-radius: 0px;
  color: #5578aa;
  background-color: inherit;
  background-image: none;
  position: relative;
  cursor: text;
  -webkit-transition: border 0.2s ease-in-out, background 0.2s ease-in-out,
    -webkit-box-shadow 0.2s ease-in-out;
  transition: border 0.2s ease-in-out, background 0.2s ease-in-out,
    -webkit-box-shadow 0.2s ease-in-out;
  transition: border 0.2s ease-in-out, background 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
  transition: border 0.2s ease-in-out, background 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
}

.css-tvc7ix-ClearButton {
  -webkit-box-flex: 1;
  flex-grow: 1;
  flex-shrink: 0;
  width: 16px;
  display: none;
  cursor: pointer;
  opacity: 0.5;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjMgKDUxMTY3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9pbnB1dC1jbGVhcjwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJUZXN0LUNhc2UtLS1DcmVhdGUtJmFtcDstRWRpdCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCIgb3BhY2l0eT0iMC41Ij4KICAgICAgICA8ZyBpZD0i55So5L6L566h55CGLeWIm+W7uueUqOS+iy3pgInmi6nmqKHlnZct5pCc57SiIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMjAyLjAwMDAwMCwgLTIwLjAwMDAwMCkiIGZpbGw9IiNBNUIxQzIiIGZpbGwtcnVsZT0ibm9uemVybyI+CiAgICAgICAgICAgIDxnIGlkPSJsZWZ0LXNpZGUiPgogICAgICAgICAgICAgICAgPGcgaWQ9ImxlZnRzaWRlLWhlYWQiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJpY29ucy9pbnB1dC1jbGVhciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjAyLjAwMDAwMCwgMjAuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJTaGFwZSI+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxIEM0LjE1LDEgMSw0LjE1IDEsOCBDMSwxMS44NSA0LjE1LDE1IDgsMTUgQzExLjg1LDE1IDE1LDExLjg1IDE1LDggQzE1LDQuMTUgMTEuODUsMSA4LDEgWiBNMTEuMDYyNSw5LjgzNzUgTDkuODM3NSwxMS4wNjI1IEw4LDkuMjI1IEw2LjE2MjUsMTEuMDYyNSBMNC45Mzc1LDkuODM3NSBMNi43NzUsOCBMNC45Mzc1LDYuMTYyNSBMNi4xNjI1LDQuOTM3NSBMOCw2Ljc3NSBMOS44Mzc1LDQuOTM3NSBMMTEuMDYyNSw2LjE2MjUgTDkuMjI1LDggTDExLjA2MjUsOS44Mzc1IFoiPjwvcGF0aD4KICAgICAgICAgICAgICAgICAgICAgICAgPC9nPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+) center center / 16px 16px no-repeat;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
}

.css-d4ujag-InputIcon {
  position: absolute;
  left: 0px;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  display: block;
  background-image: url(data:image/svg+xml;base64,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);
  background-size: 16px;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  background-repeat: no-repeat;
  background-position: center center;
}

.SectionTitle {
  display: flex;
  flex-shrink: 0;
  -webkit-box-align: center;
  align-items: center;
  height: 72px;
  padding: 0px 24px;
}

.SectionControls {
  display: flex;
  flex-shrink: 0;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 40px;
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 0px;
}

.css-1r79qah-RootSection {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 100%;
  cursor: default;
  flex: 1 0 76px;
  padding: 0px 24px;
}

.css-4ake75-folderIcon-rootFolderIcon-SectionIcon {
  display: block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-1avq18a-actionGroup {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  padding: 0px 8px;
}

.css-pk8ir0-treeControls {
  display: flex;
}

.css-1on40hp-collapseButton {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUxLjMgKDU3NTQ0KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9jb2xsYXBzZS1mb2xkZXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iU3ltYm9scyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Imljb25zL2NvbGxhcHNlLWZvbGRlciI+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTgsMTQgTDgsMTIgTDE0LDEyIEwxNCw1IEwxMCw1IEM5LjcsNSA5LjUsNC45IDkuMyw0LjcgTDYuNiwyIEw0LDIgTDQsNCBMMiw0IEwyLDEgQzIsMC40IDIuNCwwIDMsMCBMNywwIEM3LjMsMCA3LjUsMC4xIDcuNywwLjMgTDEwLjQsMyBMMTUsMyBDMTUuNiwzIDE2LDMuNCAxNiw0IEwxNiwxMyBDMTYsMTMuNiAxNS42LDE0IDE1LDE0IEw4LDE0IFoiIGlkPSJDb21iaW5lZC1TaGFwZSIgZmlsbD0iIzkxOTlBMyIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICAgICAgICAgICAgICA8cG9seWxpbmUgaWQ9IlNoYXBlIiBzdHJva2U9IiM5MTk5QTMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzLjAwMDAwMCwgOC41MDAwMDApIHNjYWxlKDEsIC0xKSB0cmFuc2xhdGUoLTMuMDAwMDAwLCAtOC41MDAwMDApICIgcG9pbnRzPSI1IDkuNSAzIDcuNSAxIDkuNSI+PC9wb2x5bGluZT4KICAgICAgICAgICAgICAgIDxwb2x5bGluZSBpZD0iU2hhcGUiIHN0cm9rZT0iIzkxOTlBMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHBvaW50cz0iNSAxNC41IDMgMTIuNSAxIDE0LjUiPjwvcG9seWxpbmU+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.icon-button {
  position: relative;
  display: block;
  width: 32px;
  height: 32px;
  background-size: 16px 16px;
  cursor: pointer;
  border-radius: 3px;
  background-position: center center;
  background-repeat: no-repeat;
}

.css-b3x8u4-expandButton {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUxLjMgKDU3NTQ0KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5pY29ucy9leHBhbmQtZm9sZGVyPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlN5bWJvbHMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJpY29ucy9leHBhbmQtZm9sZGVyIj4KICAgICAgICAgICAgPGc+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNOCwxNCBMOCwxMiBMMTQsMTIgTDE0LDUgTDEwLDUgQzkuNyw1IDkuNSw0LjkgOS4zLDQuNyBMNi42LDIgTDQsMiBMNCw0IEwyLDQgTDIsMSBDMiwwLjQgMi40LDAgMywwIEw3LDAgQzcuMywwIDcuNSwwLjEgNy43LDAuMyBMMTAuNCwzIEwxNSwzIEMxNS42LDMgMTYsMy40IDE2LDQgTDE2LDEzIEMxNiwxMy42IDE1LjYsMTQgMTUsMTQgTDgsMTQgWiIgaWQ9IkNvbWJpbmVkLVNoYXBlIiBmaWxsPSIjOTE5OUEzIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgICAgICAgICAgICAgIDxwb2x5bGluZSBpZD0iU2hhcGUiIHN0cm9rZT0iIzkxOTlBMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHBvaW50cz0iNSA5IDMgNyAxIDkiPjwvcG9seWxpbmU+CiAgICAgICAgICAgICAgICA8cG9seWxpbmUgaWQ9IlNoYXBlIiBzdHJva2U9IiM5MTk5QTMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzLjAwMDAwMCwgMTQuMDAwMDAwKSBzY2FsZSgxLCAtMSkgdHJhbnNsYXRlKC0zLjAwMDAwMCwgLTE0LjAwMDAwMCkgIiBwb2ludHM9IjUgMTUgMyAxMyAxIDE1Ij48L3BvbHlsaW5lPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=);
}

.css-1avq18a-actionGroup:not(:first-of-type) {
  margin-left: 1px;
}

.css-1avq18a-actionGroup {
  position: relative;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 32px;
  padding: 0px 8px;
}

.css-ppeaod-createFirstClassSection {
  width: 24px;
  height: 24px;
  box-shadow: rgba(19, 111, 220, 0.1) 0px 4px 8px 0px,
    rgba(16, 113, 211, 0.1) 0px 4px 18px 0px;
  cursor: pointer;
  margin: 0px 8px;
  background: url(data:image/svg+xml;base64,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) center center no-repeat rgb(50, 190, 119);
  border-radius: 50%;
  transition: background-color 0.3s ease 0s;
}

.css-142qn3c-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-1wn0gh0-sectionBranch {
  border-radius: 2px;
}

.css-15t0tot-ChildSections {
  position: relative;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-1bcfq0a-folderIcon-SectionIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-1d4bcnu-SectionName-SectionName {
  margin-left: 8px;
  font-size: 13px;
  /*font-weight: bold;*/
}

.css-hf9rxs-SectionName-SectionName-textEllipsis {
  min-width: 0px;
  font-weight: bold;
  color: rgb(0, 102, 255);
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 8px;
  overflow: hidden;
}

.css-1kjszj6-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 8px;
  overflow: hidden;
}

.css-142qn3c-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 36px;
  cursor: pointer;
  padding: 4px;
}

.case-id {
  display: flex;
  font-size: 18px;
}
</style>
