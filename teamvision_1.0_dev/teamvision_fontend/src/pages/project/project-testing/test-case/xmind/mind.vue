<template>
  <div style="height: 100%" class="case-mind">
    <div class="minder-header">
      <div v-if="isLoading" class="header-left">
        <Spin>
          <Icon type="ios-loading" size=20 class="demo-spin-icon-load"></Icon> 保存中.....
        </Spin>
      </div>
      <div v-else class="header-left">
        <div class="minder-head-item-2">
          <div>
          </div>
          <div>
            <Dropdown trigger="click" @on-click="setTemplate" style="margin-left: 20px">
              视图 <Icon type="ios-arrow-down"></Icon>
              <DropdownMenu slot="list">
                <DropdownItem name="default">逻辑结构图</DropdownItem>
                <DropdownItem name="right">思维导图</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
        <!-- <div class="minder-head-item-2">
          <div>
            <Icon type="md-undo" size="18" @click="unDo" />
          </div>
          <div>
            <Icon type="md-redo" size="18" @click="reDo" />
          </div>
        </div> -->
        <div class="minder-head-item-2">
          <div @click="addNode('AppendChildNode')">
            <Icon type="ios-add-circle-outline" size="16" /> 插入子节点[Tab|Insert]
          </div>
          <div @click="addNode('AppendSiblingNode')">
            <Icon type="ios-add-circle-outline" size="16" /> 插入同级节点[Enter]
          </div>
        </div>
        <!-- <div class="minder-head-item">
          <div @click="addNode('AppendParentNode')">
            <Icon type="ios-add-circle-outline" size="16" />插入父节点
          </div>
          <div>
            <Icon type="" size="16" />
            <span></span>
          </div>
        </div> -->
        <div class="minder-head-item-2">
          <div>
            <Icon type="ios-create-outline" size="16" />
            <span @click="editNode">编辑[Edit]</span>
          </div>
          <div>
            <Icon type="ios-trash-outline" size="16" />
            <span @click="removeNode">删除[Del]</span>
          </div>
        </div>
        <!-- <Dropdown @on-click="addNode" transfer>
            <span>
              <Icon type="ios-add" :size="24" />
            </span>
            <DropdownMenu slot="list">
              <DropdownItem name="AppendChildNode">
                <Icon type="ios-disc-outline" /> 子节点 (Tab|Insert)
              </DropdownItem>
              <DropdownItem name="AppendParentNode">
                <Icon type="ios-disc-outline" /> 父节点
              </DropdownItem>
              <DropdownItem name="AppendSiblingNode">
                <Icon type="ios-disc-outline" /> 同级节点 (Enter)
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span> -->
        <!--<span class="minder-head-item">-->
        <!--<Dropdown @on-click="setTheme" transfer>-->
        <!--<span>-->
        <!--<Tooltip content="设置主题" transfer>-->
        <!--<Icon type="ios-color-wand-outline" :size="24"/>-->
        <!--</Tooltip>-->
        <!--</span>-->
        <!--<DropdownMenu slot="list">-->
        <!--<Row style="padding: 5px;" v-for="rowItem in themeList " :key="rowItem[0].name">-->
        <!--<Col span="12">-->
        <!--<DropdownItem :name="rowItem[0].name">-->
        <!--<div-->
        <!--style="height: 30px;width: 70px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">-->
        <!--{{ rowItem[0].title }}-->
        <!--</div>-->
        <!--</DropdownItem>-->
        <!--</Col>-->
        <!--<Col span="12">-->
        <!--<DropdownItem :name="rowItem[1].name">-->
        <!--<div-->
        <!--style="height: 30px;width: 70px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">-->
        <!--{{ rowItem[1].title }}-->
        <!--</div>-->
        <!--</DropdownItem>-->
        <!--</Col>-->
        <!--</Row>-->
        <!--</DropdownMenu>-->
        <!--</Dropdown>-->
        <!--</span>-->
        <!--<span class="minder-head-item">-->
        <!--<Dropdown @on-click="setTemplate" transfer>-->
        <!--<span>-->
        <!--<Tooltip content="设置模板" transfer>-->
        <!--<Icon type="ios-snow-outline" :size="24"/>-->
        <!--</Tooltip>-->
        <!--</span>-->
        <!--<DropdownMenu slot="list">-->
        <!--<DropdownItem v-for="rowItem in templateList " :name="rowItem.name" :key="rowItem.name">-->
        <!--<div-->
        <!--style="height: 30px;padding:5px;border-radius: 15px;background: rgb(233, 223, 152);">-->
        <!--{{ rowItem.title }}-->
        <!--</div>-->
        <!--</DropdownItem>-->
        <!--</DropdownMenu>-->
        <!--</Dropdown>-->
        <!--</span>-->
        <!--<span @click="resetLayout" class="minder-head-item">-->
        <!--<Tooltip content="重置布局(Ctrl+Shift+l)" transfer>-->
        <!--<Icon type="ios-redo-outline" :size="24"/>-->
        <!--</Tooltip>-->
        <!--</span>-->
        <div class="minder-head-item"> 50%
          <Slider :value="100" transfer show-tip="hover" :max="150" :min="50" :step="10" @on-change="zoomMindmap"
            style="display: inline-block;height: 24px;width: 100px;margin-left: 10px;margin-right: 10px;">
          </Slider> 150%
        </div>
        <div class="minder-head-item">
          <tooltip content="优先级" transfer>
            <span>
              <span @click="addPriority(0)" style="cursor: pointer;">
                <Avatar icon="md-close" style="color: #f56a00;background-color: white; border: 1px solid black" />
              </span>
              <span @click="addPriority(1)" style="cursor: pointer;">
                <Avatar style="color:white;background-color: rgb(255, 18, 0);">1</Avatar>
              </span>
              <span @click="addPriority(2)" style="cursor: pointer;">
                <Avatar style="color:white;background-color: rgb(1, 70, 127);">2</Avatar>
              </span>
              <span @click="addPriority(3)" style="cursor: pointer;">
                <Avatar style="color:white;background-color: rgb(0, 175, 0);">3</Avatar>
              </span>
              <span @click="addPriority(4)" style="cursor: pointer;">
                <Avatar style="color:white;background-color: rgb(255, 150, 46);">4</Avatar>
              </span>
            </span>
          </tooltip>
        </div>
        <div @click="setCamera" class="minder-head-item">
          <tooltip content="聚焦" transfer>
            <Icon type="md-locate" size=22 />
          </tooltip>
        </div>
        <div class="minder-head-item" @click="saveData">
          <tooltip content="保存" transfer>
            <i class="fa fa-floppy-o" style="font-size: 22px;" aria-hidden="true"></i>
          </tooltip>
        </div>
        <!-- <div class="minder-head-item">
          <Icon type="md-lock" size=22 />
        </div> -->
        <div v-if="openUser.length > 0" class="minder-head-item"> {{ openUser }}正在编辑
        </div>
        <div class="minder-head-item">
          <span style="margin-right: 10px;">{{ fileName }}</span>
        </div>
      </div>
      <div class="header-right">
        <div @click="changeViewMode" class="extra">
          <Icon type="md-sync" :size="20" />树状视图
        </div>
      </div>
    </div>
    <div v-loading="isLoading" class="minder-content" :style="{ height: appBodyMainHeight - 20 + 'px' }">
      <div id="mindContainer" v-loading="loading">
        <kityMinder ref="minder" v-if="importData != null" :importJson="importData" @minder="minderHandle"
          @exportData="exportData"></kityMinder>
        <!-- <minder ref="minder" :importData="importData" @exportData="exportData"></minder> -->
        <!-- <editor ref="minder" v-if="importData != null" :importJson="importData"></editor> -->
      </div>
      <div v-show="showDetailSetting" class="testcase-toolRightPanel">
        <div class="title">
          <span v-if="selectNodeData.id >= 0"> # {{ selectNodeData.id }} {{ selectNodeData.text }} </span>
          <span v-else>Key: {{ selectNodeData.OriginalID }}</span>
          <span @click="closeSettingPanel">
            <Icon type="md-close" :size="20" />
          </span>
        </div>
        <div class="content">
          <div>用例描述: </div>
          <div style="padding: 4px 2px 4px 2px;">
            <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.note" type="textarea"
              :rows="6" placeholder="用例描述信息" />
          </div>
          <div>前置条件: </div>
          <div style="padding: 4px 2px 4px 2px;">
            <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.Precondition"
              type="textarea" :rows="6" placeholder="用例前置条件" />
          </div>
          <div>预期结果: </div>
          <div style="padding: 4px 2px 4px 2px;">
            <Input @on-blur="addNote" maxlength="1000" show-word-limit v-model="selectNodeData.ExpectResult"
              type="textarea" :rows="6" placeholder="用例预期结果" />
          </div>
        </div>
      </div>
    </div>
    <div @click="expandDetailSettingBar" class="testcase-toolRightPanelIcon">
      <Avatar v-show="showDetailSettingIcon" icon="ios-expand" size="large" />
    </div>
    <!-- <div v-if="showCommentsContainer" class="mindmap-comments-container"
      :style="{ left: selectNodePosition.x, top: selectNodePosition.y }">
      {{ selectNodeData.note }}
      {{ selectNodeData.Desc }}
      {{ selectNodeData.ExpectResult }}
      {{ selectNodeData.Precondition }}
    </div> -->
  </div>
</template>

<script>
// import * as jsonpatch from "fast-json-patch/index";
// import { applyOperation } from 'fast-json-patch/index';
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import { diffCaseTree } from '../../../../../utils/casediff';

export default {
  name: "CaseMinder",
  components: {
    //editor
  },
  props: ["projectID", "mindFileID"],
  data() {
    return {
      minder: null,
      paper: null,
      topToolbarLeft: 596,
      showDetailSetting: false,
      showDetailSettingIcon: false,
      toolbarExtend: 0,
      selectNodeData: {},
      selectNodePosition: { x: "0px", y: "0px" },
      showCommentsContainer: false,
      projectTopicTags: [],
      //originalTags: [],
      fontSize: 14,
      fileName: "",
      isLoading: false,
      loading: false,
      copyNode: false,
      saveFileMenuly: false,
      fontFamily: "微软雅黑",
      nodeBackgroundColor: "#50c28b",
      nodeFontColor: "black",
      updatedChildren: [],
      templateList: [
        { name: "default", title: "思维导图" },
        { name: "tianpan", title: "天盘图" },
        { name: "structure", title: "组织结构图" },
        { name: "filetree", title: "目录组织图" },
        { name: "right", title: "逻辑结构图" },
        { name: "fish-bone", title: "鱼骨头图" },
      ],
      themeList: [
        [
          { name: "classic", title: "经典脑图" },
          { name: "classic-compact", title: "经典紧凑" },
        ],
        [
          { name: "snow", title: "温柔冷光" },
          { name: "snow-compact", title: "紧凑冷光" },
        ],
        [
          { name: "fish", title: "鱼骨图" },
          { name: "wire", title: "线框图" },
        ],
        [
          { name: "fresh-red", title: "清新红" },
          { name: "fresh-red-compat", title: "紧凑红" },
        ],
        [
          { name: "fresh-green", title: "文艺绿" },
          { name: "fresh-green-compat", title: "紧凑绿" },
        ],
        [
          { name: "fresh-purple", title: "浪漫紫" },
          { name: "fresh-purple-compat", title: "紧凑紫" },
        ],
        [
          { name: "fresh-blue", title: "天空蓝" },
          { name: "fresh-blue-compat", title: "紧凑蓝" },
        ],
        [
          { name: "fresh-pink", title: "脑残粉" },
          { name: "fresh-pink-compat", title: "紧凑粉" },
        ],
        [
          { name: "fresh-soil", title: "你土黄" },
          { name: "fresh-soil-compat", title: "紧凑黄" },
        ],
        [
          { name: "tianpan", title: "清新红" },
          { name: "tianpan-compact", title: "紧凑天盘" },
        ],
      ],
      importData: null,
      originData: null,
      loading: false,
      timer: null,
      openUser: []
    };
  },
  computed: {
    ...mapState(['appBodyHeight', "appBodyMainHeight",]),
    ...mapState('usercenter', ['userInfo']),

    containerHeight: function () {
      return this.appBodyMainHeight - 68;
    },
    toolBarWidth: function () {
      return this.toolbarExtend + "px";
    },
  },

  created() {
    //this.getTopicTags();
  },

  mounted() {
    this.getTestCaseTree()

    window.onbeforeunload = function (e) {
      e = e || window.event;
      // 兼容IE8和Firefox 4之前的版本
      if (e) {
        e.returnValue = "关闭提示";
      }
      // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
      return "关闭提示";
    };

    this.timer = setInterval(this.checkOpened, 30000);
  },

  destroyed() {
    window.onbeforeunload = null
    clearInterval(this.timer)
    this.closeOpend()
  },

  // beforeRouteLeave(to, from, next) {
  //   const answer = window.confirm("离开前请确认保存了数据，确定要离开？");
  //   if (answer) {
  //     next();
  //   } else {
  //     next(false);
  //   }
  // },

  methods: {
    ...mapMutations("testcase", ["setTestCaseViewMode"]),

    getTestCaseTree() {
      this.loading = true;
      this.$axios.get("/api/project/testcase/" + this.mindFileID + "/mindmap").then((response) => {
        this.loading = false;
        this.importData = response.data.result.data[0];
        this.originData = response.data.result.data[0];
        this.fileName = response.data.result.FileName;
        setTimeout(() => {
          this.minder = this.$refs.minder.minder;
          this.setTheme("fresh-blue-compat");
          // this.setTemplate("default");
          this.bindevent();
          this.minder.execCommand("ExpandToLevel", 3);
          //  console.log("this.minder=", this.minder);
        }, 50);
        //setInterval(() => {
        //this.saveData()
        //}, 300000)
      }, (response) => {
        this.loading = false;
      }
      );
    },

    checkTitleLength(caseTree) {
      if (caseTree.data.text.length > 500) {
        this.$Message.error({
          content: caseTree.data.text + ':不能大于500个字节',
          duration: 10
        })
        return false
      }

      if (caseTree.children.length > 0) {
        for (let tmp of caseTree.children) {
          if (!this.checkTitleLength(tmp)) {
            return false
          }
        }
      }

      return true
    },

    checkTestCaseTreeModified() {
      var exportData = this.minder.exportJson()
      return JSON.stringify(this.handleFields(exportData.root)) === JSON.stringify(this.handleFields(this.originData.root))
    },

    saveData() {
      var exportData = this.minder.exportJson()

      if (this.checkTitleLength(exportData.root)) {
        var diff = diffCaseTree(this.handleFields(this.originData.root), this.handleFields(exportData.root))
        if (this.checkTestCaseTreeModified()) {
          this.$Message.error({
            content: "用例没有变化！",
            duration: 3,
          });
          return false
        }

        this.isLoading = true;
        let parameters = { mind_tree: JSON.stringify(exportData) };
        this.$axios.post("/api/project/testcase/" + this.mindFileID + "/mindmap/savediff", diff).then((response) => {
          // 全量保存 this.$axios.post("/api/project/testcase/" + this.mindFileID + "/mindmap/save", parameters).then((response) => {
          let rootNode = this.minder.getRoot();
          this.updateNodeID(rootNode, response.data.result.add.success);
          this.isLoading = false;
          let tmp = this.minder.exportJson()
          this.originData = this.handleFields(tmp)
        },
          (error) => {
            this.isLoading = false;
            if (error.response.status === 400) {
              let tmp = '添加失败:' + JSON.stringify(error.response.data.result.add.failed) + ';删除失败:' + JSON.stringify(error.response.data.result.del.failed) + ';修改失败:' + JSON.stringify(error.response.data.result.update.failed)
              var content = "保存失败:" + tmp
              this.$Message.error({
                content: content,
                duration: 6
              })
            } else if (error.response.status === 406) {
              var content = "保存失败，其他用户已修改:" + error.response.data.result.update_filed
              this.$Message.error({
                content: content,
                duration: 3
              })
            } else {
              this.$Message.error({
                content: "保存失败，请点击保存按钮重试！",
                duration: 3,
              });
            }
          })
      }
    },

    checkOpened() {
      this.$axios.get("/api/project/testcase/" + this.mindFileID + "/mindmap/checkopened").then((response) => {
        this.openUser = response.data.result
      }, (error) => { }
      )
    },

    closeOpend() {
      this.$axios.get("/api/project/testcase/" + this.mindFileID + "/mindmap/closeopened").then((response) => {
        this.openUser = response.data.result
      }, (error) => { }
      )
    },

    exportData(value) { },

    minderHandle(val) {
      //console.log("minderHandle ===", val)
    },

    changeViewMode: function () {
      if (!this.checkTestCaseTreeModified()) {
        this.$Modal.confirm({
          title: '是否保存 ?',
          onOk: () => {
            this.saveData()
            this.setTestCaseViewMode("tree");
          },
          onCancel: () => {
            this.setTestCaseViewMode("tree");
          }
        })
      } else {
        this.setTestCaseViewMode("tree");
      }
    },

    tagChecked: function (tagName) {
      let result = false;
      if (this.selectNodeData.resource) {
        for (let i = 0; i < this.selectNodeData.resource.length; i++) {
          if (tagName === this.selectNodeData.resource[i]) {
            result = true;
            break;
          }
        }
      }
      return result;
    },

    findNodeWithId: function (id) {
      let count = 0;
      let allNodes = this.minder.getAllNode()
      for (let i = 0; i < allNodes.length; i++) {
        if (allNodes[i].data.id === id) {
          count = count + 1;
        }
      }
      return count
    },

    copyNodeData: function (node) {
      if (this.copyNode) {
        node.data.id = this.uuid(12);
        for (let i = 0; i < node.children.length; i++) {
          this.updateNodeOriginalID(node.children[i]);
        }
      }
    },

    updateNodeOriginalID: function (node) {
      if (node) {
        node.data.id = this.uuid(12);
        let children = node.children;
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeOriginalID(node.children[i]);
          }
        } else {
          return
        }
      } else {
        return
      }
    },

    updateNodeID: function (node, idMaps) {
      if (node) {
        if (idMaps[node.data.id]) {
          let id_keys = idMaps[node.data.id].split(":")
          node.data.id = id_keys[0];
          node.data.OriginalID = id_keys[1];
        }
        let children = node.children;
        if (children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            this.updateNodeID(node.children[i], idMaps);
          }
        } else {
          return;
        }
      } else {
        return;
      }
    },

    uuid: function (len, radix) {
      let chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
      let uuid = [],
        i;
      radix = radix || chars.length;

      if (len) {
        // Compact form
        for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
      } else {
        // rfc4122, version 4 form
        let r;

        // rfc4122 requires these characters
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
        uuid[14] = "4";

        // Fill in random data.  At i==19 set the high bits of clock sequence as
        // per rfc4122, sec. 4.1.5
        for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
            r = 0 | (Math.random() * 16);
            uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
          }
        }
      }
      return uuid.join("");
    },

    // getTopicTags: function () {
    //   this.$axios.get("/api/ci/case_tags?projectID__in=0," + this.projectID + "&TagType__in=2,3")
    //     .then((response) => {
    //       this.projectTopicTags = response.data.result;
    //       this.originalTags = response.data.result;
    //     }, (response) => { }
    //     );
    // },

    closeSettingPanel: function () {
      this.showDetailSetting = false;
      this.toolbarExtend = 0;
      this.showDetailSettingIcon = true;
    },

    expandDetailSettingBar: function () {
      let node = this.minder.getSelectedNode();
      if (node.children.length == 0) {
        // console.log("node.children.length=", node.children.length);
        this.showDetailSetting = true;
        this.toolbarExtend = 328;
        this.showDetailSettingIcon = false;
      }
    },

    showDetailSettingBar: function (isShow) {
      if (!this.showDetailSettingIcon) {
        if (isShow) {
          this.showDetailSetting = isShow;
          this.toolbarExtend = 328;
        } else {
          this.showDetailSetting = false;
          this.toolbarExtend = 0;
        }
      }
    },

    addNode(command) {
      if (command == "AppendChildNode") {
        let node = this.minder.getSelectedNode();
        // console.log("select node=", node);
      }
      this.minder.execCommand(command, "分支主题");
    },

    editNode() {
      var state = this.minder.queryCommandState('EditNode')
      if (state == 0) {
        this.minder.execCommand('EditNode')
      }
    },

    addResource: function (value) {
      if (this.tagExists(value) === 0) {
        let tempTag = { projectID: 1, TagType: 3, TagName: value };
        this.$axios.post("/api/ci/case_tags", tempTag).then(
          (response) => {
            response.data.result.TagChecked = true;
            this.projectTopicTags.push(response.data.result);
            if (this.selectNodeData.resource) {
              this.selectNodeData.resource.push(value);
            } else {
              this.selectNodeData.resource = [value];
            }
          },
          (response) => { }
        );
      } else {
        this.$Message.error({
          content: "标签已经存在",
          duration: 10,
        });
      }
    },

    removeResource: function (checked, name) {
      let resource = this.selectNodeData.resource;
      if (checked) {
        if (resource) {
          if (resource.indexOf(name) < 0) {
            resource.push(name);
          }
        } else {
          resource = [name];
        }
        this.minder.execCommand("Resource", resource);
      } else {
        if (resource) {
          for (let i = 0; i < resource.length; i++) {
            if (resource[i] === name) {
              resource.splice(i, 1);
              break;
            }
          }
          this.minder.execCommand("Resource", resource);
        }
      }
    },

    tagExists: function (value) {
      let result = 0;
      for (let i = 0; i < this.projectTopicTags.length; i++) {
        if (value === this.projectTopicTags[i].TagName) {
          result = this.projectTopicTags[i].id;
        }
      }
      return result;
    },

    removeTag: function (e, value) {
      let tagID = this.tagExists(value);
      this.$axios.delete("/api/ci/case_tag/" + tagID + "/").then(
        (response) => {
          for (let i = 0; i < this.projectTopicTags.length; i++) {
            if (tagID === this.projectTopicTags[i].id) {
              this.projectTopicTags.splice(i, 1);
            }
          }
        },
        (response) => { }
      );
    },

    resetLayout() {
      this.minder.execCommand("ResetLayout");
    },

    setLayout(layoutName) {
      this.minder.execCommand("Layout", layoutName);
    },

    setTheme(theme) {
      this.minder.execCommand("Theme", theme);
      //                this.updateFileProperty('Theme',theme,this.mindFileID)
    },

    setFontFamily: function (family) {
      this.minder.execCommand("FontFamily", family);
      this.updateAllNodeData("FontFamily", family);
    },

    setFontSize: function (size) {
      this.minder.execCommand("FontSize", size);
      this.updateAllNodeData("FontSize", size);
    },

    setBackgroundColor: function (color) {
      this.minder.execCommand("Background", color);
      this.updateAllNodeData("BackgroundColor", color);
    },

    setFontColor: function (color) {
      this.minder.execCommand("ForeColor", color);
      this.updateAllNodeData("FontColor", color);
    },

    setTemplate(name) {
      this.minder.execCommand("Template", name);
      //                this.updateFileProperty('Template',name,this.mindFileID)
    },

    zoomMindmap: function (value) {
      this.minder.execCommand("Zoom", value);
      this.minder.execCommand;
    },

    setCamera: function () {
      this.minder.execCommand("camera", this.minder.getRoot(), 100);
    },

    updateFileProperty: function (fieldName, value, fileID) {
      let parameters = {};
      parameters[fieldName] = value;
      this.$axios.patch("/api/project/kitymind_file/" + fileID + "/", parameters).then(
        (response) => { },
        (response) => { }
      );
    },

    removeNode() {
      this.minder.execCommand("RemoveNode");
    },

    addPriority(priority) {
      this.minder.execCommand("Priority", priority);
    },

    addProgress(progress) {
      this.minder.execCommand("Progress", progress);
    },

    addImage() {
      this.minder.execCommand("Image", this.selectNodeData.image, "");
    },

    addLink() {
      this.minder.execCommand("HyperLink", this.selectNodeData.hyperlink, "");
    },

    unDo() {
      //this.minder.history.unDo()
    },

    reDo() {
      //this.minder.history.reDo()
    },

    addNote() {
      this.value1 = true;
      this.toolbarExtend = 80;
      this.minder.execCommand("Note", this.selectNodeData.note);
      this.selectNodeData.Desc = this.selectNodeData.note;
    },

    initProjectTags: function () {
      for (let y = 0; y < this.projectTopicTags.length; y++) {
        this.projectTopicTags[y].TagChecked = false;
      }
    },

    showComments: function (e) {
      let shapeName = e.kityEvent.targetShape.container.__KityClassName;
      if (shapeName && shapeName === "NoteIcon") {
        this.showCommentsContainer = true;
        this.selectNodePosition.y = e.kityEvent.originEvent.y - 110 + "px";
        this.selectNodePosition.x =
          e.kityEvent.originEvent.x - this.topToolbarLeft + "px";
      } else {
        this.showCommentsContainer = false;
      }
    },

    bindevent() {
      this.minder.on("click", (e) => {
        // console.log('click', e);
        let node = this.minder.getSelectedNode();
        if (node) {
          if (node.children.length == 0) {
            this.showDetailSettingBar(true);
            this.showComments(e);
            this.selectNodeData = node.data;

            if (node.data["font-size"]) {
              this.fontSize = node.data["font-size"];
            } else {
              this.fontSize = 14;
            }

            if (node.data["font-family"]) {
              this.fontFamily = node.data["font-family"];
            } else {
              this.fontFamily = "微软雅黑";
            }
          } else {
            this.showDetailSettingBar(false);
            this.selectNodeData = {};
            this.showCommentsContainer = false;
          }
        } else {
          this.showDetailSettingBar(false);
          this.selectNodeData = {};
          this.showCommentsContainer = false;
        }
      });

      this.minder.on("keydown", (e) => {
        // console.log('keydown', e);
        if (e.originEvent) {
          if (e.originEvent.key === "v" && (e.originEvent.metaKey || e.originEvent.ctrlKey)) {
            let node = this.minder.getSelectedNode();
            if (node) {
              this.copyNode = true;
            }
          }
        }
      });

      this.minder.on("mousedown", (e) => {
        // console.log('mousedown', e);
        if (e.originEvent) {
          if (e.originEvent.button === 2 || e.originEvent.button === 0) {
            let node = this.minder.getSelectedNode();
            if (node) {
              if (node.children.length == 0) {
                this.showDetailSettingBar(true);
                this.showComments(e);
                this.selectNodeData = node.data;
                this.initProjectTags();
                if (this.selectNodeData.resource) {
                  for (let i = 0; i < this.selectNodeData.resource.length; i++) {
                    // console.log(this.selectNodeData.resource[i]);
                    for (let y = 0; y < this.projectTopicTags.length; y++) {
                      // console.log(this.projectTopicTags[y]);
                      if (this.selectNodeData.resource[i] === this.projectTopicTags[y].TagName) {
                        this.projectTopicTags[y].TagChecked = true;
                        break;
                      }
                    }
                  }
                }
                if (node.data["font-size"]) {
                  this.fontSize = node.data["font-size"];
                } else {
                  this.fontSize = 14;
                }

                if (node.data["font-family"]) {
                  this.fontFamily = node.data["font-family"];
                } else {
                  this.fontFamily = "微软雅黑";
                }
              }
            } else {
              this.showDetailSettingBar(false);
              this.showCommentsContainer = false;
            }
          }
        }
      });

      this.minder.on("selectionchange", (e) => {
        // console.log('selectionchange', e);
        var nodes = this.minder.getSelectedNodes();
        if (nodes) {
          for (let i = 0; i < nodes.length; i++) {
            this.selectNodeData = nodes[i].data;
            this.copyNodeData(nodes[i]);
          }
          this.copyNode = false;
        }
      });
    },

    // https://github.com/fex-team/kityminder-core/wiki/command
    // https://github.com/fex-team/kityminder-core/wiki/api

    handleFields(node) {
      // 处理节点的字段
      if (node.data && typeof node.data === 'object') {
        if (node.data.hasOwnProperty('expandState')) {
          delete node.data.expandState;
        }
        if (node.data.hasOwnProperty('layout_mind_offset')) {
          delete node.data.layout_mind_offset;
        }
        if (node.data.hasOwnProperty('layout_left_offset')) {
          delete node.data.layout_left_offset;
        }
        if (node.data.hasOwnProperty('priority')) {
          node.data.Priority = node.data.priority;
        }
        if (node.data.hasOwnProperty('text')) {
          node.data.Title = node.data.text;
        }
      }

      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          this.handleFields(child);
        });
      }
      return node
    },

    removeIdRecursively(obj) {
      if (Array.isArray(obj)) {
        obj.forEach(this.removeIdRecursively);
      } else if (typeof obj === 'object' && obj !== null) {
        Object.keys(obj).forEach(key => {
          if (key === 'expandState') {
            delete obj[key];
          } else {
            this.removeIdRecursively(obj[key]);
          }
        });
      }
      return obj;
    }
  },

  watch: {
    "selectNodeData.resource": function (value) {
      this.initProjectTags();
      if (value) {
        for (let i = 0; i < value.length; i++) {
          // console.log(value[i]);
          for (let y = 0; y < this.projectTopicTags.length; y++) {
            // console.log(this.projectTopicTags[y]);
            if (value[i] === this.projectTopicTags[y].TagName) {
              this.projectTopicTags[y].TagChecked = true;
              break;
            }
          }
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.case-mind {
  background: #FFFFFF;

  .minder-header {
    border-bottom: 1px solid #E6E9ED;
    border-radius: 4px 4px 0 0;
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
    font-size: 12px;
    text-overflow: ellipsis;
    font-weight: 500;
    line-height: 22px;

    .header-left {
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      white-space: nowrap;
    }

    .header-right {
      display: flex;
      align-items: center;
      justify-content: center;

      .extra {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-left: 18px;
        padding-right: 18px;
      }
    }

    .minder-head-item {
      display: flex;
      align-items: center;

      cursor: pointer;
      padding-left: 18px;

      .ivu-avatar-small {
        width: 20px;
        height: 20px;
        line-height: 20px;
        border-radius: 50%;
      }
    }

    .minder-head-item-2 {
      cursor: pointer;
      padding-left: 12px;
    }
  }
}

.minder-content {
  overflow: auto;
  /* 启用溢出滚动 */
}

#mindContainer {
  height: 86vh;
  z-index: 1;

  .minder {
    height: 100%;
  }

  .minder-editor-container {
    overflow-y: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    right: inherit;
    top: 67px;
    font-family: Arial,
      "Hiragino Sans GB",
      "Microsoft YaHei",
      "WenQuanYi Micro Hei",
      sans-serif;
  }

  .tools {
    display: none
  }

  .tabBar {
    display: none;
  }
}

.top {
  display: none;
}

.testcase-toolRightPanel {
  background: #FFFFFF;
  border: 1px solid #E6E9ED;
  position: fixed;
  overflow-y: auto;
  top: 190px;
  width: 320px;
  right: 16px;
  z-index: 2;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #E6E9ED;
    padding: 4px;

    .span {
      text-align: left;
    }
  }

  .content {
    padding: 4px;
  }
}

.testcase-toolRightPanelIcon {
  position: fixed;
  top: 200px;
  width: 40px;
  right: 100px;
  box-shadow: 0px 10px 5px #888888;
  border-radius: 25px;
  cursor: pointer;
  /*border: 2px solid #50C28B;*/
  z-index: 2;
}

.mindmap-comments-container {
  position: absolute;
  background: #ffd;
  padding: 5px 15px;
  border-radius: 5px;
  max-width: 400px;
  max-height: 200px;
  overflow: auto;
  z-index: 10;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  word-break: break-all;
  white-space: normal;
  font-size: 12px;
  color: #333;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
