<template>
  <div style="height: 100%">
    <div class="css-CaseGroupTree-SectionTreeView" :style="{ height: containerHeight + 'px' }">
      <Spin size="large" fix v-if="spinShow"></Spin>
      <el-tree ref="caseGroupTree" :data="testCaseGroup" lazy :load="loadNode" :show-checkbox="checkbox"
        @node-drop="handleDrop" node-key="id" :filter-node-method="filterNode" icon-class="el-icon-caret-right"
        :highlight-current="true" draggable :expand-on-click-node="false" :allow-drop="allowDrop"
        :check-strictly="checkStrictly" :allow-drag="allowDrag" :props="defaultProps" @node-click="loadNode"
        @check="handleNodeCheck">
        <span class="css-TestCaseGroupTree-node" slot-scope="{ node, data }" @mouseover="onMouseOver(data.id)"
          @mouseout="onMouseOut(data.id)">
          <el-badge id="elbadge" style="margin-top: auto" :value="node.childcount" :max="9999">
            <span class="css-folderIcon-SectionIcon"></span>
          </el-badge>
          <span :class="isDelLine(data)">
            <span v-if="readonly" style="text-overflow: ellipsis; white-space: nowrap;">{{ data.Title }}</span>
            <label-editor-input-external-trigger v-if="!readonly"
              :url="'/project/' + projectID + '/test/test-case/' + data.id" style="display: inline-flex;color: #606266"
              :clickEdit="renameCase" @cancelUpdate="cancelUpdateCaseTitle" @updateValue="updateCaseTitle"
              placeHolder="分组名称" :id="data.id" :displayWidth="260" :displayText="data.Title">
            </label-editor-input-external-trigger>
          </span>
          <span class="css-SectionControls" v-show="showNodeToolID === data.id && !readonly">
            <Poptip v-show="showAddButtern(node)" placement="bottom-end" style="display: block;">
              <span style="padding:0px 1px 0px 1px;">
                <Icon type="md-add" />
              </span>
              <div slot="content" style="width: 260px;height: 28px;">
                <Input show-word-limit maxlength="100" search icon="md-return-left" placeholder="回车创建"
                  @on-search="createChildTestCaseGroup" />
              </div>
            </Poptip>
            <span @click="renameGroup(data.id)" style="display: block;padding:0px 1px 0px 1px;">
              <Icon type="md-create" />
            </span>
            <span @click="deleteCaseGroup(data)" style="display: block;padding:0px 1px 0px 1px;">
              <Icon type="ios-trash" />
            </span>
            <span @click="scrapTestCaseGroup(data)" style="display: block;padding:0px 1px 0px 1px;">
              <Icon type="ios-eye-off" />
            </span>
          </span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import labelEditorInputExternalTrigger from "../../../../components/common/LabelEditorExternalTigger-Input.vue";
import {
  updateTestCase,
  createTestCase,
  scrapTestCase,
  deleteTestCase,
} from "../business-service/ProjectTestCaseApiService";

import {
  setNewNodeToGroupTree,
  removeNode,
} from "../business-service/ProjectTestCaseUtilService";

export default {
  name: "ProjectTestCaseGroupTree",
  props: ["projectID", "checkbox", "readonly", "checkStrictly"],
  data() {
    return {
      columnItemHeight: 200,
      showNodeToolID: 0,
      renameCase: 0,
      selectCasesCount: 0,
      formData: {
        Title: "新建用例",
        Parent: 0,
        IsGroup: false,
        Project: 0,
        Priority: 2,
        Module: 0,
        RunTimes: 0,
      },
      testCaseGroupTree: [],
      testCaseGroup: [],
      defaultProps: {
        label: "Title",
        children: "children",
        isLeaf: false,
      },
      spinShow: true,
      node: {},
      resolveFunc: function () { },
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters("testcase", ["testCaseGroupTitle", "searchKeyword"]),
    ...mapState(['appBodyHeight', "appBodyMainHeight", "appBodyWidth"]),

    containerHeight: function () {
      return this.appBodyHeight - 160;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },
    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      }
      if (this.$route.params.versionID) {
        return this.$route.params.versionID;
      }
      return 0;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations("testcase", ["setTestCaseGroupTitle", "setSearchKeyword"]),
    ...mapMutations(["setItemViewMode"]),

    remove: function (node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex((d) => d.id === data.id);
      children.splice(index, 1);
    },

    onMouseOver: function (id) {
      this.showNodeToolID = id;
    },

    onMouseOut: function (id) {
      this.showNodeToolID = 0;
    },

    handleNodeClick(data, node, element) {
      this.$emit("clickNode", data, node, element);
    },

    handleNodeCheck: function (data, node, element) {
      this.selectCasesCount = 0;
      let selectNodes = this.$refs.caseGroupTree.getCheckedNodes();
      for (let i = 0; i < selectNodes.length; i++) {
        this.selectCasesCount =
          this.selectCasesCount + selectNodes[i].view_data.child_case_count;
      }
      let selectKeys = this.$refs.caseGroupTree.getCheckedKeys();
      let halfSelectKeys = this.$refs.caseGroupTree.getHalfCheckedKeys();
      this.$emit(
        "checkNode",
        data,
        node,
        element,
        selectKeys,
        halfSelectKeys,
        this.selectCasesCount
      );
    },

    handleDrop: function (draggingNode, dropNode, dropType, ev) {
      let parameters = { Parent: dropNode.data.id };
      if (dropType !== "inner") {
        parameters.Parent = dropNode.data.Parent;
      }
      updateTestCase(draggingNode.data.id, parameters).then(
        (response) => {
          this.$Message.success({
            content: "分组更新成功",
            duration: 3,
            closable: true,
          });
        },
        (response) => {
          this.$Message.error({
            content: "分组更新失败",
            duration: 3,
            closable: true,
          });
        }
      );
    },

    allowDrag: function (draggingNode) {
      return draggingNode.data.Parent !== 0;
    },

    allowDrop: function (draggingNode, dropNode, type) {
      if (dropNode.data.Parent === 0) {
        return type === "inner";
      } else {
        return true;
      }
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.Title.indexOf(value) !== -1;
    },

    renameGroup: function (id) {
      this.renameCase = id;
    },

    cancelUpdateCaseTitle: function () {
      this.renameCase = 0;
    },

    updateCaseTitle: function (value, id) {
      let parameters = { Title: value };
      updateTestCase(id, parameters).then(
        (response) => {
          this.$Message.success({
            content: "用例组名称修改成功",
            duration: 3,
            closable: true,
          });
        },
        (response) => {
          this.$Message.error({
            content: "用例组名称修改失败",
            duration: 3,
            closable: true,
          });
        }
      );
      this.renameCase = 0;
    },

    loadTestCaseGroupTree: function (node) {
      this.$axios
        .get("/api/project/" + this.projectID + "/testcase/treelazy?root=0")
        .then(
          (response) => {
            this.testCaseGroupTree = response.data.result;
          },
          (response) => { }
        );
    },

    createNewTestCaseGroup: function (parameters) {
      createTestCase(this.projectID, parameters).then(
        (response) => {
          response.data.result["children"] = [];
          this.$refs.caseGroupTree.append(
            response.data.result,
            parameters.Parent
          );
          //              if (parameters.Parent === 0) {
          //                this.testCaseGroupTree.push(response.data.result)
          //              } else {
          //                for(let i = 0; i< this.testCaseGroupTree.length; i++) {
          //                  setNewNodeToGroupTree(this.testCaseGroupTree[i],parameters.Parent,response.data.result)
          //                }
          //              }
          this.$Message.success({
            content: "用例组成功添加",
            duration: 3,
            closable: true,
          });
        },
        (response) => { }
      );
      this.setTestCaseGroupTitle("");
      this.$refs.caseGroupTree.childNodes = []
      //this.reloadTree()
    },

    createTestCaseRoot: function (title) {
      if (title.trim().length === 0) {
        this.$Message.error({
          content: "用例分组名称不能为空或者空格",
          duration: 3,
          closable: true,
        });
      } else {
        let parameters = this.formData;
        parameters.Title = title.trim();
        parameters.Project = this.projectID;
        parameters.Parent = 0;
        parameters.IsGroup = true;
        this.createNewTestCaseGroup(parameters);
      }
    },

    createChildTestCaseGroup: function (title) {
      let parameters = this.formData;
      parameters.Title = title.trim();
      parameters.Project = this.projectID;
      parameters.Parent = this.showNodeToolID;
      parameters.IsGroup = true;
      this.createNewTestCaseGroup(parameters);
    },

    resetChecked() {
      this.$refs.caseGroupTree.setCheckedKeys([]);
    },

    deleteCaseGroup: function (data) {
      this.$Modal.confirm({
        title: "删除确认",
        content: "您即将删除用例/组[" + data.Title + "]",
        onOk: () => {
          deleteTestCase(data.id).then(
            (response) => {
              this.$refs.caseGroupTree.remove(data);
              //        for(let i =0; i< this.testCaseGroupTree.length; i++){
              //          if(this.testCaseGroupTree[i].id === id) {
              //            this.testCaseGroupTree.splice(i,1)
              //          }else {
              //            removeNode(this.testCaseGroupTree[i],id)
              //          }
              //        }
            },
            (response) => { }
          );
        },
        onCancel: () => { },
      });
    },

    scrapTestCaseGroup: function (data) {
      let status = 0
      let modal_title = '确认取消归档'
      let modal_content = '您即将取消归档用例组['

      if (data.Status != 1) {
        status = 1
        modal_title = '确认归档'
        modal_content = '您即将归档用例组['
      }
      let parameters = { "Status": status };

      this.$Modal.confirm({
        title: modal_title,
        content: modal_content + data.Title + "]",
        onOk: () => {
          scrapTestCase(data.id, parameters).then(
            (response) => {
              this.spinShow = true
              this.reloadTree()
            },
            (response) => { }
          );
        },
        onCancel: () => { },
      });
    },

    loadChildCount(node) {
      //console.log(node)
      this.$axios.get("/api/project/testcase/" + node.data.id + "/childcount")
        .then((response) => {
          node.childcount = response.data.result.childcount;
        });
    },

    loadNode(node, resolve) {
      //console.log(node, resolve); //如果展开第一级节点，从后台加载一级节点列表
      if (node.level == 0) {
        this.node = node
        this.resolveFunc = resolve
        this.loadRootNode(node, resolve);
      } //如果展开其他级节点，动态从后台加载下一级节点列表
      if (node.level >= 1) {
        this.loadChildNode(node, node.data.id, resolve);
      }
    },
    //加载第一级节点
    loadRootNode(node, resolve) {
      this.$axios.get("/api/project/" + this.projectID + "/testcase/lazygrouptree?Root=0")
        .then((response) => {
          this.testCaseGroup = response.data.result;
          //let data = response.data.result
          //console.log("rootnode", node);
          this.spinShow = false;
          return resolve(this.testCaseGroup);
        });
    },
    //加载节点的子节点集合
    loadChildNode(node, parent_id, resolve) {
      this.loadChildCount(node)
      this.$axios.get("/api/project/" + this.projectID + "/testcase/lazygrouptree?Root=" + parent_id).then((response) => {
        let data = response.data.result;
        //console.log("childdata", data);
        return resolve(data);
      });
    },
    // 重新加载
    reloadTree() {
      this.node.childNodes = []
      this.loadNode(this.node, this.resolveFunc)
    },

    refreshNodeBy(id) {
      let node = this.$refs.caseGroupTree.getNode(id); // 通过节点id找到对应树节点对象
      node.loaded = false;
      node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
    },

    showAddButtern(node) {
      if (node.level > 5) {
        return false;
      } else {
        return true;
      }
    },

    isDelLine(data) {
      if (data.Status == 1) {
        return 'css-SectionName-textEllipsis-del-line'
      } else {
        return 'css-SectionName-textEllipsis'
      }
    },
  },

  created: function () {
    //this.loadTestCaseGroupTree()
  },

  mounted: function () { },
  watch: {
    testCaseGroupTitle: function (value) {
      if (value && value !== "") {
        this.createTestCaseRoot(value);
      }
    },
    searchKeyword: function (value) {
      this.$refs.caseGroupTree.filter(value);
    },
  },

  components: {
    labelEditorInputExternalTrigger,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.css-CaseGroupTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 8px;
  position: relative;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-TestCaseGroupTree-node {
  //flex: 1;
  display: flex;
  height: 26px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  //padding-right: 8px;
  -webkit-box-align: center;
  flex: 1 1 auto;
  padding-left: 0px;
  min-width: 0px;
  //max-width:80%;
}

.css-folderIcon-SectionIcon {
  display: inline-block;
  width: 22px;
  height: 22px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,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) center center / 16px 16px no-repeat;
}

.css-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  //padding: 0px 2px 0px 2px;
  //padding-left: 4px;
  overflow: hidden;
  color: #606266;
  margin: 0px 4px 0px 4px;
}

.css-SectionName-textEllipsis-del-line {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  //padding: 0px 2px 0px 2px;
  //padding-left: 4px;
  overflow: hidden;
  color: #606266;
  margin: 0px 4px 0px 4px;

  .text-display {
    text-decoration: line-through;
  }
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  -webkit-appearance: none;
  -webkit-user-select: text;
  outline-color: transparent;
  box-shadow: none;
}

.ivu-input {
  display: inline-block;
  width: 100%;
  height: 26px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 14px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #5578aa;
  background-color: #fff;
  background-image: none;
  position: relative;
  cursor: text;
}

.ivu-input-icon {
  width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 16px;
  text-align: center;
  color: #808695;
  position: absolute;
  right: 0;
  z-index: 3;
}

.css-SectionControls {
  flex: 0 0 auto;
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  padding-left: 6px;
  padding-right: 6px;
  //width: 52px;
}

#elbadge .el-badge__content {
  transform: scale(0.8);
  top: 12px;
  right: 2px;
  border-radius: 10px;
  min-width: 20px;
  color: #606266;
  height: 12px;
  line-height: 1px;
  text-align: center;
  padding: 0px 0px 0px 0px;
  border-style: none;
  border: 0px solid transparent;
  background: transparent;
}
</style>
