<template>
  <div>
    <Drawer class-name="case-detail" v-if="caseDetail.id !== 0" v-model="showCaseDetail" @on-close="closeDetailPanel"
      :width="40" :mask="false">
      <div slot="header">
        <span class="case-id">
          <span style="padding-top: 7px; text-align: center; display:inline-block;">#{{ caseID }}</span>
          <label-editor-input :id="caseDetail.id" @updateValue="updateCaseTitle" placeHolder="问题标题"
            style="display: inline-block;" :displayWidth="400" :displayText="caseDetail.Title">
            <template slot-scope="slotProps">
              <span
                :style="' text-overflow:ellipsis; overflow: hidden; white-space: nowrap;display: inline-block; padding-left:20px; width:80%;'">
                <span> {{ caseDetail.Title }}</span>
                <span>
                  <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
                </span>
              </span>
            </template>
          </label-editor-input>
        </span>
        <div>
          <span class="css-1avq18a-actionGroup">
            <span style="padding-right:10px;">标签</span>
            <label-editor-dropdwon-item @updateValue="setCaseTag" :itemID="caseDetail.id" :value="caseDetail.Priority"
              :itemList="dropdownEditorTags" :displayColor="caseDetail.view_data.tag_color"
              :displayText="caseDetail.view_data.priority"></label-editor-dropdwon-item>
            <span style="padding-left:10px; padding-right:10px;">准入用例</span>
            <Select v-model="accessTest" clearable style="width:50px" @on-change="selectAccessTestModel($event)">
              <Option v-for="item in accessTestList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>

            <span style="padding-left:10px; padding-right:10px;">自动化用例</span>
            <Select v-model="isOrNotAutomate" clearable style="width:50px" @on-change="selectAutomateModel($event)">
              <Option v-for="item in automateList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>

            <span style="padding-left:10px; padding-right:10px;">自动化完成</span>
            <Select v-model="automatedCompletion" clearable style="width:50px"
              @on-change="selectAutomatedCompletionModel($event)">
              <Option v-for="item in automatedCompletionList" :value="item.value" :key="item.value">{{ item.label }}
              </Option>
            </Select>
          </span>
        </div>
        <!--
        <div>
          <span class="css-1avq18a-actionGroup">
            <span style="padding-right:10px;">场景</span>
            <Select v-model="scenesInfo" clearable style="max-width:100%;width:400px" @on-change="selectModel($event)">
              <Option v-for="item in scenesList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </span>
        </div>
        -->
      </div>
      <div slot="close" style="padding-right:12px; padding-top:12px; display: inline-flex;align-items: center ">
        <Icon size="26" color="#0066ff" type="md-close-circle" />
      </div>
      <div class="case-detail-container"
        :style="'padding-top:16px;padding-left: 10px; height:' + containerHeight + 'px;overflow-y:auto;overflow-x:hidden;'">
        <div class="case-title">
          <div>
            <span>用例描述</span>
          </div>
          <div class="case-desc">
            <Input v-model="caseDetail.Desc" maxlength="1000" show-word-limit type="textarea" placeholder="用例描述"
              :rows="5" />
          </div>
        </div>
        <div class="case-title">
          <div>
            <span>前置条件</span>
          </div>
          <div class="case-desc">
            <Input v-model="caseDetail.Precondition" maxlength="1000" show-word-limit type="textarea" placeholder="前置条件"
              :rows="8" />
          </div>
        </div>
        <div class="case-title">
          <div>
            <span>预期结果</span>
          </div>
          <div class="case-desc">
            <Input v-model="caseDetail.ExpectResult" maxlength="1000" show-word-limit type="textarea" placeholder="预期结果"
              :rows="8" />
          </div>
        </div>
        <div>
          <Button type="success" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
            @click="saveCaseDesc">保存</Button>
        </div>
      </div>
    </Drawer>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import {
  getTestCase,
  updateCaseTag,
  setCaseTitle,
  updateTestCase,
} from "../business-service/ProjectTestCaseApiService";
import labelEditorVueEditor from "../../../../components/common/LabelEditor-VUEEditor.vue";
import labelEditorInput from "../../../../components/common/LabelEditor-Input.vue";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";

export default {
  name: "projectTestCaseContent",
  props: ["projectID", "groupID", "caseID", "showCase"],
  data() {
    return {
      filterTagIds: [],
      showCaseDetail: false,
      dropdownEditorTags: [],
      Scenes: "",
      automate: "",
      caseDetail: {
        id: 0,
        Desc: "",
        ExpectResult: null,
        Precondition: null,
      },
      scenesList: [],
      scenesInfo: "",
      automateList: [],
      isOrNotAutomate: "",
      accessTest: "",
      accessTestList: [],
      automatedCompletion: "",
      automatedCompletionList: [],
      tag_dt: {},
      isOrNot_dt: {},
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapState(["appBodyMainHeight", "appBodyWidth"]),
    ...mapState('testcase', ['testCaseTagList']),
    ...mapGetters("testcase", [
      "testCaseGroupTitle",
      "caseFilters",
    ]),
    containerHeight: function () {
      return this.appBodyMainHeight - 50;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },

    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", ["setCaseFilters", "setRefreshCase"]),

    selectModel: function (e) {
      this.caseDetail.Scenes = e;
    },

    selectAutomateModel: function (e) {
      this.caseDetail.automate = e;
    },

    selectAccessTestModel: function (e) {
      this.caseDetail.accessTest = e;
    },
    selectAutomatedCompletionModel: function (e) {
      this.caseDetail.automatedCompletion = e;
    },

    setCaseTag: function (caseID, tag) {
      updateCaseTag(caseID, this.tag_dt[tag]);
      this.setRefreshCase(true);
    },

    filterCaseGroup: function (keyword) {
      let value = { type: 1, filters: [keyword] };
      this.setCaseFilters(value);
    },

    filterCaseByTag: function (value) {
      let tagID = parseInt(value);
      let tagIndex = this.filterTagIds.indexOf(tagID);
      if (tagIndex > -1) {
        this.filterTagIds.splice(tagIndex, 1);
      } else {
        this.filterTagIds.push(tagID);
      }
      let filters = { type: 2, filters: this.filterTagIds };
      this.setCaseFilters(filters);
    },

    loadTestCase: function (caseID) {
      getTestCase(caseID).then(
        (response) => {
          //console.log(response);
          this.caseDetail = response.data.result;
          for (let i = 0; i < this.scenesList.length; i++) {
            if (this.scenesList[i].value == this.caseDetail.Scenes) {
              this.scenesInfo = this.scenesList[i].value;
              break;
            }
          }

          if (this.caseDetail.automate == 1) {
            this.isOrNotAutomate = 1;
          } else {
            this.isOrNotAutomate = 0;
          }

          if (this.caseDetail.accessTest == 1) {
            this.accessTest = 1;
          } else {
            this.accessTest = 0;
          }

          if (this.caseDetail.automatedCompletion == 1) {
            this.automatedCompletion = 1;
          } else {
            this.automatedCompletion = 0;
          }
        },
        (response) => { }
      );
    },

    loadCaseTags: function () {
      for (let i = 0; i < this.testCaseTagList.length; i++) {
        let tempTag = { label: "", value: 0, color: "" };
        tempTag.label = this.testCaseTagList[i].TagName;
        tempTag.value = this.testCaseTagList[i].id;
        tempTag.color = this.testCaseTagList[i].TagColor;
        this.tag_dt[this.testCaseTagList[i].id] = this.testCaseTagList[i].TagValue;
        this.dropdownEditorTags.push(tempTag);
      }
    },

    loadScenesTags: function () {
      this.$axios.get("/api/project/" + this.projectID + "/scenes/info").then(
        (response) => {
          this.scenesList = response.data.result;
        },
        (response) => { }
      );
    },

    loadAutomoteTags: function () {
      let arrayPush = [];
      this.$axios.get("/api/project/tags?TagType=7").then(
        (response) => {
          let tempData = response.data.result;
          for (let i = 0; i < tempData.length; i++) {
            arrayPush.push({
              value: tempData[i].TagValue,
              label: tempData[i].TagName,
            });
            this.isOrNot_dt[tempData[i].TagValue] = tempData[i].TagName;
          }
          this.automateList = arrayPush;
          this.accessTestList = arrayPush;
          this.automatedCompletionList = arrayPush;

          this.isOrNotAutomate = this.automateList[1].value;
          this.accessTest = this.accessTestList[1].value;
          this.automatedCompletion = this.automatedCompletionList[1].value;
        },
        (response) => { }
      );
    },

    updateCaseTitle: function (value, id) {
      this.caseDetail.Title = value;
      setCaseTitle(value, id);
      this.renameCase = 0;
      this.setRefreshCase(true);
    },

    saveCaseDesc: function () {
      let parameters = { Desc: this.caseDetail.Desc };
      parameters["ExpectResult"] = this.caseDetail.ExpectResult;
      parameters["Precondition"] = this.caseDetail.Precondition;
      parameters["Scenes"] = this.caseDetail.Scenes;
      parameters["automate"] = this.caseDetail.automate;
      parameters["accessTest"] = this.caseDetail.accessTest;
      parameters["automatedCompletion"] = this.caseDetail.automatedCompletion;
      updateTestCase(this.caseDetail.id, parameters).then(
        (response) => {
          this.$Message.success({
            content: "用例信息保存成功",
            duration: 3,
            closable: true,
          });
          this.setRefreshCase(true);
        },
        (response) => {
          this.$Message.error({
            content: "用例信息保存失败",
            duration: 3,
            closable: true,
          });
        }
      );
    },

    closeDetailPanel: function () {
      this.$emit("closeDrawer", false);
    },
  },
  created: function () {
    if (this.caseID && this.caseID > 0) {
      this.loadTestCase(this.caseID);
    }
    this.loadCaseTags();
    this.loadScenesTags();
    this.loadAutomoteTags();
  },
  mounted: function () { },
  watch: {
    versionID: function (value) { },

    caseID: function (value) {
      if (value && value > 0) {
        this.loadTestCase(this.caseID);
        this.showCaseDetail = true;
      }
    },

    showCase: function (value) {
      this.showCaseDetail = value;
    },
  },

  components: {
    labelEditorInput,
    labelEditorVueEditor,
    labelEditorDropdwonItem,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.ivu-select-single .ivu-select-selection {
  height: 22px;
}

.case-title {
  display: block;
  padding-bottom: 16px;
  color: #202d40;
  font-weight: bold;
}

.case-desc {
  display: block;
  min-height: 100px;
  color: #8c96a0;
  padding-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
}

.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  line-height: 22px;
}

.ivu-select-single .ivu-select-selection .ivu-select-placeholder {
  line-height: 22px;
}
</style>
