<template>
  <div style="height: 100%;width:100%">
    <div class="css-TestCaseTree-SectionTreeView">
      <Spin size="large" fix v-if="spinShow"></Spin>
      <el-tree ref="caseTree" :data="testCaseTree" :show-checkbox="checkbox" @node-drop="handleDrop"
        icon-class="el-icon-caret-right" :filter-node-method="filterNode" :highlight-current="true" draggable
        empty-text="请选择要查看的用例分组" :expand-on-click-node="false" :allow-drop="allowDrop" :allow-drag="allowDrag"
        :props="defaultProps" :default-expand-all="true">
        <span class="css-TestCaseTree-node" slot-scope="{ node, data }" @mouseover="onMouseOver(data.id)"
          @mouseout="onMouseOut(data.id)">
          <span v-if="data.IsGroup" class="css-TestCaseTree-node">
            <span class="css-CaseTree-folderIcon-SectionIcon"></span>
            <span class="css-CaseTree-SectionName-textEllipsis">{{ node.label }}</span>
          </span>
          <span v-if="!data.IsGroup && data.id !== -1">
            <span style="display:inline-flex">
              <span style="font-size: 13px;">
                <Checkbox></Checkbox>
                <Icon :size="16" type="ios-document-outline" />
                <span>{{ data.id }}</span>
              </span>
              <span class="css-CaseTree-SectionName-textEllipsis">
                <label-editor-input-external-trigger @clickText="showCaseDetail" url=""
                  style="display: inline-flex;color: #606266" :clickEdit="renameCase"
                  @cancelUpdate="cancelUpdateCaseTitle" @updateValue="updateCaseTitle" placeHolder="分组名称" :id="data.id"
                  :displayWidth="500" :displayText="data.Title">
                </label-editor-input-external-trigger>
              </span>
            </span>
          </span>
          <span v-if="!data.IsGroup && data.id !== -1">
            <span style="display:inline-flex;align-items: center; padding: 0px 4px 0px 4px;">
              <span class="css-SectionControls" v-show="showNodeToolID === data.id">
                <span @click="copyCase(data.id, data.Parent)" style="display: block;padding:0px 1px 0px 1px;">
                  <Icon type="ios-copy" />
                </span>
                <span @click="renameCaseTitle(data.id)" style="display: block;padding:0px 1px 0px 1px;">
                  <Icon type="md-create" />
                </span>
                <span @click="deleteCase(data.id, data.Title)" style="display: block;padding:0px 1px 0px 1px;">
                  <Icon type="ios-trash" />
                </span>
              </span>
              <span style="width: auto; display:inline-flex;align-items: center; padding: 0px 4px 0px 4px;">
                <label-editor-dropdwon-item @updateValue="setCaseTag" :itemID="data.id" :value="data.Priority"
                  :itemList="tagList" :displayColor="data.view_data.tag_color"
                  :displayText="data.view_data.priority"></label-editor-dropdwon-item>
              </span>
              <el-tooltip effect="dark" :content="data.scenes_name" placement="top">
                <span style="width: auto;display:inline-flex;align-items: left">
                  {{ data.scenes_name }}
                </span>
              </el-tooltip>
              <span v-show="data.accessTest" style="width: auto;display:inline-flex; padding: 0px 4px 0px 4px;">
                <Tooltip theme="light" content="准入用例">
                  <Icon type="ios-key" />
                </Tooltip>
              </span>
              <span style="width: auto;display:inline-flex; padding: 0px 4px 0px 4px;">
                {{ data.RunTimes }}次
              </span>
            </span>
          </span>
          <span v-if="data.id === -1"
            style="border-bottom:1px solid #f5f7f9;width: 100%;display: inline-flex;padding-bottom: 5px;color: #9199a3">
            <span v-if="addCaseGroup !== data.Parent" class="add-new-case" style="width: 300px;">
              <span v-if="!data.IsGroup" style="font-size: 13px;">
                <i class="el-icon-plus"></i>
              </span>
              <span @click="newTestCase(data.Parent)" class="">{{ node.label }}</span>
            </span>
            <span v-if="addCaseGroup === data.Parent" style="display: inline-flex;padding-top: 8px;">
              <Input v-model="formData.Title" search icon="md-return-left" @on-search="addNewCase(data.Parent)"
                placeholder="用例名称" maxlength="100" show-word-limit style="width: 300px;">
              <ButtonGroup slot="suffix" style="width: 100px; margin-left: 10px; padding-top: 0px;" shape="circle"
                size="small">
                <Icon :size="20" @click="addNewCase(data.Parent)" color="black" class="cursor-hand"
                  type="md-checkmark" />
                <Icon :size="20" @click="cancelNewCase" color="black" class="cursor-hand" type="md-close" />
              </ButtonGroup>
              </Input>
            </span>
          </span>
        </span>
      </el-tree>
    </div>
    <test-case-detail style="margin-top: 100px;" @closeDrawer="closeDetail" :projectID="projectID" :groupID="groupID"
      :caseID="selectCaseID" :showCase="showCase"></test-case-detail>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import labelEditorInputExternalTrigger from "../../../../components/common/LabelEditorExternalTigger-Input.vue";
import labelEditorDropdwonItem from "../../../../components/common/LabelEditor-DropdownItem.vue";
import {
  updateTestCase,
  createTestCase,
  updateCaseTag,
  setCaseTitle,
  deleteTestCase,
  copyTestCase,
} from "../business-service/ProjectTestCaseApiService";
import {
  setNewButtonToTree,
  setNewNodeToCaseTree,
  removeNode,
} from "../business-service/ProjectTestCaseUtilService";
import TestCaseDetail from "./ProjectTestCaseDetail.vue";

export default {
  name: "projectTestCaseTree",
  props: ["projectID", "groupID", "checkbox", "caseID"],
  data() {
    return {
      columnItemHeight: 200,
      showNodeToolID: 0,
      selectCaseID: 0,
      showCase: false,
      renameCase: 0,
      addCaseGroup: -1,
      formData: {
        Title: "新建用例",
        Parent: 0,
        IsGroup: false,
        Project: 0,
        Priority: 2,
        Module: 0,
        RunTimes: 0,
        Creator: 0,
      },
      testCaseTree: [],
      defaultProps: {
        children: "children",
        label: "Title",
      },
      spinShow: false,
    };
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapState('testcase', ['testCaseTagList']),
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters(["appBodyMainHeight", "appBodyWidth",]),
    ...mapGetters("testcase", [
      "testCaseGroupTitle",
      "caseFilters",
      "refreshCase",
    ]),
    containerHeight: function () {
      return this.appBodyMainHeight - 10;
    },

    containerWidth: function () {
      return this.appBodyWidth;
    },
    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      }
      if (this.$route.params.versionID) {
        return this.$route.params.versionID;
      }
      return 0;
    },
    tagList: function () {
      return this.getCaseTags()
    },
    project: function () {
      let result = 0;
      if (this.projectID) {
        result = this.projectID;
      }
      return result;
    },
  },
  methods: {
    ...mapGetters('testcase', ['getCaseTags']),
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("testcase", ["setTestCaseGroupTitle", "setSearchKeyword", "setRefreshCase", "setShowCaseID",]),

    onMouseOver: function (id) {
      this.showNodeToolID = id;
    },

    onMouseOut: function () {
      this.showNodeToolID = 0;
    },

    handleDrop: function (draggingNode, dropNode, dropType, ev) {
      let parameters = { Parent: dropNode.data.id };
      if (dropType !== "inner") {
        parameters.Parent = dropNode.data.Parent;
      }
      updateTestCase(draggingNode.data.id, parameters).then(
        (response) => {
          this.$Message.success({
            content: "分组更新成功",
            duration: 3,
            closable: true,
          });
        },
        (response) => {
          this.$Message.error({
            content: "分组更新失败",
            duration: 3,
            closable: true,
          });
        }
      );
    },

    allowDrag: function (draggingNode) {
      return draggingNode.data.Parent !== 0;
    },

    allowDrop: function (draggingNode, dropNode, type) {
      if (!dropNode.data.IsGroup) return false;
      else {
        if (dropNode.data.Parent === 0) {
          return type === "inner";
        } else {
          return true;
        }
      }
    },

    filterNode(value, data) {
      //console.log(value, data);
      if (value.type === 1) {
        if (!value.filters[0]) return true;
        return data.Title.indexOf(value.filters[0]) !== -1;
      }
      if (value.type === 2) {
        if (value.filters.length === 0) return true;
        //console.log(data.Priority);
        return value.filters.indexOf(data.Priority) > -1;
      }
      if (value.type === 3) {
        if (value.filters.length === 0) return true;
        //console.log("accessTest filter=", data.accessTest);
        return value.filters.indexOf(data.accessTest) > -1;
      }
    },

    renameCaseTitle: function (id) {
      this.renameCase = id;
    },

    cancelUpdateCaseTitle: function () {
      this.renameCase = 0;
    },

    updateCaseTitle: function (value, id) {
      setCaseTitle(value, id);
      this.renameCase = 0;
      this.setRefreshCase(true);
      this.$emit("closeDrawer", false);
    },

    setCaseTag: function (caseID, tag) {
      updateCaseTag(caseID, tag);
      this.setRefreshCase(true);
    },

    showCaseDetail: function (caseID) {
      this.showCase = true;
      this.selectCaseID = caseID;
    },

    closeDetail: function (value) {
      this.showCase = value;
    },

    loadTestCaseTree: function () {
      this.$axios.get("/api/project/" + this.projectID + "/testcase/tree?IsGroup=0&Root=" + this.groupID).then((response) => {
        this.spinShow = false;
        this.testCaseTree = response.data.result;
        setNewButtonToTree(this.testCaseTree[0]);
      },
        (response) => { }
      );
    },

    newTestCase: function (id) {
      this.addCaseGroup = id;
    },

    cancelNewCase: function () {
      this.addCaseGroup = -1;
    },

    createNewTestCase: function (parameters) {
      createTestCase(this.projectID, parameters).then(
        (response) => {
          if (parameters.Parent === 0) {
            this.testCaseTree.push(response.data.result);
          } else {
            for (let i = 0; i < this.testCaseTree.length; i++) {
              setNewNodeToCaseTree(
                this.testCaseTree[i],
                parameters.Parent,
                response.data.result
              );
            }
          }
          //            this.$refs.caseTree.filter('');
          this.$Message.success({
            content: "用例成功添加",
            duration: 3,
            closable: true,
          });
        },
        (response) => { }
      );
    },

    addNewCase: function (parent) {
      if (this.formData.Title.trim().length === 0) {
        this.$Message.error({
          content: "用例名称不能为空",
          duration: 3,
          closable: true,
        });
      } else {
        let parameters = this.formData;
        parameters.Project = this.projectID;
        parameters.Creator = this.userInfo.id;
        parameters.Parent = parent;
        parameters.IsGroup = false;
        parameters.Priority = this.tagList[1].value;
        this.createNewTestCase(parameters);
        this.addCaseGroup = 0;
      }
    },

    deleteCase: function (id, title) {
      this.$Modal.confirm({
        title: "删除确认",
        content: "您即将删除用例/组[" + title + "]",
        onOk: () => {
          deleteTestCase(id, title).then(
            (response) => {
              //console.log(this.testCaseTree);
              for (let i = 0; i < this.testCaseTree.length; i++) {
                if (this.testCaseTree[i].id === id) {
                  this.testCaseTree.splice(i, 1);
                } else {
                  removeNode(this.testCaseTree[i], id);
                }
              }
            },
            (response) => { }
          );
        },
        onCancel: () => { },
      });
    },

    copyCase: function (caseID, parent) {
      copyTestCase(caseID).then(
        (response) => {
          if (parent === 0) {
            this.testCaseTree.push(response.data.result);
          } else {
            for (let i = 0; i < this.testCaseTree.length; i++) {
              setNewNodeToCaseTree(
                this.testCaseTree[i],
                parent,
                response.data.result
              );
            }
          }
        },
        (response) => { }
      );
    },
  },
  created: function () {
    if (this.groupID !== undefined) {
      this.loadTestCaseTree();
    }
  },
  mounted: function () {
    //this.spinShow = true;
  },
  watch: {
    groupID: function (value) {
      if (this.groupID !== "0") {
        this.spinShow = true;
        this.loadTestCaseTree();
      }
    },
    caseFilters: function (value) {
      this.$refs.caseTree.filter(value);
    },

    refreshCase: function (value) {
      //console.log(value);
      if (value) {
        this.spinShow = true;
        this.loadTestCaseTree();
        this.setRefreshCase(false);
      }
    },
    caseID: function (value) {
      //console.log(value);
      this.selectCaseID = value;
    },
  },

  components: {
    labelEditorInputExternalTrigger,
    labelEditorDropdwonItem,
    TestCaseDetail,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.css-TestCaseTree-SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px 20px;
  position: relative;
}

.SectionTreeView {
  overflow-y: auto;
  flex: 1 1 auto;
  padding: 8px 4px;
}

.css-TestCaseTree-node {
  flex: 1;
  display: flex;
  height: 28px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  //padding-right: 8px;
}

.css-CaseTree-folderIcon-SectionIcon {
  display: inline-block;
  width: 24px;
  height: 24px;
  flex: 0 0 16px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDQ5LjEgKDUxMTQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5mb2xkZXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iVGVzdC1DYXNlLS0tQ3JlYXRlLSZhbXA7LUVkaXQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSLnlKjkvovnrqHnkIYt5Yib5bu655So5L6LLemAieaLqeaooeWdlyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTQ2LjAwMDAwMCwgLTcyLjAwMDAwMCkiPgogICAgICAgICAgICA8ZyBpZD0ibGVmdC1zaWRlIj4KICAgICAgICAgICAgICAgIDxnIGlkPSJjYXNlLXNlY3Rpb24tbGlzdCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4wMDAwMDAsIDY0LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJjYXNlLXNlY3Rpb24iPgogICAgICAgICAgICAgICAgICAgICAgICA8ZyBpZD0ic2VjdGlvbi1yb3ciPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGcgaWQ9ImZvbGRlciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDYuMDAwMDAwLCA4LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxnIGlkPSJ5ZWxsb3ciPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMS4wMDAwMDApIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0wLDIgTDE2LDIgTDE2LDEzIEMxNiwxMy41NTIyODQ3IDE1LjU1MjI4NDcsMTQgMTUsMTQgTDEsMTQgQzAuNDQ3NzE1MjUsMTQgNi43NjM1Mzc1MWUtMTcsMTMuNTUyMjg0NyAwLDEzIEwwLDIgWiIgaWQ9IlJlY3RhbmdsZS0xMCIgZmlsbD0iI0ZGRDk0QiI+PC9wYXRoPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTEsMCBMNi44NTcxNDI4NiwwIEw4LDIgTDAsMiBMMCwxIEMtNi43NjM1Mzc1MWUtMTcsMC40NDc3MTUyNSAwLjQ0NzcxNTI1LDEuMDE0NTMwNjNlLTE2IDEsMCBaIiBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRjlDNDMzIj48L3BhdGg+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==) center center / 18px 18px no-repeat;
}

.css-CaseTree-SectionName-textEllipsis {
  min-width: 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1 1 auto;
  padding: 0px 4px 0px 4px;
  //padding-left: 4px;
  overflow: hidden;
  color: #606266;
  margin: 0px 4px 0px 4px;
}

.css-142qn3c-SectionTreeView .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px;
  cursor: pointer;
  padding: 4px;
}

input {
  outline-color: invert;
  outline-style: none;
  outline-width: 0px;
  border: none;
  border-style: none;
  text-shadow: none;
  -webkit-appearance: none;
  -webkit-user-select: text;
  outline-color: transparent;
  box-shadow: none;
}

.add-new-case:hover {
  color: #0066ff;
}

.ivu-input {
  display: inline-block;
  width: 100%;
  height: 26px;
  line-height: 1.5;
  padding: 4px 7px;
  font-size: 14px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #5578aa;
  background-color: #fff;
  background-image: none;
  position: relative;
  cursor: text;
}

.ivu-input-icon {
  width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 16px;
  text-align: center;
  color: #808695;
  position: absolute;
  right: 0;
  z-index: 3;
}

.text-display {
  padding: 0px 0px;
}

.ivu-dropdown-menu {
  min-width: 200px;
}
</style>
