<template>
  <div :style="'height:' + containerHeight + 'px'">
    <Layout>
      <Sider hide-trigger :width="testCasePlanWidth" :style="{ background: '#fff', 'border-left': '1px solid #fff', }">
        <test-case-panel :projectID="projectID"></test-case-panel>
      </Sider>
      <Content>
        <test-case-content :projectID="projectID" :groupID="groupID" :caseID="caseID"></test-case-content>
      </Content>
    </Layout>
  </div>
</template>

<script>
import TestCasePanel from "./ProjectTestCasePanel.vue";
import TestCaseContent from "./ProjectTestCaseContent.vue";

import { mapGetters, mapMutations, mapActions, mapState } from "vuex";

export default {
  name: "projectTestingCase",
  props: [
    "projectID",
    "groupID",
    "caseID",
  ],
  data() {
    return {
      testCasePlanWidth: 360,
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight', 'appBodyWidth', 'appBodyHeight']),
    ...mapState('testcase', ['testCasePlanShow']),
    containerHeight: function () {
      return this.appBodyHeight
    },
  },
  methods: {
    ...mapActions('testcase', ['getTestCaseTags'])
  },

  created: function () {
    //console.log('ProjectTesting.vue created')
    this.projectID = parseInt(this.$route.params.projectID)
    this.getTestCaseTags()
  },

  watch: {
    testCasePlanShow: function (value) {
      if (value == true) {
        this.testCasePlanWidth = 360;
      } else {
        this.testCasePlanWidth = 0;
      }
    },
  },
  components: {
    TestCasePanel,
    TestCaseContent
  }
}
</script>
