//初始化项目以及版本信息


let setNewNodeToGroupTree = (node,id,childNode) => {
  if(node.children) {

    if (node.id === id) {
      node.children.push(childNode)
      return
    } else  {
      for(let i =0; i< node.children.length; i++)
      {
        setNewNodeToGroupTree(node.children[i],id,childNode)
      }
    }
  }
  return
}

let setNewNodeToCaseTree = (node,id,childNode) => {
  if(node.children) {

    if (node.id === id) {
      node.children.splice(node.view_data.child_case_count,0,childNode)
      node.view_data.child_case_count = node.view_data.child_case_count+1
      return
    } else  {
      for(let i =0; i< node.children.length; i++)
      {
        setNewNodeToCaseTree(node.children[i],id,childNode)
      }
    }
  }
  return
}

let setNewButtonToTree = (node) => {
  if(node.children) {
    let new_button_data = {id: -1,Parent: node.id,Title: "添加用例"}
    if(node.children.length>0) {
      for(let i =0; i< node.children.length; i++)
      {
        setNewButtonToTree(node.children[i])
      }
      node.children.splice(node.view_data.child_case_count,0,new_button_data)
    } else {
      node.children.push(new_button_data)
    }
  }
  return
}


let removeNode = (node,id) =>{
  if(node.children) {
    if(node.children.length>0) {
      for(let i =0; i< node.children.length; i++)
      {
        if(node.children[i].id === id) {
          node.children.splice(i,1)
          return
        }
      }
      for(let i =0; i< node.children.length; i++)
      {
        removeNode(node.children[i],id)
      }
    }
  }
  return
}

let transferTaskStatusDropdownItemData = (taskStatusData) => {
  let tagList = []
  for(let i =0; i< taskStatusData.length; i++) {
    let tempTag = {label: '',value: 0,color: ''}
    tempTag.label = taskStatusData[i].Desc
    tempTag.value = taskStatusData[i].Status
    tempTag.color = ''
    tagList.push(tempTag)
  }
  return tagList
}




let mindmapFileValidateRules = {

  FileName: [
    { type: 'string', required: true, min: 1, max: 50, message: '文件名称长度必须在1-50个字符之间！', trigger: 'blur' }
  ],
  ProjectVersion: [
    { type: 'array', required: true,len: 2, message: '请选择版本或者创建版本后再创建测试点！' }
  ],
}

export {
  mindmapFileValidateRules,
  setNewNodeToGroupTree,
  setNewButtonToTree,
  setNewNodeToCaseTree,
  removeNode,
  transferTaskStatusDropdownItemData
}
