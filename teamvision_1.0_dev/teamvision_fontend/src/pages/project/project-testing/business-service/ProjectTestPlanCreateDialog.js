//初始化项目以及版本信息
import axios from 'axios'

let testplanValidateRules = {
  Version: [
    { type: 'number', required: true, min: 1, message: '请选择版本', trigger: 'change' }
  ],
  Title: [
    { type: 'string', required: true, min: 1, max: 50, message: '问题标题长度在1-50个字符之间', trigger: 'blur' }
  ],
  fortestings: [
    { type: 'array', required: true, 'min-len': 1, message: '必须关联一个提测', trigger: 'change' }
  ],
  requireNum: [
    { type: 'string', required: true, min: 1, max: 99, message: '需求个数不能为空', trigger: 'blur' }
  ],
  estimatedDate: [
    { type: 'array', required: true, min: 2, message: '请选择预计开始 - 结束时间', trigger: 'change' },
  ],
  writeTestCases: [
    { type: 'array', required: true, min: 2, message: '请选择编写测试用例开始 - 结束时间', trigger: 'change' }
  ],
  testCaseReview: [
    { type: 'array', required: true, min: 2, message: '请选择测试用例评审的时间', trigger: 'change' }
  ],
  execCase: [
    { type: 'array', required: true, min: 2, message: '请选择测试用例执行的开始 - 结束时间', trigger: 'change' }
  ],
  performanceTest: [
    { type: 'array', required: true, min: 2, message: '请选择性能测试的开始 - 结束时间', trigger: 'change' }
  ],
}

export {
  testplanValidateRules,
}
