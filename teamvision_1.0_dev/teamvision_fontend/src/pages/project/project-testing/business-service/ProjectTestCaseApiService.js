//初始化项目以及版本信息

import axios from 'axios'
import { Message, Modal } from 'view-design'

let testCaseViewUpdateDeleteApi = '/api/project/testcase/'  // /api/project/testcase/<case_id>
let testCaseTagApi = '/api/project/tags?TagType=6'
let testCaseList = '/api/project/testcases?'
let testPlanCreate = '/api/project/testplan/create'
let CaseReviewCreate = '/api/project/{projectID}/casereview'
let versionPlanApi = '/api/project/'
let caseResultApi = '/api/project/task/task_status?Type='
let planCaseResults = '/api/project/testplan/case_results?TestPlan='
let planDetailUri = '/api/project/testplan/'
let planCaseTree = '/api/project/testplan/{PLANID}/case_tree'
let testReportCreate = '/api/project/testreport/create'
let updateReport = '/api/project/testreport/'
let updateTestPlanOwnerApi = '/api/project/testplan/{PLANID}/update_owners/'
let testPlanCaseResultUrl = '/api/project/testplan/case_result/'
let updateTestPlanApi = '/api/project/testplan/'
let scrapTestCaseGroupApi = '/api/project/testcase/{case_id}/updatestatus'
let planVersionsApi = '/api/project/{project_id}/testplan/versions'
let testReportWebParts = '/api/project/{projectID}/testreport/webparts'
let testPlanCaseUrl = '/api/project/testplan/case/{CASEID}'

let createTestReport = (parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post(testReportCreate, parameters).then(response => {
      Message.success({
        content: '报告创建成功',
        duration: 3,
        closable: true
      })
      resolve(response)
    }, response => {
      Message.error({
        content: '报告创建失败',
        duration: 3,
        closable: true
      })
      reject(response)
    })

  })
  return initPromise
}

let updateTestCase = (caseID, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(testCaseViewUpdateDeleteApi + caseID, parameters).then(response => {
      let initData = response.data.result
      resolve(initData)
    }, response => {
      reject(response.data.result)
    })
  })
  return initPromise
}

let deleteTestCase = (caseID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.delete(testCaseViewUpdateDeleteApi + caseID).then(response => {
      Message.success({
        content: '用例/组成功删除',
        duration: 3,
        closable: true
      })
      let initData = response
      resolve(initData)
    }, response => {
      Message.error({
        content: '用例/组删除失败',
        duration: 3,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

let scrapTestCase = (caseId, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(scrapTestCaseGroupApi.replace('{case_id}', caseId), parameters).then(response => {
      Message.success({
        content: '操作成功',
        duration: 3,
        closable: true
      })
      let initData = response.data.result
      resolve(initData)
    }, response => {
      Message.error({
        content: '操作失败',
        duration: 3,
        closable: true
      })
      reject(response.data.result)
    })
  })
  return initPromise
}

let copyTestCase = (caseID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post(testCaseViewUpdateDeleteApi + caseID + '/copy').then(response => {
      Message.success({
        content: '用例复制成功',
        duration: 10,
        closable: true
      })
      let initData = response
      resolve(initData)
    }, response => {
      Message.error({
        content: '用例复制失败',
        duration: 10,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

let getTestReportWebPartList = (projectID) => {

  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testReportWebParts.replace('{projectID}', projectID)).then(response => {
      let initData = response
      resolve(initData)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let updateCaseTag = (caseID, tag) => {
  let parameters = { Priority: tag }
  updateTestCase(caseID, parameters).then(response => {
    Message.success({
      content: '用例优先级修改成功',
      duration: 3,
      closable: true
    })
  }, response => {
    Message.error({
      content: '用例优先级修改失败',
      duration: 3,
      closable: true
    })
  })

}

let setCaseTitle = (value, id) => {
  let parameters = { Title: value }
  updateTestCase(id, parameters).then(response => {
    Message.success({
      content: '用例组名称修改成功',
      duration: 3,
      closable: true
    })
  }, response => {
    Message.error({
      content: '用例组名称修改失败',
      duration: 3,
      closable: true
    })
  })

}

let getTestCase = (caseID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testCaseViewUpdateDeleteApi + caseID).then(response => {
      let initData = response
      resolve(initData)
    }, response => {

      reject(response)
    })
  })
  return initPromise
}

let getPlanCaseResults = (planId) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(planCaseResults + planId).then(response => {
      let initData = response
      resolve(initData)
    }, response => {

      reject(response)
    })
  })
  return initPromise
}

let getTestCaseList = (parentID, All) => {
  let initPromise = new Promise(function (resolve, reject) {
    let parameters = 'Parent=' + parentID + '&IsGroup=' + All
    axios.get(testCaseList + parameters).then(response => {
      let initData = response
      resolve(initData)
    }, response => {

      reject(response)
    })
  })
  return initPromise
}

let getPlanCaseTree = (planId) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(planCaseTree.replace('{PLANID}', planId)).then(response => {
      let initData = response
      resolve(initData)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}


let getTestCaseResults = (statusType) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(caseResultApi + statusType).then(response => {
      let initData = response
      resolve(initData)
    }, response => {

      reject(response)
    })
  })
  return initPromise
}

let createTestCase = (projectID, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post('/api/project/' + projectID + '/testcase/create', parameters).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let createTestPlan = (parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post(testPlanCreate, parameters).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}


let createCaseReview = (projectID, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post(CaseReviewCreate.replace('{projectID}', projectID), parameters).then(response => {
      Message.success({
        content: '创建成功',
        duration: 3,
        closable: true
      })
      resolve(response)
    }, response => {
      Message.success({
        content: '创建失败',
        duration: 3,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

let loadVersionsPlans = (projectID, versionId = 0) => {
  let initPromise = new Promise(function (resolve, reject) {
    let req_url = versionPlanApi + projectID + '/version/testplans'
    if (versionId != 0) {
      req_url = versionPlanApi + projectID + '/version/testplans?v=' + versionId
    }
    axios.get(req_url).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}


let loadPlanVersions = (projectID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(planVersionsApi.replace('{project_id}', projectID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let getVersionTestPlans = (versionID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get('/api/project/testplan/version/' + versionID).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadPlanDetail = (planId) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(planDetailUri + planId).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadCaseTagList = () => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testCaseTagApi).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let export2XmindFile = (groupID, groupName, version) => {
  let realUrl = '/api/project/testcase/group/' + groupID + '/export?' + 'version=' + version
  axios({ url: realUrl, method: 'get', responseType: 'arraybuffer' }).then(response => {
    let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }))
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', groupName + '(' + groupID + ').xmind')
    document.body.appendChild(link)
    link.click()
    link.remove()
  }, response => {
  })
}

let exportPytestFile = (groupID) => {
  let realUrl = '/api/project/testcase/group/' + groupID + '/exportpytest'
  axios({
    url: realUrl,
    method: 'get',
    responseType: 'arraybuffer'
  }).then(response => {
    let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }))
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    let disposition = response.headers["content-disposition"];
    let fileName = decodeURIComponent(disposition.split("filename=")[1]);
    link.setAttribute("download", fileName);
    document.body.appendChild(link)
    link.click()
    link.remove()
  })
}

let mindmapFileValidateRules = {

  FileName: [
    { type: 'string', required: true, min: 1, max: 50, message: '文件名称长度必须在1-50个字符之间！', trigger: 'blur' }
  ],
  ProjectVersion: [
    { type: 'array', required: true, len: 2, message: '请选择版本或者创建版本后再创建测试点！' }
  ],
}


let updateTestPlanOwner = (caseID, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(updateTestPlanOwnerApi.replace('{PLANID}', caseID), parameters).then(response => {
      let msg = response.data.result.message
      Message.success({
        content: msg,
        duration: 3,
        closable: false
      })
      resolve(response.data.result)
    }, response => {
      Message.error({
        content: '分配失败',
        duration: 3,
        closable: false
      })
      reject(response.data.result)
    })
  })
  return initPromise
}

let getTestPlanCaseApi = (execCaseID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testPlanCaseUrl.replace('{CASEID}', execCaseID)).then(response => {
      resolve(response.data.result)
    }, response => {
      reject(response.data.result)
    })
  })
  return initPromise
}

let updateTestPlanCase = (execCaseID, req_data) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(testPlanCaseResultUrl + execCaseID, req_data).then(response => {
      Message.success({
        content: execCaseID + '执行结果提交成功',
        duration: 2,
        closable: true
      })
      let resp_Data = response.data.result
      resolve(resp_Data)
    }, response => {
      Message.error({
        content: execCaseID + '执行结果提交失败',
        duration: 2,
        closable: true
      })
      reject(response.data.result)
    })
  })
  return initPromise
}

let deleteTestPlanApi = (planId) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.delete(planDetailUri + planId).then(response => {
      Message.success({
        content: '测试计划删除成功',
        duration: 2,
        closable: true
      })
      let initData = response
      resolve(initData)
    }, response => {
      Message.error({
        content: '测试计划删除失败',
        duration: 2,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

let updateTestPlanStatus = (planId, statusType) => {
  let reqdata = {
    "Status": statusType,
  }
  let testPlanStatus = {
    1: "新建",
    2: "开始测试",
    3: "完成",
    4: "归档",
    5: "暂停"
  }

  let initPromise = new Promise(function (resolve, reject) {
    axios.patch('/api/project/testplan/' + planId + '/update_status', reqdata).then(response => {
      Message.success({
        content: '测试计划' + testPlanStatus[statusType],
        duration: 2,
        closable: true
      })
      let initData = response
      resolve(initData)
    }, response => {
      Message.error({
        content: '测试计划' + testPlanStatus[statusType] + '失败',
        duration: 2,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

let updateTestPlan = (testPalnId, formItem) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(updateTestPlanApi + testPalnId, formItem).then(response => {
      Message.success({
        content: '更新成功',
        duration: 2,
        closable: true
      })
      let initData = response
      resolve(initData)
    }, response => {
      Message.error({
        content: '更新失败',
        duration: 2,
        closable: true
      })
      reject(response)
    })
  })
  return initPromise
}

export {
  mindmapFileValidateRules,
  updateTestCase,
  createTestCase,
  loadCaseTagList,
  getTestCase,
  updateCaseTag,
  setCaseTitle,
  deleteTestCase,
  scrapTestCase,
  copyTestCase,
  export2XmindFile,
  getTestCaseList,
  createTestPlan,
  loadVersionsPlans,
  getVersionTestPlans,
  getTestCaseResults,
  getPlanCaseResults,
  loadPlanDetail,
  getPlanCaseTree,
  createTestReport,
  updateTestPlanOwner,
  updateTestPlanCase,
  deleteTestPlanApi,
  updateTestPlanStatus,
  updateTestPlan,
  exportPytestFile,
  loadPlanVersions,
  getTestReportWebPartList,
  createCaseReview,
  getTestPlanCaseApi,
}
