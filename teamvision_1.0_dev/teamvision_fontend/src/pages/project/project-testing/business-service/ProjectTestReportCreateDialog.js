//初始化项目以及版本信息
import axios from 'axios'

let testReportValidateRules = {
  Title: [
    { type: 'string', required: true, min: 1, max: 50, message: '问题标题长度在1-50个字符之间', trigger: 'blur' }
  ],
  // Version: [
  //   { type: 'integer', required: false, message: '必须关联一个版本', trigger: 'blur', validator: (rule, value) => value > 0, }
  // ],
  TestPlan: [
    { type: 'number', required: true, message: '必须关联一个测试计划', trigger: 'change', validator: (rule, value) => value.length > 0, }
  ]
}

export {
  testReportValidateRules,
}
