//初始化项目以及版本信息

import axios from 'axios'

//api/project/testcase/<case_id>
let projectTagApi = '/api/project/tags?TagType='
let projectTaskStatustApi = '/api/project/task/task_status?Type='
let projectDetailUri = '/api/project/{projectID}/detail'
let projectMemberApi = '/api/project/{projectID}/project_members'
let projectVersionApi = '/api/project/{projectID}/versions'
let projectModulesApi = '/api/project/{projectID/modules'
let projectFortestingListApi = '/api/project/{projectID}/version/{VERSION}/fortestings?Status__in=2,3'
let projectTestPlans = '/api/project/{projectID}/testplans'
let projectRequirements = '/api/project/{projectID}/version/{VERSION}/requirements'
let projectTestReports = '/api/project/{projectID}/testreports'
let projectTestReportDetail = '/api/project/testreport/'
let projectTestReportMembers = '/api/project/testreport/{REPORTID}/teamates'
let projectTestReportCaseResultStatistics = '/api/project/testreport/{REPORTID}/caseresult/statistics'
let projectTestReportIssueStatistics = '/api/project/testreport/{REPORTID}/issue/statistics'
let projectIssueStatusCountUri = '/api/project/{projectID}/{VERSIONID}/statistics/status_summary?'
let testReportIssuesUri = '/api/project/testreport/{REPORTID}/issues'
let testReportCasesUri = '/api/project/testreport/{REPORTID}/cases'
let sendTestReportApiUri = '/api/project/testreport/{REPORTID}/send'


let getProjectInfo = (projectID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectDetailUri.replace('{projectID}', projectID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let getProjectTaskStatus = (statusType) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTaskStatustApi + statusType).then(response => {
      let initData = response
      resolve(initData)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectTagList = (tagType) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTagApi + tagType).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectMembers = (projectID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectMemberApi.replace('{projectID}', projectID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })

  })
  return initPromise
}

let getProjectIssueCountStatistics = (projectID, versionID, filters) => {

  let initPromise = new Promise(function (resolve, reject) {
    let url = projectIssueStatusCountUri.replace('{projectID}', projectID).replace('{VERSIONID}', versionID) + filters
    axios.get(url).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })

  })
  return initPromise
}


let loadProjectVersions = (projectID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectVersionApi.replace('{projectID}', projectID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectModules = (projectID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectModulesApi.replace('{projectID}', projectID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectTestReportCaseResultStatistics = (reportID) => {

  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestReportCaseResultStatistics.replace('{REPORTID}', reportID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })

  })
  return initPromise
}

let loadProjectTestReports = (projectID, filter) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestReports.replace('{projectID}', projectID) + filter).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectTestReportTeamMates = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestReportMembers.replace('{REPORTID}', reportID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadTestReportIssues = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testReportIssuesUri.replace('{REPORTID}', reportID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadTestReportCases = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(testReportCasesUri.replace('{REPORTID}', reportID)).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectRequirements = (projectID, versionID, filter) => {
  let initPromise = new Promise(function (resolve, reject) {
    let url = projectRequirements.replace('{projectID}', projectID)
    url = url.replace('{VERSION}', versionID)
    axios.get(url + filter).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectTestPlan = (projectID, filters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestPlans.replace('{projectID}', projectID) + filters).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectTestReport = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestReportDetail + reportID).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let loadProjectFortestings = (projectID, versionId) => {
  let initPromise = new Promise(function (resolve, reject) {
    let url = projectFortestingListApi.replace('{projectID}', projectID).replace('{VERSION}', versionId)
    axios.get(url).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let CopyProjectTestReport = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get(projectTestReportCopy + reportID + '/copy').then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}
let deleteProjectTestReport = (reportID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.delete(projectTestReportDetail + reportID).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let updateProjectTestReport = (reportID, parameters) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.patch(projectTestReportDetail + reportID, parameters).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

let sendTestReportApi = (reportID, reportImg = "") => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.post(sendTestReportApiUri.replace('{REPORTID}', reportID), reportImg).then(response => {
      resolve(response)
    }, error => {
      reject(error)
    })
  })
  return initPromise
}

let getForTesting = (fortestingID) => {
  let initPromise = new Promise(function (resolve, reject) {
    axios.get('/api/project/fortesting/' + fortestingID).then(response => {
      resolve(response)
    }, response => {
      reject(response)
    })
  })
  return initPromise
}

export {
  getProjectInfo,
  getProjectTaskStatus,
  loadProjectTagList,
  loadProjectVersions,
  loadProjectTestPlan,
  loadProjectMembers,
  loadProjectModules,
  loadProjectFortestings,
  loadProjectTestReports,
  loadProjectTestReport,
  loadProjectRequirements,
  CopyProjectTestReport,
  deleteProjectTestReport,
  loadProjectTestReportCaseResultStatistics,
  loadProjectTestReportTeamMates,
  updateProjectTestReport,
  getProjectIssueCountStatistics,
  loadTestReportIssues,
  loadTestReportCases,
  sendTestReportApi,
  getForTesting
}
