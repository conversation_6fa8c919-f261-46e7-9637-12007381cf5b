<template>
  <div>
    <Card :padding="2">
      <div slot="title">
        <span style="margin-right: 10px;">
          <Select v-model="selectedProject" style="width:200px" size="small" filterable clearable placeholder="选择项目"
            @on-change="filterMindFile">
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </span>
        <span style="margin-right: 10px;">
          <Select v-model="selectedProject" style="width:200px" size="small" filterable clearable placeholder="选择版本"
            @on-change="filterMindFile">
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </span>
        <RadioGroup v-model="fileType" type="button" size="small" @on-change="filterMindFile">
          <Radio label="3">全部</Radio>
          <Radio label="0">1个月</Radio>
          <Radio label="1">3个月</Radio>
          <Radio label="2">6个月</Radio>
        </RadioGroup>
      </div>
      <Row>
        <div style="height: 45px;background-color: white;margin-top: 0px;border-bottom: 1px solid #eef2f6;">
          <Col span="2">
          <span
            style="display: inline-block;padding: 16px 5px 5px 5px;text-align: center;width: 100%;color:#5578aa;">项目</span>
          </Col>
          <Col span="22">
          <Row>
            <Col span="2">
            <span
              style="display: inline-block;padding: 16px 5px 5px 5px;text-align: center;width: 100%;color:#5578aa;">功能模块</span>
            </Col>
            <Col span="22">
            <Row align="middle" style="padding: 16px 16px 6px 26px;">
              <Col span="16">
              </Col>
              <Col span="6">
              </Col>
              <Col span="2">
              </Col>
            </Row>
            </Col>
          </Row>
          </Col>
        </div>
      </Row>
      <div :style="'overflow-y:scroll;height:' + containerHeight + 'px;'">
        <Row align="middle" class="border1" type="flex">
          <Col span="2" style="text-align: center;">
          <div style="padding: 16px;">
            <div style="height: 20px; background-color:#5578aa"></div>
            <div class="projectBorder">
              <div>
                <Icon type="ios-radio-button-on" color="#5578aa"></Icon> Teamvision
              </div>
              <div>
                <div style="padding: 6px;font-size: 12px;">
                  需求总数： 120
                </div>
              </div>
            </div>
          </div>
          </Col>
          <Col span="22" style="border-left: 1px solid #eef2f6;">
          <Row align="middle" type="flex" style="border-bottom: 1px solid #f5f7f9;">
            <Col span="2" style="padding: 16px;text-align: center;">
            <div>
              <div style="height: 8px; background-color: #5578aa">
              </div>
              <div class="featureBorder">
                <div style="padding-bottom: 6px; ">我的报告</div>
                <Progress :stroke-width="3" style="padding: 0px !important;" :percent="10"
                  :stroke-color="['#108ee9', '#87d068']" />
              </div>
            </div>
            </Col>
            <Col span="22" style="padding: 16px; border-left: 1px solid #eef2f6;">
            <Row align="middle" type="flex">
              <Col span="24">
              <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
                <Col span="16">
                <span style="width:24px; display: inline-block;">
                  <tooltip content="需求优先级">
                    <Avatar style="background-color: #5578aa;" size="small">P1</Avatar>
                  </tooltip>
                </span>
                <span style="width:120px;display: inline-block;">
                  <tooltip content="预计上线日期">
                    <span
                      style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;margin-right: 10px;">
                      <i>2019年12月28日</i>
                    </span>
                  </tooltip>
                </span>
                <span style="padding-right: 10px;color: #5578aa;">
                  <tooltip content="规划版本">
                    <i>[1.0.0]</i>
                  </tooltip>
                </span>
                <span style="color: #5578aa;text-decoration: underline;cursor:pointer;">
                  需求主题
                </span>

                </Col>
                <Col span="2">
                <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                  <Icon type="ios-radio-button-on" color="#5578aa"></Icon> 待排期
                </span>
                </Col>
                <Col span="5">
                <span style="padding: 5px;">
                  <tooltip content="需求负责人">
                    <Avatar style="background-color: #22aaaa;">小一</Avatar>
                  </tooltip>
                </span>
                <span style="padding: 5px;">
                  <tooltip content="当前负责人">
                    <Avatar style="background-color: #5578aa;">小二</Avatar>
                  </tooltip>
                </span>
                </Col>
                <Col span="1">
                <span>
                  <Icon type="ios-more" size="32" />
                </span>
                </Col>
              </Row>
              </Col>
            </Row>
            <Row align="middle" type="flex">
              <Col span="24">
              <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
                <Col span="16">
                <span style="width:24px; display: inline-block;">
                  <tooltip content="需求优先级">
                    <Avatar style="background-color: #5578aa;" size="small">P1</Avatar>
                  </tooltip>
                </span>
                <span style="width:120px;display: inline-block;">
                  <tooltip content="预计上线日期">
                    <span
                      style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;margin-right: 10px;">
                      <i>2019年12月1日</i>
                    </span>
                  </tooltip>
                </span>
                <span style="padding-right: 10px;color: #5578aa;">
                  <tooltip content="规划版本">
                    <i>[1.0.0]</i>
                  </tooltip>
                </span>
                <span style="color: #5578aa;text-decoration: underline;"> 需求主题 </span>
                </Col>
                <Col span="2">
                <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                  <Icon type="ios-radio-button-on" color="#5578aa"></Icon> 待排期
                </span>
                </Col>
                <Col span="5">
                <span style="padding: 5px;">
                  <tooltip content="需求负责人">
                    <Avatar style="background-color: #22aaaa;">小一</Avatar>
                  </tooltip>
                </span>
                <span style="padding: 5px;">
                  <tooltip content="当前负责人">
                    <Avatar style="background-color: #5578aa;">小二</Avatar>
                  </tooltip>
                </span>
                </Col>
                <Col span="1">
                <span>
                  <Icon type="ios-more" size="32" />
                </span>
                </Col>
              </Row>
              </Col>
            </Row>
            <Row align="middle" type="flex">
              <Col span="24">
              <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
                <Col span="16">
                <span style="width:24px; display: inline-block;">
                  <tooltip content="需求优先级">
                    <Avatar style="background-color: #5578aa;" size="small">P1</Avatar>
                  </tooltip>
                </span>
                <span style="width:120px;display: inline-block;">
                  <tooltip content="预计上线日期">
                    <span
                      style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;margin-right: 10px;">
                      <i>2019年12月1日</i>
                    </span>
                  </tooltip>
                </span>
                <span style="padding-right: 10px;color: #5578aa;">
                  <tooltip content="规划版本">
                    <i>[1.0.0]</i>
                  </tooltip>
                </span>
                <span style="color: #5578aa;text-decoration: underline;"> 需求主题 </span>

                </Col>
                <Col span="2">
                <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                  <Icon type="ios-radio-button-on" color="#5578aa"></Icon> 待排期
                </span>
                </Col>
                <Col span="5">
                <span style="padding: 5px;">
                  <tooltip content="需求负责人">
                    <Avatar style="background-color: #22aaaa;">小一</Avatar>
                  </tooltip>
                </span>
                <span style="padding: 5px;">
                  <tooltip content="当前负责人">
                    <Avatar style="background-color: #5578aa;">小二</Avatar>
                  </tooltip>
                </span>
                </Col>
                <Col span="1">
                <span>
                  <Icon type="ios-more" size="32" />
                </span>
                </Col>
              </Row>
              </Col>
            </Row>
            <Row align="middle" type="flex">
              <Col span="24">
              <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
                <Col span="16">
                <span style="width:24px; display: inline-block;">
                  <tooltip content="需求优先级">
                    <Avatar style="background-color: #5578aa;" size="small">P1</Avatar>
                  </tooltip>
                </span>
                <span style="width:120px;display: inline-block;">
                  <tooltip content="预计上线日期">
                    <span
                      style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;margin-right: 10px;">
                      <i>2019年12月1日</i>
                    </span>
                  </tooltip>
                </span>
                <span style="padding-right: 10px;color: #5578aa;">
                  <tooltip content="规划版本">
                    <i>[1.0.0]</i>
                  </tooltip>
                </span>
                <span style="color: #5578aa;text-decoration: underline;"> 需求主题 </span>

                </Col>
                <Col span="2">
                <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                  <Icon type="ios-radio-button-on" color="#5578aa"></Icon> 待排期
                </span>
                </Col>
                <Col span="5">
                <span style="padding: 5px;">
                  <tooltip content="需求负责人">
                    <Avatar style="background-color: #22aaaa;">小一</Avatar>
                  </tooltip>
                </span>
                <span style="padding: 5px;">
                  <tooltip content="当前负责人">
                    <Avatar style="background-color: #5578aa;">小二</Avatar>
                  </tooltip>
                </span>
                </Col>
                <Col span="1">
                <span>
                  <Icon type="ios-more" size="32" />
                </span>
                </Col>
              </Row>
              </Col>
            </Row>
            <Row align="middle" type="flex">
              <Col span="24">
              <Row style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
                <Col span="16">
                <span style="width:24px; display: inline-block;">
                  <tooltip content="需求优先级">
                    <Avatar style="background-color: #5578aa;" size="small">P1</Avatar>
                  </tooltip>
                </span>
                <span style="width:120px;display: inline-block;">
                  <tooltip content="预计上线日期">
                    <span
                      style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;margin-right: 10px;">
                      <i>2019年12月1日</i>
                    </span>
                  </tooltip>
                </span>
                <span style="padding-right: 10px;color: #5578aa;">
                  <tooltip content="规划版本">
                    <i>[1.0.0]</i>
                  </tooltip>
                </span>
                <span style="color: #5578aa;text-decoration: underline;">
                  需求主题
                </span>
                </Col>
                <Col span="2">
                <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
                  <Icon type="ios-radio-button-on" color="#5578aa"></Icon> 待排期
                </span>
                </Col>
                <Col span="5">
                <span style="padding: 5px;">
                  <tooltip content="需求负责人">
                    <Avatar style="background-color: #22aaaa;">小一</Avatar>
                  </tooltip>
                </span>
                <span style="padding: 5px;">
                  <tooltip content="当前负责人">
                    <Avatar style="background-color: #5578aa;">小二</Avatar>
                  </tooltip>
                </span>
                </Col>
                <Col span="1">
                <span>
                  <Icon type="ios-more" size="32" />
                </span>
                </Col>
              </Row>
              </Col>
            </Row>
            </Col>
          </Row>
          </Col>
        </Row>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'

export default {
  name: 'projectSummary',
  props: ['projectID'],
  data() {
    return {
      data: [99, 71, 78, 25, 36, 92],
      line: '',
    }
  },
  mounted() {
    this.calculatePath();
  },
  methods: {
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setObjectChange']),

    getRandomColor: function () {
      return '#' + Math.floor(Math.random() * 0xffffff).toString(16)
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapGetters(['appBodyHeight', 'appBodyWidth']),
    containerHeight: function () {
      return this.appBodyHeight - 132
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    randomColor: function () {
      return '#99aecc'
    }

  },
  created: function () {

  },
  watch: {

  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;

}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}
</style>
