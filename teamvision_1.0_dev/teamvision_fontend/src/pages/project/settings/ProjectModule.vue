<template>
  <div style="padding-top:16px;">
    <div style="width: 60%;margin-left: auto;margin-right: auto; margin-bottom: 16px;">
      <Input v-model="formData.Name" search enter-button="添加" @on-search="createModule" icon="ios-clock-outline"
        :maxlength="50" placeholder="输入模块名称，回车创建模块" style="width: 100%" />
    </div>
    <Card v-for="mod in moduleList" :padding="10" :key="mod.id" :bordered="false" dis-hover
      class="member-card cursor-hand">
      <Row>
        <Col :xl="20" :lg="20" :md="18" :sm="18">
        <span style="font-weight: bold;">
          <label-editor-input style="max-width: 400px;" @updateValue="updateModuleTitle" placeHolder="模块名称" :id="mod.id"
            :displayText="mod.Name"></label-editor-input>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="6" :sm="6">
        <span>
          <Dropdown transfer @on-click="deleteModule">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem :name="mod.id + ':' + mod.Name">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'

export default {
  name: 'ProjectModule',
  props: ['moduleList', 'projectID'],
  data() {
    return {
      modules: [],
      formData: {
        projectID: 0,
        Name: ''
      }
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),

    updateModuleTitle: function (value, id) {
      let parameters = { Name: value }
      if (value !== '') {
        this.$axios.patch('/api/project/project_module/' + id + '/', parameters).then(response => {
          for (let i = 0; i < this.modules.length; i++) {
            if (this.modules[i].id === parseInt(id)) {
              this.modules.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '模块标题更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.success({
            content: '模块标题更新失败',
            duration: 10,
            closable: true
          })
        })
      }

    },

    createModule: function () {
      this.formData.projectID = this.projectID
      if (this.formData.Name.trim() !== '') {
        this.$axios.post('/api/project/' + this.projectID + '/modules', this.formData).then(response => {
          this.modules.push(response.data.result)
          this.$Message.success({
            content: '模块添加成功',
            duration: 10,
            closable: true
          })
          this.formData.Name = ''
          this.setCreateDialogShow(false)
        }, response => {
          this.$Message.error({
            content: '模块添加失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    deleteModule: function (value) {
      let moduleInfo = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除项目模块[' + moduleInfo[1] + ']',
        onOk: () => {
          this.$axios.delete('/api/project/project_module/' + moduleInfo[0] + '/').then(response => {
            for (let i = 0; i < this.modules.length; i++) {
              if (this.modules[i].id === parseInt(moduleInfo[0])) {
                this.modules.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '模块删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '模块删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })

    }

  },

  created: function () {
    this.modules = this.moduleList
  },

  mounted: function () {
  },

  watch: {
    moduleList: function () {
      this.modules = this.moduleList
    }
  },

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 50px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}
</style>
