<template>
  <div :style="'overflow-y:auto;height:' + containerHeight + 'px'">
    <project-info v-if="pageView === 'info'" :project="projectInfo"></project-info>
    <project-member v-if="pageView === 'member'" :projectID="projectID"
      :memberList="projectInfo.Members"></project-member>
    <project-mile-stone v-if="pageView === 'version'" :projectID="projectID"></project-mile-stone>
    <project-module v-if="pageView === 'module'" :projectID="projectID"
      :moduleList="projectInfo.Modules"></project-module>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import ProjectInfo from './ProjectInfo.vue'
import ProjectMember from './ProjectMember.vue'
import ProjectMileStone from './ProjectMileStone.vue'
import ProjectModule from './ProjectModule.vue'

export default {
  name: 'ProjectSettings',
  props: ['projectID', 'page'],
  data() {
    return {
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapState('project', ['currentProject']),
    ...mapGetters('projectglobal', ['createDocumentType']),

    containerHeight: function () {
      return this.appBodyMainHeight
    },

    pageView: function () {
      return this.page.trim().toLowerCase()
    },

    projectInfo: function () {
      return this.currentProject
    },

    projectID() {
      return parseInt(this.$route.params.projectID, 0);
    }
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow', 'setHeadMenu']),
  },

  created: function () {
  },

  mounted: function () {
  },

  watch: {
  },

  components: {
    labelEditorInput,
    ProjectInfo,
    ProjectMember,
    ProjectModule,
    ProjectMileStone
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.document-card {
  width: 200px;
  height: 150px;
  margin: 10px;
  float: left;
}
</style>
