<template>
  <div style="padding-top:16px;">
    <Card v-for="member in members" :key="member.id" :bordered="false" dis-hover class="member-card cursor-hand">
      <Row>
        <Col :xl="16" :lg="16" :md="16" :sm="16">
        <span style="font-weight: bold;">
          <img class="member-avatar" :src="member.avatar" />
          <span>{{ member.name }}</span>
          <span v-if="userInfo.id === member.PMMember" style="margin-left: 15px;">
            <Tag color="success" class="member-tag">It's you!</Tag>
          </span>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span style="margin-left: 15px;">
          <span style="width: 60px;display: inline-block">
            <Tag :color="member.role_color" class="member-tag">{{ member.role_name }}</Tag>
          </span>
          <Dropdown transfer @on-click="setMemberRole" v-if="projectPermision > 3">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon type="ios-settings-outline" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem v-for="role in projectRoleList" :key="role.id"
                :name="role.id + ':' + role.PRName + ':' + role.PRColor + ':' + member.id">
                <span>
                  <Tag :color="role.PRColor" class="member-tag">{{ role.PRName }}</Tag>
                </span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span v-if="projectPermision > 3">
          <Dropdown transfer @on-click="deleteMember">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem :name="member.id + ':' + member.name">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
    <Modal :value="createDialogShow" title="添加成员" :width="600" @on-visible-change="onModalStatusChange"
      @on-cancel="cancel" :styles="{ bottom: '20px', top: '50px' }">
      <Tabs value="name1" @on-click="switchTab">
        <TabPane label="添加" name="name1">
          <Transfer :data="userKeys" :target-keys="targetUserKeys" filterable :filter-method="filterMethod"
            @on-change="moveMember"></Transfer>
        </TabPane>
        <TabPane label="导入" name="name2">
          <span>源项目: </span>
          <Select @on-change="onSelectSourceProject" v-model="importProject" style="width:200px">
            <Option v-for="item in allMyProject" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </TabPane>
      </Tabs>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" @click="addMember" shape="circle">添加
        </Button>
        <Button type="default" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'

export default {
  name: 'ProjectMember',
  props: ['memberList', 'projectID'],
  data() {
    return {
      projectRoleList: [],
      members: [],
      userKeys: [],
      importProject: 0,
      targetUserKeys: [],
      allMyProject: []
    }
  },
  computed: {
    ...mapState(['appBodyHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapState('projectglobal', ['createDialogShow']),

    containerHeight: function () {
      return this.appBodyHeight - 110
    },

    projectPermision: function () {
      let result = 0
      for (let i = 0; i < this.members.length; i++) {
        if (this.members[i].PMMember === parseInt(this.userInfo.id)) {
          result = this.members[i].PMRoleID
          break
        }
      }
      return result
    }

  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow', 'setProjectRole']),

    getProjectRoles: function () {
      this.$axios.get('/api/project/0/project_roles').then(response => {
        this.projectRoleList = response.data.result
      }, response => {

      })
    },

    setMemberRole: function (value) {
      let memberProperty = value.split(':')
      let memberID = parseInt(memberProperty[3])
      for (let i = 0; i < this.members.length; i++) {
        if (this.members[i].id === memberID) {
          this.members[i].PMRoldID = parseInt(memberProperty[0])
          this.members[i].role_name = memberProperty[1]
          this.members[i].role_color = memberProperty[2]
        }
      }
      let parameters = { PMRoleID: parseInt(memberProperty[0]) }
      this.$axios.patch('/api/project/project_member/' + memberID + '/', parameters).then(response => {
        this.$Message.success({
          content: '成员角色设置成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '成员角色设置失败',
          duration: 10,
          closable: true
        })

      })

    },

    deleteMember: function (value) {
      let deleteValues = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除项目成员[' + deleteValues[1] + ']',
        onOk: () => {
          for (let i = 0; i < this.members.length; i++) {
            if (this.members[i].id === parseInt(deleteValues[0])) {
              this.members.splice(i, 1)
              break
            }
          }

          for (let i = 0; i < this.targetUserKeys.length; i++) {
            if (this.targetUserKeys[i] === parseInt(deleteValues[0])) {
              this.targetUserKeys.splice(i, 1)
              break
            }
          }


          this.$axios.delete('/api/project/project_member/' + deleteValues[0] + '/').then(response => {
            this.$Message.success({
              content: '成员已经成功移除',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '移除成员失败',
              duration: 10,
              closable: true
            })
          })

        },
        onCancel: () => {

        }
      })
    },

    onModalStatusChange: function (value) {
      this.initTargetKeys()
    },

    cancel: function () {
      this.setCreateDialogShow(false)
    },

    onSelectSourceProject: function (value) {
      for (let i = 0; i < this.allMyProject.length; i++) {
        if (this.allMyProject[i].id === value) {

          for (let j = 0; j < this.allMyProject[i].Members.length; j++) {
            this.targetUserKeys.push(this.allMyProject[i].Members[j].PMMember)
          }
        }
      }
    },

    switchTab: function () {
      this.targetUserKeys = []
    },

    addMember: function () {
      let parameters = { userID: this.targetUserKeys }
      this.$axios.post('/api/project/' + this.projectID + '/project_members', parameters).then(response => {
        this.members.push(...response.data.result)
        this.$Message.success({
          content: '成员添加成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '成员添加失败',
          duration: 10,
          closable: true
        })
      })
      this.setCreateDialogShow(false)
    },

    moveMember(newTargetKeys) {
      this.targetUserKeys = newTargetKeys
    },

    filterMethod(data, query) {
      return data.label.indexOf(query) > -1;
    },

    getUserList: function () {
      this.$axios.get('/api/common/users/list').then(response => {
        let userList = response.data.result
        for (let i = 0; i < userList.length; i++) {
          this.userKeys.push({
            key: userList[i].id,
            label: userList[i].name,
            description: userList[i].email
          })
        }
      }, response => {
        // error callback
      })
    },

    initTargetKeys: function () {
      try {
        for (let i = 0; i < this.members.length; i++) {
          this.targetUserKeys.push(this.members[i].PMMember)
        }
      } catch (error) {
        //console.log('init targetUserKey fail')
      }
    },
    loadProjectInfo: function () {
      this.$axios.get('/api/project/list?extinfo=1&home=1').then(response => {
        let tempData = response.data.result
        this.allMyProject = tempData
      }, response => {
        // error callback
      })
    },

  },

  created: function () {
    this.members = this.memberList
    this.getProjectRoles()
    this.getUserList()
    this.loadProjectInfo()
  },

  mounted: function () {

  },

  watch: {
    memberList: function () {
      this.members = this.memberList
    }
  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 45px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

.member-avatar {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  padding-bottom: 2px;
  margin-right: 15px;
}

.member-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
}
</style>
