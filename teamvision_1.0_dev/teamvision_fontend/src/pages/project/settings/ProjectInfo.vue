<template>
  <div class="info-body">
    <Card>
    <Form ref="projectCreate" :model="projectInfo" :label-width="80" :rules="ruleCustom">
      <FormItem label="项目名称" prop="PBTitle">
        <Input v-model="projectInfo.PBTitle" placeholder="项目名称1-50个字符"/>
      </FormItem>
      <FormItem label="项目Key" prop="PBKey">
        <Input readonly v-model="projectInfo.PBKey" placeholder="项目唯一标示，1-10英文字符"/>
      </FormItem>
      <!--<FormItem label="平台" prop="PBPlatform">-->
        <!--<Select v-model="projectInfo.PBPlatform" :filterable="true" placeholder="项目平台">-->
          <!--<Option v-for="platform in platforms" :key="platform.id" :value="platform.DicDataValue">{{ platform.DicDataName }}</Option>-->
        <!--</Select>-->
      <!--</FormItem>-->
      <FormItem label="负责人" prop="PBLead">
        <Select v-model="projectInfo.PBLead" :filterable="true" placeholder="项目负责人">
          <Option v-for="user in userList" :key="user.id" :value="user.id">{{ user.name }} ({{ user.email }})</Option>
        </Select>
      </FormItem>
      <FormItem label="空间" prop="ProductSpace">
        <Select v-model="projectInfo.ProductSpace" :filterable="true" placeholder="任务类型">
          <Option v-for="product in spaceList" :key="product.id" :value="product.id">{{ product.Title }}</Option>
        </Select>
      </FormItem>
      <FormItem label="可见性" prop="PBVisiableLevel">
        <Row>
          <Col span="8" offset="0">
          <RadioGroup v-model="projectInfo.PBVisiableLevel" vertical>
            <Radio :label="1">
              <Icon type="locked" :size="20" ></Icon>
              <span>私有</span>
              <span style="color:#737373;padding-left: 20px;">项目仅自己可见</span>
            </Radio>
            <Radio :label="2">
              <Icon type="contrast" :size="20"></Icon>
              <span>内部</span>
              <span style="color:#737373;padding-left: 20px;">项目可以被同组成员看到</span>
            </Radio>
            <Radio :label="3">
              <Icon type="ios-world" :size="20"></Icon>
              <span>公开</span>
              <span style="color:#737373;padding-left: 20px;"  >项目对所有人员可见</span>
            </Radio>
          </RadioGroup>
          </Col>
        </Row>
      </FormItem>
    </Form>
    <Button type="success" shape="circle" @click="updateProject('projectCreate')">保存</Button>
    <Button v-if="showDelete" type="error" shape="circle" @click="deleteProject">删除</Button>
    <Alert v-if="showDelete" style="margin-top: 10px;" type="error" show-icon>删除项目将会删除所有与项目相关的内容，版本，提测等都会被删除.
        删除项目是不可恢复操作，请谨慎操作。</Alert>
    </Card>
  </div>
</template>

<script>
  import { mapState, mapGetters, mapMutations } from 'vuex'
  import { projectValidateRules } from '../ProjectCreateDialog'
  import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'

  export default {
    name: 'ProjectInfo',
    props: ['project'],
    data () {
      return {
        projectInfo: {},
        userList: [],
//        platforms: [],
        ruleCustom: {
          ...projectValidateRules
        }
      }
    },
    computed: {
      ...mapState(['appBodyMainHeight', 'spaceList']),
      ...mapState('usercenter', ['userInfo']),
      ...mapGetters('projectglobal',['createDocumentType']),
      ...mapGetters('document',['breadNav']),
      ...mapState('project', ('')),
      containerHeight: function () {
        return this.appBodyMainHeight
      },
      showDelete: function(){
        //console.log(this.userInfo.id, this.projectInfo.PBCreator)
        return this.userInfo.id === this.projectInfo.PBCreator
      }

    },

    methods: {
      ...mapMutations('projectglobal',['setCreateDocumentType','setCreateDialogShow']),

      updateProject (name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.$axios.put('/api/project/' + this.projectInfo.id + '/detail', this.projectInfo).then(response => {
              this.$Message.success({
                content: '项目信息更新成功！',
                duration: 10
              })
            }, response => {
              this.$Message.error({
                content: '项目信息更新失败，请联系管理员或者重试',
                duration: 3
              })
            })
          }
        })
      },

      deleteProject: function () {
        this.$Modal.confirm({
          title: '删除确认',
          content: '您即将删除项目['+this.projectInfo.PBTitle+']',
          onOk: () => {
            this.$axios.delete('/api/project/' + this.projectInfo.id + '/detail').then(response => {
              this.$Message.success({
                content: '项目删除成功',
                duration: 3,
                closable: true
              })
              this.$router.replace('/project')
            }, response => {
              this.$Message.success({
                content: '项目删除失败',
                duration: 3,
                closable: true
              })
            })
          },
          onCancel: () => {

          }
        })
      }

    },

    created: function() {
//      this.$axios.get('/api/common/dicconfig/5/dicconfigs').then(response => {
//        this.platforms=response.data.result
//      }, response => {
//        // error callback
//      })

      this.$axios.get('/api/common/users/list').then(response => {
        this.userList=response.data.result
      }, response => {
        // error callback
      })

      this.projectInfo = this.project
    },

    mounted: function () {
    },

    watch: {
      project: function () {
        this.projectInfo = this.project
      }

    },

    components: {
      labelEditorInput
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

.info-body {
  width: 40%;
  padding-top:16px;
  margin-left: auto;
  margin-right: auto;
}

</style>
