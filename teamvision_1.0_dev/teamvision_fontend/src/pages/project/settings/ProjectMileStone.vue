<template>
  <div style="padding-top:16px;">
    <Card v-if="!version.VersionFiled" v-for="version in versions" :key="version.id" :bordered="false" dis-hover
      class="version-card cursor-hand">
      <Row>
        <Col :xl="10" :lg="10" :md="12" :sm="12">
        <div style="font-weight: bold;">
          <label-editor-input @updateValue="updateVersionTitle" placeHolder="版本标题" :id="version.id"
            :displayText="version.VVersion"></label-editor-input>
        </div>
        </Col>
        <Col :xl="10" :lg="10" :md="8" :sm="8">
        <span style="color:#757575;padding-right: 50px;">
          <DatePicker :open="sopen && version.id === selectVersion" :value="startDate" confirm type="date"
            @on-change="handleSChange" @on-ok="handleSOk">
            <a href="javascript:void(0)" @click="handleSClick(version.id)">
              开始日期:
              <Icon type="ios-calendar-outline"></Icon>
              <template> {{ version.VStartDate }}</template>
            </a>
          </DatePicker>
        </span>
        <span style="color:#757575">
          <DatePicker :open="ropen && version.id === selectVersion" :value="releaseDate" confirm type="date"
            @on-change="handleRChange" @on-ok="handleROk">
            <a href="javascript:void(0)" @click="handleRClick(version.id)">
              结束日期:
              <Icon type="ios-calendar-outline"></Icon>
              <template> {{ version.VReleaseDate }}</template>
            </a>
          </DatePicker>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span>
          <Dropdown transfer @on-click="versionOperation">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <!--<DropdownItem name="1"><Icon type="ios-create-outline" />-->
              <!--<span >编辑</span>-->
              <!--</DropdownItem>-->
              <DropdownItem :name="'2' + ':' + version.id + ':' + version.VVersion">
                <Icon type="ios-paw" />
                <span>归档</span>
              </DropdownItem>
              <DropdownItem :name="'3' + ':' + version.id + ':' + version.VVersion">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
    <Card v-if="version.VersionFiled" v-for="version in versions" :key="version.id" :bordered="false" dis-hover
      class="version-filed-card cursor-hand">
      <Row>
        <Col :xl="1" :lg="1" :md="2" :sm="2" style="width: 30px;">
        <div>
          <Tooltip content="已归档" transfer>
            <Icon type="ios-folder-outline" />
          </Tooltip>
        </div>
        </Col>
        <Col :xl="9" :lg="9" :md="10" :sm="10">
        <div style="font-weight: bold;">
          <label-editor-input @updateValue="updateVersionTitle" placeHolder="版本标题" :id="version.id"
            :displayText="version.VVersion"></label-editor-input>
        </div>
        </Col>
        <Col :xl="10" :lg="10" :md="8" :sm="8">
        <span style="color:#757575;padding-right: 50px;">
          <DatePicker :open="sopen && version.id === selectVersion" :value="startDate" confirm type="date"
            @on-change="handleSChange" @on-ok="handleSOk">
            <a href="javascript:void(0)" @click="handleSClick(version.id)">
              开始日期:
              <Icon type="ios-calendar-outline"></Icon>
              <template> {{ version.VStartDate }}</template>
            </a>
          </DatePicker>
        </span>
        <span style="color:#757575">
          <DatePicker :open="ropen && version.id === selectVersion" :value="releaseDate" confirm type="date"
            @on-change="handleRChange" @on-ok="handleROk">
            <a href="javascript:void(0)" @click="handleRClick(version.id)">
              结束日期:
              <Icon type="ios-calendar-outline"></Icon>
              <template> {{ version.VReleaseDate }}</template>
            </a>
          </DatePicker>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span>
          <Dropdown transfer @on-click="versionOperation">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <!--<DropdownItem name="1"><Icon type="ios-create-outline" />-->
              <!--<span >编辑</span>-->
              <!--</DropdownItem>-->
              <DropdownItem :name="'4' + ':' + version.id + ':' + version.VVersion">
                <Icon type="ios-paw" />
                <span>恢复版本</span>
              </DropdownItem>
              <DropdownItem :name="'3' + ':' + version.id + ':' + version.VVersion">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
    <Modal :value="createDialogShow" title="添加版本" :width="600" @on-cancel="cancel"
      :styles="{ bottom: '20px', top: '50px' }">
      <div>
        <Form ref="createVersion" :model="formItem" :label-width="80" :rules="ruleCustom">
          <FormItem label="标题" prop="VVersion">
            <Input v-model="formItem.VVersion" placeholder="版本规划标题" />
          </FormItem>
          <FormItem label="开始时间" prop="VStartDate">
            <DatePicker type="date" placeholder="默认T+1完成" format="yyyy-MM-dd" v-model="formItem.VStartDate">
            </DatePicker>
          </FormItem>
          <FormItem label="发布时间" prop="VReleaseDate">
            <DatePicker type="date" placeholder="默认T+1完成" format="yyyy-MM-dd" v-model="formItem.VReleaseDate">
            </DatePicker>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" @click="addVersion('createVersion')" shape="circle">添加
        </Button>
        <Button type="default" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import { versionValidateRules } from './ProjectVersionCreate'

export default {
  name: 'ProjectMileStone',
  props: ['projectID'],
  data() {
    return {
      versions: [],
      sopen: false,
      ropen: false,
      selectVersion: 0,
      startDate: '',
      releaseDate: '',
      formItem: {
        VProjectID: 0,
        VVersion: '',
        VStartDate: '',
        VReleaseDate: ''
      },
      ruleCustom: {
        ...versionValidateRules
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['rightSidePanelShow',]),

    ...mapState('projectglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyHeight
    },

    versionStatusColor: function () {
      let colorPool = ['orange', '#19be6b', '']
      return colorPool[1]
    }

  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow']),

    addVersion: function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.formItem.VProjectID = this.projectID
          this.formItem.VStartDate = this.formatDate(this.formItem.VStartDate)
          this.formItem.VReleaseDate = this.formatDate(this.formItem.VReleaseDate)
          this.$axios.post('/api/project/' + this.projectID + '/versions', this.formItem).then(response => {
            this.versions.push(response.data.result.all_versions)
            this.$Message.success({
              content: '版本添加成功',
              duration: 10,
              closable: true
            })
            this.setCreateDialogShow(false)
          }, response => {
            this.$Message.success({
              content: '版本添加失败',
              duration: 10,
              closable: true
            })
          })
        }
      })
    },

    formatDate: function (dateString) {
      let tempDate = new Date(dateString)
      let year = tempDate.getFullYear()
      let month = tempDate.getMonth() + 1
      let day = tempDate.getDate()
      return year + '-' + month + '-' + day
    },

    updateVersionTitle: function (value, id) {
      let parameters = { VVersion: value }
      if (value !== '') {
        this.$axios.patch('/api/project/project_version/' + id + '/', parameters).then(response => {
          for (let i = 0; i < this.versions.length; i++) {
            if (this.versions[i].id === parseInt(this.selectVersion)) {
              this.versions.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '版本标题更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.success({
            content: '版本标题更新失败',
            duration: 10,
            closable: true
          })
        })
      }
    },
    cancel: function () {
      this.setCreateDialogShow(false)
    },

    versionOperation: function (value) {
      let versionInfo = value.split(':')
      if (versionInfo[0] === '3') {
        this.deleteVersion(value)
      }
      if (versionInfo[0] === '2') {
        this.filedVersion(value, true)
      }
      if (versionInfo[0] === '4') {
        this.filedVersion(value, false)
      }

    },

    deleteVersion: function (value) {
      let versionInfo = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除项目版本[' + versionInfo[2] + ']',
        onOk: () => {
          this.$axios.delete('/api/project/project_version/' + versionInfo[1] + '/').then(response => {
            for (let i = 0; i < this.versions.length; i++) {
              if (this.versions[i].id === parseInt(versionInfo[1])) {
                this.versions.splice(i, 1)
                break
              }
            }
            this.$Message.success({
              content: '版本删除成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '版本删除失败',
              duration: 10,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })
    },

    filedVersion: function (value, filed) {
      let parameters = { VersionFiled: filed }
      let versionInfo = value.split(':')
      this.$axios.patch('/api/project/project_version/' + versionInfo[1] + '/', parameters).then(response => {
        for (let i = 0; i < this.versions.length; i++) {
          if (this.versions[i].id === parseInt(versionInfo[1])) {
            this.versions[i].VersionFiled = filed
            break
          }
        }
        this.$Message.success({
          content: '版本' + versionInfo[2] + '归档成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '版本' + versionInfo[2] + '归档失败',
          duration: 10,
          closable: true
        })
      })
    },

    loadProjectVersion: function (projectID) {
      this.$axios.get('/api/project/' + projectID + '/versions').then(response => {
        this.versions = response.data.result.all_versions
      }, response => {
      })
    },

    handleSClick(versionID) {
      this.selectVersion = versionID
      this.sopen = !this.sopen
    },
    handleSChange(date) {
      this.startDate = date
    },
    handleSOk() {
      let parameters = { VStartDate: this.startDate }
      if (this.startDate !== '') {
        this.$axios.patch('/api/project/project_version/' + this.selectVersion + '/', parameters).then(response => {
          for (let i = 0; i < this.versions.length; i++) {
            if (this.versions[i].id === parseInt(this.selectVersion)) {
              this.versions.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '开始日期更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.success({
            content: '开始日期更新失败',
            duration: 10,
            closable: true
          })
        })
      }
      this.sopen = false
    },
    handleRClick(versionID) {
      this.selectVersion = versionID
      this.ropen = !this.ropen
    },
    handleRChange(date) {
      this.releaseDate = date
    },
    handleROk() {
      let parameters = { VReleaseDate: this.releaseDate }
      if (this.releaseDate !== '') {
        this.$axios.patch('/api/project/project_version/' + this.selectVersion + '/', parameters).then(response => {
          for (let i = 0; i < this.versions.length; i++) {
            if (this.versions[i].id === parseInt(this.selectVersion)) {
              this.versions.splice(i, 1, response.data.result)
              break
            }
          }
          this.$Message.success({
            content: '发布日期更新成功',
            duration: 3,
            closable: true
          })
        }, response => {
          this.$Message.success({
            content: '发布日期更新失败',
            duration: 3,
            closable: true
          })
        })
      }
      this.ropen = false
    }

  },

  created: function () {
    this.loadProjectVersion(this.projectID)

  },

  mounted: function () {
  },

  watch: {

    projectID: function () {
      this.loadProjectVersion(this.projectID)
    }

  },

  components: {
    labelEditorInput
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.version-card {
  width: 100%;
  height: 50px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

.version-filed-card {
  width: 100%;
  height: 50px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
  opacity: 0.7;
}
</style>
