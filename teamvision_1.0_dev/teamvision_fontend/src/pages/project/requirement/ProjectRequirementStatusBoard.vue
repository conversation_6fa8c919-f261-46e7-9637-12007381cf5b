<template>
  <div>
    <i-table  :height="height"  :columns="columns2" :data="data4">
      <template slot-scope="{ row }" slot="project">
        <div style="padding: 6px;">
          <div style="padding: 3px;">{{ row.city }}</div>
          <div style="padding: 3px; font-size: 12px;">版本:{{ row.age }}</div>
          <div style="padding: 3px; font-size: 12px;">模块:{{ row.name }}</div>
        </div>
      </template>

      <template slot-scope="{ row }" slot="status">
        <div style="height: 20px;background-color: #22aaaa;width: 200px;margin-left: -20px;text-align: center" >
            <span style="color: #fff;padding-left: 6px;font-size: 12px">
              测试中
            </span>
        </div>
        <div style="padding-top: 5px;width: 200px;margin-left: -10px;">
          <div>
            <span style="padding-top: 2px; font-size: 12px;">
              <span style="width: 62px;display: inline-block">
                UI:
              </span><Icon type="md-checkmark-circle" color="#22aaaa" /></span>
            <span style="padding-top: 2px; font-size: 12px;">
              <span style="width: 61px;display: inline-block">
                FE:</span>
              <Icon type="md-checkmark-circle" color="#22aaaa" />
            </span>
          </div>
          <div>
            <span style="padding-top: 2px; font-size: 12px;">
              <span style="width: 60px;display: inline-block">
                后端:
              </span>
              <Icon type="md-checkmark-circle" />
            </span>
            <span style="padding-top: 2px; font-size: 12px;">
              <span style="width: 60px;display: inline-block">
                客户端:
              </span>
              <Icon type="md-checkmark-circle" /></span>
          </div>
        </div>
      </template>
    </i-table>
  </div>
</template>

<script>
  import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'


  export default {
    name: 'projectRequirementStatusBoard',
    props: ['height'],
    data() {
      return {
        columns2: [
          {
            title: '',
            key: 'age',
            width: 10,
            fixed: 'left'
          },
          {
            title: '需求',
            key: 'name',
            width: 360,
            fixed: 'left',
            tooltip: true
          },
          {
            title: '项目',
            key: 'city',
            slot: 'project',
            width: 200,
            sortable: true,
            filters: [
              {
                label: 'Greater than 25',
                value: 1
              },
              {
                label: 'Less than 25',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.age > 25;
              } else if (value === 2) {
                return row.age < 25;
              }
            }
          },
          {
            title: '状态',
            key: 'address',
            width: 200,
            slot: 'status',
            sortable: true,
            filters: [
              {
                label: 'Greater than 25',
                value: 1
              },
              {
                label: 'Less than 25',
                value: 2
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
              if (value === 1) {
                return row.age > 25;
              } else if (value === 2) {
                return row.age < 25;
              }
            }
          },
          {
            title: '上线时间',
            key: 'city',
            width: 150,
            sortable: true,
          },

          {
            title: '负责人',
            key: 'province',
            width: 100
          },
          {
            title: '研发',
            key: 'city',
            width: 100
          },
          {
            title: '测试',
            key: 'city',
            width: 100
          },

          {
            title: '案例',
            key: 'zip',
            width: 150
          },

          {
            title: '问题',
            key: 'zip',
            width: 150
          },


          {
            title: '优先级',
            key: 'city',
            width: 100
          },
          {
            title: '提测时间',
            key: 'city',
            width: 150,
            fixed: 'right'
          }
        ],
        data4: [
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: '2020年5月8日',
            zip: 100000,
//            cellClassName: {
//              age: 'demo-table-info-cell-age',
//              address: 'demo-table-info-cell-address'
//            }
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000,
//            cellClassName: {
//              age: 'demo-table-info-cell-age',
//              address: 'demo-table-info-cell-address'
//            }
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000
          },
          {
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            province: 'America',
            city: 'New York',
            zip: 100000
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'Washington, D.C. No. 1 Lake Park',
            province: 'America',
            city: 'Washington, D.C.',
            zip: 100000
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park',
            province: 'Australian',
            city: 'Sydney',
            zip: 100000
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park',
            province: 'Canada',
            city: 'Ottawa',
            zip: 100000,
            cellClassName: {
              age: 'demo-table-info-cell-age',
              address: 'demo-table-info-cell-address'
            }
          }
        ]
        }
      },
    computed: {
      ...mapGetters('projectglobal', ['projectVersion','objectChange']),
      ...mapGetters(['appBodyHeight','appBodyWidth','userInfo']),
      containerHeight: function () {
        return this.appBodyHeight-132
      },

      containerWidth: function () {
        return this.appBodyWidth
      },



    },
    methods: {
      ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange']),




    },
    created: function () {

    },
    watch: {

    },

    components: {
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">

  /*.ivu-table .demo-table-info-row td{*/
    /*background-color: #2db7f5;*/
    /*color: #fff;*/
  /*}*/
  /*.ivu-table .demo-table-error-row td{*/
    /*background-color: #ff6600;*/
    /*color: #fff;*/
  /*}*/
  /*.ivu-table td.demo-table-info-column{*/
    /*background-color: #2db7f5;*/
    /*color: #fff;*/
  /*}*/
  /*.ivu-table .demo-table-info-cell-name {*/
    /*background-color: #2db7f5;*/
    /*color: #fff;*/
  /*}*/
  .ivu-table .demo-table-info-cell-age {
    background-color: #ff6600;
    color: #fff;
  }
  /*.ivu-table .demo-table-info-cell-address {*/
    /*background-color: #187;*/
    /*color: #fff;*/
  /*}*/

</style>
