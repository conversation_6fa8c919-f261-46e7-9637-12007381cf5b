<template>
  <Modal :value="dialogShow" title="添加新需求" :mask-closable="false" :width="700" @on-cancel="cancel"
    :styles="{ bottom: '20px', top: '50px' }">
    <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
      <Form ref="createRequment" :model="formItem" :label-width="80" :rules="ruleCustom">
        <FormItem label="项目" prop="Project">
          <Select v-model="formItem.Project" :filterable="true" placeholder="需求所属项目" @on-change="onProjectChange">
            <Option v-for="project in sourceProject" :key="project.id" :value="project.id">{{ project.PBTitle }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="标题" prop="Title">
          <Input v-model="formItem.Title" :maxlength="50" show-word-limit placeholder="需求概述" />
        </FormItem>

        <FormItem label="预计发版日期" prop="TestingDeadLineDate">
          <DatePicker type="date" v-model="formItem.ReleaseDate" placeholder="预计发版日期"></DatePicker>
        </FormItem>

        <FormItem label="描述" prop="Desc">
          <vue-editor v-model="formItem.Desc" :editorToolbar="editorToolBar" placeholder="需求描述"></vue-editor>
        </FormItem>

        <FormItem label="版本" prop="Version">
          <Select v-model="formItem.Version" :filterable="true" placeholder="需求所属版本规划">
            <Option v-for="version in projectVersions" :key="version.id" :value="version.id">{{ version.VVersion }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="模块" prop="Module">
          <Select v-model="formItem.Module" :filterable="true" placeholder="问题所属模块">
            <Option v-for="module in projectModules" :key="module.id" :value="module.id">{{ module.Name }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="优先级" prop="Version">
          <Select v-model="formItem.Version" :filterable="true" placeholder="需求所属版本规划">
            <Option v-for="version in projectVersions" :key="version.id" :value="version.id">{{ version.VVersion }}
            </Option>
          </Select>
        </FormItem>

        <FormItem>
          <Upload ref="upload" multiple type="drag" paste action="/api/project/issue/attachments"
            :on-success="handleSuccess" :on-remove="handleRemove" :format="[]" :max-size="10240"
            :default-file-list="formItem.defaultList" :on-format-error="handleFormatError"
            :on-exceeded-size="handleMaxSize">
            <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>点击，拖拽，粘贴上传附件</p>
            </div>
          </Upload>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button v-if="createDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
        @click="ok('createRequment')">添加
      </Button>
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { VueEditor } from 'vue2-editor'
import { initProjectVersions, issueValidateRules } from './ProjectRequirementCreateDialog'


export default {
  name: 'ProjectReqCreateDialog',
  props: {
    projectID: {
      type: [Number, String],
      default: 0
    },
    versionID: {
      type: [Number, String],
      default: 0
    }
  },

  data() {
    return {
      sourceProject: [],
      projectVersions: [],
      projectModules: [],
      issueStatus: [],
      editorToolBar: [
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }], [{ 'color': [] }, { 'background': [] }],
      ],
      formItem: {
        Title: '',
        Module: 0,
        Desc: '需求描述或需求文档连接',
        Project: 0,
        Version: 0,
        ReleaseDate: '',
        uploadList: [],
        defaultList: []
      },
      ruleCustom: {
        ...issueValidateRules
      }
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['viewDialogShow', 'projectVersion', 'createReqType']),
    ...mapState(['appBodyHeight']),
    ...mapState('projectglobal', ['createDialogShow']),
    ...mapState('project', ['projectList']),

    containerHeight: function () {
      if (this.appBodyHeight - 100 > 600) {
        return 600
      } else {
        return this.appBodyHeight - 600
      }
    },

    project: function () {
      return parseInt(this.projectID)
    },

    projectVersion: function () {

      return parseInt(this.versionID)

    },

    dialogShow: function () {
      return this.createReqType === 1
    },

    projectMembers: function () {
      let project = this.formItem.Project[0]
      let result = []
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === project) {
          result = this.sourceProject[i].Members
        }
      }
      return result
    }
  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setViewDialogShow', 'setTaskChange', 'setCreateReqType']),
    ...mapMutations('issue', ['setIssueChange']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCreateDialogShow(false)
          this.$axios.post('/api/project/' + this.formItem.Project[0] + '/version/' + this.formItem.Project[1] + '/issues', this.formItem).then(response => {
            this.setIssueChange(true)
            this.formItem.Title = ' '
            this.formItem.Desc = '<p>步骤:</p></br><p>实际结果:</p></br><p>期望结果:</p>'
            this.formItem.uploadList = []
          }, response => {
          })
          this.$refs[name].resetField()
        }
      })
    },
    cancel() {
      this.setCreateReqType(0)
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.formItem.uploadList.push(file.id)
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过10M限制',
        duration: 10,
        closable: true
      })
    },

    onProjectChange(value) {
      let project = value
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === project) {
          this.projectVersions = this.sourceProject[i].Versions
          this.projectModules = this.sourceProject[i].Modules
        }
      }
    },
  },

  created() {
    this.sourceProject = this.projectList
    this.onProjectChange(this.project)
    this.formItem.Project = this.project
  },
  mounted() {
  },
  watch: {
  },
  components: {
    VueEditor,
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
</style>
