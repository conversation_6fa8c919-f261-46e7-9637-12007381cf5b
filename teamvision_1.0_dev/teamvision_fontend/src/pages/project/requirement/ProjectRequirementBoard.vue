<template>
  <div :style="{ width: containerWidth + 'px', height: containerHeight + 'px', overflow: 'scroll' }">
    <board-column v-if="itemViewMode === 'board'" v-for="storyColumn in storyColumns" :key="storyColumn.id"
      :columnHeight="appBodyHeight - 175" :columnID="storyColumn.id" :group="storyColumn.group"
      v-bind:itemList.sync="storyColumn.data" :columnTitle="storyColumn.title + '-' + storyColumn.count"
      style="border:none" @end="onEnd" @reachBottom="onReachBottom">
      <template slot-scope="slotProps">
        <story-board-item :story="slotProps.element" :taskStatusList="taskStatusList" @onViewTaskItem="viewTaskItem"
          @view-story="onViewStory"></story-board-item>
      </template>
    </board-column>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import BoardColumn from '../../../components/common/BoardColumn.vue'
import StoryBoardItem from './ProjectStoryBoardItem.vue'
import { loadProjectRequirements, updateStoryProperty } from '../business-service/ProjectApiService.js'

export default {
  name: 'projectRequirementBoard',
  props: ['projectID', 'priority', 'risk', 'version'],
  data() {
    return {
      storyColumns: [],
      storyStatus: [],
      taskStatusList: []
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange', 'createReqType']),
    ...mapGetters(['appBodyHeight', 'appBodyWidth', 'userInfo', 'itemViewMode']),

    containerHeight: function () {
      return this.appBodyHeight - 70
    },

    containerWidth: function () {
      if (this.$route.name === 'homeRequirement') {
        return this.appBodyWidth - 240
      } else {
        return this.appBodyWidth
      }
    },

  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange', 'setProjectVersion']),
    onStart() {
      console.log('start')
    },
    onEnd(evt) {
      console.log(evt)
      let toID = evt.to.getAttribute('id')
      let fromID = evt.from.getAttribute('id')
      let itemOldIndex = evt.oldIndex
      let itemNewIndex = evt.newIndex
      let itemID = evt.item.getAttribute('id')
      this.alterColumnData(fromID, toID, itemID, itemOldIndex, itemNewIndex)
      let parameters = { Status: toID }
      updateStoryProperty(itemID, parameters, "状态").then(response => { }, response => { })
    },

    onRemove() {
      console.log('remove')
    },
    onMove() {
      console.log('move')
    },
    alterColumnData(fromID, toID, itemID, itemOldIndex, itemNewIndex) {
      let dragItem = null
      this.storyColumns.forEach(function (storyColumn, index) {
        if (storyColumn.id === parseInt(fromID)) {
          storyColumn.count = storyColumn.count - 1
          for (let i = 0; i < storyColumn.data.length; i++) {
            if (storyColumn.data[i].id === parseInt(itemID)) {
              dragItem = storyColumn.data[i]
              storyColumn.data.splice(i, 1)
              break
            }
          }
        }
      })

      this.storyColumns.forEach(function (storyColumn, index) {
        if (storyColumn.id === parseInt(toID)) {
          storyColumn.count = storyColumn.count + 1
          storyColumn.data.splice(itemNewIndex, 0, dragItem)
        }
      })
    },
    onReachBottom(columnid) {
      for (let i = 0; i < this.storyColumns.length; i++) {
        if (this.storyColumns[i].id === parseInt(columnid)) {
          let moreStories = []
          let stories = this.storyColumns
          this.storyColumns[i].page = this.storyColumns[i].page + 1
          let filter = '?page=' + this.storyColumns[i].page + '&'
          filter = filter + 'Status=' + columnid + '&'
          if (this.priority !== 0) {
            filter = filter + 'Priority=' + this.priority + '&'
          }
          if (this.risk !== 0) {
            filter = filter + 'Risk=' + this.risk + '&'
          }
          loadProjectRequirements(this.projectID, this.version, filter).then(response => {
            this.storyColumns[i].data.push(...response.data.result.results)
          }, response => {
            this.$Message.error({
              content: '没有更多需求可以展示',
              duration: 10,
              closable: true
            })
          })
        }
      }
    },


    onViewStory(storyID) {
      this.$emit('view-story', storyID)
    },

    viewTaskItem: function (value) {
      this.$emit('viewTaskItem', value)
    },

    getReqFilters: function (status, priority, risk) {
      let filter = '?'
      if (status !== 0) {
        filter = filter + 'Status=' + status + '&'
      }
      if (priority !== 0) {
        filter = filter + 'Priority=' + priority + '&'
      }
      if (risk !== 0) {
        filter = filter + 'Risk=' + risk + '&'
      }
      return filter
    },

    loadStories: function (status, priority, risk) {
      let filter = this.getReqFilters(status, priority, risk)
      loadProjectRequirements(this.projectID, 0, filter).then(response => {
        for (let i = 0; i < this.storyColumns.length; i++) {
          if (this.storyColumns[i].id + '' === status + '') {
            this.storyColumns[i].count = response.data.result.count
            this.storyColumns[i].data = response.data.result.results
          }

        }
      }, response => {

      })

    },

    loadAllStatusStory: function () {
      for (let i = 0; i < this.storyStatus.length; i++) {
        this.loadStories(this.storyStatus[i].Status, this.priority, this.risk)
      }
    },
    loadReqColumns: function () {
      this.$axios.get('/api/project/task/task_status?Type=3').then(response => {
        this.storyStatus = response.data.result
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 3) {
            let tempTag = { id: 0, title: '', group: 'StoryColumn', count: 0, data: [], page: 1 }
            tempTag.id = tempItem.Status
            tempTag.title = tempItem.Desc
            this.storyColumns.push(tempTag)
            this.loadStories(tempItem.Status, this.priority, this.risk)
          }
        }
      }, response => {
      })
    },

    loadTaskStatus: function () {
      this.$axios.get('/api/project/task/task_status?Type=1').then(response => {
        console.log(response)
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 1) {
            let tempTag = { label: '', id: 0 }
            tempTag.label = tempItem.Desc
            tempTag.id = tempItem.Status
            this.taskStatusList.push(tempTag)
          }
        }
      }, response => {
      })
    },



  },
  created: function () {
    this.loadReqColumns()
    this.loadTaskStatus()
  },
  watch: {
    projectID: function (value, old) {
      if (value > 0) {
        this.loadAllStatusStory()
      }
    },
    priority: {
      handler: function (value, old) {
        this.loadAllStatusStory()
      },
      deep: true
    },
    risk: {
      handler: function (value, old) {
        this.loadAllStatusStory()
      },
      deep: true
    },
    version: {
      handler: function (value, old) {
        this.loadAllStatusStory()
      },
      deep: true
    },
    itemViewMode: function (value, old) {
      console.log(value)
      if (value === 'board') {
        this.loadAllStatusStory()
      }
    }

  },

  components: {
    BoardColumn,
    StoryBoardItem
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;

}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}
</style>
