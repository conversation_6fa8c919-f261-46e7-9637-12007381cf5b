//初始化项目以及版本信息
import axios from 'axios'
let initProjectVersions = function (tempData) {
  let projectVersions = []
  for (let i = 0; i < tempData.length; i++) {
    let tempProject = {}
    tempProject.value = tempData[i].id
    tempProject.label = tempData[i].PBTitle
    tempProject.children = []
    for (let j = 0; j < tempData[i].Versions.length; j++) {
      let tempChild = {}
      tempChild.label = tempData[i].Versions[j].VVersion
      tempChild.value = tempData[i].Versions[j].id
      tempProject.children.push(tempChild)
    }
    projectVersions.push(tempProject)
  }
  return projectVersions
}


let issueValidateRules = {
  projectID: [
    { type: 'number', required: true, message: '请需求所属项目！' }
  ],
  Title: [
    { type: 'string', required: true, min: 1, max: 100, message: '问题标题长度在1-100个字符之间', trigger: 'blur' }
  ]
}

export {
  initProjectVersions,
  issueValidateRules,
}
