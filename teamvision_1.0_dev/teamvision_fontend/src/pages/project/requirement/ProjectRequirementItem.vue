<template>
  <div :style="'overflow-y:scroll;height:' + containerHeight + 'px;'">
    <Row align="middle" type="flex" style="border-bottom: 1px solid #f5f7f9;">
      <Col :lg="layout.otherLayout.featureLayout.requirementWidth"
        :md="layout.otherLayout.featureLayout.requirementWidth - 2"
        style="padding: 16px; border-left: 1px solid #eef2f6;">
      <Row align="middle" type="flex" style="padding: 0px 10px 5px 10px;height: 40px;">
        <Col span="24">
        <div @click="addNewReq" style="cursor: pointer; width: inherit;text-align: center;">
          <Tooltip content="添加新需求">
            <Icon type="ios-add-circle" color="#22aaaa" :size="28" />
          </Tooltip>
        </div>
        </Col>
      </Row>
      <Row style="padding: 0px 10px 0px 10px; height: 40px;" v-if="addNewRequirement">
        <Col span="12">
        <span>
          <Input v-model="reqFormData.Title" :maxlength="50" show-word-limit placeholder="需求标题" />
        </span>
        </Col>
        <Col span="10">
        <span style="margin-left: 10px;">
          <DatePicker v-model="reqFormData.ReleaseDate" type="date" placeholder="预计发布日期"></DatePicker>
        </span>
        <span style="margin-left: 10px;">
          <Select v-model="reqFormData.Priority" style="width: 80px;">
            <Option v-for="priority in reqPriority" :value="priority.Status" :key="priority.id">{{ priority.Desc
              }}
            </Option>
          </Select>
        </span>
        <span>
          <ButtonGroup shape="circle">
            <Button @click.native="newRequirement" icon="md-checkmark"></Button>
            <Button @click.native="cancelNewRequirement" icon="md-close"></Button>
          </ButtonGroup>
        </span>
        </Col>
      </Row>
      <CheckboxGroup v-model="selectReqList">
        <Row align="middle" type="flex" v-for="req, index in projectRequirementList" :key="req.id"
          style="padding: 10px 10px 5px 10px;border-bottom: 1px solid #eef2f6;">
          <Col :lg="12" :md="21">
          <span style="width: 38px;">
            <Tooltip content="选择需求，创建提测" v-if="req.FortestingID === 0">
              <Checkbox :label="req.id" :disabled="req.FortestingID !== 0"></Checkbox>
            </Tooltip>
            <Tooltip content="需求已提测" v-if="req.FortestingID !== 0">
              <Checkbox :label="req.id" :disabled="req.FortestingID !== 0"></Checkbox>
            </Tooltip>
          </span>
          <span style="display: inline-block;margin-left: 5px;">
            <tooltip content="需求优先级">
              <tag color="primary">{{ req.ViewData.Priority }} </tag>
            </tooltip>
          </span>
          <!-- <span
                style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 14px; margin-left: 10px;margin-right: 10px;">
                <Tooltip content="风险">
                  {{ req.ViewData.Risk }}
                </Tooltip>
              </span> -->
          <!--<span style="padding-right: 10px;color: #5578aa;width: 60px;display: inline-block;">-->
          <!--<tooltip content="规划版本">-->
          <!--<i>[{{ req.ViewData.Version }}]</i>-->
          <!--</tooltip>-->
          <!--</span>-->
          <span @click="viewReqItem(req.id)" style="color: #5578aa;text-decoration: underline;cursor:pointer;">
            {{ req.Title }}
          </span>
          </Col>
          <Col :lg="3" :md="3">
          <span style="border: 1px solid #eef2f6; padding: 3px; color:#5578aa;font-size: 12px;">
            <Icon type="ios-radio-button-on" color="#5578aa"></Icon> {{ req.ViewData.Status }}
          </span>
          </Col>
          <Col :lg="7" :md="0" :sm="0">
          <span style="padding: 5px;">
            <tooltip content="需求负责人">
              <Tag style="background-color: #22aaaa;">{{ req.ViewData.Owner }}</Tag>
            </tooltip>
          </span>
          <!-- <span style="padding: 5px;">
                <tooltip content="当前负责人">
                  <Tag style="background-color: #5578aa;">{{ req.ViewData.ExecutiveOwner }}
                  </Tag>
                </tooltip>
              </span> -->
          <span style="margin-left: 10px;display: inline-block;">
            <tooltip content="创建时间">
              <span
                style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;padding-right: 10px;">
                <i>{{ req.CreationTime }}</i>
              </span>
            </tooltip>
          </span>
          <span style="margin-left: 10px;display: inline-block;">
            <tooltip content="预计上线日期">
              <span
                style="color: #5578aa;border-radius:5px;background-color:#eef2f6;padding: 3px;margin-left: 10px;padding-right: 10px;">
                <i>{{ req.ReleaseDate }}</i>
              </span>
            </tooltip>
          </span>
          </Col>
          <Col :lg="2">
          <span style="display: inline-block; margin-right: 10px;text-align: center;">
            <Dropdown @on-click="onReqOperation">
              <span style="color: #5578aa">
                <Icon type="ios-more" size="32" />
              </span>
              <DropdownMenu slot="list">
                <DropdownItem :name="'delete:' + index">删除</DropdownItem>
                <DropdownItem :disabled="req.FortestingID !== 0" :name="'fortesting:' + index">提测
                </DropdownItem>
                <!--<DropdownItem :name="'task:'+index+':'+moduleIndex">任务</DropdownItem>-->
              </DropdownMenu>
            </Dropdown>
          </span>
          </Col>
        </Row>
      </CheckboxGroup>
      </Col>
    </Row>
    <Row v-if="addNewModule" align="middle" type="flex" style="border: 1px dashed #99aecc;">
      <Col :lg="layout.otherLayout.featureLayout.width" :md="layout.otherLayout.featureLayout.width + 2"
        style="padding: 16px;text-align: center;">
      <div>
        <div style="height: 8px; background-color: #5578aa">
        </div>
        <div class="newFeatureBorder">
          <div style="padding-bottom: 6px; ">
            <label-editor-input :id="0" :editing="1 === 1" :displayWidth="140" @cancelUpdate="cancelCreateModule"
              @updateValue="createModule" placeHolder="问题标题" displayText="新建模块"></label-editor-input>
          </div>
          <Progress :stroke-width="3" style="padding: 0px !important;" :percent="0"
            :stroke-color="['#108ee9', '#87d068']" />
          <!--<div  style="margin-top:0px;color:#5578aa;cursor: pointer; height: 3px;"><Icon v-if="showNewModule" type="ios-add-circle" /></div>-->
        </div>
      </div>
      </Col>
      <Col :lg="layout.otherLayout.featureLayout.requirementWidth"
        :md="layout.otherLayout.featureLayout.requirementWidth - 2"
        style="padding: 16px; border-left: 1px solid #eef2f6;">
      <CheckboxGroup v-model="selectReqList" @on-change="selectReq">
      </CheckboxGroup>
      </Col>
    </Row>
    <project-fortesting-create-dialog :fortestingID="0" :projectID="projectID" :versionID="projectVersion"
      :requirementList="selectedReqItems">
    </project-fortesting-create-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import ProjectFortestingCreateDialog from '../project-fortesting/ProjectFortestingCreateDialog.vue'

export default {
  name: 'projectReuqirementItem',
  props: ['dynicLayout', 'projectReqList', 'projectID'],
  data() {
    return {
      projectRequirementList: [],
      line: '',
      selectReqList: [],
      selectedReqItems: [],
      localLayout: {
        projectWidth: 0,
        otherLayout: {
          width: 0,
          featureLayout: {
            width: 0,
            requirementWidth: 0
          }
        }
      },
      reqStatus: [],
      reqRisk: [],
      reqPriority: [],
      showNewReq: false,
      addNewRequirement: false,
      addNewModule: false,
      reqFormData: {
        Title: '',
        Module: 0,
        projectID: 0,
        Version: 0,
        Priority: 3,
        ReleaseDate: '',
        Status: 1,
        Owner: 0,
        Creator: 0,
        RiskLevel: 1
      }
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['projectVersion', 'objectChange']),
    ...mapState(['appBodyHeight', 'appBodyWidth']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyHeight - 132
    },

    containerWidth: function () {
      return this.appBodyWidth
    },

    layout: function () {
      if (this.dynicLayout) {
        return this.dynicLayout
      } else {
        return this.localLayout
      }
    }

  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange']),

    onMouseOnReq: function (value) {
      this.showNewReq = true
    },

    onMouseOutReq: function () {
      this.showNewReq = false
      //        this.mouseOnReqID = 0
    },

    viewReqItem: function (value) {
      this.$emit('viewReqItem', value)
    },

    cancelCreateModule: function () {
      this.addNewModule = false
    },

    newRequirement: function () {
      this.reqFormData.Owner = this.userInfo.id
      this.reqFormData.Creator = this.userInfo.id
      this.reqFormData.projectID = this.projectID
      this.reqFormData.Version = this.projectVersion
      if ((this.reqFormData.ReleaseDate + '').length > 10) {
        let s = new Intl.DateTimeFormat('zh-cn')
        this.reqFormData.ReleaseDate = s.format(this.reqFormData.ReleaseDate).replace('/', '-').replace('/', '-')
      }
      if (this.reqFormData.ReleaseDate !== '' && this.reqFormData.Title.trim() !== '') {
        this.$axios.post('/api/project/' + this.projectID + '/requirements', this.reqFormData).then(response => {
          this.cancelNewRequirement()
          this.projectRequirementList.push(response.data.result)
          this.$Message.success({
            content: '需求添加成功',
            duration: 3,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '需求添加失败',
            duration: 3,
            closable: true
          })
        })
      } else {
        this.$Message.warning({
          content: '【需求标题】，【预计发布时间】为必填项',
          duration: 3,
          closable: true
        })
      }
    },

    cancelNewRequirement: function () {
      this.addNewRequirement = false
      this.reqFormData.Title = ''
      this.reqFormData.ReleaseDate = ''
    },

    createModule: function (value, id) {
      this.addNewModule = false
      let parameters = { Name: value, projectID: this.projectID }
      if (value.trim() !== '') {
        this.$axios.post('/api/project/' + this.projectID + '/modules', parameters).then(response => {
          this.projectReq.ViewData.Modules.push(response.data.result)
          this.$Message.success({
            content: '模块添加成功',
            duration: 3,
            closable: true
          })
        }, response => {
          this.$Message.error({
            content: '模块添加失败',
            duration: 3,
            closable: true
          })
        })
      }
    },

    loadReqPriority: function () {
      this.$axios.get('/api/project/task/task_status').then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 3) {
            this.reqStatus.push(tempItem)
          }
          if (tempItem.Type === 4) {
            this.reqPriority.push(tempItem)
          }
          if (tempItem.Type === 5) {
            this.reqRisk.push(tempItem)
          }
        }
      }, response => {
      })
    },

    updateModuleTitle: function (value, id) {
      let parameters = { Name: value }
      if (value !== '') {
        this.$axios.patch('/api/project/project_module/' + id + '/', parameters).then(response => {
          this.$Message.success({
            content: '模块标题更新成功',
            duration: 10,
            closable: true
          })
        }, response => {
          this.$Message.success({
            content: '模块标题更新失败',
            duration: 10,
            closable: true
          })
        })
      }
    },

    addNewReq: function () {
      this.addNewRequirement = true
    },

    OnaddNewModule: function () {
      this.addNewModule = true
    },

    onReqOperation: function (op) {
      let opArray = op.split(':')
      if (opArray[0] === 'delete') {
        this.deleteReq(parseInt(opArray[1]))
      }

      if (opArray[0] === 'fortesting') {
        this.doFortesting(parseInt(opArray[1]))
      }
    },

    deleteReq: function (reqIndex) {
      let deleteReq = this.projectRequirementList[reqIndex]
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除ID为[' + deleteReq.id + ']的需求',
        onOk: () => {
          this.$axios.delete('/api/project/requirement/' + deleteReq.id).then(response => {
            this.projectRequirementList.splice(reqIndex, 1)
            this.$Message.success({
              content: '需求删除成功',
              duration: 3,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '需求删除失败',
              duration: 3,
              closable: true
            })
          })
        },
        onCancel: () => {

        }
      })
    },

    doFortesting: function (reqIndex) {
      let selectReq = this.projectRequirementList[reqIndex]
      if (!this.findReqIdExists(selectReq.id, this.selectReqList)) {
        this.selectReqList.push(selectReq.id)
      }
      this.selectedReqItems = []
      if (this.selectReqList && this.selectReqList.length > 0) {
        for (let i = 0; i < this.selectReqList.length; i++) {
          let req = this.getReqByID(this.selectReqList[i], this.projectReq)
          let tempData = { id: req.id, title: req.Title, releaseDate: req.ReleaseDate }
          this.selectedReqItems.push(tempData)
        }
      }
      this.setCreateDialogShow(true)
    },

    findReqIdExists: function (req, reqList) {
      let result = false
      for (let i = 0; i < reqList.length; i++) {
        if (req === reqList[i]) {
          result = true
          break
        }
      }
      return result
    },

    getReqByID: function (id, projectReqInfo) {
      let result = null
      for (let j = 0; j < this.projectRequirementList.length; j++) {
        if (id === this.projectRequirementList[j].id) {
          result = this.projectRequirementList[j]
          break
        }
      }
      return result
    },

  },

  created: function () {
    this.loadReqPriority()
    this.projectRequirementList = this.projectReqList
  },
  watch: {
    projectReqList: function (value) {
      this.projectRequirementList = this.projectReqList
    }
  },

  components: {
    labelEditorInput,
    ProjectFortestingCreateDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}

.newFeatureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border: 1px dashed #99aecc;

}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;

}
</style>
