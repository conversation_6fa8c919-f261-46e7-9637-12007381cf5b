<template>
  <div class="layout">
    <Layout :style="'overflow-y:scroll;height:' + containerHeight + 'px;'">
      <Sider hide-trigger :style="{ background: '#fff' }">
        <Card title="迭代/版本" style="height: 100%;">
          <span style="margin-right: 10px;">
            <span>版本规划:</span>
            <Select v-model="filterPanel.Version" filterable placeholder="选择版本" @on-change="onVersionChange">
              <Option :value="0">全部</Option>
              <!-- <Option :value="0">需求池</Option> -->
              <Option v-for="item in projectVersions" :value="item.id" :key="item.id">{{ item.VersionLabel }}</Option>
            </Select>
          </span>
        </Card>
      </Sider>
      <Content>
        <Card :padding="2" :bordered="false" :dis-hover="true">
          <div slot="title">
            <span style="margin-right: 10px;">
              <span>模块:</span>
              <Select v-model="filterPanel.Module" style="width:100px" filterable placeholder="选择模块"
                @on-change="filterReqirements">
                <Option :value="0">全部</Option>
                <Option v-for="item in projectModules" :value="item.id" :key="item.id">{{ item.Name }}</Option>
              </Select>
            </span>
            <span style="margin-right: 10px;">
              <span>状态:</span>
              <Select v-model="filterPanel.Status" style="width:100px" filterable placeholder="选择状态"
                @on-change="filterReqirements">
                <Option :value="0">全部</Option>
                <Option v-for="item in reqStatus" :value="item.Status" :key="item.id">{{ item.Desc }}</Option>
              </Select>
            </span>
            <span style="margin-right: 10px;">
              <span>优先级:</span>
              <Select v-model="filterPanel.Priority" style="width:100px" filterable placeholder="选择优先级"
                @on-change="filterReqirements">
                <Option :value="-1">全部</Option>
                <Option v-for="item in reqPriority" :value="item.Status" :key="item.value">{{ item.Desc }}</Option>
              </Select>
            </span>
            <span style="margin-right: 10px;">
              <span>风险:</span>
              <Select v-model="filterPanel.Risk" style="width:100px" filterable placeholder="选择风险"
                @on-change="filterReqirements">
                <Option :value="0">全部</Option>
                <Option v-for="item in reqRisk" :value="item.Status" :key="item.value">{{ item.Desc }}</Option>
              </Select>
            </span>
            <span v-if="itemViewMode === 'list'" style="cursor: pointer; ">
              <Button :loading="reloadReq" shape="circle" icon="md-refresh" @click="reloadReqlist"></Button>
            </span>
          </div>
          <!--<Row>-->
          <!--<div style="height: 45px;background-color: white;margin-top: 0px;border-bottom: 1px solid #eef2f6;">-->
          <!--<Col :span="defaultLayout.projectWidth" v-if="defaultLayout.projectWidth>0">-->
          <!--<span style="display: inline-block;padding: 16px 5px 5px 5px;text-align: center;width: 100%;color:#5578aa;">项目</span>-->
          <!--</Col>-->
          <!--<Col :span="defaultLayout.otherLayout.width">-->
          <!--<Row>-->
          <!--<Col :span="defaultLayout.otherLayout.featureLayout.width">-->
          <!--<span style="display: inline-block;padding: 16px 5px 5px 5px;text-align: center;width: 100%;color:#5578aa;">功能模块</span>-->
          <!--</Col>-->
          <!--<Col :span="defaultLayout.otherLayout.featureLayout.requirementWidth">-->
          <!--</Col>-->
          <!--</Row>-->
          <!--</Col>-->
          <!--</div>-->
          <!--</Row>-->
          <div v-if="itemViewMode === 'board'">
            <!-- <project-requirement-board ref="reqBoard" @view-story="viewReqItem" @viewTaskItem="viewTaskItem"
              :projectID="selectedProject" :version="selectedVersion" :status="filterStatus" :priority="filterPriority" :risk="filterRisk">
            </project-requirement-board> -->
          </div>
          <div v-if="itemViewMode === 'list'">
            <project-requirement-item :projectReqList="projectReqList" :dynicLayout="defaultLayout"
              :projectID="projectID" @selectReqItem="selectReqList" @viewReqItem="viewReqItem">
            </project-requirement-item>
            <div style="margin: 10px;overflow: hidden">
              <div style="float: right; padding-right: 80px;">
                <Page :total="page.total" :page-size="page.pageSize" :current="page.current" @on-change="changePage" />
              </div>
            </div>
          </div>
        </Card>
      </Content>
    </Layout>
    <project-req-create-dialog></project-req-create-dialog>
    <!--<project-fortesting-create-dialog :fortestingID="0" :projectID="projectID" :versionID="version" :requirementList="selectedReqItems"></project-fortesting-create-dialog>-->
    <Drawer title="需求信息" v-model="showRequirementDetail" :transfer="false" :inner="true" :width="50" :mask="false">
      <project-requirement-detail :reqItem="viewedReqItem" :projectID="projectID" @updateReqItem="updateReqItem">
      </project-requirement-detail>
    </Drawer>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import ProjectRequirementItem from './ProjectRequirementItem.vue'
import ProjectReqCreateDialog from './ProjectRequirementCreateDialog.vue'
import ProjectRequirementDetail from './ProjectRequirementDetail.vue'
import ProjectFortestingCreateDialog from '../project-fortesting/ProjectFortestingCreateDialog.vue'
//import ProjectRequirementBoard from './ProjectRequirementBoard.vue'
import {
  loadProjectFortestings, getProjectInfo, loadProjectVersions, loadProjectModules
} from "../business-service/ProjectApiService"

export default {
  name: 'projectRequirement',
  props: ['projectID', 'layout'],
  components: {
    ProjectRequirementItem,
    ProjectReqCreateDialog,
    ProjectRequirementDetail,
    ProjectFortestingCreateDialog,
    // ProjectRequirementBoard
  },
  data() {
    return {
      selectedProject: 0,
      selectedModule: 0,
      selectedVersion: 0,
      sourceProject: [],
      projectVersions: [],
      selectedReqItems: [],
      projectReqList: [],
      viewedReqItem: 0,
      reqStatus: [],
      reqRisk: [],
      reqPriority: [],
      filterPanel: {
        page_size: 15,
        Version: 0,
        Module: 0,
        Status: 0,
        Priority: -1,
        Risk: 0,
        fileType: 0,
      },
      reloadReq: false,
      showRequirementDetail: false,
      defaultLayout: {
        projectWidth: 0,
        otherLayout: {
          width: 24,
          featureLayout: {
            width: 3,
            requirementWidth: 21
          }
        }
      },
      page: {
        current: 1,
        total: 1,
        pageSize: 15,
      },
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['objectChange', 'createReqType']),
    ...mapState(['appBodyHeight', 'appBodyMainHeight', 'appBodyWidth', 'itemViewMode']),
    ...mapState('project', ['projectList', 'projectModules']),

    containerHeight: function () {
      return this.appBodyHeight
    },

    containerWidth: function () {
      return this.appBodyWidth
    },
  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setObjectChange', 'setProjectVersion']),
    ...mapMutations(['setItemViewMode']),

    filterReqirements: function () {
      this.loadRequirementList()
    },

    selectReqList: function (value) {
      this.selectedReqItems = []
      if (value && value.length > 0) {
        for (let i = 0; i < value.length; i++) {
          let tempData = { id: value[i], title: value[i] }
          this.selectedReqItems.push(tempData)
        }
      }
    },

    onVersionChange: function (value) {
      this.selectedVersion = value
      this.loadRequirementList()
    },

    viewReqItem: function (value) {
      this.viewedReqItem = value
      this.showRequirementDetail = true
    },

    reloadReqlist: function () {
      this.page.current = 1
      this.loadRequirementList()
    },

    getFilters: function () {
      let filters = '?page=' + this.page.current
      filters = filters + '&page_size=' + this.filterPanel.page_size

      if (this.filterPanel.Version != 0) {
        filters = filters + '&Version=' + this.filterPanel.Version
      }
      if (this.filterPanel.Module != 0) {
        filters = filters + '&Module=' + this.filterPanel.Module
      }
      if (this.filterPanel.Status != 0) {
        filters = filters + '&Status=' + this.filterPanel.Status
      }
      if (this.filterPanel.Priority != -1) {
        filters = filters + '&Priority=' + this.filterPanel.Priority
      }
      if (this.filterPanel.Risk != 0) {
        filters = filters + '&Risk=' + this.filterPanel.Risk
      }

      return filters
    },

    loadRequirementList: function () {
      this.reloadReq = true
      let urlArg = this.getFilters()
      this.$axios.get('/api/project/' + this.projectID + '/requirement' + urlArg).then(response => {
        this.reloadReq = false
        this.projectReqList = response.data.result.results
        this.page.total = response.data.result.count
      }, response => {
        this.reloadReq = false
      })
    },

    updateReqItem: function (reqItem) {
      this.loadRequirementList()
    },

    loadReqPriority: function () {
      this.$axios.get('/api/project/task/task_status').then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          let tempItem = response.data.result[i]
          if (tempItem.Type === 3) {
            this.reqStatus.push(tempItem)
          }
          if (tempItem.Type === 4) {
            this.reqPriority.push(tempItem)
          }
          if (tempItem.Type === 5) {
            this.reqRisk.push(tempItem)
          }
        }
      }, response => {
      })
    },

    loadMyProject: function () {
      this.sourceProject = this.projectList
      if (this.projectID && this.projectID !== 0) {
        this.selectedProject = this.projectID
        loadProjectVersions(this.selectedProject).then(response => {
          this.projectVersions = response.data.result.all_versions
        })
      } else {
        this.selectedProject = this.sourceProject[0].id
      }
    },

    changePage: function (value) {
      this.page.current = value
      this.loadRequirementList()
    }

  },
  created: function () {
    this.setItemViewMode("list")
    this.loadReqPriority()
    if (this.layout) {
      this.defaultLayout = this.layout
    }
    if (this.$route.name === 'homeRequirement') {
      this.selectedProject = 0
    } else {
      this.selectedProject = this.projectID
    }
    this.reloadReqlist()
  },
  mounted: function () {
    if (this.$route.params.requirementID != undefined) {
      this.viewReqItem(this.$route.params.requirementID)
    }
    this.loadMyProject()
  },
  watch: {
    // selectedProject: function () {
    //   this.loadRequirementList(this.selectedProject)
    // },

    // selectedVersion: function () {
    //   this.loadRequirementList(this.selectedProject)
    // },

    layout: function () {
      if (this.layout) {
        this.defaultLayout = this.layout
      }
    }

  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;
}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

.border1 {
  border-left: 1px solid #eef2f6;
  border-right: 1px solid #eef2f6;
  border-bottom: 1px solid #eef2f6;
}

.featureBorder {
  line-height: 25px;
  text-align: center;
  padding: 10px;
  /*border-top: 8px solid #18b566 !important;*/
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}

.projectBorder {
  line-height: 20px;
  text-align: center;
  padding: 10px 16px 10px 16px;
  border-left: 1px solid #99aecc;
  border-right: 1px solid #99aecc;
  border-bottom: 1px solid #99aecc;
}
</style>
