<template>
  <div>
    <Card :padding="0" style="margin-top: 0px;border-color: #eef2f6" dis-hover>
      <!--<div class="home-summary-card-title">项目进展:-->
      <!--</div>-->
      <p slot="title">项目任务</p>
      <span slot="extra">
        <RadioGroup v-model="state" type="button" size="small" @on-change="filterTasks">
          <Radio label="-1">全部</Radio>
          <Radio label="0">待处理</Radio>
          <Radio label="1">处理中</Radio>
          <Radio label="2">已完成</Radio>
        </RadioGroup>
      </span>
      <span slot="extra" style="margin-left: 20px; ">
        <RadioGroup v-model="dateRange" type="button" size="small" @on-change="filterTasks">
          <Radio label="-7">7天</Radio>
          <Radio label="-15">15天</Radio>
          <Radio label="-30">30天</Radio>
        </RadioGroup>
      </span>
      <Card dis-hover :padding="0" :bordered="false" :style="'height:' + summaryHeight + 'px' + '; overflow-y:scroll;'">
        <Row v-for="project in projectTaskList" :key="project.id" style="border-bottom: 1px solid #eef2f6">
          <Col span="24" style="border-radius: 10px 0px 0px 10px">
          <div
            style="height: 54px;padding: 16px;border-bottom: 1px solid #e8eaec;border-top: 1px solid #e8eaec;border-radius: 5px 0px 0px 5px;">
            <span style="font-size: 16px;font-weight: bold;margin-right: 50px;">
              <router-link :to="'/project/' + project.id + '/issue/all'">
                <span style="padding-right:5px;">
                  <Avatar :src="project.PBAvatar"></Avatar>
                </span>
                <span style="color:#5578aa;text-decoration: underline;">
                  {{ project.PBTitle }}
                </span>
              </router-link>
            </span>
            <span style="font-size: 12px;margin-right: 20px;">任务总数: {{ project.ViewData.tasks.length }} 个</span>
            <span style="font-size: 12px;margin-right: 20px;">待处理任务:</span>
            <span style="font-size: 12px;border: 1px solid black;border-radius: 5px;padding: 2px 5px 2px 5px;">
              <Tooltip content="待处理/总数">
                <span style="color: darkred"> {{ project.ViewData.pending_task_count }} </span>
                <!--<span style="color: darkblue">-->
                <!--</span>-->
              </Tooltip>
            </span>
          </div>
          <div style="margin-left: 40px;">
            <Row v-for="task in project.ViewData.tasks" :key="task.id">
              <div style="height: 60px;padding: 16px 16px 10px 16px;">
                <Col span="16">
                <span>
                  <Tooltip content="完成日期">
                    <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
                      <Icon type="ios-calendar" color="#19be6b" /> {{ task.deadline_format }}
                    </i>
                  </Tooltip>
                </span>
                <i style="padding-right: 10px;">[{{ task.VersionName }}]</i>
                <span @click="showTaskDetail(task.id)" style="text-decoration: underline;cursor: pointer">
                  {{ task.Title }}
                </span>
                </Col>
                <Col span="2">
                <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                  <Icon type="ios-radio-button-on" color="#19be6b" />
                  {{ task.ViewData.Status }}
                </span>
                </Col>
                <Col span="2">
                <!--<span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">-->
                <!--<Tooltip content="工作量">-->
                <!--<span>-->
                <!--{{ fortesting.ViewData.duration }} 天-->
                <!--</span>-->
                <!--</Tooltip>-->
                <!--</span>-->
                </Col>
                <Col span="4" style="margin-top: -6px;">
                <span style="font-size: 12px;padding-right: 10px;">
                  <Tooltip content="负责人">
                    <Poptip v-if="task.Owner.length > 1" trigger="hover" transfer>
                      <Avatar style="background-color:#5578aa;">{{ task.OwnerName }}</Avatar>
                      <span v-if="task.Owner.length > 1" class="custom-avatar"
                        style="background-color: #ccd7e5;margin-left: -28px;"></span>
                      <div slot="content">
                        <span v-for="owner in task.ViewData.Owner" :key="owner.id"
                          :style="'background-color:' + owner.color + ';margin-left: 10px;font-size:12px;'"
                          class="custom-avatar">{{ owner.first_name }}</span>
                      </div>
                    </Poptip>
                    <span v-else>
                      <Avatar style="background-color:#5578aa;">{{ taskOwnerName }}</Avatar>
                    </span>
                  </Tooltip>
                </span>
                </Col>
              </div>
            </Row>
          </div>
          </Col>
        </Row>
        <Spin v-if="loading" size="large" fix></Spin>
      </Card>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'projectTaskList',
  props: {
    projectID: {
      type: [Number, String],
      default: 0
    },

    version: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      dateRange: '-7',
      state: '0',
      projectTaskList: [],
      loading: false
    }
  },
  computed: {
    ...mapState(['appBodyHeight']),
    summaryHeight: function () {
      return this.appBodyHeight - 66
    }
  },
  methods:
  {
    loadProjectTaskList: function () {
      this.loading = true
      this.$axios.get('/api/project/' + this.projectID + '/tasks/dashboard?date_range=' + this.dateRange + '&status=' + this.state).then(response => {
        this.projectTaskList = response.data.result
        this.loading = false
      }, response => {
        this.loading = false
      })

    },

    filterTasks: function (value) {
      this.loadProjectTaskList()
    },

    showTaskDetail: function (taskID) {
      this.$emit('onViewTaskItem', taskID)
    }

  },
  created: function () {
    this.loadProjectTaskList()

  },
  mounted: function () {
  },
  watch: {
    projectID: function (value) {
      this.loadProjectTaskList()
    }
  },

  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.custom-avatar {
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 50%;
}
</style>
