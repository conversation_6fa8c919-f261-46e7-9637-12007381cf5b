<template>
  <div :style="'height:' + appBodyHeight + 'px'">
    <el-card :bordered="true" :dis-hover="true" :padding="0" style="background-color: #f5f7f9;height: 100%; ">
      <project-task-board v-if="itemViewMode === 'board'" :projectID="projectID" :versionID="versionID"
        @onViewTaskItem="viewTaskItem">
      </project-task-board>
      <project-task-gannt v-if="itemViewMode === 'gannt'" :projectID="projectID"
        :versionID="versionID"></project-task-gannt>
      <project-task-list v-if="itemViewMode === 'list'" :projectID="projectID" :versionID="versionID"
        @onViewTaskItem="viewTaskItem"></project-task-list>
      <project-task-create-dialog :taskID="taskItemID" v-if="createDialogShow"></project-task-create-dialog>
      <Drawer @on-close="onPanelClose" title="筛选分析" :value="rightSidePanelShow" :inner="true" :transfer="false"
        :width="30" :mask="false">
        <project-task-filter :projectID="projectID" :versionID="versionID"></project-task-filter>
      </Drawer>
      <Drawer @on-close="onPanelClose" :title="'任务 #' + taskItemID" v-model="showTaskDetail" :transfer="false"
        :inner="true" :width="50" :mask="true">
        <project-task-info :taskID="taskItemID" :projectID="projectID" :versionID="versionID"></project-task-info>
      </Drawer>
    </el-card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from "vuex";
import ProjectTaskCreateDialog from "./ProjectTaskCreateDialog.vue";
import ProjectTaskInfo from "./ProjectTaskInfo.vue";
import ProjectTaskBoard from "./ProjectTaskBoard.vue";
import ProjectTaskFilter from "./ProjectTaskFilter.vue";
import ProjectTaskGannt from "./ProjectTaskGannt.vue";
import ProjectTaskList from "./ProjectTaskList.vue";

export default {
  name: "projectTaskView",
  props: {
    projectID: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      columnItemHeight: 200,
      taskItemID: 0,
      showTaskDetail: false,
    };
  },
  computed: {
    ...mapState(['appBodyHeight', "appBodyMainHeight"]),
    ...mapState('projectglobal', ['createDialogShow']),
    ...mapGetters("task", ["taskChange", "taskFilterStatus", "taskFilterOwners", "taskFilterKeyword", "taskFilters", "taskGanntMaxSize",]),
    ...mapGetters("projectglobal", ["projectVersion", "rightSidePanelShow", "taskViewMode",
    ]),
    ...mapGetters(["itemViewMode"]),

    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      } else {
        return this.$route.params.versionID;
      }
    },
  },
  methods: {
    ...mapMutations(["setItemViewMode"]),
    ...mapMutations("task", ["setTaskChange", "setTaskGanntMaxSize"]),
    ...mapMutations("projectglobal", ["setViewDialogShow", "setRightPanelShow", "setProjectVersion",]),

    onPanelClose() {
      this.setRightPanelShow(false);
    },

    viewTaskItem(taskID) {
      this.showTaskDetail = true;
      this.taskItemID = parseInt(taskID);
    },
  },
  created: function () {
    this.projectID = parseInt(this.$route.params.projectID)
    this.setItemViewMode("board");
  },
  mounted: function () {
    if (this.$route.params.taskId != undefined) {
      this.viewTaskItem(this.$route.params.taskId)
    }
  },
  watch: {},
  components: {
    ProjectTaskCreateDialog,
    ProjectTaskBoard,
    ProjectTaskFilter,
    ProjectTaskGannt,
    ProjectTaskInfo,
    ProjectTaskList,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;
}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}
</style>
