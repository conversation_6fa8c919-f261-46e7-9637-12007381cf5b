<template>
  <div :id="task.id" :key="task.id">
    <Card :padding="0" class="board-column-item"
      :style="'border-radius:0px !important;border-left: 3px solid ' + task.PriorityFormator">
      <div class="board-item-body">
        <div class="cursor-hand">
          <Checkbox @on-change="onFinishedTask" v-if="task.Status !== 2"></Checkbox>
          <span @click="onViewTask" style="font-size: 12px;color: #5578aa;">
            {{ task.ChildStatus }}
            <Tag> #{{ task.id }} </Tag>
            <i>[ {{ task.ProjectName }}-{{ task.VersionName }} ]</i>
            {{ task.Title }}
          </span>
        </div>
      </div>
      <div class="board-item-avatar">
        <span style="font-size: 10px;">
          <Tooltip content="任务类型">
            <Tag style="padding: 0px 5px 0px 5px;" :color="task.ViewData.TaskType.label_color">{{
              task.ViewData.TaskType.label }}
            </Tag>
          </Tooltip>
        </span>
        <span v-if="task.Status === 1">
          <Tooltip content="截止时间">
            <Tag style="padding: 0px 5px 0px 5px;" :color="task.DeadLineFormat.color">{{
              task.DeadLineFormat.label }}</Tag>
          </Tooltip>
        </span>
        <span v-if="task.Status === 0">
          <Tooltip content="截止时间">
            <Tag style="padding: 0px 5px 0px 5px;" :color="task.DeadLineFormat.color">
              {{ task.DeadLineFormat.label }}
            </Tag>
          </Tooltip>
        </span>
        <span v-if="task.Owner.length > 1">
          <Poptip trigger="hover" transfer>
            <Avatar style="background-color:#5578aa;" size="small">{{ taskOwnerName }}</Avatar>
            <span class="custom-avatar" style="background-color: #ccd7e5;margin-left: -20px;"></span>
            <div slot="content">
              <!--<Avatar v-for="owner in task.ViewData.Owner" :key="owner.id" :style="'background-color:'+ owner.color +';margin-left: 10px;'">{{ owner.first_name }}</Avatar>-->
              <span v-for="owner in task.ViewData.Owner" :key="owner.id"
                :style="'background-color:' + owner.color + ';font-size:12px;'" class="custom-avatar">{{
                  owner.first_name }}</span>
            </div>
          </Poptip>
        </span>
        <span v-else>
          <Avatar style="background-color:#5578aa;" size="small">{{ taskOwnerName }}</Avatar>
        </span>
        <span>
          <Dropdown v-if="userInfo.id === task.Creator" trigger="click" :transfer="true" placement="bottom-start">
            <Icon type="ios-list" size="26" />
            <DropdownMenu slot="list">
              <DropdownItem>
                <Icon type="ios-trash-outline" />
                <span @click="onDeleteTask(task.id, task.Title)" style="font-size: 10px;">删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
      </div>
      <!--<Badge count="10"></Badge>-->


    </Card>
  </div>
</template>

<script>

import { mapState, mapGetters, mapMutations } from 'vuex'
export default {
  name: 'ProjectTaskItem',
  props: ['task'],
  data() {
    return {
      msg: 'Welcome to Your Vue.js App'
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),

    taskOwnerName: function () {
      let result = this.task.OwnerName
      for (let i = 0; i < this.task.ViewData.Owner.length; i++) {
        if (this.userInfo.id === this.task.ViewData.Owner[i].id) {
          result = this.task.ViewData.Owner[i].first_name
          break
        }
      }
      return result
    }

  },
  methods: {
    ...mapMutations('task', ['setTaskChange']),
    onTaskItemClick(event) {
      this.setCreateDialogShow(true)
      let taskID = event.target.getAttribute('id')
      this.taskItemID = parseInt(taskID)
    },
    onViewTask(event) {
      this.$emit('view-task', this.task.id)
    },
    onDeleteTask(taskID, taskTitle) {
      this.$Modal.confirm({
        title: '任务删除确认',
        content: '<p>确定要删除任务【' + taskTitle + '】</p>',
        onOk: () => {
          this.deleteTask(taskID)
        },
        onCancel: () => {
        }
      })
    },

    onFinishedTask: function (value) {
      this.$emit('finished-task', this.task.id, this.task.Status)
    },

    deleteTask(taskID) {
      //console.log(taskID)
      this.$axios.delete('/api/project/task/' + taskID).then(response => {
        this.setTaskChange(true)
      }, response => {
        this.setTaskChange(true)

      })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 60px;
  max-height: 300px;
  min-width: 300px;
  width: 320px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

.board-item-avatar {
  width: 100%;
  padding: 10px;
  text-align: right;
}

.board-item-avatar span {
  vertical-align: middle;
}

.board-item-body {
  width: 100%;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;
  color: #5578aa;
}

.custom-avatar {
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
}
</style>
