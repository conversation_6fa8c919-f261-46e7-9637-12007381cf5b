<template>
  <div style="color:#5578aa">
    <Card :shadow="false" :padding="0" :bordered="false" dis-hover>
      <div class="filter-block">
        <div class="filter-block-body">
          <Input @on-search="onSearchTask" search placeholder="输入任务标题，回车搜索" />
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-eye"></i> 状态</p>
        </div>
        <div class="filter-block-body">
          <CheckboxGroup v-model="filterPanel.status" @on-change="onStatusChange">
            <Row>
              <Col :lg="8" :sm="12">
              <Checkbox label="0">待处理</Checkbox>
              </Col>
              <Col :lg="8" :sm="12">
              <Checkbox label="1">处理中</Checkbox>
              </Col>
              <Col :lg="8" :sm="12">
              <Checkbox label="2">已完成</Checkbox>
              </Col>
              <Col :lg="8" :sm="12">
              <Checkbox label="3">暂停</Checkbox>
              </Col>
            </Row>
          </CheckboxGroup>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-hand-o-right fa-fw"></i> 执行人</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.owners" filterable multiple :max-tag-count="4" @on-change="onOwnerChange">
            <Option v-for="item in projectMembers" :value="item.PMMember" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-hand-o-right fa-fw"></i> 任务类型</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.TaskType" placeholder="任务类型" @on-change="onTaskTypeChange">
            <Option v-for="item in projectTaskTypes" :value="item.id" :key="item.id">{{ item.Label }}</Option>
          </Select>
        </div>
      </div>
    </Card>
    <Divider orientation="left">统计</Divider>
    <Card :shadow="false" :padding="0" :bordered="false" dis-hover>
      <div class="filter-block">
        <div class="filter-block-body">
          <Row>
            <Col :lg="8" :sm="8" style="padding: 5px;">
            <Card dis-hover style="padding: 5px 5px 5px 5px;height: 70px;" :padding="0">
              <div style="font-size: 10px;">今日完成</div>
              <div style="width: inherit;text-align: center;">{{ taskSummaryCount.today_finished_count }}</div>
            </Card>
            </Col>
            <Col :lg="8" :sm="8" style="padding: 5px;">
            <Card dis-hover style="padding: 5px 5px 5px 5px;height: 70px;" :padding="0">
              <div style="font-size: 10px;">延期任务:</div>
              <div style="width: inherit;text-align: center;">{{ taskSummaryCount.delayed_count }}</div>
            </Card>
            </Col>
            <Col :lg="8" :sm="8" style="padding: 5px;">
            <Card dis-hover style="padding: 5px 5px 5px 5px;height: 70px;" :padding="0">
              <div style="font-size: 10px;">延期完成</div>
              <div style="width: inherit;text-align: center;">{{ taskSummaryCount.delay_finished_count }}</div>
            </Card>
            </Col>
          </Row>
        </div>
      </div>
    </Card>
    <Card :shadow="false" :padding="0" :bordered="false" dis-hover>
      <div class="x-bar">
        <div :id="id"></div>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import HighCharts from 'highcharts'

export default {
  name: 'projectTaskFilter',
  props: {
    projectID: {
      type: [Number, String],
    },
    versionID: {
      type: [Number, String],
    }
  },
  data() {
    return {
      projectMembers: [],
      projectTaskTypes: [],
      filterPanel: {
        status: [],
        owners: [],
        keyWords: '',
        taskType: 0
      },
      id: 'taskStatusSummary',
      pieChartOption: {
        chart: {
          type: 'pie',
        },
        title: {
          text: '任务状态分布'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: false
            },
            showInLegend: true
          }
        },
        tooltip: {
          headerFormat: '{series.name}<br>',
          pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
        },
        series: [{
          name: '任务占比',
          data: [
          ]
        }]

      },

      taskSummaryCount: {}

    }
  },
  computed: {
    ...mapGetters('task', ['taskChange', 'taskFilters']),
    ...mapGetters('projectglobal', ['projectVersion', 'rightSidePanelShow']),
    ...mapState(['appBodyHeight']),
    containerHeight: function () {
      return this.appBodyHeight - 26
    },
    project: function () {
      let result = 0
      if (this.projectID) {
        result = this.projectID
      }
      return result
    }

  },
  methods:
  {
    ...mapMutations('task', ['setTaskChange', 'setTaskFilterStatus', 'setTaskFilterOwners', 'setTaskFilterKeyword', 'setTaskFilterType']),
    ...mapMutations('projectglobal', ['setViewDialogShow', 'setProjectVersion']),

    getProjectMembers(project) {
      this.$axios.get('/api/project/' + project + '/project_members').then(response => {
        this.projectMembers = response.data.result
      }, response => {
      })
    },

    loadProjectTaskType: function () {
      this.$axios.get('/api/project/task_type').then(response => {
        this.projectTaskTypes = response.data.result
      }, response => {
        // error callback
      })
    },

    onSearchTask(keyword) {
      this.setTaskFilterKeyword(keyword)
    },

    onStatusChange(value) {
      this.setTaskFilterStatus(value)
    },


    onTaskTypeChange(value) {
      this.setTaskFilterType(value)
    },

    onOwnerChange(value) {
      this.setTaskFilterOwners(value)
    },

    setTaskStatusPie(projectID, versionID) {
      this.$axios.get('/api/project/' + projectID + '/version/' + versionID + '/statistics/task_status_pie').then(response => {
        this.pieChartOption.title.text = response.data.result.chart_title
        this.pieChartOption.series[0].data = response.data.result.series_data
        HighCharts.chart(this.id, this.pieChartOption)
      }, response => {
      })
    },

    setTaskSummaryCount(projectID, versionID) {
      this.$axios.get('/api/project/' + projectID + '/version/' + versionID + '/statistics/task_summary_count').then(response => {
        this.taskSummaryCount = response.data.result
      }, response => {
      })
    }

  },
  created: function () {
    this.getProjectMembers(this.project)
    this.loadProjectTaskType()
  },
  mounted: function () {
  },
  watch: {
    projectID: function (value) {
      this.getProjectMembers(this.project)
    },

    versionID: function (value) {
      this.setTaskStatusPie(this.projectID, value)
    },

    rightSidePanelShow: function (value) {
      if (value) {
        this.setTaskStatusPie(this.projectID, this.projectVersion)
        this.setTaskSummaryCount(this.projectID, this.projectVersion)
        this.getProjectMembers(this.project)

      }
    }
  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.filter-block {
  margin-top: 15px;
  padding-left: 15px;
}

.filter-block-title {
  padding: 0px;
}

.filter-block-body {
  padding: 10px;
}
</style>
