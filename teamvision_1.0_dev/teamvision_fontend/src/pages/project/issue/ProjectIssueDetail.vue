<template>
  <div style="margin: -16px;" class="issue-detail" v-if="issueDetail">
    <div class="issue-title">
      <Button v-if="issueDetail.Status === 5" style="margin-right: 10px;" @click="onResloveIssue" type="success"
        icon="ios-construct" ghost>解决</Button>
      <Button v-if="issueDetail.Status === 2" style="margin-right: 10px;" @click="onResloveIssue" type="success"
        icon="ios-construct" ghost>解决</Button>
      <Button v-if="issueDetail.Status !== 3" style="margin-right: 10px;" @click="onCloseIssue" type="warning"
        icon="md-checkmark-circle-outline" ghost>关闭</Button>
      <Button v-if="issueDetail.Status === 3" style="margin-right: 10px;" @click="onReOpenIssue" type="error"
        icon="md-redo" ghost>重新打开</Button>
      <Button v-if="issueDetail.Status === 4" style="margin-right: 10px;" @click="onReOpenIssue" type="error"
        icon="md-redo" ghost>重新打开</Button>
    </div>
    <Divider style="margin:0px" />
    <div class="issue-detail-body">
      <div class="issue-detail-summary">
        <div style="height: 80px;" class="issue-header">
          <div style="height: 35px;display: inline-flex">
            <span class="issue-detail-id">#{{ issueDetail.id }}</span>
            <span class="issue-detail-title">
              <label-editor-input @updateValue="updateIssueTitle" placeHolder="问题标题" style="display: inline-block;"
                :displayWidth="issueTitleMaxWidth" :displayText="issueDetail.Title">
              </label-editor-input>
            </span>
          </div>
          <div class="issue-status">
            <div class="issue-detail-creation">
              <span class="issue-detail-createdate">状态:
                <i :class="'fa ' + issueDetail.status_name.Label + ' ' + issueDetail.status_name.LabelStyle"></i>
                <span>{{ issueDetail.status_name.Name }}</span>
              </span>
              <span class="issue-detail-createdate">解决结果:
                <i :class="'fa ' + issueDetail.solution_name.Label + ' ' + issueDetail.solution_name.LabelStyle"></i>
                <span>{{ issueDetail.solution_name.Name }}</span>
              </span>
            </div>
            <div class="issue-detail-creation">
              <span class="issue-detail-creator">{{ issueDetail.view_data.creator_name }} 创建于 {{
                issueDetail.CreationTime
              }}</span>
              <span class="issue-detail-createdate">{{ issueDetail.update_date }} 更新。</span>
            </div>
          </div>
          <div class="issue-detail-creation" style="font-size: 14px;">
            <span class="issue-detail-creator">关联需求:</span>
            <span class="issue-detail-createdate">
              <label-editor-select @updateValue="updateIssueRequirement" :optionList="projectRequirements"
                :displayText="issueDetail.requirement_detail" :value="issueDetail.Requirement"
                :itemID="issueDetail.Requirement">
                <template slot-scope="slotProps">
                  <span style="font-size: 14px;">{{ issueDetail.requirement_detail ? issueDetail.requirement_detail :
                    '无'
                    }}</span>
                  <span style="display: inline-block">
                    <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
                  </span>
                </template>
              </label-editor-select>
            </span>
          </div>
        </div>
      </div>
      <Divider style="margin:0px" />
      <div class="issue-detail-editor">
        <div class="issue-detail-desc">
          <div class="issue-detail-desc-title">
            <i class="fa fa-sticky-note"></i>描述：
          </div>
          <div class="issue-detail-desc-content-veiw">
            <label-editor-vue-editor placeHolder="问题描述" @updateValue="updateIssueDesc" :displayText="issueDetail.Desc">
            </label-editor-vue-editor>
          </div>
          <Divider style="margin:0px" />
          <div class="issue-detail-attachment">
            <Row>
              <Col :span="18">
              <span style="color: #b2b2b2;font-size: 14px;">
                <Icon type="ios-attach" :size="20" />附件：
              </span>
              </Col>
              <Col :span="6">
              <span @click="uploadIssueAttachment">
                <Button icon="md-cloud-upload" shape="circle">上传</Button>
              </span>
              </Col>
            </Row>
            <div style="padding-top: 20px;">
              <Row v-for="attachment in issueDetail.attachments_detail" :key="attachment.id" class="attachment-item">
                <Col :span="18">
                <span class="cursor-hand"
                  @click="onViewAttachment(attachment.FileName, attachment.id, attachment.FileSuffixes)">
                  <Icon type="ios-attach" :size="15" />{{ attachment.FileName }}
                </span>
                </Col>
                <Col :span="6">
                <a :href="'/api/project/issue/' + issueID + '/attachment/' + attachment.id">
                  <Icon type="ios-cloud-download" :size="20" />
                </a>
                <span class="cursor-hand" @click="delectAttachment(attachment.id, attachment.FileName)">
                  <Icon type="ios-trash" :size="20" />
                </span>
                <span>
                  <Time :time="attachment.CreationTime" />
                </span>
                </Col>
              </Row>
            </div>
            <Divider style="margin:0px" />
            <div class="issue-detail-comments">
              <div style="font-size: 15px;margin-bottom: 15px;">
                <Icon type="ios-megaphone" :size="15" /> 问题动态
              </div>
              <div style="padding-left: 20px;padding-bottom: 80px;">
                <Timeline>
                  <TimelineItem v-for="item in issueActivities" :key="item.id" color="#3b73af">
                    <Icon :size="20" type="md-add-circle" slot="dot"></Icon>
                    <span style="font-size: 15px; white-space: normal">
                      {{ item.creator_name }} {{ item.action_flag_name }} {{ item.action_type_name }}
                      <span v-html="item.FieldDesc"></span>
                      <span v-html="item.Message"></span>
                      <span style="text-decoration: line-through;">{{ item.OldValue }}</span>
                      <span v-html="item.NewValue"></span>
                    </span>
                    <span style="float: right;padding-right: 20px;">{{ item.CreationTime }}</span>
                  </TimelineItem>
                </Timeline>
              </div>
            </div>
            <div class="issue-comments-input">
              <Input search enter-button="发布" @on-search="onAddComments" size="large" placeholder="输入内容，回车发布备注信息" />
            </div>
          </div>
        </div>
        <div class="issue-detail-option">
          <div class="issue-detail-option-content">
            <div class="issue-detail-option-field-name">发现方式:
              <i class="fa fa-hand-o-right"></i>
              <span>{{ issueDetail._discover_way ? issueDetail._discover_way : "无" }}</span>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <span class="issue-detail-option-field-name">严重性:</span>
            <span>
              <label-select :options="getIssueSeverityList" :value="issueDetail.Severity"
                @updateValue="updateIssueSeverity">
                <template slot-scope="slotProps">
                  <i :class="'fa ' + issueDetail.severity_name.Label + ' ' + issueDetail.severity_name.LabelStyle"></i>
                  <span>{{ issueDetail.severity_name.Name }}</span>
                </template>
              </label-select>
            </span>
          </div>
          <div class="issue-detail-option-content">
            <span class="issue-detail-option-field-name">优先级:</span>
            <span>
              <label-select :options="issuePriorityList" :value="issueDetail.Priority"
                @updateValue="updateIssuePriority">
                <template slot-scope="slotProps">
                  <i class="fa fa-fire issue-status-reopen"></i>
                  <span>{{ issueDetail.priority_name }} </span>
                </template>
              </label-select>
            </span>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-option-field-name">经办人:
              <Select v-model="issueDetail.Processor" @on-change="onProcessorChange" label-in-value style="width:100px"
                filterable>
                <Option v-for="item in projectMembers" :value="item.PMMember" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span>版本： </span>
                <a href="javascript:void(0)">
                  <span ref="Version">{{ issueDetail.view_data.version_name }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list" style="max-height: 300px;overflow-y: scroll;">
                  <DropdownItem v-for="item in projectVersions" :selected="item.id === issueDetail.Version"
                    :name="item.id + ',' + item.VVersion + ',' + 'Version'" :key="item.id">{{ item.VVersion }}
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span ref="Team"> <i class="fa fa-hand-o-right"></i> 团队： </span>
                <a href="javascript:void(0)">
                  <span ref="Team">{{ issueDetail.team_name ? issueDetail.team_name : "无" }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in projectTeams" :selected="item.id === issueDetail.Team"
                    :name="item.id + ',' + item.Name + ',' + 'Team'" :key="item.Value">{{ item.Name }}</DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span> <i class="fa fa-cube"></i> 模块：</span>
                <a href="javascript:void(0)">
                  <span ref="Module"> {{ issueDetail.module_name ? issueDetail.module_name : "无" }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in projectModules" :selected="item.id === issueDetail.Module"
                    :name="item.id + ',' + item.Name + ',' + 'Module'" :key="item.id">{{ item.Name }}</DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span> <i class="fa fa-sticky-note"></i> 系统：</span>
                <a href="javascript:void(0)">
                  <span ref="DeviceOS"> {{ issueDetail.os_name }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in DeviceOS" :selected="item.id === issueDetail.DeviceOS"
                    :name="item.Value + ',' + item.Name + ',' + 'DeviceOS'" :key="item.Value">{{ item.Name }}
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span> <i class="fa fa-chain"></i> 问题分类： </span>
                <a href="javascript:void(0)">
                  <span ref="IssueCategory">{{ issueDetail.category_name }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in issueCategoriesList" :selected="item.id === issueDetail.IssueCategory"
                    :name="item.Value + ',' + item.Name + ',' + 'IssueCategory'" :key="item.Value">{{ item.Name }}
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
          <div class="issue-detail-option-content">
            <div :span="24" class="issue-detail-field-content">
              <Dropdown @on-click="onFieldItemClick">
                <span> <i class="fa fa-leaf"></i> 项目阶段： </span>
                <a href="javascript:void(0)">
                  <span ref="ProjectPhase"> {{ issueDetail.project_phrase_name }}</span>
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem v-for="item in issuePhraseList" :selected="item.id === issueDetail.ProjectPhase"
                    :name="item.Value + ',' + item.Name + ',' + 'ProjectPhase'" :key="item.Value">{{ item.Name }}
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <Modal v-model="viewAttachment.showDialog" class-name="attachment-view" fullscreen :styles="fullScreenStyle"
        footer-hide :title="viewAttachment.fileName">
        <div v-if="viewAttachment.isPicture == false && viewAttachment.isVideo == false">
          <Alert type="error" show-icon>
            <span slot="desc"> 不支持非图片/视频类文件的预览，请下载这个文件！</span>
          </Alert>
        </div>
        <div v-if="viewAttachment.isPicture">
          <img :src="'/api/project/issue/' + issueID + '/attachment/' + viewAttachment.fileID"
            style="max-width: 1920px; max-height:1080px" />
        </div>
        <div v-if="viewAttachment.isVideo">
          <VideoPlayer :urlPath="'/api/project/issue/' + issueID + '/attachment/' + viewAttachment.fileID" />
        </div>
      </Modal>
    </div>
    <Modal v-model="showUploadAttachmentDialog" title="附件上传" @on-ok="onPatchIssueAttachment">
      <Tabs>
        <TabPane label="本地上传">
          <Upload ref="upload" multiple type="drag" paste action="/api/project/issue/attachments"
            :on-success="handleSuccess" :on-remove="handleRemove" :format="[]" :max-size="20480"
            :default-file-list="defaultList" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
            <div style="padding: 20px 0">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <p>点击，拖拽，粘贴上传附件</p>
            </div>
          </Upload>
        </TabPane>
        <TabPane label="扫码上传" style="padding-left: 35%;">
          <img :src="'/api/common/toolkit/qrcode?content=' + qrcodeContent" style="height: 100px;width: 100px;" />
        </TabPane>
      </Tabs>
    </Modal>
    <Modal v-model="resloveIssueDialog.isShow" :title="resloveIssueDialog.title" @on-ok="resloveIssueResult">
      <Form ref="resloveIssue" :model="formItem" :label-width="40">
        <FormItem v-if="resloveIssueDialog.dialogType === 1" label="解决结果" prop="ResloveResult">
          <Select v-model="formItem.ResloveResult">
            <Option v-for="item in issueResolvedResult" :key="item.Value" :value="item.Value">{{ item.Name }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="描述" prop="Desc">
          <vue-editor v-model="formItem.status_desc" :editorToolbar="editorToolBar" placeholder="问题描述"></vue-editor>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import labelEditorSelect from '../../../components/common/LabelEditor-Select.vue'
import LabelSelect from '../../../components/common/Label-Select.vue'
import labelEditorVueEditor from '../../../components/common/LabelEditor-VUEEditor.vue'
import VideoPlayer from '../../../components/common/VideoPlayer.vue'
import { VueEditor } from 'vue2-editor'
import { mapGetters, mapMutations, mapState } from 'vuex'
import { loadProjectMembers } from '../business-service/ProjectApiService'

export default {
  name: 'ProjectIssueDetail',
  props: {
    projectID: {
      type: [Number, String],
      default: 0
    },
    issueID: {
      type: [Number, String],
      default: 0
    },
  },
  data() {
    return {
      fullScreenStyle: {
        width: '100% !important',
        height: '100%'
      },
      issueDetail: {
        view_data: {
          creator_name: ""
        }
      },
      projectTeams: [],
      projectModules: [],
      projectRequirements: [],
      projectMembers: [],
      projectVersions: [],
      DeviceOS: [],
      issueResolvedResult: [],
      issueActivities: [],
      viewAttachment: {
        isPicture: false,
        isVideo: false,
        showDialog: false,
        fileName: '',
        fileID: 0
      },
      issueFieldValue: {
        Processor: 0
      },
      editorToolBar: [
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }], [{ 'color': [] }, { 'background': [] }],
      ],
      showUploadAttachmentDialog: false,
      uploadList: [],
      defaultList: [],
      resloveIssueDialog: {
        isShow: false,
        title: '',
        dialogType: 0
      },
      issueComments: {
        Message: '',
        ActionFlag: 1,
        ActionType: 2,
        Issue: 0,
        OldValue: '',
        NewValue: '',
        FieldName: ''
      },
      formItem: {
        ResloveResult: 1,
        status_desc: '',
        Status: 0,
      }
    }
  },

  computed: {
    ...mapState(['appBodyHeight', 'appBodyWidth']),
    ...mapState('usercenter', ['userInfo',]),
    ...mapState('issue', ['issuePhraseList', 'issueCategoriesList', 'issuePriorityList']),
    ...mapGetters('issue', ['getIssueSeverityList']),

    qrcodeContent: function () {
      return 'http://' + window.location.host + '/project/issue/' + this.issueID + '/mobile/upload'
    },

    issueTitleMaxWidth: function () {
      // console.log(this.appBodyWidth * 0.6)
      return this.appBodyWidth * 0.6 - 200
    },

    // issueSeverityList: function () {
    //   return this.getIssueSeverityList
    // },
  },

  methods: {
    ...mapMutations('issue', ['setIssueChange']),

    loadIssueDetail: function (issueID) {
      this.$axios.get('/api/project/issue/' + issueID).then(response => {
        this.issueDetail = response.data.result
        this.loadIssueProjectModules(this.issueDetail.Project)
        this.getMyProjectMembers(this.issueDetail.Project)
        this.loadProjectVersions(this.issueDetail.Project)
      })
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.uploadList.push(file.id)
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList
      // console.log(this.uploadList)
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过20M限制',
        duration: 10,
        closable: true
      })
    },

    uploadIssueAttachment: function () {
      this.showUploadAttachmentDialog = true
    },

    delectAttachment: function (fileID, fileName) {
      this.$Modal.confirm({
        title: '附件删除确认',
        content: '即将删除附件' + fileName,
        onOk: () => {
          this.$axios.delete('/api/project/issue/' + this.issueDetail.id + '/attachment/' + fileID).then(response => {
            this.loadIssueDetail(this.issueDetail.id)
          }, response => { })
        },
        onCancel: () => {
        }
      })
    },

    onPatchIssueAttachment: function () {
      let paras = {}
      paras['uploadList'] = this.uploadList
      this.$axios.patch('/api/project/issue/' + this.issueDetail.id + '/attachment/0', paras).then(response => {
        this.$Message.success({
          content: '文件上传成功',
          duration: 3,
          closable: true
        })
        this.uploadList = []
        this.loadIssueDetail(this.issueDetail.id)
      }, response => {
        this.uploadList = []
      })
    },

    onResloveIssue: function () {
      this.resloveIssueDialog.isShow = true
      this.resloveIssueDialog.title = '解决问题'
      this.resloveIssueDialog.dialogType = 1
      this.formItem.Status = 4
      this.formItem.Solution = this.formItem.ResloveResult
      console.log(this.formItem)
    },

    onCloseIssue: function () {
      this.resloveIssueDialog.isShow = true
      this.resloveIssueDialog.title = '关闭问题'
      this.resloveIssueDialog.dialogType = 2
      this.formItem.Status = 3
    },

    onReOpenIssue: function () {
      this.resloveIssueDialog.isShow = true
      this.resloveIssueDialog.title = '重新打开问题'
      this.resloveIssueDialog.dialogType = 3
      this.formItem.Status = 5
    },

    resloveIssueResult: function () {
      this.formItem['operation'] = this.resloveIssueDialog.dialogType
      this.formItem.Solution = this.formItem.ResloveResult
      this.$axios.patch('/api/project/issue/' + this.issueDetail.id, this.formItem).then(response => {
        this.issueDetail = response.data.result
        this.setIssueChange(true)
        // this.loadIssueDetail(this.issueID)
        this.loadIssueActivities(this.issueID)
      }, response => {
      })
    },

    updateIssueTitle: function (value) {
      if (value.trim() !== this.issueDetail.Title.trim()) {
        this.updateIssueInputField(this.issueDetail.Title.trim(), value.trim(), 'Title')
      }
    },

    updateIssueDesc: function (value) {
      if (value.trim() !== this.issueDetail.Desc.trim()) {
        this.updateIssueInputField(this.issueDetail.Desc.trim(), value.trim(), 'Desc')
      }
    },

    updateIssueInputField: function (oldValue, newValue, fieldName) {
      if (oldValue.length < 2000 && newValue.length < 2000) {
        this.issueComments.OldValue = oldValue
        this.issueComments.NewValue = newValue
      } else {
        this.issueComments.OldValue = ''
        this.issueComments.NewValue = ''
      }
      this.issueDetail[fieldName] = newValue
      this.issueComments.Message = '属性'
      this.issueComments.Issue = this.issueID
      this.issueComments.Creator = this.userInfo.id
      this.issueComments.FieldName = fieldName
      this.issueComments.ActionFlag = 2
      this.issueComments.ActionType = 1
      this.updateIssueProperty(fieldName, newValue)
    },

    updateIssuePriority: function (value, label) {
      if (value !== this.issueDetail.Priority) {
        this.onFieldItemClickNew('Priority', value, label, this.issueDetail.priority_name)
      }
    },

    updateIssueSeverity: function (value, label) {
      if (value !== this.issueDetail.Severity) {
        this.onFieldItemClickNew('Severity', value, label, this.issueDetail.severity_name.Name)
      }
    },

    updateIssueVersion: function (value, label) {
      if (value !== this.issueDetail.Version) {
        this.onFieldItemClickNew('Version', value, label, this.issueDetail.view_data.version_name)
      }
    },


    loadMyTeams: function () {
      this.$axios.get('/api/common/teams/my').then(response => {
        this.projectTeams = response.data.result
      }, response => {
      })
    },


    loadIssueReslovedResult: function () {
      this.$axios.get('/api/project/issue/resolve_results').then(response => {
        this.issueResolvedResult = response.data.result
      }, response => {
      })
    },

    loadIssueProjectModules: function (projectID) {
      if (projectID) {
        this.$axios.get('/api/project/' + projectID + '/project_modules').then(response => {
          this.projectModules = response.data.result
        }, response => {
        })
      }
    },


    loadIssueOS: function () {
      this.$axios.get('/api/project/issue/os').then(response => {
        this.DeviceOS = response.data.result
      }, response => {
      })
    },

    getMyProjectMembers(projecID) {
      loadProjectMembers(projecID).then(response => {
        this.projectMembers = response.data.result
      })
    },

    loadIssueActivities: function (issueID) {
      this.$axios.get('/api/project/issue/' + issueID + '/activities').then(response => {
        this.issueActivities = response.data.result
      }, response => {
      })
    },

    loadProjectRequirements: function (projectID) {
      this.$axios.get(' /api/project/' + projectID + '/requirement?ordering=-CreationTime&page_size=20&Status=4,5').then(response => {
        this.projectRequirements = response.data.result.results
        this.projectRequirements.forEach(item => {
          item.label = item.Title
        })
      })
    },

    loadProjectVersions(projectID) {
      this.$axios.get('/api/project/' + projectID + '/versions').then(response => {
        this.projectVersions = response.data.result.all_versions
      }, response => {
      })
    },

    onProcessorChange: function (value) {
      this.issueComments.OldValue = ''
      this.issueDetail['Processor'] = parseInt(value.value)
      this.issueComments.Message = '属性'
      this.issueComments.Issue = this.issueID
      this.issueComments.Creator = this.userInfo.id
      this.issueComments.NewValue = value.label
      this.issueComments.FieldName = 'Processor'
      this.issueComments.ActionFlag = 2
      this.issueComments.ActionType = 1
      this.updateIssueProperty('Processor', parseInt(value.value))
    },

    onCreatorChange: function (value) {
      this.issueComments.OldValue = ''
      this.issueDetail['Creator'] = parseInt(value.value)
      this.issueComments.Message = '属性'
      this.issueComments.Issue = this.issueID
      this.issueComments.Creator = this.userInfo.id
      this.issueComments.NewValue = value.label
      this.issueComments.FieldName = 'Creator'
      this.issueComments.ActionFlag = 2
      this.issueComments.ActionType = 1
      this.updateIssueProperty('Creator', parseInt(value.value))

    },

    onFieldItemClick: function (name) {
      let fieldItem = name.split(',')
      if (fieldItem.length === 3) {
        this.issueComments.OldValue = this.$refs[fieldItem[2]].innerHTML
        this.issueDetail[fieldItem[2]] = parseInt(fieldItem[0])
        this.issueComments.Message = '属性'
        this.issueComments.Issue = this.issueID
        this.issueComments.Creator = this.userInfo.id
        this.issueComments.NewValue = fieldItem[1]
        this.issueComments.FieldName = fieldItem[2]
        this.issueComments.ActionFlag = 2
        this.issueComments.ActionType = 1
        this.updateIssueProperty(fieldItem[2], parseInt(fieldItem[0]))
      }
    },

    onFieldItemClickNew: function (fieldName, value, newLabel, oldLabel) {
      this.issueComments.FieldName = fieldName
      this.issueComments.OldValue = oldLabel
      this.issueComments.NewValue = newLabel
      this.issueComments.Message = '属性'
      this.issueComments.Issue = this.issueID
      this.issueComments.Creator = this.userInfo.id
      this.issueComments.ActionFlag = 2
      this.issueComments.ActionType = 1
      this.updateIssueProperty(fieldName, value)
    },

    updateIssueProperty: function (fieldName, value) {
      let field = {}
      field[fieldName] = value
      this.$axios.patch('/api/project/issue/' + this.issueID, field).then(response => {
        this.issueDetail = response.data.result
        this.$axios.post('/api/project/issue/' + this.issueID + '/activities', this.issueComments).then(response => {
          this.$Message.success({
            content: '问题成功更新。',
            duration: 3,
            closable: true
          })
          this.setIssueChange(true)
          this.loadIssueActivities(this.issueID)
        }, response => {
        })
      }, response => {
      })
    },
    updateIssueRequirement: function (newValue, oldValue, id) {
      let reqirement = {
        Requirement: newValue
      }
      this.$axios.patch('/api/project/issue/' + this.issueID, reqirement).then(response => {
        this.issueDetail = response.data.result
        this.$Message.success({
          content: '关联需求更新成功。',
          duration: 3,
          closable: true
        })

      })
    },

    onAddComments: function (value) {
      if (value.trim() !== '') {
        this.issueComments.Message = value
        this.issueComments.Issue = this.issueID
        this.issueComments.Creator = this.userInfo.id
        this.issueComments.NewValue = ''
        this.issueComments.OldValue = ''
        this.issueComments.FieldName = ''
        this.issueComments.ActionType = 2
        this.issueComments.ActionFlag = 1
        this.$axios.post('/api/project/issue/' + this.issueID + '/activities', this.issueComments).then(response => {
          this.loadIssueActivities(this.issueID)
        }, response => {
        })
      }
    },

    onViewAttachment: function (fileName, fileID, FileSuffixes) {
      this.viewAttachment.showDialog = true
      this.viewAttachment.fileName = fileName
      this.viewAttachment.fileID = fileID
      if (FileSuffixes === 'jpg' || FileSuffixes === 'jpeg' || FileSuffixes === 'png') {
        this.viewAttachment.isPicture = true
      } else if (FileSuffixes === 'mp4' || FileSuffixes === 'flv') {
        this.viewAttachment.isVideo = true
      } else {
        this.viewAttachment.isPicture = false
      }
    }
  },
  created() {
    this.loadIssueDetail(this.issueID)
    this.loadMyTeams()
    this.loadIssueOS()
    this.loadIssueReslovedResult()
    this.loadProjectRequirements(this.projectID)
    this.getMyProjectMembers(this.projectID)
    this.loadProjectVersions(this.projectID)
  },
  watch: {
    issueID: function (value) {
      this.loadIssueDetail(value)
      this.loadIssueActivities(value)
    }
  },
  components: {
    labelEditorInput,
    labelEditorSelect,
    labelEditorVueEditor,
    VueEditor,
    VideoPlayer,
    LabelSelect
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import './ProjectIssueStatus';

.issue-detail {
  font-size: 14px;
  color: #5578aa;
}

.issue-title {
  padding: 10px 16px 10px 16px;
}

.issue-detail-body {}

.issue-status {
  display: flex;
  justify-content: space-between;
}

.issue-detail-summary {
  padding: 10px 16px 10px 16px;
}

.issue-detail-editor {
  display: flex;
}



.issue-detail-attachment {
  padding: 16px;
}

.issue-detail-comments {
  padding: 16px;
  min-height: 300px;
  color: #aabcd4;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.issue-comments-input {
  //position: fixed;
  // bottom: 0px;
  //height: 40px;
  //width: 60%;
}

.issue-detail-id {
  display: inline-flex;
  font-weight: 400;
  color: #5578aa;
  font-size: 20px;
}

.issue-default-title {
  display: inline-flex;
  font-weight: 400;
  color: #444;
  font-size: 18px;
}

.issue-detail-title {
  display: inline-block;
  font-weight: 400;
  color: #5578aa;
  font-size: 20px;
  width: 100%;
  padding-left: 10px;
}

.issue-detail-creation {
  font-size: 12px;
  color: #5578aa;
}

.issue-detail-creator {
  display: inline-block;
  padding: 0px 0px 0px 5px;
}

.issue-detail-createdate {
  display: inline-block;
  padding: 0px 0px 5px 5px;
}

.issue-detail-option {
  flex: 1;
  background: #fbfbfe;
  padding: 12px;
  font-size: 15px;
  font-weight: 400;
}

.issue-detail-option-content {
  vertical-align: middle;
  color: #3b73af;
  height: 36px;
}

.issue-detail-option-field-name {
  font-size: 14px;
  color: #5578aa;
  padding: 0px 0px 0px 0px;
}

.issue-detail-desc {
  flex: 4;
  padding: 16px;
  max-width: 80%;
}

.issue-detail-desc-title {
  margin-bottom: 2px;
  color: #5578aa;
  padding: 0px 5px 5px 0px;
  font-size: 14px;
}

.issue-detail-desc-content-veiw {
  outline: none;
  border: none;
  overflow: scroll;
  // max-height: 350px;
  min-height: 120px;
  margin-bottom: 2px;
  color: #5578aa;
  padding: 10px 16px 10px 16px;
}

.issue-detail-desc-content-veiw img {
  width: 100px;
  height: auto;
}

.issue-detail-field-content {
  padding-bottom: 2px;
  color: #5578aa;
  padding: 0px 0px 0px 0px;
  font-size: 14px;
}

.issue-detail-body a {
  color: #3b73af;
}

.attachment-item {
  padding: 5px 10px 5px 15px;
  font-size: 14px;
  color: #5578aa;
}

.attachment-view {
  width: 100% !important;
  height: 100%;
  position: absolute;
}
</style>
