<template>
  <Card :shadow="false" :padding="0" :bordered="false" dis-hover
    style="border: 1px solid #e4e5e7;border-top: 1px solid #e4e5e7;color:#5578aa;">
    <div style="height: 40px;border-bottom: 1px solid #e4e5e7;padding:10px;padding-top: 10px;">
      <span class="pull-right">
        <!--<ButtonGroup >-->
        <!--<Button icon="ios-trash ">-->
        <!--清除-->
        <!--</Button>-->
        <!--</ButtonGroup>-->
      </span>
    </div>
    <div :style="'overflow-y:auto;height:' + containerHeight + 'px; padding-right:4px'">
      <div class="filter-block" v-if="this.$route.name == 'homeIssue'">
        <div class="filter-block-title">
          <p><i class="fa fa-file fw"></i> 项目</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.projects" filterable multiple @on-change="onProjectChange">
            <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-road fa-fw"></i> 版本</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.versions" filterable multiple
            :disabled="filterPanel.projects.length == 0 || filterPanel.projects.length > 1"
            @on-change="onFiltersChange">
            <Option v-for="item in projectVersions" :value="item.id" :key="item.id">{{ item.VVersion }} </Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-eye"></i> 状态</p>
        </div>
        <div class="filter-block-body">
          <Row>
            <CheckboxGroup v-model="filterPanel.status" @on-change="onFiltersChange">
              <div style="flex-direction: row;display: flex;flex-wrap: wrap;">
                <Col v-for="item in issueStatusList" :key="item.id" :lg="8" :sm="12">
                <Checkbox :label="item.Value">
                  <i :class="'fa ' + item.Label + ' ' + item.LabelStyle"></i>
                  <span>{{ item.Name }}</span>
                </Checkbox>
                </Col>
              </div>
            </CheckboxGroup>
          </Row>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-shield fa-fw"></i> 严重性</p>
        </div>
        <div class="filter-block-body">
          <Row>
            <CheckboxGroup v-model="filterPanel.severity" @on-change="onFiltersChange">
              <div style="flex-direction: row;display: flex;flex-wrap: wrap;">
                <Col v-for="item in issueSeverityList" :lg="8" :sm="12" :key="item.id">
                <Checkbox :label="item.Value">
                  <i :class="'fa ' + item.Label + ' ' + item.LabelStyle"></i>
                  <span>{{ item.Name }}</span>
                </Checkbox>
                </Col>
              </div>
            </CheckboxGroup>
          </Row>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-group fa-fw"></i> 优先级</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.issuePriority" filterable multiple @on-change="onFiltersChange">
            <Option v-for="item in issuePriorityList" :value="item.Value" :key="item.Value">{{ item.Name }}</Option>
          </Select>
        </div>
      </div>
      <!-- <div v-if="$route.name !== 'homeIssue'" class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-group fa-fw"></i> 团队</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.teams" filterable multiple @on-change="onFiltersChange">
            <Option v-for="item in projectTeams" :value="item.id" :key="item.id">{{ item.Name }}</Option>
          </Select>
        </div>
      </div> -->
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-hand-o-right fa-fw"></i> 经办人</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.processors" filterable multiple :disabled="filterPanel.projects.length > 1"
            @on-change="onFiltersChange">
            <Option v-for="item in memberList" :value="item.PMMember" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-user fa-fw"></i> 报告人</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.reportors" filterable multiple :disabled="filterPanel.projects.length > 1"
            @on-change="onFiltersChange">
            <Option v-for="item in memberList" :value="item.PMMember" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-braille fa-fw"></i> 解决结果</p>
        </div>
        <div class="filter-block-body">
          <Row>
            <CheckboxGroup v-model="filterPanel.resolveResult" @on-change="onFiltersChange">
              <div style="flex-direction: row;display: flex;flex-wrap: wrap;">
                <Col v-for="item in issueResolvedResult" :lg="8" :sm="12" :key="item.id">
                <Checkbox :label="item.Value">
                  <i :class="'fa ' + item.Label + ' ' + item.LabelStyle"></i>
                  <span>{{ item.Name }}</span>
                </Checkbox>
                </Col>
              </div>
            </CheckboxGroup>
          </Row>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-calendar-o fa-fw"></i> 创建时间</p>
        </div>
        <div class="filter-block-body">
          <DatePicker v-model="filterPanel.createTimeRange" format="yyyy-MM-dd" type="daterange" show-week-numbers
            placement="bottom-end" transfer placeholder="请选择创建时间范围" @on-change="handleDatePickerChange"
            style="width: 100%;">
          </DatePicker>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-group fa-fw"></i> 项目阶段</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.projectPhrases" filterable multiple @on-change="onFiltersChange">
            <Option v-for="item in issuePhraseList" :value="item.Value" :key="item.Value">{{ item.Name }}</Option>
          </Select>
        </div>
      </div>
      <div class="filter-block">
        <div class="filter-block-title">
          <p><i class="fa fa-group fa-fw"></i> 问题分类</p>
        </div>
        <div class="filter-block-body">
          <Select v-model="filterPanel.issueCategories" filterable multiple @on-change="onFiltersChange">
            <Option v-for="item in issueCategoriesList" :value="item.Value" :key="item.Value">{{ item.Name }}</Option>
          </Select>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { loadProjectVersions, loadProjectMembers } from '../business-service/ProjectApiService'
import { getSpaceUserListApi } from '../../../api/user.js'

export default {
  name: 'projectIssueFilter',
  props: {
    projectID: {
      type: [Number, String],
      default: 0,
    }
  },
  data() {
    return {
      projectVersions: [],
      memberList: [],
      projectTeams: [],
      issueResolvedResult: [],
      filterPanel: {
        projects: [],
        versions: [],
        teams: [],
        status: [2, 4, 5],
        processors: [],
        reportors: [],
        createTimeRange: [],
        resolveResult: [],
        severity: [],
        projectPhrases: [],
        issueCategories: [],
        issuePriority: []
      }
    }
  },
  computed: {
    ...mapGetters('task', ['taskChange']),
    ...mapGetters('projectglobal', ['projectVersion']),
    ...mapState(['appBodyHeight', 'appBodyMainHeight', 'spaceUserList']),
    ...mapState('project', ['projectList']),
    ...mapState('usercenter', ['userInfo', 'defSpace']),
    ...mapState('issue', ['issueStatusList', 'issueSeverityList', 'issuePhraseList', 'issueCategoriesList', 'issuePriorityList']),

    containerHeight: function () {
      return this.appBodyHeight - 46
    },
  },
  methods: {
    ...mapMutations('issue', ['setIssueFilters']),
    ...mapMutations('projectglobal', ['setViewDialogShow']),

    handleDatePickerChange: function (value) {
      this.filterPanel.createTimeRange[0] = value[0]
      this.filterPanel.createTimeRange[1] = value[1]
      this.setFilters(value)
    },

    onFiltersChange: function (value) {
      this.setFilters(value)
    },

    setFilters: function (value) {
      let filters = ''
      // 生成项目ID过滤
      if (this.filterPanel.projects.length > 0) {
        filters = filters + 'Project__in=' + this.filterPanel.projects + '&'
      }

      // 生成版本过滤
      if (this.filterPanel.versions.length > 0) {
        filters = filters + 'Version__in=' + this.filterPanel.versions + '&'
      }

      // 生成状态过滤
      if (this.filterPanel.status.length > 0) {
        filters = filters + 'Status__in=' + this.filterPanel.status + '&'
      }
      // 生成严重性过滤
      if (this.filterPanel.severity.length > 0) {
        filters = filters + 'Severity__in=' + this.filterPanel.severity + '&'
      }

      // 生成优先级过滤
      if (this.filterPanel.issuePriority.length > 0) {
        filters = filters + 'Priority__in=' + this.filterPanel.issuePriority + '&'
      }

      // 暂时不用 team
      // // 生成团队过滤
      // if (this.filterPanel.teams.length > 0) {
      //   filters = filters + 'Team__in=' + this.filterPanel.teams + ',0&'
      // }

      // 生成经办人过滤
      if (this.filterPanel.processors.length > 0) {
        // console.log(this.filterPanel.processors)
        filters = filters + 'Processor__in=' + this.filterPanel.processors + '&'
      }
      // 生成报告人过滤
      if (this.filterPanel.reportors.length > 0) {
        filters = filters + 'Creator__in=' + this.filterPanel.reportors + '&'
      }
      // 生成创建时间过滤
      if (this.filterPanel.createTimeRange.length > 1) {
        if (this.filterPanel.createTimeRange[0] !== '') {
          filters = filters + 'CreationTime_after=' + this.filterPanel.createTimeRange[0] + '&' + 'CreationTime_before=' + this.filterPanel.createTimeRange[1] + '&'
        }
      }
      // 生成项目阶段过滤
      if (this.filterPanel.projectPhrases.length > 0) {
        filters = filters + 'ProjectPhase__in=' + this.filterPanel.projectPhrases + ',0&'
      }
      // 生成问题分类过滤
      if (this.filterPanel.issueCategories.length > 0) {
        filters = filters + 'IssueCategory__in=' + this.filterPanel.issueCategories + ',0&'
      }
      // 生成解决结果过滤
      if (this.filterPanel.resolveResult.length > 0) {
        filters = filters + 'Solution__in=' + this.filterPanel.resolveResult + ',0&'
      }
      this.setIssueFilters(filters)
    },

    initFilterPanel: function () {

      if (parseInt(this.projectID) !== 0) {
        this.filterPanel.projects = []
        this.filterPanel.projects.push(parseInt(this.projectID))
        // console.log('this.filterPanel.projects',this.filterPanel.projects)
      }

      if (this.$route.name === 'homeIssue') {
        // this.filterPanel.processors = []
        // this.filterPanel.processors.push(parseInt(this.userInfo.id))
        // let currentUser = {}
        // currentUser['PMMember'] = parseInt(this.userInfo.id)
        // currentUser['id'] = parseInt(this.userInfo.id)
        // currentUser['name'] = this.userInfo.last_name + this.userInfo.first_name
        // this.memberList.push(currentUser)
        // this.filterPanel.processors = [this.userInfo.id]
        this.setFilters()
      }
    },

    loadMyTeams: function () {
      this.$axios.get('/api/common/teams/my').then(response => {
        this.projectTeams = response.data.result
      }, response => {
      })
    },

    loadIssueReslovedResult: function () {
      this.$axios.get('/api/project/issue/resolve_results').then(response => {
        this.issueResolvedResult = response.data.result
      }, response => {
      })
    },

    getMyProjectMembers(projecID) {
      loadProjectMembers(projecID).then(response => {
        this.memberList = response.data.result
      })
    },

    getProjectVersions(projectID) {
      loadProjectVersions(projectID).then(response => {
        this.projectVersions = response.data.result.all_versions
      })
    },

    onProjectChange: function (value) {
      this.initFilterPanel()
      this.setFilters(value)
      if (value.length === 0 || value.length === this.projectList.length) {
        this.filterPanel.versions = []
        this.filterPanel.processors = []
        this.filterPanel.reportors = []
        this.loadSpaceMemberList()
      }
      if (value.length === 1) {
        this.getProjectVersions(value)
        this.getMyProjectMembers(value)
        for (let i = 0; i < this.projectList.length; i++) {
          if (this.projectList[i].id === parseInt(value[0])) {
            this.projectVersions = this.projectList[i].Versions
            this.memberList = this.projectList[i].Members
          }
        }
      }
    },

    loadSpaceMemberList() {
      getSpaceUserListApi().then(response => {
        this.memberList = response.data.result
        this.memberList.forEach(element => {
          element.PMMember = element.id
        });
      })
    },

  },

  created: function () {
    this.loadMyTeams()
    this.loadIssueReslovedResult()
  },

  mounted: function () {
    if (this.$route.name !== 'homeIssue') {
      this.onProjectChange([this.projectID])
    } else {
      this.loadSpaceMemberList()
    }
  },

  watch: {
  },

  components: {}
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import 'ProjectIssueStatus';

.filter-block {
  margin-top: 15px;
  padding-left: 15px;
}

.filter-block-title {
  padding: 0px;
}

.filter-block-body {
  padding: 10px;
}
</style>
