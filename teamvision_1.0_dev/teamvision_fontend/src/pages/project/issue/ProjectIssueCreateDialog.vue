<template>
  <Modal :value="dialogShow" title="添加新问题" :mask-closable="false" :width="800" @on-cancel="cancel"
    :styles="{ bottom: '20px', top: '50px' }">
    <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'" v-if="dialogShow">
      <Form ref="createIssue" :model="formItem" :rules="ruleCustom" label-position="top">
        <div style="display: flex;">
          <div style="flex: 4; padding: 10px; margin-right: 10px;">
            <FormItem label="标题" prop="Title">
              <Input v-model="formItem.Title" placeholder="问题概述" />
            </FormItem>
            <FormItem label="问题描述" prop="Desc">
              <vue-editor v-model="formItem.Desc" :editorToolbar="editorToolBar" useCustomImageHandler
                @imageAdded="handleImageAdded" placeholder="问题描述">
              </vue-editor>
            </FormItem>
            <FormItem label="附件">
              <Upload ref="upload" multiple type="drag" paste action="/api/project/issue/attachments"
                :on-success="handleSuccess" :on-remove="handleRemove" :format="[]" :max-size="10240"
                :default-file-list="formItem.defaultList" :on-format-error="handleFormatError"
                :on-exceeded-size="handleMaxSize">
                <div style="padding: 8px 0">
                  <Icon type="ios-cloud-upload" size="36" style="color: #3399ff"></Icon>
                  <p>点击，拖拽，粘贴上传附件</p>
                </div>
              </Upload>
            </FormItem>
          </div>
          <div style="flex: 1; padding: 10px; margin-left: 10px;background: #fbfbfe;">
            <FormItem label="项目" prop="Project" v-if="routerName == 'homeIssue'">
              <Select v-model="formItem.Project">
                <Option v-for="item in projectList" :value="item.id" :key="item.id">{{ item.PBTitle }}</Option>
              </Select>
            </FormItem>
            <FormItem label="经办人" prop="Processor">
              <Select v-model="formItem.Processor" :filterable="true" placeholder="默认创建者">
                <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{
                  member.name }} </Option>
              </Select>
            </FormItem>
            <FormItem label="关联需求" prop="Requirement">
              <Select v-model="formItem.Requirement" :filterable="true" placeholder="关联需求" transfer
                :disabled="projectRequirements.length == 0">
                <Option v-for="requirement in projectRequirements" :key="requirement.id" :value="requirement.id">{{
                  requirement.id }}-{{ requirement.Title }}</Option>
              </Select>
            </FormItem>
            <FormItem label="版本">
              <Select v-model="formItem.Version" transfer :disabled="projectVersions.length == 0">
                <Option v-for="item in projectVersions" :value="item.id" :key="item.id">{{ item.VVersion }}</Option>
              </Select>
            </FormItem>
            <!-- <FormItem label="模块" prop="Module">
              <Select v-model="formItem.Module" :filterable="true" placeholder="问题所属模块">
                <Option v-for="pmodule in projectModules" :key="pmodule.id" :value="pmodule.id">{{ pmodule.Name }}
                </Option>
              </Select>
            </FormItem> -->
            <FormItem label="严重性" prop="Severity">
              <Select v-model="formItem.Severity" :filterable="true" placeholder="问题严重性" transfer>
                <Option v-for="severity in issueSeverityList" :key="severity.Value" :value="severity.Value">{{
                  severity.Name }} </Option>
              </Select>
            </FormItem>
            <FormItem label="优先级" prop="Priority">
              <Select v-model="formItem.Priority" :filterable="true" placeholder="问题优先级" transfer>
                <Option v-for="priority in issuePriorityList" :key="priority.Value" :value="priority.Value">{{
                  priority.Name }} </Option>
              </Select>
            </FormItem>
            <FormItem label="阶段" prop="ProjectPhase">
              <Select v-model="formItem.ProjectPhase" :filterable="true" placeholder="问题发现阶段" transfer>
                <Option v-for="phrase in issuePhraseList" :key="phrase.Value" :value="phrase.Value">{{ phrase.Name
                  }} </Option>
              </Select>
            </FormItem>
            <FormItem label="分类" prop="IssueCategory">
              <Select v-model="formItem.IssueCategory" :filterable="true" placeholder="请选择分类" transfer>
                <Option v-for="category in issueCategoriesList" :key="category.Value" :value="category.Value">{{
                  category.Name }} </Option>
              </Select>
            </FormItem>
            <FormItem label="发现方式" prop="discover_way">
              <Select v-model="formItem.discover_way" :filterable="true" placeholder="发现方式" transfer>
                <Option v-for="item in issue_discover_way" :key="item.value" :value="item.value">{{ item.name
                  }}</Option>
              </Select>
            </FormItem>

            <!-- <FormItem label="项目角色" prop="Team">
            <Select  v-model="formItem.Team" :filterable="true" placeholder="选择项目角色">
              <Option v-for="team in projectTeams" :key="team.id" :value="team.id">{{ team.Name }}
              </Option>
            </Select>
          </FormItem> 
          <FormItem label="系统" prop="DeviceOS">
            <Select  v-model="formItem.DeviceOS" :filterable="true" placeholder="问题系统">
              <Option v-for="os in DeviceOS" :key="os.Value" :value="os.Value">{{ os.Name }}
              </Option>
            </Select>
          </FormItem> -->
          </div>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <Button v-if="createDialogShow" type="success" style="width: 60px; height:30px;" shape="circle"
        @click="ok('createIssue')">添加
      </Button>
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import { VueEditor } from 'vue2-editor'
import { initProjectVersions, issueValidateRules } from './ProjectIssueCreateDialog'
import { loadProjectVersions, loadProjectMembers } from '../business-service/ProjectApiService'

export default {
  name: 'ProjectIssueCreateDialog',
  props: {
    projectID: {
      type: [Number, String],
      default: 0
    },
    versionID: {
      type: [Number, String],
      default: 0
    }
  },

  data() {
    return {
      sourceProject: [],
      projectTeams: [],
      projectModules: [],
      projectVersions: [],
      projectRequirements: [],
      projectMembers: [],
      issueResolvedResult: [],
      DeviceOS: [],
      IssuePriority: [],
      editorToolBar: [
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'color': [] }, { 'background': [] }],
        ['image']
      ],
      formItem: {
        Title: '',
        Team: 0,
        Processor: 0,
        Severity: 0,
        Module: 0,
        Desc: '<p>步骤:</p></br><p>实际结果:</p></br><p>期望结果:</p>',
        Project: 0,
        Requirement: 0,
        Version: 0,
        ProjectPhase: 0,
        IssueCategory: 0,
        DeviceOS: 0,
        Priority: 0,
        uploadList: [],
        defaultList: []
      },
      ruleCustom: {
        ...issueValidateRules
      },
      issue_discover_way: [
        {
          value: 1,
          name: "手工"
        }, {
          value: 2,
          name: "自动化",
        }, {
          value: 3,
          name: "流量回放",
        }, {
          value: 4,
          name: "告警",
        }, {
          value: 6,
          name: "混沌工程",
        }, {
          value: 5,
          name: "其他",
        },
      ]
    }

  },
  computed: {
    ...mapGetters('projectglobal', ['viewDialogShow',]),
    ...mapState(['appBodyHeight']),
    ...mapState('projectglobal', ['createDialogShow']),
    ...mapState('project', ['projectList', 'projectsVersions']),
    ...mapState('issue', ['issueSeverityList', 'issuePhraseList', 'issueCategoriesList', 'issuePriorityList']),

    containerHeight: function () {
      return this.appBodyHeight - 100
    },

    project: function () {
      return parseInt(this.projectID)
    },

    dialogShow: function () {
      return (this.createDialogShow || this.viewDialogShow)
    },

    routerName: function () {
      return this.$route.name;
    },

  },
  methods: {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setViewDialogShow', 'setTaskChange']),
    ...mapMutations('issue', ['setIssueChange']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.post('/api/project/issue', this.formItem).then(response => {
            this.setIssueChange(true)
            this.formItem.Title = ' '
            this.formItem.Desc = '<p>步骤:</p></br><p>实际结果:</p></br><p>期望结果:</p>'
            this.formItem.uploadList = []
            this.setCreateDialogShow(false)
          }, response => {
          })

          this.$refs[name].resetField()
        }
      })
    },

    handleImageAdded: function (file, Editor, cursorLocation, resetUploader) {
      // An example of using FormData
      // NOTE: Your key could be different such as:
      // formData.append('file', file)

      if (file.size / 1024 / 1024 > 10) {
        this.$Message.error({
          content: '附件文件不能大于10M',
          duration: 10,
          closable: true
        })
        return
      }

      if (/^image/.test(file.type)) {
        //创建一个reader
        var that = this
        let reader = new FileReader()
        //将图片转成base64格式
        reader.readAsDataURL(file)
        //读取成功后的回调
        reader.onloadend = function () {
          let result = this.result
          let img = new Image()
          img.src = result
          //console.log('********未压缩前的图片大小********')
          //console.log(result.length / 1024)
          img.onload = function () {
            if (result.length / 1024 / 1024 < 1) {
              let data = that.compressImage(img, 0.8)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }

            else if (result.length / 1024 / 1024 > 1 && result.length / 1024 / 1024 < 3) {
              let data = that.compressImage(img, 0.5)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }
            else if (result.length / 1024 / 1024 > 3 && result.length / 1024 / 1024 < 6) {
              let data = that.compressImage(img, 0.3)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }
            else if (result.length / 1024 / 1024 > 6) {
              let data = that.compressImage(img, 0.2)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }

          }
        }
      }

      let formData = new FormData()

      //      formData.append("file", file)
      //      this.$axios({
      //        url: '/api/project/issue/attachments',
      //        method: "POST",
      //        data: formData
      //      })
      //        .then(result => {
      //          console.log(result)
      //          let url = result.data.result.url; // Get url from response
      //          Editor.insertEmbed(cursorLocation, 'image', url);
      //          resetUploader();
      //        })
      //        .catch(err => {
      //          console.log(err);
      //        });
    },

    compressImage(img, size) {
      let canvas = document.createElement('canvas')
      let ctx = canvas.getContext('2d')
      let initSize = img.src.length
      let width = img.width
      let height = img.height

      if (width > 600) {
        width = 600
      }

      if (height > 800) {
        height = 600
      }

      canvas.width = width
      canvas.height = height
      // 铺底色
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0, width, height)
      //进行最小压缩
      let ndata = canvas.toDataURL('image/jpeg', size)
      //('*******压缩后的图片大小*******')
      // console.log(ndata)
      //console.log(ndata.length / 1024)
      return ndata
    },

    cancel() {
      this.setCreateDialogShow(false)
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.formItem.uploadList.push(file.id)
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过10M限制',
        duration: 10,
        closable: true
      })
    },

    onProjectChange(value, selectedData) {
      let project = value[0]
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === project) {
          this.projectMember = this.sourceProject[i].Members
          this.loadIssueProjectModules(project)
        }
      }
    },

    loadProjectVersions(projectID) {
      this.$axios.get('/api/project/' + projectID + '/versions').then(response => {
        this.projectVersions = response.data.result.all_versions
      }, response => {
      })
    },


    loadMyTeams: function () {
      this.$axios.get('/api/common/teams/my').then(response => {
        this.projectTeams = response.data.result
      }, response => {
      })
    },

    loadIssueProjectModules: function (projectID) {
      this.$axios.get('/api/project/' + projectID + '/project_modules').then(response => {
        this.projectModules = response.data.result
      }, response => {
      })
    },

    loadIssueOS: function () {
      this.$axios.get('/api/project/issue/os').then(response => {
        this.DeviceOS = response.data.result
      }, response => {
      })
    },

    loadProjectRequirements: function (projectID) {
      this.$axios.get('/api/project/' + projectID + '/requirement?ordering=-CreationTime&page_size=20&Status=4,5').then(response => {
        this.projectRequirements = response.data.result.results
      })
    },

    getMyProjectMembers(projectID) {
      loadProjectMembers(projectID).then(response => {
        this.projectMembers = response.data.result
      })
    },

    loadMyProject: function () {
      this.sourceProject = this.projectList
      this.projectVersions = initProjectVersions(this.projectList)
      for (let i = 0; i < this.sourceProject.length; i++) {
        if (this.sourceProject[i].id === this.project) {
          this.formItem.Project[1] = this.sourceProject[i].LatestVersion
          break;
        }
      }
    }
  },

  created() {
    this.formItem.Project = this.project
    this.loadMyTeams()
    this.loadIssueOS()

    if (this.$route.name !== 'homeIssue') {
      this.getMyProjectMembers(this.project)
      this.loadProjectVersions(this.project)
      this.loadProjectRequirements(this.project)
      this.loadIssueProjectModules(this.project)
    }
  },
  mounted() {
  },
  watch: {
    'formItem.Project'(newVal, oldVal) {
      // console.log("newVal=", newVal, oldVal)
      if (newVal != oldVal) {
        this.loadProjectVersions(newVal)
        this.loadProjectRequirements(newVal)
        this.getMyProjectMembers(newVal)
      }
    },
  },
  components: {
    VueEditor,
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.demo-upload-list {
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.ivu-form-item {
  margin-bottom: 16px;
}
</style>
