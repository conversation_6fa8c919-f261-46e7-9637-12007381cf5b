<template>
  <div>
    <Card :padding="0" :bordered="false" :shadow="false" dis-hover
      style="border: 1px solid #e4e5e7;border-top: 1px solid #e4e5e7;">
      <div style="height: 40px; border-bottom: 1px solid #e4e5e7; padding-left: 20px;padding-top:10px;color:#5578aa">
        <span>
          <Input v-model="issueSearchKey" search placeholder="输入关键字，回车搜索" @on-search="onSearchIssue"
            style="width:300px ;padding-right: 20px;" />
        </span>
        <span style="padding-right: 10px;">{{ issueListObject.count }} 个相关问题</span>
        <span style="cursor: pointer; ">
          <Button :loading="reloadReq" shape="circle" icon="md-refresh" @click="onRefreshIssue"></Button>
        </span>
      </div>
      <div :style="'overflow-y:auto;height:' + containerHeight + 'px;'">
        <div style="padding: 5px 10px 5px 0px;min-height: 600px;" v-loading="showLoading">
          <!-- 注释掉下滑加载，改为分页 
         <Scroll :on-reach-bottom="handleReachBottom" loading-text="拼命加载中" :height="containerHeight">
          <project-issue-item v-for="issue in issueListObject.issueData" :key="issue.id" :issue="issue"
            @view-issue="onIssueItemClick" @view-next-issue="onIssueItemKeyDown"></project-issue-item>
        </Scroll> -->
          <project-issue-item v-for="issue in issueListObject.issueData" :key="issue.id" :issue="issue"
            @view-issue="onIssueItemClick" @view-next-issue="onIssueItemKeyDown">
          </project-issue-item>
        </div>
        <div style="margin: 10px;overflow: hidden">
          <div style="float: right; padding-right: 80px;">
            <Page :total="page.total" :page-size="page.pageSize" :current="page.current" @on-change="changePage" />
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import ProjectIssueItem from './ProjectIssueItem.vue'

export default {
  name: 'projectIssueList',
  props: {
    projectID: {
      type: [Number, String],
      default: 0,
    },
    issueID: {
      type: [Number, String],
    }
  },
  data() {
    return {
      columnItemHeight: 200,
      issueListObject: {
        next: null,
        issueData: [],
        count: 0
      },
      issueSearchKey: '',
      showLoading: false,
      reloadReq: false,
      page: {
        current: 1,
        total: 1,
        pageSize: 10,
      },
    }
  },
  computed: {
    ...mapGetters('issue', ['issueChange', 'issueFilters', 'searchKeyword']),
    ...mapGetters('projectglobal', ['projectVersion']),
    ...mapState(['appBodyHeight', 'appBodyMainHeight']),
    containerHeight: function () {
      return this.appBodyHeight - 46
    },
  },
  methods:
  {
    ...mapMutations('issue', ['setIssueChange', 'setShowIssueDetail', 'setSelectIssueID', 'setSearchKeyword']),
    ...mapMutations('projectglobal', ['setViewDialogShow']),

    onIssueItemClick: function (issueID) {
      this.setShowIssueDetail(true)
      this.setSelectIssueID(issueID)
    },

    onIssueItemKeyDown: function (issueID) {
      for (let i = 0; i < this.issueListObject.issueData.length; i++) {
        if (this.issueListObject.issueData[i].id + '' === issueID) {
          if (i + 1 < this.issueListObject.issueData.length)
            this.setShowIssueDetail(true)
          this.setSelectIssueID(this.issueListObject.issueData[i + 1].id)
        }
      }
    },

    onRefreshIssue: function () {
      this.loadIssueList(this.projectID, 0)
    },

    onSearchIssue: function (value) {
      this.setSearchKeyword(value)
      this.loadIssueList(this.projectID, 0)
    },

    handleReachBottom: function () {
      if (this.issueListObject.next != null) {
        return new Promise(resolve => {
          setTimeout(() => {
            this.$axios.get(this.issueListObject.next).then(response => {
              this.issueListObject.issueData.push(...response.data.result.results)
              this.issueListObject.next = response.data.result.next
              this.issueListObject.count = response.data.result.count
            }, response => {
            })
            resolve();
          }, 500)
        })
      } else {
        this.$Message.warning({
          content: '没有更多问题了',
          duration: 3,
          closable: true
        }
        )
      }
      this.showLoading = false
    },

    loadIssueList: function (projectID, projectVersion) {
      let searchFilter = ''
      if (this.issueSearchKey.trim() !== '') {
        searchFilter = 'search=' + this.issueSearchKey + '&'
      }
      this.showLoading = true
      this.$axios.get('/api/project/issue/list?' + searchFilter + this.issueFilters + 'page=' + this.page.current).then(response => {
        this.showLoading = false
        this.issueListObject.issueData = []
        this.issueListObject.issueData.push(...response.data.result.results)
        this.issueListObject.next = response.data.result.next
        this.issueListObject.count = response.data.result.count
        this.page.pageSize = response.data.result.page_size
        this.page.total = response.data.result.count
      }, response => {
        this.showLoading = false
      })
    },

    updateIssueList: function (issue) {
      for (let i = 0; i < this.issueListObject.issueData.length; i++) {
        if (this.issueListObject.issueData[i].id + '' === issue.id) {
          this.issueListObject.issueData[i] = issue
        }
      }
    },

    changePage: function (value) {
      this.page.current = value
      this.loadIssueList(this.projectID, 0)
    }
  },

  created: function () {
    this.loadIssueList(this.projectID, 0)
  },

  mounted: function () {
    if (this.$route.params.fortestingId != undefined) {
      this.onViewFortesting(this.$route.params.fortestingId)
    }
  },
  watch: {
    issueChange: function (value) {
      if (value) {
        this.loadIssueList(this.projectID, 0)
        this.setIssueChange(false)
      }
    },

    issueFilters: function (value) {
      this.page.current = 1
      this.page.total = 1
      this.loadIssueList(this.projectID, 0)
      this.setIssueChange(false)
    }

  },

  components: {
    ProjectIssueItem
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
