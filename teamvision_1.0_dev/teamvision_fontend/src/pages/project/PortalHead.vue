<template>
  <div class="app-body-header-bar-default">
    <Row align="middle" justify="center">
      <Col span="1" offset="19" :lg="4" :md="4" :sm="8" :xs="10">
      <AutoComplete @on-search="filterProjectList" @on-change="keyworkChange" :transfer="true"
        v-model="projectSearchKey" icon="ios-search" placeholder="输入项目名称，查找项目！" style="width:100%;">
        <List border style="max-height:500px;">
          <ListItem v-for="item in myProjectList" :key="item.id" v-if="item.Display">
            <router-link :to="'/project/' + item.id + '/test/test-case'" tag="li">
              <span class="project-search-item-title">
                <Avatar :src="item.PBAvatar" />
              </span>
              <span class="project-search-item-title">{{ item.PBTitle }}</span>
            </router-link>
          </ListItem>
        </List>
        <!--
            <div class="project-search-item" v-for="item in myProjectList" v-if="item.Display">
              <a :href="'/project/'+ item.id +'/issue/all'" style="color:inherit">
                <span class="project-search-item-title">
                  <Avatar  :src="item.PBAvatar" />
                </span>
                <span class="project-search-item-title">{{ item.PBTitle }}</span>
              </a>
            </div>
          -->
      </AutoComplete>
      </Col>
      <Col span="1" offset="0" :lg="1" :md="6" :sm="8" :xs="12">
      <Button type="success" shape="circle" icon="md-add" @click="addProject"></Button>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from "vuex";

export default {
  name: "CIHead",
  props: ["menuItem"],
  data() {
    return {
      projectSearchKey: "",
    };
  },
  computed: {
    ...mapGetters("project", ["getFilterProjectList"]),
    myProjectList: function () {
      return this.getFilterProjectList;
    },
  },
  methods: {
    ...mapMutations("project", [
      "setProjectCreateDialogShow",
      "setProjectSearchKey",
      "filterProjectList",
    ]),

    keyworkChange: function (value) {
      if (value === '') {
        this.setProjectSearchKey(value);
        this.filterProjectList()
      }
      this.setProjectSearchKey(value);
    },

    initProjectDisplayStatus: function () {
      for (let i = 0; i < this.myProjects.length; i++) {
        this.myProjects[i].Display = true;
      }
    },

    addProject: function () {
      this.setProjectCreateDialogShow(true);
    },
  },
  created: function () {
  },
  components: {},
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import "../../layout/appBody";
@import "../../layout/appHead";
@import "../../assets/teamvision/global/less/global";

.project-search-item {
  padding: 4px 0;
  border-bottom: 1px solid #f6f6f6;
  height: 40px;
  text-align: left;
  line-height: 40px;
}

.project-search-item-title {
  color: #5578aa;
  padding-left: 20px;
}
</style>
