<template>
  <div style="margin:0px;">
    <Card dis-hover :padding="10" style="color: #f5f5f5">
      <Breadcrumb style="padding-left: 16px;">
        <BreadcrumbItem :key="item.id" v-for="item in breadNav" :to="'/project/' + projectID + '/documents/' + item.id">
          {{
            item.FileName }}</BreadcrumbItem>
      </Breadcrumb>
    </Card>
    <div :style="'padding:10px;overflow-y:auto;height:' + containerHeight + 'px'">
      <Card v-if="document.Type === 1" :key="document.id" v-for="document in projectFiles" dis-hover :padding="0"
        style="border-color: #c88400; border: 2px solid #c88400;" class="document-card">
        <div style="padding-left: 10px;padding-right: 10px;">
          <span>
            <Icon :size="16" type="ios-folder" color="#c88400"></Icon>
            ({{ document.ChildCount }})
          </span>
          <span class="pull-right">
            <span>
              <Dropdown @on-click="openDialog">
                <a href="javascript:void(0)">
                  <Icon type="ios-list" />
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem :name="'1:' + document.id" style="padding: 0px;   padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 删除
                  </DropdownItem>
                  <DropdownItem :name="'2:' + document.id" style="padding: 0px;   padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 移动
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </span>
          </span>
        </div>
        <Divider style="margin-top: 5px;margin-bottom:5px;" />
        <div style="padding-left: 10px;padding-right: 10px;">
          <center style="display: block;height:60px;padding-top: 15px;">
            <label-editor-input :id="document.id" :editing="document.id === 0" :displayWidth="140"
              @cancelUpdate="cancelUpdateTitle" @updateValue="updateDocumentTitle" placeHolder="问题标题"
              :displayText="document.FileName"></label-editor-input>
          </center>
        </div>
        <div>
          <center>
            <router-link :to="'/project/' + projectID + '/documents/' + document.id" :exact="false" tag="span"
              class="cursor-hand">
              <a @click="viewFolder(document.id, document.FileName)"
                style="width: 40px;display: inline-block;text-decoration: underline;color:inherit;"
                class="cursor-hand">打开</a>
            </router-link>
          </center>
        </div>
      </Card>
      <Card v-if="document.Type === 2" v-for="document in projectFiles" :key="document.id" dis-hover :padding="0"
        style=" border: 2px solid #22aaaa;" class="document-card">
        <div style="padding-left: 10px;padding-right: 10px;">
          <span style="padding-top: 5px;color: #1d953f">
            <i class="fa fa-file-excel-o fa-fw"></i>
          </span>
          <span class="pull-right">
            <span>
              <Dropdown @on-click="openDialog">
                <a href="javascript:void(0)">
                  <Icon type="ios-list" />
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem :name="'1:' + document.id" style="padding: 0px; padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 删除
                  </DropdownItem>
                  <DropdownItem :name="'2:' + document.id" style="padding: 0px;   padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 移动
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </span>
          </span>
        </div>
        <Divider style="margin-top: 5px;margin-bottom:5px;" />
        <div style="padding-left: 10px;padding-right: 10px;">
          <center style="display: block;height:60px;padding-top: 15px;">
            <label-editor-input :id="document.id" :editing="document.id === 0" @cancelUpdate="cancelUpdateTitle"
              @updateValue="updateDocumentTitle" :displayWidth="140" placeHolder="问题标题"
              :displayText="document.FileName"></label-editor-input>
          </center>
        </div>
        <div>
          <center>
            <router-link :to="'/project/documents/excel/' + document.id" :exact="false" tag="span" class="cursor-hand">
              <a style="width: 40px;display: inline-block;text-decoration: underline;color: inherit;" target="_blank"
                class="cursor-hand">查看</a>
            </router-link>
            <span v-if="!lockedByMe(document.LockBy)" @click="onEditExcel(document.id)" class="cursor-hand">
              <a style="width: 40px;display: inline-block;text-decoration: underline;color: inherit"
                class="cursor-hand">编辑</a>
            </span>
            <span v-if="lockedByMe(document.LockBy)" @click="unLockExcel(document.id)" class="cursor-hand">
              <a style="width: 40px;display: inline-block;text-decoration: underline;color: inherit"
                class="cursor-hand">解锁</a>
            </span>
          </center>
        </div>
      </Card>
      <Card v-if="document.Type === 3" v-for="document in projectFiles" :key="document.id" dis-hover :padding="0"
        style=" border: 2px solid #5578aa;" class="document-card">
        <div style="padding-left: 10px;padding-right: 10px;">
          <span style="padding-top: 5px;">
            <Icon type="ios-cloud-done" color="#5578aa" :size="20" />
          </span>
          <span class="pull-right">
            <span>
              <Dropdown @on-click="openDialog">
                <a href="javascript:void(0)">
                  <Icon type="ios-list" />
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem :name="'1:' + document.id" style="padding: 0px; padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 删除
                  </DropdownItem>
                  <DropdownItem :name="'2:' + document.id" style="padding: 0px;   padding-left: 16px;">
                    <Icon :size="16" type="ios-trash"></Icon> 移动
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </span>
          </span>
        </div>
        <Divider style="margin-top: 5px;margin-bottom:5px;" />
        <div style="padding-left: 10px;padding-right: 10px;">
          <center style="display: block;height:60px;padding-top: 15px;">
            <label-editor-input :id="document.id" :editing="document.id === 0" @updateValue="updateDocumentTitle"
              :displayWidth="140" placeHolder="问题标题" :displayText="document.FileName"></label-editor-input>
          </center>
        </div>
        <div>
          <center>

            <Poptip trigger="hover" :content="host + '/api/project/document/' + document.id + '/download_document'">
              <span @click="downloadDocument(document.id)"
                style="width: 40px;display: inline-block;text-decoration: underline;color: inherit;"
                class="cursor-hand">下载</span>
            </Poptip>
          </center>
        </div>
      </Card>
    </div>
    <kendo-dialog ref="dialog" :modal="true" :height="400" :width="600" :visible="false" :title="'文件夹'"
      :closable="false">
      <kendo-notification ref="staticNotification" :hide-on-click="true" :position-top="30"
        :position-left="500"></kendo-notification>
      <kendo-tree-view ref="treeview" :data-source="remoteDataSource" :data-text-field="'FileName'"
        @select="onSelectFolder">
      </kendo-tree-view>

      <kendo-dialog-action :text="'取消'" :action="cancelMove"></kendo-dialog-action>
      <kendo-dialog-action :text="'移动'" :action="moveDocument" :primary="true">
      </kendo-dialog-action>
    </kendo-dialog>

    <kendo-dialog ref="deleteConfirmDialog" :modal="true" :width="280" :visible="false" :title="'删除确认'">
      <div>点击确定，删除文件！</div>
      <kendo-dialog-action :text="'取消'" :action="cancelMove"></kendo-dialog-action>
      <kendo-dialog-action :text="'删除'" :primary="true" :action="deleteDocument">
      </kendo-dialog-action>
    </kendo-dialog>

    <Modal v-model="showUploadDocumentDialog" title="附件上传" @on-ok="onUploadDocument"
      @on-visible-change="onUploadDialogStatusChange">
      <Upload ref="upload" multiple type="drag" paste action="/api/project/document/upload_document"
        :on-success="handleSuccess" :on-remove="handleRemove" :format="[]" :max-size="20480"
        :default-file-list="defaultList" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
        <div style="padding: 20px 0">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <p>点击，拖拽，粘贴上传附件</p>
        </div>
      </Upload>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import { KendoDialog, KendoDialogAction } from '@progress/kendo-dialog-vue-wrapper'
import { KendoTreeView } from '@progress/kendo-treeview-vue-wrapper'
import { KendoNotification } from '@progress/kendo-popups-vue-wrapper'


export default {
  name: 'ProjectDocuments',
  props: ['projectID', 'folderID'],
  data() {
    return {
      breadNavMenus: [],
      defaultList: [],
      uploadList: [],
      staticNotificationWidget: null,
      projectFiles: [],
      selectedFolder: 0,
      selectedDocument: 0,
      currentFolder: 0,
      showUploadDocumentDialog: false,
      formData: {
        id: 0,
        projectID: 0,
        Type: 1,
        FileID: 0,
        Owner: 0,
        FileName: '',
        Parent: null
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
    containerHeight: function () {
      return this.appBodyHeight - 56
    },

    folder: function () {
      if (this.folderID) {
        return this.folderID
      } else {
        return ''
      }
    },

    host: function () {
      return window.location.host
    },

    remoteDataSource: function () {
      return new kendo.data.HierarchicalDataSource({
        transport: {
          read: {
            url: '/api/project/' + this.projectID + '/documents/?Parent__isnull=True&ReadOnly=False&Type=1',
            dataType: 'json'
          }
        },
        schema: {
          model: {
            id: 'id',
            hasChildren: 'HasChild'
          },
          data: function (resp) {
            let root = {
              id: null,
              projectID: 0,
              Type: 1,
              FileID: 0,
              Owner: 0,
              FileName: '根目录',
              Parent: null
            }
            let soucreData = resp.result
            soucreData.splice(0, 0, root)
            return soucreData
          }
        }
      })
    }
  },

  methods: {
    ...mapMutations('projectglobal', ['setCreateDocumentType', 'setCreateDialogShow']),
    ...mapMutations('document', ['setbreadNav', 'setEditDocument']),
    updateDocumentTitle: function (title, id) {
      if (parseInt(id) === 0) {
        if (title !== '') {
          if (this.folder !== '') {
            this.formData.Parent = parseInt(this.folder)
          }
          this.formData.FileName = title
          this.$axios.post('/api/project/' + this.projectID + '/documents/', this.formData).then(response => {
            //              let tempData = response.data.result
            this.loadProjectDocuments(this.projectID, this.folder)
          }, response => {
            // error callback
          })
        }
      } else {
        this.$axios.patch('/api/project/document/' + id + '/', { FileName: title }).then(response => {
          //            let tempData = response.data.result
        }, response => {
          // error callback
        })
      }
      this.setCreateDocumentType(0)
      this.setCreateDialogShow(false)
    },

    cancelUpdateTitle: function (title, id) {
      if (parseInt(id) === 0) {
        for (let i = 0; i < this.projectFiles.length; i++) {
          if (this.projectFiles[i].id === 0) {
            this.projectFiles.splice(i, 1)
          }
        }
      }
      this.setCreateDocumentType(0)
      this.setCreateDialogShow(false)
    },

    addFolder: function (value) {
      if (value === 1) {
        let folder = this.formData
        folder.FileName = '新建文件夹'
        folder.Type = value
        folder.projectID = this.projectID
        this.projectFiles.push(folder)
      }

      if (value === 2) {
        let folder = this.formData
        folder.Type = value
        folder.FileName = '新建Excel.xlsx'
        folder.projectID = this.projectID
        this.projectFiles.push(folder)
      }

      if (value === 3) {
        this.showUploadDocumentDialog = true
        this.setCreateDocumentType(0)
      }
    },

    onUploadDocument: function () {
      let paras = {}
      paras['uploadList'] = this.uploadList
      paras['project_id'] = this.projectID
      paras['parent'] = this.folder
      this.$axios.patch('/api/project/document/upload_document', paras).then(response => {
        for (let i = 0; i < response.data.result.length; i++) {
          this.projectFiles.push(response.data.result[i])
        }
        this.$Message.success({
          content: '文件上传成功',
          duration: 10,
          closable: true
        })
        this.uploadList = []
      }, response => {
        this.uploadList = []
      })

    },

    onUploadDialogStatusChange: function (value) {
      this.setCreateDialogShow(false)
      if (value) {
        this.uploadList = []
        this.defaultList = []
      }
    },

    downloadDocument: function (documentID) {
      let realUrl = '/api/project/document/' + documentID + '/download_document'
      this.$axios({ url: realUrl, method: 'get', responseType: 'arraybuffer' }).then(response => {
        let url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        let downLoadDocument = this.findDocument(documentID)
        if (downLoadDocument !== '') {
          link.setAttribute('download', downLoadDocument.FileName)
        } else {
          link.setAttribute('download', this.projectID)
        }
        document.body.appendChild(link)
        link.click()
        link.remove()
      }, response => {
      })
    },

    findDocument: function (documentID) {
      let result = ''
      for (let i = 0; i < this.projectFiles.length; i++) {
        if (this.projectFiles[i].id === documentID) {
          result = this.projectFiles[i]
          break;
        }
      }
      return result
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.uploadList.push(file.id)
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过20M限制',
        duration: 10,
        closable: true
      })
    },

    openDialog: function (documentID) {
      let actions = documentID.split(':')
      this.selectedDocument = parseInt(actions[1])
      if (parseInt(actions[0]) === 2) {
        this.$refs.dialog.kendoWidget().open()
      }

      if (parseInt(actions[0]) === 1) {
        this.$refs.deleteConfirmDialog.kendoWidget().open()
      }
    },

    deleteDocument: function () {
      if (this.selectedDocument !== 0) {
        this.$axios.delete('/api/project/document/' + this.selectedDocument).then(response => {
          this.removeDocumentFromFolder(this.selectedDocument)
          this.selectedDocument = 0
        }, response => {
          // error callback
        })
      }
    },

    moveDocument: function (e) {

      if (this.selectedDocument !== 0 && this.selectedFolder !== 0 && (this.selectedDocument !== this.selectedFolder)) {
        this.$axios.patch('/api/project/document/' + this.selectedDocument + '/', { Parent: this.selectedFolder }).then(response => {
          this.removeDocumentFromFolder(this.selectedDocument)
          this.selectedDocument = 0
          this.selectedFolder = 0
        }, response => {
          // error callback
        })
      } else {
        this.staticNotificationWidget.show('请确保选择了目标目录（目标目录和源目录不能相同）', 'info')
      }
    },

    cancelMove: function () {
      this.selectedDocument = 0
      this.selectedFolder = 0
    },

    onSelectFolder: function (e) {
      let item = this.$refs.treeview.kendoWidget().dataItem(e.node)
      if (this.selectedDocument + '' !== item.id + '') {
        this.selectedFolder = item.id
      } else {
        this.staticNotificationWidget.show('目标目录和源目录不能相同', 'info')
      }
    },

    removeDocumentFromFolder: function (documentID) {
      for (let i = 0; i < this.projectFiles.length; i++) {
        if (this.projectFiles[i].id === parseInt(documentID)) {
          this.projectFiles.splice(i, 1)
        }
      }

    },

    loadProjectDocuments: function (projectID, folderID) {
      let documentFilter = '?Parent__isnull=True'
      if (folderID) {
        documentFilter = '?Parent=' + folderID
        this.$axios.get('/api/project/document/' + folderID).then(response => {
          let tempData = response.data.result
          this.setbreadNav([tempData.id, tempData.FileName])
        }, response => {
          // error callback
        })
      } else {
        this.setbreadNav(['', 'Home'])
      }
      this.$axios.get('/api/project/' + projectID + '/documents' + documentFilter).then(response => {
        let tempData = response.data.result
        this.projectFiles = tempData
      }, response => {
        // error callback
      })
    },

    lockedByMe: function (lockerID) {
      let result = false
      if (this.userInfo.id === lockerID) {
        result = true
      }
      return result
    },

    unLockExcel: function (documentID) {
      let parameters = { LockBy: null }
      this.$axios.patch('/api/project/document/' + documentID + '/', parameters).then(response => {
        for (let i = 0; i < this.projectFiles.length; i++) {
          if (parseInt(documentID) === this.projectFiles[i].id) {
            this.projectFiles[i].LockBy = null
          }
        }
        this.$Message.success({
          content: '文档解锁成功，其他成员可以编辑了！',
          duration: 30,
          closable: true
        })
      }, response => {
        // error callback
      })

    },

    onEditExcel: function (documentID) {
      this.$axios.get('/api/project/document/' + documentID).then(response => {
        let editDocument = response.data.result
        if (editDocument.LockBy === null) {
          let parameters = { LockBy: this.userInfo.id }
          this.$axios.patch('/api/project/document/' + documentID + '/', parameters).then(response => {
            for (let i = 0; i < this.projectFiles.length; i++) {
              if (parseInt(documentID) === this.projectFiles[i].id) {
                this.projectFiles[i].LockBy = this.userInfo.id
              }
            }
            window.open('/project/documents/excel/' + editDocument.id + '/edit')
          }, response => {
            // error callback
          })
        }
        if (editDocument.LockBy === this.userInfo.id) {
          window.open('/project/documents/excel/' + editDocument.id + '/edit')
        }
        if (editDocument.LockBy !== this.userInfo.id && editDocument.LockBy !== null) {

          this.$Message.error({
            content: '文档被[' + editDocument.LockerName + ']锁定。',
            duration: 30,
            closable: true
          })
        }

      }, response => {
        // error callback
      })
    }

  },

  created: function () {
    this.loadProjectDocuments(this.projectID, this.folder)
  },

  mounted: function () {
    this.staticNotificationWidget = this.$refs.staticNotification.kendoWidget()
  },

  watch: {

    createDocumentType: function (value) {
      if (value !== 0) {
        this.addFolder(value)
      }
    },

    projectID: function () {
      this.loadProjectDocuments(this.projectID, this.folder)
    },

    folder: function () {
      this.loadProjectDocuments(this.projectID, this.folder)
    }
  },

  components: {
    labelEditorInput,
    KendoDialog,
    KendoDialogAction,
    KendoTreeView,
    KendoNotification
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.document-card {
  width: 150px;
  height: 120px;
  margin: 10px;
  float: left;
}
</style>
