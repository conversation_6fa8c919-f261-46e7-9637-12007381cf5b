<template>
  <div id="vueapp" class="vue-app">
    <spreadsheet ref="spreadsheet" :columns="100" :rows="rowCount" :toolbar-home="toolbar" :rowHeight="rowHeight"
      :sheetsbar="true" @changeFormat="onChange" @change="onChange" @select="onSelect">
      <spreadsheet-sheet :name="'Sheet1'" :sheets-rows-cells-enable="false" :data-source="datasource" :rows="rows"
        :columns="columns">
      </spreadsheet-sheet>
    </spreadsheet>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
// import {
//   Spreadsheet,
//   SpreadsheetSheet,
//   SpreadsheetInstaller
// } from '@progress/kendo-spreadsheet-vue-wrapper'

let _this = this
export default {
  name: 'projectTaskList',
  props: ['documentID', 'viewMode'],
  data() {
    return {
      excelDocument: null,
      lockByMe: false,
      rowCount: 500,
      rowHeight: 25,
      columnCount: 100,
      rows: [],
      columns: [],
      selectedRows: [],
      selectedRange: {},
      datasource: {
        transport: {
          read: function (options) {

          },
          submit: function (options) {
            //console.log(options)
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters('document', ['editDocument']),
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    containerHeight: function () {
      return this.appBodyHeight
    },

    toolbar: function () {
      let saveButton = {
        type: 'button',
        text: '保存',
        showText: 'both',
        icon: 'k-icon k-i-save',
        click: () => {
          let documentCotent = this.$refs['spreadsheet'].kendoWidget().toJSON()
          this.saveDocument(this.excelDocument.id, kendo.stringify(documentCotent))
        }
      }
      if (!this.viewMode) {
        saveButton = {}
      }
      return [saveButton,
        'open',
        'exportAs',
        ['cut', 'copy', 'paste'],
        ['bold', 'italic', 'underline'],
        'backgroundColor', 'textColor',
        'borders',
        'fontSize', 'fontFamily',
        'alignment',
        'textWrap',
        ['formatDecreaseDecimal', 'formatIncreaseDecimal'],
        'format',
        'merge',
        'freeze',
        'filter'
      ]
    },

    rowCounts: function () {
      return this.rows.length + 1
    }
  },
  methods:
  {
    ...mapMutations(['setAppHeadShow', 'setAppBodyTop', 'setAppBodyHeight']),
    ...mapMutations('task', ['setViewDialogShow', 'setTaskChange']),
    onChange: function (e) {
      let sheet = this.$refs['spreadsheet'].kendoWidget().toJSON()
      //console.log(kendo.stringify(sheet))
    },

    onSelect: function (arg) {
      this.selectedRange = arg.range
      arg.range.forEachCell((row, column, value) => {
        this.selectedRows.push(row)
        this.$refs['spreadsheet'].kendoWidget().activeSheet().rowHeight(25)
      })
    },

    saveDocument: function (documentID, documentContent) {
      let parameters = { ExcelContent: documentContent }
      this.$axios.patch('/api/project/document/' + documentID + '/', parameters).then(response => {
        this.$Message.success({
          content: '文档内容已经成功保存',
          duration: 30,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '文档内容保存失败，请注意备份文档',
          duration: 30,
          closable: true
        })
      })
    },

  },
  created: function () {
    this.setAppHeadShow(false)
    this.setAppBodyTop(0)
    let windowHeight = window.innerHeight
    this.setAppBodyHeight(windowHeight)
  },

  mounted: function () {
    let spreadsheet = this.$refs.spreadsheet.kendoWidget()
    let windowHeight = window.innerHeight
    spreadsheet.element.css('height', windowHeight + 'px')
    spreadsheet.element.css('width', '100%')
    spreadsheet.element.css('padding-left', '2px')
    spreadsheet.resize()

    this.$axios.get('/api/project/document/' + this.documentID).then(response => {
      this.excelDocument = response.data.result
      let spreadsheet = this.$refs.spreadsheet.kendoWidget()
      let dataSource = JSON.parse(this.excelDocument.ExcelContent)
      spreadsheet.fromJSON(dataSource)
    }, response => {
    })
  },

  destroyed: function () {
  },

  watch: {},

  components: {
    Spreadsheet,
    SpreadsheetSheet,
    SpreadsheetInstaller
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="less">
.board-column-item {
  margin-bottom: 5px;
  margin-top: 5px;
  min-height: 74px;
  max-height: 200px;
  width: 280px;
}

.board-item-priority {
  width: 1px;
  display: inline-block;
  float: left;
  height: 170px;
}

.board-item-body {
  width: 235px;
  display: inline-table;
  word-wrap: break-word;
  white-space: initial;
  padding: 10px;

}

.board-item-rightbar {
  display: inline-table;
}

.board-item-avatar {
  margin-right: 15px;
  margin-top: 10px;
}

#hot-preview {
  width: 100%;
  height: 100%;
  overflow: scroll;
  padding-left: 10px;
  padding-right: 10px;
}

button {
  margin: 20px 20px;
}

.handsontable .currentRow {
  background-color: #E7E8EF;
}

.handsontable .currentCol {
  background-color: #F9F9FB;
}

#test-hot {
  width: 800px;
  height: 800px;
  overflow: scroll;
}

#vueapp {
  margin: -16px -14px -16px -16px;
}
</style>
