<template>
  <Card :padding="0" style="margin-top: 0px;border-color: #eef2f6;color: #5578aa" dis-hover>
    <!--<div class="home-summary-card-title">项目进展:-->
    <!--</div>-->
    <p slot="title">测试状态</p>
    <span slot="extra">
      <RadioGroup v-model="state" type="button" size="small" @on-change="filterFortestings">
        <Radio label="0">全部</Radio>
        <Radio label="1">未发布</Radio>
        <Radio label="2">已发布</Radio>
      </RadioGroup>
    </span>
    <span slot="extra" style="margin-left: 10px; ">
      <Date-picker :value="filterDate" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期"
        size="small" @on-change="handleChangeDate" style="width: 220px; padding-left:10px;padding-right:10px">
      </Date-picker><Button type="primary" shape="circle" icon="ios-search" size="small"
        @click="filterFortestings">搜索</Button>
    </span>
    <Card dis-hover :padding="0" :bordered="false" :style="'height:' + summaryHeight + 'px' + '; overflow-y:scroll;'">
      <div v-for="project in projectStateList" :key="project.id" style="border-bottom: 1px solid #eef2f6">
        <div style="border-radius: 10px 0px 0px 10px">
          <div
            style="height: 54px;padding: 16px;border-bottom: 1px solid #e8eaec;border-top: 1px solid #e8eaec;border-radius: 5px 0px 0px 5px;">
            <span style="font-size: 16px;font-weight: bold;margin-right: 50px;">
              <router-link :to="'/project/' + project.id + '/issue/all'">
                <span style="padding-right:5px;">
                  <Avatar shape="square" :src="project.PBAvatar"></Avatar>
                </span>
                <span style="color:#5578aa;text-decoration: underline;"> {{ project.PBTitle }} </span>
              </router-link>
            </span>
            <span style="font-size: 12px;margin-right: 20px;">测试需求共计: {{ project.ViewData.Fortestings.length
              }}个</span>
            <span style="font-size: 12px;margin-right: 20px;">待处理问题:</span>
            <span style="font-size: 12px;border: 1px solid black;border-radius: 5px;padding: 2px 5px 2px 5px;">
              <Tooltip content="待解决问题/总数">
                <span style="color: darkred"> {{ project.ViewData.IssueCount.opened }}</span>/
                <span style="color: darkblue">
                  {{ project.ViewData.IssueCount.total }}
                </span>
              </Tooltip>
            </span>
          </div>
          <div style="margin-left: 40px;">
            <Row v-for="fortesting in project.ViewData.Fortestings" :key="fortesting.id"
              style="height: 40px;padding: 10px 16px 10px 16px;">
              <Col span="14">
              <span style="width: 20px;display: inline-block;">
                <Tooltip v-if="fortesting.SelfValidateResult === -1" transfer content="开发自测结果未通过"
                  style="margin-right: 5px;">
                  <Icon type="ios-close-circle-outline" :size="16" color="#b22c46" />
                </Tooltip>
                <Tooltip v-if="fortesting.SelfValidateResult === 0" transfer content="开发自测结果未关联"
                  style="margin-right: 5px;">
                  <Icon type="ios-information-circle-outline" :size="16" color="orange" />
                </Tooltip>
                <Tooltip v-if="fortesting.SelfValidateResult > 0" transfer content="开发自测结果通过"
                  style="margin-right: 5px;">
                  <Icon type="ios-checkmark-circle-outline" :size="16" color="green" />
                </Tooltip>
              </span>
              <span>
                <Tooltip content="预计发版日期">
                  <i style="padding: 2px 5px 2px 5px; border: 1px solid #f6f5f1; font-size: 14px;border-radius: 5px;">
                    <Icon type="ios-calendar" color="#19be6b" /> {{ fortesting.ReleaseDate }}
                  </i>
                </Tooltip>
              </span>
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">{{ fortesting.VersionName
                }}</span>
              <span @click="showFortestingDetail(fortesting.id)" style="text-decoration: underline;cursor: pointer"> {{
                fortesting.Topic }} </span>
              </Col>
              <Col span="2">
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="开发负责人">{{ fortesting.ViewData.commitor_name ? fortesting.ViewData.commitor_name :
                  '-' }}
                </Tooltip>
              </span>
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="测试负责人">{{ fortesting.ViewData.tester_name ? fortesting.ViewData.tester_name : '-' }}
                </Tooltip>
              </span>
              </Col>
              <Col span="1">
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Icon type="ios-radio-button-on" color="#19be6b" /> {{ fortesting.ViewData.status_name }}
              </span>
              </Col>
              <Col span="4">
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="提测时间">{{ fortesting.CommitTime ? fortesting.CommitTime.substring(0, 10) :
                  fortesting.CreationTime.substring(0, 10) }} </Tooltip>
              </span>
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="测试开始时间">{{ fortesting.TestingStartDate ? fortesting.TestingStartDate.substring(0, 10)
                  : '-' }}
                </Tooltip>
              </span>
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="测试完成时间">{{ fortesting.TestingFinishedDate ?
                  fortesting.TestingFinishedDate.substring(0, 10) : '-' }}
                </Tooltip>
              </span>
              </Col>
              <Col span="2">
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="前置时间"> {{ fortesting.ViewData.duration }} 天
                </Tooltip>
              </span>
              <span style="border: 1px solid #f6f5f1;padding: 3px; border-radius: 5px;">
                <Tooltip content="测试工时"> {{ fortesting.ViewData.test_days }} 天
                </Tooltip>
              </span>
              </Col>
            </Row>
          </div>
        </div>
      </div>
      <Spin v-if="loading" size="large" fix></Spin>
    </Card>
  </Card>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { getCurrDateDifference } from "../../../utils/utils.js"

export default {
  components: {},
  name: 'ProjectFortestingStateList',
  props: ['projectID'],
  data() {
    return {
      state: '0',
      projectStateList: [],
      loading: false,
      filterDate: [],
    }
  },

  computed: {
    ...mapState(['appBodyHeight']),
    summaryHeight: function () {
      return this.appBodyHeight - 70
    }
  },

  methods: {
    loadFortestingList: function () {
      this.loading = true
      let dateRange = this.filterDate
      this.$axios.get('/api/project/' + this.projectID + '/fortestings/dashboard?date_range=' + dateRange + '&status=' + this.state).then(response => {
        this.projectStateList = response.data.result
        this.loading = false
      }, response => {
        this.loading = false
      })

    },

    setFilterDate: function () {
      this.filterDate = getCurrDateDifference(30)
    },

    handleChangeDate: function (date) {
      this.filterDate = date;
    },

    filterFortestings: function (value) {
      this.loadFortestingList()
    },

    showFortestingDetail: function (fortestingID) {
      this.$emit('view-fortesting', fortestingID)
    }

  },

  created: function () {
    this.setFilterDate()
    this.loadFortestingList()
  },

  mounted: function () {
  },

  watch: {
    projectID: function (value) {
      this.loadFortestingList()
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
