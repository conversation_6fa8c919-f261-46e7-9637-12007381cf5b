<template>
  <div v-if="forTestingInfo">
    <div class="t-drawer-content-header">
      <span style="font-size: 16px; font-weight: bold">提测单[#{{ forTestingInfo.id }}]</span>
      <span style="font-size: 16px; font-weight: bold">[{{ forTestingInfo.ViewData.status_name
        }}]</span>
    </div>
    <Divider style="margin:0px" />
    <div style="display: flex; justify-content: space-between;padding-bottom: 20px;padding: 14px;">
      <div class="issue-detail-creation">
        <div class="issue-detail-creation" style="font-size: 14px;"
          v-if="forTestingInfo.ViewData.requirements.length > 0">
          <div class="issue-detail-creator">
            <h4>关联需求:
              <li class="issue-detail-createdate" v-for="fortesting in forTestingInfo.ViewData.requirements">
                <Tag color="primary"># {{ fortesting.id }}</Tag>
                <router-link :to="'/project/' + fortesting.ProjectID + '/requirement/' + fortesting.id">
                  {{ fortesting.Title }}
                </router-link>
              </li>
            </h4>
          </div>
        </div>
      </div>
      <div class="issue-detail-creation">
        <span class="issue-detail-creator">
          <Icon type="ios-information-circle" color="#5578aa" size="14" /> {{ forTestingInfo.ViewData.creator_name }}
          于{{ forTestingInfo.CreationTime }}创建;
        </span>
        <span class="issue-detail-createdate" v-if="forTestingInfo.CommitTime">
          {{ forTestingInfo.ViewData.commitor_name }}于{{ forTestingInfo.CommitTime }}提测。
        </span>
      </div>
    </div>
    <Divider style="margin:0px" />
    <Card :bordered="false" :dis-hover="true" :padding=0>
      <Form ref="editFortesting" :model="formItem" label-position="top" :rules="ruleCustom">
        <div style="display: flex;">
          <div style="flex: 5; padding: 14px; margin-right: 10px; max-width: 80%">
            <FormItem label="主题:" prop="Topic" style="width: 100%;">
              <Input :disabled="!isEdit" v-model="formItem.Topic" placeholder="提测主题" />
            </FormItem>
            <FormItem label="提测内容:" prop="TestingFeature">
              <Table :columns="columns" :data="contentData" :show-header="false" disabled-hover>
                <template slot-scope="{ row, index }" slot="name">
                  <Input :disabled="!isEdit" @on-blur="handleSave(row, index)" v-model="row.name" type="textarea"
                    placeholder="请输入提测内容，点击+，添加更多提测内容" />
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <div v-if="index === contentData.length - 1">
                    <Icon @click="handleAdd(row, index)" type="ios-add-circle" style="color: #32be77" :size="18"
                      class="cursor-hand" />
                  </div>
                  <Icon v-if="index !== contentData.length - 1" @click="handleRemoveRow(row, index)"
                    type="ios-close-circle" style="color: red" :size="18" class="cursor-hand" />
                </template>
              </Table>
              <!--<vue-editor v-model="formItem.TestingFeature" :editorToolbar="editorToolBar" @text-change="commitContentChange" placeholder="提测内容"></vue-editor>-->
            </FormItem>
            <FormItem label="测试建议:" prop="TestingAdvice">
              <vue-editor :readOnly="!isEdit" v-model="formItem.TestingAdvice" :editorToolbar="editorToolBar"
                placeholder="测试建议">
              </vue-editor>
            </FormItem>
            <FormItem label="更多描述:">
              <Input :disabled="!isEdit" v-model="formItem.Description" type="textarea"
                :autosize="{ minRows: 3, maxRows: 5 }" placeholder="更多描述 ...">
              </Input>
            </FormItem>
            <FormItem label="相关链接:" prop="Link">
              <Table :columns="columns" :data="linkData" :show-header="false" disabled-hover>
                <template slot-scope="{ row, index }" slot="name">
                  <Input :disabled="!isEdit" maxlength="200" show-word-limit @on-blur="handleLinkSave(row, index)"
                    v-model="row.link" type="text" placeholder="请输入提测关联链接，点击+，添加更多" />
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <Icon v-if="index === linkData.length - 1" @click="handleLinkAdd(row, index)" type="ios-add-circle"
                    style="color: #32be77" :size="18" class="cursor-hand" />
                  <Icon v-if="index !== linkData.length - 1" @click="handleLinkRemove(row, index)"
                    type="ios-close-circle" style="color: red" :size="18" class="cursor-hand" />
                </template>
              </Table>
            </FormItem>
            <div style="border-top: 1px solid rgba(0, 0, 0, 0.1); margin-top: 20px"></div>
            <div style="font-size: 14px; padding-top: 5px; color: #495060">
              <Icon type="android-attach" :size="14"></Icon>附件
            </div>
            <FormItem>
              <div class="demo-upload-list" :key="item.id" v-for="item in formItem.attachments.uploadList">
                <div>{{ item.name }}</div>
                <div class="demo-upload-list-cover">
                  <a :href="item.url">
                    <Icon type="ios-cloud-download-outline" />
                  </a>
                  <Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
                </div>
              </div>
              <Upload ref="upload" multiple type="drag" action="/api/project/fortesting/upload_files"
                style="display: inline-block; width: 100px" :show-upload-list="false"
                :default-file-list="formItem.attachments.defaultList" :on-success="handleSuccess" :format="[
                  'jpg', 'jpeg', 'png', 'pdf', 'txt', 'sql', 'docx', 'doc', 'xlsx',]" :max-size="10240"
                :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize"
                :before-upload="handleBeforeUpload">
                <Icon type="ios-cloud-upload" :size="20" />
              </Upload>
            </FormItem>
            <!-- <FormItem>
            <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="ok('editFortesting')">更新</Button>
          </FormItem> -->
            <Card :dis-hover="true" :bordered="false" style="margin-top: 20px; color: #5578aa">
              <div slot="title" style="color: #5578aa">
                <span>测试反馈</span>
              </div>
              <div>
                <Row style="line-height: 32px; padding: 16px">
                  <Col span="18"> BVT测试结果 </Col>
                  <Col span="6">
                  <i-switch v-model="formItem.BVTPassed" @on-change="changeBVTResult" size="large">
                    <span slot="open">通过</span>
                    <span slot="close">未通过</span>
                  </i-switch>
                  </Col>
                </Row>
              </div>
            </Card>
            <div style="min-height: 200px; margin-top: 20px">
              <Tabs value="name1">
                <TabPane label="关联任务" name="name1">
                  <project-requirement-task :itemID="fortestingID" :projectID="formItem.projectID"
                    :version="formItem.VersionID" :itemType="2"></project-requirement-task>
                </TabPane>
              </Tabs>
            </div>
          </div>
          <div style="flex: 1; padding: 10px; margin-left: 10px;background: #fbfbfe;">
            <FormItem label="项目:" prop="ProjectID">
              <Select :disabled="!isEdit" v-model="formItem.ProjectID" placeholder="项目">
                <Option v-for="project in projectList" :key="project.id" :value="project.id">{{
                  project.PBTitle }}
                </Option>
              </Select>
            </FormItem>
            <FormItem label="发版日期:" prop="TestingDeadLineDate">
              <div v-if="!isEdit"> {{ formItem.TestingDeadLineDate ? TestingDeadLineDate : '无' }}</div>
              <DatePicker v-if="isEdit" type="date" v-model="formItem.TestingDeadLineDate" placeholder="预计发版日期">
              </DatePicker>
            </FormItem>
            <FormItem label="优先级:" prop="priority">
              <Select :disabled="!isEdit" v-model="formItem.priority" placeholder="a">
                <Option v-for="item in priorityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
            <FormItem label="测试人员:" prop="Testers">
              <Select :disabled="!isEdit" v-model="formItem.Testers" placeholder="测试人员" @on-change="selectTester">
                <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{
                  member.name }}</Option>
              </Select>
              <Tooltip content="修改测试人:" theme="light">
                <Button v-if="showChangeTester" style="margin-left: 4px" @click="changeTester" type="primary"
                  shape="circle" icon="md-checkmark">
                </Button>
              </Tooltip>
            </FormItem>
            <!-- <FormItem label="关联自测结果" prop="SelfValidateResult">
                <Select v-model="formItem.SelfValidateResult" style="width:200px">
                  <Option v-for="item in testResultFileList" :value="item.id" :key="item.id">{{ item.FileName }}</Option>
                </Select>
                <Poptip content="按照提测版本选择测试点列表中的BVT验证文件,作为开发自测的结果关联到本次提测。">
                  <Icon type="ios-help-circle" :size="20" color="#5578aa" style="cursor: pointer;" />
                </Poptip>
              </FormItem> -->
          </div>
        </div>
      </Form>
    </Card>
    <div v-if="forTestingInfo.Status === 2">
      <Row justify="end">
        <Col>
        <Button type="warning" @click="ok('editFortesting')">保存</Button>
        <Button type="warning" @click="abandonFortesting">废弃</Button>
        <Button type="warning" @click="backToFortesting">打回</Button>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from "vuex";
import { initFortestingForm, initProjectVersions, fortestingValidateRules, } from "./ProjectFortestingCreateDialog";
import { VueEditor } from "vue2-editor";
import ProjectRequirementTask from "../requirement/ProjectReqirementTask.vue";
import { getForTesting } from '../business-service/ProjectApiService'

export default {
  name: "ProjectFortestingInfo",
  props: ["fortestingID", "projectID", "versionID"],
  data() {
    return {
      isEdit: true,
      forTestingInfo: null,
      content: "",
      editorToolBar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ color: [] }, { background: [] }],
      ],
      testResultFileList: [],
      formItem: {
        Topic: "",
        ProjectModuleID: 0,
        ProjectID: 0,
        VersionID: 0,
        CodeRepertory: "",
        SelfValidateResult: 0,
        Branch: "",
        BVTPassed: false,
        TestingAdvice: "",
        TestingFeature: "",
        ProjectVersion: [],
        attachments: {
          defaultList: [],
          imgName: "",
          visible: false,
          uploadList: [],
        },
        Link: "",
        Description: "",
        Testers: 0,
        priority: 0,
        CreationTime: null,
        Status: 0
      },
      columns: [
        {
          title: "",
          slot: "name",
        },
        {
          title: "",
          slot: "action",
          width: 100,
        },
      ],
      contentData: [
        {
          name: "",
          id: 0,
        },
      ],
      linkData: [
        {
          link: "",
          id: 0,
        },
      ],
      editIndex: 0, // 当前聚焦的输入框的行数
      editName: "", // 第一列输入框，当然聚焦的输入框的输入内容，与 data 分离避免重构的闪烁
      ruleCustom: {
        ...fortestingValidateRules,
      },
      showChangeTester: false,
      priorityList: [
        {
          value: 0,
          label: "P0",
        },
        {
          value: 1,
          label: "P1",
        },
        {
          value: 2,
          label: "P2",
        },
      ],
      submitFormButtonShow: false,
    };
  },
  computed: {
    ...mapGetters("projectglobal", ["viewDialogShow", "projectVersion", "project",]),
    ...mapState(["appBodyHeight"]),
    ...mapState("project", ["projectList", "projectsVersions", "projectMembers",]),
    containerHeight: function () {
      return this.appBodyHeight - 100;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setCreateDialogShow", "setViewDialogShow", "setObjectChange",]),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.formItem["RequirementData"] = this.contentData;
          for (let i = 0; i < this.contentData.length; i++) {
            if (this.contentData[i].id && this.contentData[i].id + "" !== "0") {
              this.formItem.TestingFeature =
                this.formItem.TestingFeature.replace(this.contentData[i].name + "{;}", "");
            }
          }
          this.$axios.put("/api/project/fortesting/" + this.fortestingID + "/", this.formItem).then((response) => {
            this.setObjectChange(true);
            this.$Message.success({
              content: "提测保存成功",
              duration: 3,
              closable: true,
            });
          }, (response) => {
            this.$Message.error({
              content: "提测保存失败",
              duration: 3,
              closable: true,
            });
          }
          );
        }
      });
    },
    cancel() {
      this.setCreateDialogShow(false);
      this.setViewDialogShow(false);
    },

    changeBVTResult: function (value) {
      this.$axios.patch("/api/project/fortesting/" + this.fortestingID + "/update_property", { BVTPassed: value }).then((response) => {
        this.setObjectChange(true);
        this.$Message.success({
          content: "BVT结果更新成功",
          duration: 10,
          closable: true,
        });
      },
        (response) => {
          this.$Message.error({
            content: "BVT结果保存失败",
            duration: 10,
            closable: true,
          });
        }
      );
    },

    handleView(name) {
      this.formItem.attachments.imgName = name;
      this.formItem.attachments.visible = true;
    },
    handleRemove(file) {
      const fileList = this.formItem.attachments.uploadList;
      this.formItem.attachments.uploadList.splice(fileList.indexOf(file), 1);
      this.removeFile(file.id);
      //          this.updateAttachments()
    },
    handleSuccess(res, file) {
      file.url = res.result.url;
      file.id = res.result.file_id;
      this.formItem.attachments.uploadList = this.$refs.upload.fileList;
    },
    handleFormatError(file) {
      this.$Message.warning({
        content: "文件格式不正确,格式：'jpg','jpeg','png','pdf','txt','sql','docx','doc','xlsx'",
        duration: 10,
        closable: true,
      });
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: "文件大小超过10M限制",
        duration: 10,
        closable: true,
      });
    },

    handleBeforeUpload() { },
    onProjectChange(value, selectedData) {
      let project = value[0];
      this.formItem.projectID = value[0];
      this.formItem.VersionID = value[1];
      this.formItem.SelfValidateResult = 0;
      this.formItem.ProjectVersion = value;
      this.loadMyTestResultFileList();
    },
    commitContentChange: function (delta, oldDelta, source) { },
    updateAttachments: function () {
      let result = "";
      for (let i = 0; i < this.formItem.attachments.uploadList.length; i++) {
        result = this.formItem.attachments.uploadList[i].id + "," + result;
      }
      if (result !== "") {
        this.$axios.patch("/api/project/fortesting/" + this.fortestingID + "/", {
          Attachment: result,
        }).then(
          (response) => { },
        );
      }
    },

    handleAdd(row, index) {
      this.contentData.push({ name: "", id: 0 });
    },

    handleRemoveRow: function (row, index) {
      this.contentData.splice(index, 1);
      this.formItem.TestingFeature = this.formItem.TestingFeature.replace(row.name, "");
    },

    handleSave(row, index) {
      let result = "";
      if (this.contentData[index].name !== "") {
        let oldValue = this.contentData[index].name + "{;}";
        this.formItem.TestingFeature = this.formItem.TestingFeature.replace(oldValue, "");
      }
      if (row.name.trim() !== "") {
        result = result + row.name + "{;}";
        this.contentData[index].name = row.name;
      }
      this.formItem.TestingFeature = this.formItem.TestingFeature + result;
    },

    removeFile: function (file_id) {
      this.$axios.delete("/api/project/fortesting/delete_file/" + file_id).then((response) => { },
      );
    },

    handleLinkSave(row, index) {
      let result = "";
      if (this.linkData[index].link !== "") {
        let oldValue = this.linkData[index].link + "{;}";
        this.formItem.Link = this.formItem.Link.replace(oldValue, "");
      }
      if (row.link.trim() !== "") {
        result = result + row.link + "{;}";
        this.linkData[index].link = row.link;
      }
      this.formItem.Link = this.formItem.Link + result;
    },

    handleLinkAdd() {
      this.linkData.push({ link: "", id: 0 });
    },

    handleLinkRemove: function (row, index) {
      this.linkData.splice(index, 1);
      this.formItem.Link = this.formItem.Link.replace(row.link, "");
    },

    loadMyTestResultFileList: function () {
      let project = 0;
      let version = 0;
      if (this.formItem.ProjectVersion[0]) {
        project = this.formItem.ProjectVersion[0];
      }

      if (this.formItem.ProjectVersion[1]) {
        version = this.formItem.ProjectVersion[1];
      }
      this.$axios.get("/api/project/" + project + "/version/" + version + "/mindmap_files?FileType=2").then((response) => {
        this.testResultFileList = response.data.result;
      },
        (response) => {
          // error callback
        }
      );
    },

    // loadForTesting: function () {
    //   this.formItem.id = this.fortesting;
    //   let defaultTask = this.formItem;
    //   this.contentData = [];
    //   let defaultTesingFeature = this.contentData;

    //   if (this.formItem.id !== 0) {
    //     initFortestingForm(this.fortesting).then(function (initData) {
    //       defaultTask.Topic = initData.Topic;
    //       defaultTesingFeature.push(...initData.FortestingFeature);
    //       defaultTask.TestingFeature = initData.TestingFeature;
    //       defaultTask.TestingAdvice = initData.TestingAdvice.replace("div", "p");
    //       defaultTask.ProjectModuleID = initData.ProjectModuleID;
    //       defaultTask.ProjectVersion = [initData.projectID, initData.VersionID];
    //       defaultTask.projectID = initData.projectID;
    //       defaultTask.VersionID = initData.VersionID;
    //       defaultTask.BVTPassed = initData.BVTPassed;
    //       defaultTask.SelfValidateResult = initData.SelfValidateResult;
    //       defaultTask.TestingDeadLineDate = initData.ReleaseDate;
    //       defaultTask.attachments.defaultList = initData.Attachments;
    //       defaultTask.attachments.uploadList = initData.Attachments;
    //       defaultTask.Description = initData.Description;
    //       defaultTask.Testers = initData.Testers;
    //       defaultTask.CreationTime = initData.CreationTime;
    //       defaultTask.Link = initData.Link;
    //       this.handleLink(defaultTask)
    //     });
    //     //console.log(this.linkData)
    //   }
    // },

    getForTestingInfo: function (forTestingID) {
      getForTesting(forTestingID).then(response => {
        this.forTestingInfo = response.data.result
        this.formItem.Topic = this.forTestingInfo.Topic
        this.formItem.ProjectID = this.forTestingInfo.ProjectID
        this.formItem.ProjectName = this.forTestingInfo.ProjectName
        this.formItem.TestingDeadLineDate = this.forTestingInfo.TestingDeadLineDate
        this.formItem.TesterName = this.forTestingInfo.TesterName
        this.formItem.RiskLevel = this.forTestingInfo.RiskLevel
        this.formItem.Testers = this.forTestingInfo.Testers
        this.formItem.TestingAdvice = this.forTestingInfo.TestingAdvice
        this.formItem.Description = this.forTestingInfo.Description
        this.formItem.FortestingFeature = this.forTestingInfo.FortestingFeature
        this.contentData = this.forTestingInfo.FortestingFeature
        this.handleLink(this.forTestingInfo)
      })
    },

    handleLink: function (forTesting) {
      let tmp = [];
      if (forTesting.Link) {
        let LinkDataTmp = forTesting.Link.split(",");
        for (let i = 0; i < LinkDataTmp.length; i++) {
          var obj = {};
          obj.id = i;
          obj.link = LinkDataTmp[i].replace("[", "").replace("]", "")
            .replace(RegExp(" ", "g"), "")
            .replace(RegExp("'", "g"), "");
          tmp.push(obj);
        }
      }
      this.linkData = tmp;
    },


    selectTester: function (value) {
      this.showChangeTester = true;
    },

    changeTester: function () {
      let tester = {
        Testers: this.formItem.Testers,
      };
      this.$axios.patch("/api/project/fortesting/" + this.fortestingID + "/", tester).then((response) => {
        this.$Message.success({
          content: "修改成功",
          duration: 3,
          closable: true,
        });
        this.setObjectChange(true);
      },
        (response) => {
          this.$Message.error({
            content: "修改失败",
            duration: 3,
            closable: true,
          });
        }
      );
    },

    abandonFortesting: function () {
      this.$Modal.confirm({
        title: "提示",
        content: "确认废弃该提测单?",
        onOk: () => {
          this.updateFortestingStatus(6)
        }
      })
    },

    backToFortesting: function () {
      this.$Modal.confirm({
        title: "提示",
        content: "确认打回提测单?",
        onOk: () => {
          this.updateFortestingStatus(7)
        }
      })
    },

    updateFortestingStatus: function (status) {
      this.$axios.patch('/api/project/fortesting/' + this.fortestingID + '/update_status', { 'Status': parseInt(status) }).then(response => {
        this.$Message.success({
          content: "操作成功",
          duration: 3,
          closable: true
        })
        this.setObjectChange(true);
        this.$emit('isShowFortestingDetail', false)
      }, response => {
        this.$Message.error({
          content: response.data.result.message,
          duration: 3,
          closable: true
        })
      })
    },

  },
  created() {
    //this.loadMyTestResultFileList();
  },
  mounted() {
    //this.formItem.attachments.uploadList = this.$refs.upload.fileList;
    // this.formItem.ProjectVersion[0] = this.projectID;
    // this.formItem.ProjectVersion[1] = this.versionID;
    this.getForTestingInfo(this.fortestingID);
  },
  watch: {
    // fortesting: function () {
    //   this.loadForTesting();
    // },

  },
  components: {
    VueEditor,
    ProjectRequirementTask,
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.ivu-form-item {
  margin-bottom: 10px;
}

.demo-upload-list {
  display: inline-block;
  width: 100px;
  height: 100px;
  text-align: center;
  margin: 5px;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}

.t-drawer-content-header {
  border-bottom: 1px solid #e8eaec;
  padding: 14px;
  line-height: 1;
}
</style>
