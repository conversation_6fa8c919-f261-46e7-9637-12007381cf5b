<template>
  <Modal :value="dialogShow" :title="dialogTitle" :width="900" @on-cancel="cancel"
    :styles="{ bottom: '20px', top: '20px' }">
    <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
      <Form ref="createFortesting" label-position="top" :model="formItem" :rules="ruleCustom">
        <div style="display: flex;">
          <div style="flex: 4; padding: 10px; margin-right: 10px;">
            <FormItem label="主题" prop="Topic">
              <Input v-model="formItem.Topic" maxlength="200" show-word-limit placeholder="提测主题" />
            </FormItem>
            <!-- <FormItem label="关联自测结果" prop="SelfValidateResult">
          <Select v-model="formItem.SelfValidateResult" style="width:200px">
            <Option v-for="item in testResultFileList" :value="item.id" :key="item.id">{{ item.FileName }}</Option>
          </Select>
          <Poptip content="按照提测版本选择测试点列表中的BVT验证文件,作为开发自测的结果关联到本次提测。">
            <Icon type="ios-help-circle" :size="20" color="#5578aa" style="cursor: pointer;" />
          </Poptip>
        </FormItem> -->
            <!--<FormItem label="代码仓库" prop="CodeRepertory">-->
            <!--<Input v-model="formItem.CodeRepertory" placeholder="代码仓库地址"/>-->
            <!--</FormItem>-->
            <!--<FormItem label="分支" prop="Branch">-->
            <!--<Input v-model="formItem.Branch" placeholder="代码分支"/>-->
            <!--</FormItem>-->
            <FormItem label="提测内容" prop="TestingFeature">
              <Table :columns="columns" :data="contentData" :show-header="false" disabled-hover>
                <template slot-scope="{ row, index }" slot="name">
                  <Input maxlength="2000" show-word-limit @on-blur="handleSave(row, index)" v-model="row.name"
                    type="text" placeholder="请输入提测内容，点击+，添加更多提测内容" />
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <Icon v-if="index === contentData.length - 1" @click="handleAdd(row, index)" type="ios-add-circle"
                    style="color:#32be77;" :size="18" class="cursor-hand" />
                  <Icon v-if="index !== contentData.length - 1" @click="handleRemoveRow(row, index)"
                    type="ios-close-circle" style="color:red;" :size="18" class="cursor-hand" />
                </template>
              </Table>
            </FormItem>
            <FormItem label="测试建议" prop="TestingAdvice">
              <vue-editor v-model="formItem.TestingAdvice" :editorToolbar="editorToolBar"
                placeholder="测试建议"></vue-editor>
            </FormItem>
            <FormItem label="相关链接" prop="Link">
              <Table :columns="columns" :data="linkData" :show-header="false" disabled-hover>
                <template slot-scope="{ row, index }" slot="name">
                  <Input maxlength="500" show-word-limit @on-blur="handleLinkSave(row, index)" v-model="row.link"
                    type="text" placeholder="请输入提测关联链接，点击+，添加更多" />
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <Icon v-if="index === linkData.length - 1" @click="handleLinkAdd(row, index)" type="ios-add-circle"
                    style="color:#32be77;" :size="18" class="cursor-hand" />
                  <Icon v-if="index !== linkData.length - 1" @click="handleLinkRemove(row, index)"
                    type="ios-close-circle" style="color:red;" :size="18" class="cursor-hand" />
                </template>
              </Table>
            </FormItem>
            <FormItem label="其他说明">
              <Input v-model="formItem.Description" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }"
                placeholder="其他说明 ..."></Input>
            </FormItem>
            <div style="border-top: 1px solid rgba(0,0,0,.1);margin-top: 20px;"></div>
            <div style="font-size: 14px;padding-top: 5px;color:#495060">
              <Icon type="android-attach" :size="14"></Icon>
              附件
            </div>
            <FormItem>
              <div class="demo-upload-list" :key="item.id" v-for="item in formItem.attachments.uploadList">
                <div>{{ item.name }}</div>
                <div class="demo-upload-list-cover">
                  <a :href="item.url">
                    <Icon type="ios-cloud-download-outline" />
                  </a>
                  <Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
                </div>
              </div>
              <Upload ref="upload" multiple type="drag" action="/api/project/fortesting/upload_files"
                style="display: inline-block;width:100px;" :show-upload-list="false"
                :default-file-list="formItem.attachments.defaultList" :on-success="handleSuccess"
                :format="['jpg', 'jpeg', 'png', 'pdf', 'txt', 'sql', 'docx', 'doc', 'xlsx']" :max-size="10240"
                :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize"
                :before-upload="handleBeforeUpload">
                <Icon type="ios-cloud-upload" :size="20" />
              </Upload>
            </FormItem>
          </div>
          <div style="flex: 1; padding: 10px; margin-left: 10px;background: #fbfbfe;">
            <FormItem label="项目" prop="ProjectID" v-if="routerName == 'homeFortesting'">
              <Select v-model="formItem.ProjectID">
                <Option v-for="project in projectList" :value="project.id" :key="project.id">{{ project.PBTitle }}
                </Option>
              </Select>
            </FormItem>
            <FormItem label="测试人" prop="Tester">
              <Select v-model="formItem.Tester" :filterable="true" placeholder="测试人">
                <Option v-for="member in projectMembers" :key="member.PMMember" :value="member.PMMember">{{
                  member.name }} </Option>
              </Select>
            </FormItem>
            <FormItem label="优先级" prop="priority">
              <Select v-model="formItem.priority">
                <Option v-for="item in priorityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
            <FormItem label="发版日期" prop="TestingDeadLineDate">
              <DatePicker type="date" v-model="formItem.TestingDeadLineDate" placeholder="预计发版日期" transfer>
              </DatePicker>
            </FormItem>
            <FormItem label="版本">
              <Select v-model="formItem.VersionID" transfer :disabled="projectVersions.length == 0">
                <Option v-for="item in projectVersions" :value="item.id" :key="item.id">{{ item.VVersion }}</Option>
              </Select>
            </FormItem>
          </div>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <Button v-if="createDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
        @click="ok('createFortesting')">提交
      </Button>
      <Button v-if="viewDialogShow" type="success" style="width: 80px; height:30px;" shape="circle"
        @click="ok('createFortesting')">保存
      </Button>
      <!--<Button type="ghost"  style="width: 80px; height:30px;"  shape="circle" @click="cancel">取消</Button>-->
    </div>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations, mapState, mapActions } from 'vuex'
import { initFortestingForm, initProjectVersions, fortestingValidateRules } from './ProjectFortestingCreateDialog'
import { loadProjectVersions, loadProjectMembers } from '../business-service/ProjectApiService'
import { VueEditor } from 'vue2-editor'

export default {
  name: 'ProjectFortestingCreateDialog',
  props: ['fortestingID', 'projectID', 'versionID', 'requirementList'],
  data() {
    return {
      content: '',
      defaultprojectID: [],
      editorToolBar: [
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }], [{ 'color': [] }, { 'background': [] }],
      ],
      testResultFileList: [],
      dialogTitle: '添加提测',
      projectMembers: [],
      projectVersions: [],
      formItem: {
        Topic: '',
        ProjectModuleID: 0,
        ProjectID: 0,
        VersionID: 0,
        SelfValidateResult: 0,
        CodeRepertory: '',
        Branch: '',
        TestingAdvice: '',
        TestingFeature: '',
        TestingDeadLineDate: '',
        attachments: {
          defaultList: [],
          imgName: '',
          visible: false,
          uploadList: []
        },
        Link: '',
        Description: '',
        priority: 0,
        Tester: null,
      },
      columns: [
        {
          title: '',
          slot: 'name',
        },
        {
          title: '',
          slot: 'action',
          width: 100
        }
      ],
      contentData: [{ name: '', id: 0 }],
      linkData: [{ link: '', id: 0 }],
      editIndex: 0,  // 当前聚焦的输入框的行数
      editName: '',  // 第一列输入框，当然聚焦的输入框的输入内容，与 data 分离避免重构的闪烁
      ruleCustom: {
        ...fortestingValidateRules
      },
      priorityList:
        [
          {
            value: 0,
            label: 'P0'
          },
          {
            value: 1,
            label: 'P1'
          },
          {
            value: 2,
            label: 'P2'
          },
        ]
    }
  },
  computed: {
    ...mapGetters('projectglobal', ['viewDialogShow', 'projectVersion', 'project']),
    ...mapState(['appBodyMainHeight']),
    ...mapState('projectglobal', ['createDialogShow']),
    ...mapState('project', ['projectList', 'projectsVersions',]),

    containerHeight: function () {
      return this.appBodyMainHeight - 100
    },

    dialogShow: function () {
      return (this.createDialogShow)
    },

    routerName: function () {
      return this.$route.name;
    },

  },
  methods:
  {
    ...mapMutations('projectglobal', ['setCreateDialogShow', 'setCreateReqType', 'setObjectChange']),
    ...mapActions('projectglobal', ['loadProjectInfo']),

    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.createDialogShow) {
            this.formItem['RequirementData'] = this.contentData
            for (let i = 0; i < this.contentData.length; i++) {
              if (this.contentData[i].id && this.contentData[i].id + '' !== '0') {
                this.formItem.TestingFeature = this.formItem.TestingFeature.replace(this.contentData[i].name + '{;}', '')
              }
            }
            if (this.projectID !== undefined) {
              this.formItem.ProjectID = parseInt(this.projectID)
            }
            this.formItem.VersionID
            this.$axios.post('/api/project/' + this.formItem.ProjectID + '/version/' + this.formItem.VersionID + '/fortestings', this.formItem).then(response => {
              this.setObjectChange(true)
              this.formItem.attachments.uploadList = []
              this.$emit('createComplete', 1)
              this.$Message.success({
                content: '提测创建成功',
                duration: 3,
                closable: true
              })
            }, response => {
              this.$emit('createComplete', 0)
              this.$Message.error({
                content: '提测创建失败',
                duration: 3,
                closable: true
              })

            })
          }
          this.setCreateDialogShow(false)
          this.setCreateReqType(0)
        }
      })
    },
    cancel() {
      this.setCreateDialogShow(false)
      this.setCreateReqType(0)
    },
    handleView(name) {
      this.formItem.attachments.imgName = name
      this.formItem.attachments.visible = true
    },
    handleRemove(file) {
      const fileList = this.formItem.attachments.uploadList
      this.formItem.attachments.uploadList.splice(fileList.indexOf(file), 1)
      this.removeFile(file.id)
    },
    handleSuccess(res, file) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.formItem.attachments.uploadList = this.$refs.upload.fileList
    },
    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\',\'pdf\',\'txt\',\'sql\',\'docx\',\'doc\',\'xlsx\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过10M限制',
        duration: 10,
        closable: true
      })
    },
    handleBeforeUpload() { },

    commitContentChange: function (delta, oldDelta, source) { },

    updateAttachments: function () {
      let result = ''
      for (let i = 0; i < this.formItem.attachments.uploadList.length; i++) {
        result = this.formItem.attachments.uploadList[i].id + ',' + result
      }
      if (result !== '') {
        this.$axios.patch('/api/project/fortesting/' + this.fortestingID + '/', { 'Attachment': result }).then(response => {
        }, response => {
        })
      }
    },

    handleAdd(row, index) {
      this.contentData.push({ name: '', id: 0 })
    },

    handleRemoveRow: function (row, index) {
      this.contentData.splice(index, 1)
      this.formItem.TestingFeature = this.formItem.TestingFeature.replace(row.name, '')
    },

    handleSave(row, index) {
      let result = ''
      if (this.contentData[index].name !== '') {
        let oldValue = this.contentData[index].name + '{;}'
        this.formItem.TestingFeature = this.formItem.TestingFeature.replace(oldValue, '')
      }
      if (row.name.trim() !== '') {
        result = result + row.name + '{;}'
        this.contentData[index].name = row.name
      }
      this.formItem.TestingFeature = this.formItem.TestingFeature + result
    },

    handleLinkAdd() {
      this.linkData.push({ link: '', id: 0 })
    },

    handleLinkRemove: function (row, index) {
      this.linkData.splice(index, 1)
      this.formItem.Link = this.formItem.Link.replace(row.link, '')
    },

    handleLinkSave(row, index) {
      let result = ''
      if (this.linkData[index].link !== '') {
        let oldValue = this.linkData[index].link + '{;}'
        this.formItem.Link = this.formItem.Link.replace(oldValue, '')
      }
      if (row.link.trim() !== '') {
        result = result + row.link + '{;}'
        this.linkData[index].link = row.link
      }
      this.formItem.Link = this.formItem.Link + result
    },

    removeFile: function (file_id) {
      this.$axios.delete('/api/project/fortesting/delete_file/' + file_id).then(response => {
      }, response => { })
    },

    onProjectChange(value, selectedData) {
      let project = value[0]
      this.formItem.ProjectID = value[0]
      this.formItem.VersionID = value[1]
      this.formItem.SelfValidateResult = 0
      this.loadMyTestResultFileList()
    },

    loadMyTestResultFileList: function () {
      let project = 0
      let version = 0

      this.$axios.get('/api/project/' + project + '/version/' + version + '/mindmap_files?FileType=2').then(response => {
        this.testResultFileList = response.data.result
      }, response => {
        // error callback
      })
    },

    getMyProjectMembers(projectID) {
      if (projectID > 0) {
        loadProjectMembers(projectID).then(response => {
          this.projectMembers = response.data.result
        })
      }
    },

    loadProjectVersions(projectID) {
      if (projectID > 0) {
        this.$axios.get('/api/project/' + projectID + '/versions').then(response => {
          this.projectVersions = response.data.result.all_versions
        }, response => {
        })
      }
    },

    loadForTesting: function () {
      this.formItem.id = 0
      this.contentData = [{ name: '' }]
      if (this.requirementList && this.requirementList.length > 0) {
        for (let i = 0; i < this.requirementList.length; i++) {
          let tempData = { name: this.requirementList[i].title, id: this.requirementList[i].id }
          this.contentData.push(tempData)
        }
      }
      let defaultTask = this.formItem
      defaultTask.Topic = ''
      defaultTask.Branch = ''
      defaultTask.CodeRepertory = ''
      defaultTask.TestingFeature = ''
      defaultTask.TestingAdvice = ''
      defaultTask.ProjectModuleID = 0
      defaultTask.SelfValidateResult = 0
      defaultTask.attachments.defaultList = []
      defaultTask.attachments.uploadList = []
    }
  },
  created() {
    this.loadMyTestResultFileList()
    this.loadForTesting()
  },
  mounted() {
    this.formItem.attachments.uploadList = this.$refs.upload.fileList
    if (this.projectID > 0) {
      this.formItem.ProjectID = parseInt(this.projectID)
      this.getMyProjectMembers(this.projectID)
      this.loadProjectVersions(this.projectID)
      this.loadProjectInfo(this.projectID)
    }

  },
  watch: {
    requirementList: function (value) {
      this.contentData = [{ name: '' }]
      if (value && value.length > 0) {
        for (let i = 0; i < value.length; i++) {
          let tempData = { name: value[i].title, id: this.requirementList[i].id }
          this.contentData.push(tempData)
        }
      }
    },

    'formItem.ProjectID'(newVal, oldVal) {
      // console.log("newVal=", newVal, oldVal)
      if (newVal != oldVal) {
        if (newVal != undefined) {
          this.formItem.ProjectID = newVal
          this.getMyProjectMembers(newVal)
          this.loadProjectVersions(newVal)
        }
      }
    },

  },
  components: {

    VueEditor
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.ivu-table-cell {
  padding-left: 0px;
}

.demo-upload-list {
  display: inline-block;
  width: 100px;
  height: 100px;
  text-align: center;
  margin: 5px;
  line-height: 60px;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-right: 4px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}

.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}

.demo-upload-list-cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
</style>
