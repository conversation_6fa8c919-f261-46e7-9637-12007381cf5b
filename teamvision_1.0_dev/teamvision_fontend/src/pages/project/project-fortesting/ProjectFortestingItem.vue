<template>
  <div :id="fortesting.id" :key="fortesting.id">
    <Card :padding="0" style="margin-top: 8px;">
      <div class="board-column-item">
        <div class="fortesting-board-item-body">
          <span @click="onViewFortesting" style="color:#5578aa; width: 83%;">
            <Tag> {{ fortesting.id }} </Tag>
            <i>[ {{ fortesting.ProjectName }}-{{ fortesting.VersionName }} ]</i>{{ fortesting.Topic }}
          </span>
          <span style="width: 15%;">
            <Dropdown trigger="click" :transfer="true">
              <Button shape="circle" href="javascript:void(0)" style="font-size:12px;">
                <Icon type="ios-mail-outline" />
                <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem>
                  <Icon type="ios-trending-down" style="padding-right: 10px" />
                  <a style="color: inherit"
                    :href="'/project/' + fortesting.projectID + '/fortesting/' + fortesting.id + '/report/bvt'"
                    target="_blank">BVT</a>
                </DropdownItem>
                <DropdownItem>
                  <Icon type="ios-trending-up" style="padding-right: 10px" />
                  <a style="color: inherit"
                    :href="'/project/' + fortesting.projectID + '/fortesting/' + fortesting.id + '/report/progress'"
                    target="_blank">进度</a>
                </DropdownItem>
                <DropdownItem>
                  <Icon type="ios-unlock-outline" style="padding-right: 10px" />
                  <a style="color: inherit"
                    :href="'/project/' + fortesting.projectID + '/fortesting/' + fortesting.id + '/report/testcomplete'"
                    target="_blank">完成</a>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </span>
        </div>
        <div class="fortesting-board-item-avatar">
          <Tooltip content="发版时间">
            <Tag v-if="fortesting.ReleaseDate !== ''"> {{ fortesting.ReleaseDate }} </Tag>
          </Tooltip>
          <Tag v-if="fortesting.DeadLineFormat" color="default">
            {{ fortesting.DeadLineFormat }}
          </Tag>
          <Tooltip content="开发负责人">
            <Tag v-if="fortesting.ViewData.commitor_name !== ''" color="success"> {{ fortesting.ViewData.commitor_name
              }}
            </Tag>
          </Tooltip>
          <Tooltip content="测试负责人" transfer>
            <Tag v-if="fortesting.ViewData.tester_name !== ''" color="primary"> {{ fortesting.ViewData.tester_name }}
            </Tag>
          </Tooltip>
          <Tooltip v-if="fortesting.SelfValidateResult === -1" transfer content="开发自测结果未通过">
            <Avatar icon="ios-close-circle-outline" style="background-color: #b22c46;">
            </Avatar>
          </Tooltip>
          <Tooltip v-if="fortesting.SelfValidateResult === 0" transfer content="开发自测结果未关联">
            <Avatar icon="ios-information-circle-outline" style="background-color: orange">
            </Avatar>
          </Tooltip>
          <Tooltip v-if="fortesting.SelfValidateResult > 0" transfer content="开发自测结果通过">
            <Avatar icon="ios-checkmark-circle-outline" style="background-color: #87d068">
            </Avatar>
          </Tooltip>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>

export default {
  name: 'ProjectFortestingItem',
  props: ['fortesting'],
  data() {
    return {
      msg: 'Welcome to Your Vue.js App'
    }
  },
  methods: {
    onViewFortesting(event) {
      this.$emit('view-fortesting', this.fortesting.id)
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.ivu-tag {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  margin: 1px 4px 1px 0;
  padding: 0 4px;
  border-radius: 3px;
  font-size: 12px;
  vertical-align: middle;
  opacity: 1;
  overflow: hidden;
}

.board-column-item {
  text-align: justify;
  min-height: 100px;
  max-height: 320px;
  min-width: 260px;
  width: 280px;
  display: flex;
  flex-direction: column;
  /* 垂直排列 */
  justify-content: space-between;
  /* 上下对齐 */
}

.fortesting-board-item-body {
  width: 100%;
  word-wrap: break-word;
  white-space: normal;
  padding: 8px;
  text-align: justify;
  flex: 1;
}

.fortesting-board-item-avatar {
  padding: 8px;
  font-size: 10px;
  text-align: right;
  flex: 1;

  margin-top: auto;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  height: 50px;
}

.fortesting-board-item-body:after {
  // content: "";
  display: inline-block;
  overflow: hidden;
  width: 100%;
}

.fortesting-board-item-body span {
  display: inline-block;
  text-align: left;
  vertical-align: top;
  font-size: 12px;
}
</style>
