<template>
  <div style="height: 100%; padding: 2px;" class="demo">
    <el-card :bordered="true" :dis-hover="true" :body-style="{ padding: '8px' }"
      :style="'height:100%; background-color: #f5f7f9; overflow-x: scroll;'">
      <div v-if="itemViewMode === 'board'" style="white-space: nowrap; text-align: center">
        <board-column v-for="fortestinglist in fortestingForColumns" :key="fortestinglist.id"
          :columnID="fortestinglist.id" :group="fortestinglist.group" v-bind:itemList.sync="fortestinglist.data"
          :columnTitle="fortestinglist.title + '-' + fortestinglist.count" style="border: none" @end="onEnd"
          @reachBottom="onReachBottom">
          <template slot-scope="slotProps">
            <project-forteting-item :fortesting="slotProps.element"
              @view-fortesting="onViewFortesting"></project-forteting-item>
          </template>
        </board-column>
      </div>
      <project-task-state-list v-if="itemViewMode === 'list'" :projectID="projectID"
        @view-fortesting="onViewFortesting">
      </project-task-state-list>
    </el-card>
    <Drawer v-if="showFortestingDetail" v-model="showFortestingDetail" @on-close="isShowFortestingDetail" :inner="true"
      :transfer="false" :width="60" :mask="ture">
      <project-fortesting-info :fortestingID="fortestingItemID" :projectID="projectID" :versionID="versionID">
      </project-fortesting-info>
    </Drawer>
    <project-fortesting-create-dialog :fortestingID="fortestingItemID" :projectID="projectID" :versionID="versionID">
    </project-fortesting-create-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";
import BoardColumn from "../../../components/common/BoardColumn.vue";
import ProjectFortestingCreateDialog from "./ProjectFortestingCreateDialog.vue";
import ProjectFortestingInfo from "./ProjectFortestingInfo.vue";
import ProjectFortetingItem from "./ProjectFortestingItem.vue";
import ProjectTaskStateList from "./ProjectFortestingStateList.vue";
import { getProjectFortestings } from "./ProjectFortesting";

export default {
  name: "projectFortesting",
  props: {
    projectID: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      columnItemHeight: 200,
      fortestingList: [],
      fortestingItemID: 0,
      showFortestingDetail: false,
      viewMode: "board",
    };
  },
  computed: {
    ...mapState(["appBodyHeight", "appBodyMainHeight"]),
    ...mapGetters("projectglobal", ["projectVersion", "objectChange"]),
    ...mapGetters(["itemViewMode"]),

    versionID: function () {
      if (this.projectVersion) {
        return this.projectVersion;
      }
      if (this.$route.params.versionID) {
        return this.$route.params.versionID;
      }
      return 0;
    },

    fortestingForColumns: function () {
      if (this.objectChange === true) {
        this.fortestingList = this.getColumnFortestings();
        this.setObjectChange(false);
      }
      return this.fortestingList;
    },
  },
  methods: {
    ...mapMutations("projectglobal", ["setViewDialogShow", "setObjectChange"]),
    ...mapMutations(["setItemViewMode"]),

    changeViewMode: function (value) {
      //console.log(value)
    },

    getColumnFortestings() {
      let result = [];
      let abandonBackToFortestings = {
        id: "6,7",
        title: "打回/废弃",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };
      let waitForFortestings = {
        id: 1,
        title: "待提测",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };
      let commitedFortestings = {
        id: 2,
        title: "已提测",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };
      let processFortestings = {
        id: 3,
        title: "测试中",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };
      let finishedFortestings = {
        id: 4,
        title: "测试完成",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };
      let releaseFortestings = {
        id: 5,
        title: "已上线",
        group: "ProjectFortesting",
        page: 1,
        count: 0,
        data: [],
      };

      result.push(abandonBackToFortestings);
      result.push(waitForFortestings);
      result.push(commitedFortestings);
      result.push(processFortestings);
      result.push(finishedFortestings);
      result.push(releaseFortestings);

      for (let i = 0; i < result.length; i++) {
        this.getFortestingList(this.projectID, this.versionID, result[i].id, result[i].page).then(function (response) {
          result[i].data = response.data.result.results;
          result[i].count = response.data.result.count;
        });
      }
      return result;
    },
    onStart() {
      //console.log('start')
    },
    onEnd(evt) {
      let toID = evt.to.getAttribute("id");
      let fromID = evt.from.getAttribute("id");
      let itemOldIndex = evt.oldIndex;
      let itemNewIndex = evt.newIndex;
      let itemID = evt.item.getAttribute("id");
      this.alterColumnData(fromID, toID, itemID, itemOldIndex, itemNewIndex);
      this.$axios.patch("/api/project/fortesting/" + itemID + "/update_status", { Status: toID, }).then((response) => {
        if (response.data.result.Status) {
          this.$Message.success({
            content: response.data.result.message,
            duration: 10,
            closable: true,
          });
        }
      },
        (response) => {
          //            this.setObjectChange(true)
          this.$Message.error({
            content: response.data.result.message,
            duration: 10,
            closable: true,
          });
        }
      );
    },

    onRemove() {
      //console.log('remove')
    },

    onMove() {
      //console.log('move')
    },

    alterColumnData(fromID, toID, itemID, itemOldIndex, itemNewIndex) {
      let dragItem = null;
      this.fortestingForColumns.forEach(function (fortestingList, index) {
        if (fortestingList.id === parseInt(fromID)) {
          fortestingList.count = fortestingList.count - 1;
          for (let i = 0; i < fortestingList.data.length; i++) {
            if (fortestingList.data[i].id === parseInt(itemID)) {
              dragItem = fortestingList.data[i];
              fortestingList.data.splice(i, 1);
              break;
            }
          }
        }
      });

      this.fortestingForColumns.forEach(function (fortestingList, index) {
        if (fortestingList.id === parseInt(toID)) {
          fortestingList.count = fortestingList.count + 1;
          fortestingList.data.splice(itemNewIndex, 0, dragItem);
        }
      });
    },

    onReachBottom(columnid) {
      for (let i = 0; i < this.fortestingForColumns.length; i++) {
        if (this.fortestingForColumns[i].id === parseInt(columnid)) {
          let moreFortestings = [];
          let fortestings = this.fortestingForColumns;
          this.fortestingForColumns[i].page =
            this.fortestingForColumns[i].page + 1;
          this.getFortestingList(this.projectID, this.versionID, this.fortestingForColumns[i].id, this.fortestingForColumns[i].page).then(function (value) {
            moreFortestings = value.data.result.results;
            fortestings[i].data.push(...moreFortestings);
          });
          break;
        }
      }
    },

    getFortestingList(projectID, versionID, status, page) {
      let url = "/api/project/" + projectID + "/version/" + versionID + "/fortestings?Status__in=" + status + "&page=" + page;
      let result = getProjectFortestings(url, {}, "get");
      return result;
    },

    onFortestingItemClick(event) {
      this.setCreateDialogShow(true);
      let fortestingID = event.target.getAttribute("id");
      this.fortestingItemID = parseInt(fortestingID);
    },

    onViewFortesting(fortestingID) {
      this.showFortestingDetail = true;
      this.fortestingItemID = parseInt(fortestingID);
    },

    isShowFortestingDetail() {
      this.showFortestingDetail = false;
    },

    getForTestingInfo: function (forTestingID) {
      // getForTesting(forTestingID).then(response => {
      // },)
    },
  },

  created() {
    this.fortestingList = this.getColumnFortestings();
    this.setItemViewMode("board");
  },

  mounted() {
    if (this.$route.params.fortestingId != undefined) {
      this.onViewFortesting(this.$route.params.fortestingId);
    }
  },

  watch: {
    versionID: function (value) {
      this.fortestingList = this.getColumnFortestings();
    },
  },

  components: {
    BoardColumn,
    ProjectFortestingCreateDialog,
    ProjectFortetingItem,
    ProjectFortestingInfo,
    ProjectTaskStateList,
  },

};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
::v-deep .ivu-drawer-body {
  width: 100%;
  height: calc(100% - 51px);
  padding: 0px !important;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  position: absolute;
  overflow: auto;
}
</style>
