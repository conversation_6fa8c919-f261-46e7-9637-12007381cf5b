//初始化项目以及版本信息
import axios from 'axios'
let initProjectVersions = function (tempData) {
  let projectVersions = []
  for (let i = 0; i < tempData.length; i++) {
    let tempProject = {}
    tempProject.value = tempData[i].id
    tempProject.label = tempData[i].PBTitle
    tempProject.children = []
    for (let j = 0; j < tempData[i].Versions.length; j++) {
      let tempChild = {}
      tempChild.label = tempData[i].Versions[j].VVersion
      tempChild.value = tempData[i].Versions[j].id
      tempProject.children.push(tempChild)
    }
    projectVersions.push(tempProject)
  }
  return projectVersions
}

let initFortestingForm = (fortestingID) => {
  let defaultTask = {
    id: fortestingID,
    Topic: '',
    ProjectModuleID: 0,
    CodeRepertory: '',
    Branch: '',
    TestingAdvice: '',
    TestingFeature: '',
    ProjectVersion: [],
    attachments: {
      defaultList: [],
      imgName: '',
      visible: false,
      uploadList: []
    }
  }

  let initPromise = new Promise(function (resolve, reject) {
    axios.get('/api/project/fortesting/' + fortestingID).then(response => {
      let initData = response.data.result
      resolve(initData)
    }, response => {
      resolve(defaultTask)
    })
  })
  return initPromise
}

let fortestingValidateRules = {
  ProjectID: [
    { type: 'integer', required: true, min: 1, message: '请选择项目！', }
  ],
  ProjectModuleID: [
    { type: 'integer', required: true, message: '请选择模块！' }
  ],
  Topic: [
    { type: 'string', required: true, min: 1, max: 50, message: '标题长度必须在1-200个字符之间！', trigger: 'blur' }
  ],
  TestingFeature: [
    { type: 'string', required: true, min: 1, max: 100, message: '提测内容长度必须在1-2000个字符之间' }
  ],
  TestingDeadLineDate: [
    { type: 'date', required: true, message: '预计发版日期不能为空' }
  ],
  TestingAdvice: [
    { type: 'string', required: true, min: 1, max: 2000, message: '提测建议长度必须在1-2000个字符之间' }
  ],
  priority: [
    { required: true, message: '请选择优先级！' }
  ],
  Tester: [
    { type: 'integer', required: true, message: '请选择测试负责人', }
  ],
}

export {
  initProjectVersions,
  fortestingValidateRules,
  initFortestingForm
}
