<template>
  <div :style="'height:' + containerHeight + 'px;overflow-y:auto;'">
    <Row style="height: inherit;">
      <Col span="9" style="">
      <div style="text-align:center;width: 300px; margin-left: auto;margin-right: auto;margin-top:200px">
        <div style="width: 150px">
          <img src="../../assets/teamvision/global/images/logo.jpg"
            style="width: 60px;height: 60px; border-radius: 30px;" />
        </div>
        <div style="text-align: left">
          <span class="appName">Teamvision</span>
        </div>
        <div class="slogan">
          <ul>
            <li>
              <div>
                <Icon size="18" type="md-checkmark" />
                <span>需求协作</span>
              </div>
              <div class="slogan-item-desc">
                <span>需求生命周期管理，度量</span>
              </div>
            </li>
            <li>
              <div>
                <Icon size="18" type="md-checkmark" />
                <span>项目管理</span>
              </div>
              <div class="slogan-item-desc">
                <span>需求，Bug,任务，测试过程管理度量</span>
              </div>
            </li>
            <li>
              <div>
                <Icon size="18" type="md-checkmark" />
                <span>代码构建</span>
              </div>
              <div class="slogan-item-desc">
                <span>代码仓库，构建任务，流水线，发布管理</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
      </Col>
      <Col span="15" style="height:inherit;background-color: #FFFFFF;">
      <div :style="'text-align:center;width: 400px; margin-left: auto;margin-right: auto;margin-top:200px'">
        <Card class="user_login_dialog" :bordered="false" dis-hover>
          <Row type="flex" justify="center">
            <Col>
            <div style="line-height: 30px;">
              <span style="font-size: 22px;letter-spacing: 3px;">登录</span>
            </div>
            <div style="line-height: 30px;">
              <span style="font-size: 12px;color:#323A45">登录到工作台</span>
            </div>
            </Col>
          </Row>
          <Form ref="login" label-position="top" :model="loginForm" :label-width="0" class="login-form"
            :rules="validateRules">
            <FormItem label="" prop="email">
              <Input v-model="loginForm.email" placeholder="登录邮箱" size="default" />
            </FormItem>
            <FormItem label="" prop="Title">
              <Input type="password" v-model="loginForm.password" placeholder="登录密码" size="default" />
            </FormItem>
            <Button class="login-button2" long @click="handleSubmit">登录</Button>
          </Form>
        </Card>
      </div>
      </Col>
      <!-- <Col span="4" offset="4">
      <Card class="user_login_dialog" dis-hover>
        <Form ref="login" :model="loginForm" :label-width="0" :rules="validateRules" style="padding: 20px;">
          <FormItem label="" prop="email">
            <Input type="email" size="large" style="height: 48px;" v-model="loginForm.email" placeholder="登录邮箱" />
          </FormItem>
          <FormItem label="" prop="Title">
            <Input type="password" size="large" style="height: 48px;" v-model="loginForm.password" placeholder="登录密码" />
          </FormItem>
          <Button shape="circle" long @click="handleSubmit">登录</Button>
        </Form>
      </Card>
      </Col> -->
    </Row>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import { loginValidateRules } from "./login";

export default {
  name: "Login",
  props: [],
  data() {
    return {
      loginForm: {
        email: "",
        password: "",
      },
      validateRules: loginValidateRules,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters("systemglobal", ["createDialogShow"]),

    containerHeight: function () {
      return document.body.clientHeight
    },

    routerName: function () {
      return this.$route.name;
    },
    loginBackground: function () {
      let random_num = Math.floor(Math.random() * (10 - 1)) + 1;
      return "url(/static/global/images/login/login" + random_num + ".jpg)";
    },
  },

  methods: {
    ...mapMutations("usercenter", ["setLogin", "setUserInfo",]),
    ...mapMutations("systemglobal", ["setCreateDialogShow", ""]),
    ...mapActions('usercenter', ['handleLogin', 'getUserInfo']),

    updateProfiles: function () { },
    changeAvatar: function () { },

    handleSubmit: function () {
      this.handleLogin(this.loginForm).then(res => {
        if (res.result.login === true) {
          this.getUserInfo().then(res => {
            //console.log("this.$route=", this.$route)
            try {
              if (this.$route.query.redirect) {
                let url = decodeURIComponent(this.$route.query.redirect)
                this.$router.push({ path: url });
              } else {
                this.$router.push({ path: '/' });
              }
            } catch (err) {
              this.$router.push({ path: '/' })
            }
          })
        } else {
          this.$Message.error({
            content: res.result.message,
            duration: 3,
            closable: true
          })
        }
      })
    },

  },

  created: function () { },

  mounted: function () { },

  watch: {},

  components: {},
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.slogan {
  font-family: '幼圆' !important;
  //font-size: 35px !important;
  margin-bottom: 25px;
  display: block;
  font-weight: 400;
  //text-align: center;
  letter-spacing: 6px;
  color: #323A45;
}

.slogan ul {
  text-align: left;
  list-style: none;
}

.slogan ul li {
  line-height: 40px;
}

.slogan-item-desc {
  margin-left: 35px;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 1px;
  color: #323A45;
}

.appName {
  font-family: 'Dotum' !important;
  font-size: 24px;
  font-weight: 400;
  text-align: left;
  color: #323A45;
  width: 270px;
  letter-spacing: 2px;
}

.user_login_dialog {
  width: 480px;
  height: 360px;
  border-radius: 10px;
  background: #FFFFFF;
  color: #0C0C0C;
}

.login-form {
  width: 100%;
  height: 100%;
  //margin-top: -130px;
  padding: 20px 50px 50px 50px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  position: absolute;
  color: #0C0C0C;
}

.login-button2 {
  background-color: #515a6e !important;
  /*border-radius: 10px;*/
  border-color: #202d40;
  width: 100%;
  font-size: 14px;
  color: #f0f5ff;
  height: 32px;
  padding-top: 0px;
  margin-top: 20px;
}

.ivu-input-large {
  font-size: 14px;
  padding: 6px 7px;
  height: 48px;
}
</style>

<style lang="less">
.login-form .ivu-input-large {
  font-size: 16px;
  padding: 6px 7px;
  height: 48px;
}
</style>
