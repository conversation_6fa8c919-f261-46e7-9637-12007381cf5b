let webhookValidateRules = {
  name: [
    { type: 'string', required: true, min: 1, max: 60, message: '长度4-32位字符串' }
  ],
  key:[
    { type: 'string', required: true, min: 8, max: 8, message: '长度8位字符串' }
  ],
  callback_url: [
    { type: 'string', required: true, min: 10, max: 200, message: '长度10-200位字符串' }
  ],
  auth_key: [
    { type: 'string', required: true, min: 8, max: 64, message: '长度8-64位字符串' }
  ],
  class_name: [
    {required: true,}
  ],
  attribute_name: [
    {required: true,}
  ],
}

export {
  webhookValidateRules
}
