<template>
  <div :style="'padding-top:2px;max-height:' + containerHeight + 'px;overflow:scroll'">
    <Table size="small" :columns="columns1" :data="webhookList">
      <template slot-scope="{ row, index }" slot="active">
        <i-switch :value="row.is_active" size="small" @on-change="changeActive(index)"> </i-switch>
      </template>
      <template slot-scope="{ row, index }" slot="delete">
        <!-- <Button icon="md-trash"  @click="deleteWebHook(index)" shape="circle"></Button> -->
        <Button size="small" icon="md-trash" @click="deleteWebHook(index)">Delete</Button>
        <Button size="small" icon="md-build" @click="showdialog(index)">Edit</Button>
      </template>
    </Table>
    <Modal v-model="dialogShow" title="新建WebHook" :width="600" @on-cancel="cancel"
      :styles="{ bottom: '20px', top: '50px' }">
      <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
        <Form ref="webHookForm" :model="formData" :label-width="80" :rules="ruleCustom">
          <FormItem label="名称" prop="name">
            <Input v-model="formData.name" maxlength="60" show-word-limit placeholder="名称" />
          </FormItem>
          <FormItem label="唯一Key" prop="key">
            <Row>
              <Col span="18">
              <Input v-model="formData.key" maxlength="8" show-word-limit placeholder="唯一Key" />
              </Col>
              <Col span="4" offset="1">
              <Button type="primary" @click="createKey">随机生成</Button>
              </Col>
            </Row>
          </FormItem>
          <FormItem label="回调Url" prop="callback_url">
            <Input v-model="formData.callback_url" maxlength="200" show-word-limit placeholder="回调Url" />
          </FormItem>
          <FormItem label="回调Key" prop="auth_key">
            <Input v-model="formData.auth_key" maxlength="60" show-word-limit placeholder="认证Key" />
          </FormItem>
          <FormItem label="类名称" prop="class_name">
            <Select v-model="formData.class_name" :max-tag-count="2" @on-change="changeAttribute">
              <Option v-for="item in classList" :key="item.id" :value="item.class_name">{{ item.class_name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="属性" prop="attribute_name">
            <Select v-model="formData.attribute_name" transfer filterable placeholder="属性名">
              <Option v-for="item in attributeList" :key="item.id" :value="item.attribute_name">{{ item.attribute_name
                }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="触发状态" prop="status">
            <CheckboxGroup>
              <Checkbox label="新建" v-model="formData.is_create"></Checkbox>
              <Checkbox label="修改" v-model="formData.is_modified"></Checkbox>
              <Checkbox label="删除" v-model="formData.is_del"></Checkbox>
            </CheckboxGroup>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button v-if="is_create" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="createWebHook()">添加</Button>
        <Button v-if="!is_create" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="updateWebHook()">更新</Button>
        <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { webhookValidateRules } from './WebHook'
import { createRandom } from '../../../libs/util'

export default {
  name: 'WebHook',
  props: [''],
  data() {
    return {
      columns1: [
        {
          title: 'Id',
          key: 'id',
          align: 'center',
          width: 60,
        },
        {
          title: '创建时间',
          key: 'created_time',
          align: 'center',
          width: 180,
        },
        {
          title: '名称',
          key: 'name',
          align: 'center',
        },
        {
          title: 'Key',
          key: 'key',
          align: 'center',
          width: 120,
        },
        {
          title: '回调Url',
          key: 'callback_url',
          align: 'center',
        },
        {
          title: '认证Key',
          key: 'auth_key',
          align: 'center',
        },
        {
          title: '类名',
          key: 'class_name',
          align: 'center',
        },
        {
          title: '属性名',
          key: 'attribute_name',
          align: 'center',
        },
        {
          title: '触发条件',
          align: 'center',
          children: [
            {
              title: '新建',
              key: 'is_create',
              align: 'center',
              width: 80,
            },
            {
              title: '修改',
              key: 'is_modified',
              align: 'center',
              width: 80,
            },
            {
              title: '删除',
              key: 'is_del',
              align: 'center',
              width: 80,
            },
          ]
        },
        {
          title: '开启/关闭',
          key: 'is_active',
          slot: 'active',
          align: 'center',
          width: 100,
        },
        {
          title: '删除/修改',
          key: 'is_active',
          slot: 'delete',
          align: 'center',
          width: 180,
        },
      ],
      webhookList: [],
      formData: {
        id: 0,
        name: '',
        key: '',
        created_by: 0,
        callback_url: '',
        auth_key: '',
        class_name: '',
        attribute_name: '',
        is_create: false,
        is_modified: false,
        is_del: false
      },
      ruleCustom: {
        ...webhookValidateRules
      },
      classList: [],
      attributeList: [],
      is_create: true
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapState(['appBodyMainHeight',]),
    ...mapState('systemglobal', ['createDialogShow']),
    ...mapGetters('systemglobal', ['systemAdminearchKey']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },
    dialogShow: function () {
      if (this.$route.name == 'systemWebHook') {
        return this.createDialogShow
      }
      return false
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),
    ...mapMutations('user', ['setShowUserDetail']),

    getWebHookList() {
      this.$axios.get('/api/webhook/list').then(response => {
        this.webhookList = response.data.result
      }
      )
    },

    getClassAttribute() {
      this.$axios.get('/api/webhook/class/list').then(response => {
        this.classList = response.data.result
      }
      )
    },

    changeAttribute() {
      for (let i = 0; i < this.classList.length; i++) {
        if (this.formData.class_name == this.classList[i].class_name) {
          this.attributeList = this.classList[i].attribute
        }
      }
    },

    changeActive(index) {
      let webhookActive = {
        "is_active": !this.webhookList[index].is_active
      }
      this.$axios.patch('/api/webhook/' + this.webhookList[index].id, webhookActive).then(response => {
        this.$Message.info('开启状态：' + response.data.result.is_active);
        this.webhookList[index].is_active = response.data.result.is_active
      }
      )
    },

    createWebHook() {
      let webHook = {
        "name": this.formData.name,
        "key": this.formData.key,
        "created_by": this.userInfo.id,
        "callback_url": this.formData.callback_url,
        "auth_key": this.formData.auth_key,
        "class_name": this.formData.class_name,
        "attribute_name": this.formData.attribute_name,
        "is_create": this.formData.is_create,
        "is_modified": this.formData.is_modified,
        "is_del": this.formData.is_del
      }
      this.$axios.post('/api/webhook/list', webHook).then(response => {
        let new_webhook = response.data.result
        this.webhookList.push(new_webhook)
        this.setCreateDialogShow(false)
        this.$refs['webHookForm'].resetFields()
      }, response => {
        this.$Message.info('创建失败');
      }
      )

    },

    deleteWebHook(index) {
      this.$Modal.confirm({
        title: '删除确认',
        onOk: () => {
          this.$axios.delete('/api/webhook/' + this.webhookList[index].id).then(response => {
            this.webhookList.splice(index, 1)
            this.$Message.success({
              content: '已经成功删除',
              duration: 3,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '删除失败',
              duration: 3,
              closable: true
            })
          })

        }
      })
    },

    showdialog(index) {
      this.is_create = false;
      this.setCreateDialogShow(true);
      //Object.assign(this.formData, this.webhookList[index])
      this.formData = this.webhookList[index];
      for (let i = 0; i < this.classList.length; i++) {
        if (this.formData.class_name == this.classList[i].class_name) {
          this.attributeList = this.classList[i].attribute;
        }
      }
    },

    updateWebHook() {
      let webHook = {
        "name": this.formData.name,
        "key": this.formData.key,
        "created_by": this.userInfo.id,
        "callback_url": this.formData.callback_url,
        "auth_key": this.formData.auth_key,
        "class_name": this.formData.class_name,
        "attribute_name": this.formData.attribute_name,
        "is_create": this.formData.is_create,
        "is_modified": this.formData.is_modified,
        "is_del": this.formData.is_del
      }
      this.$axios.put('/api/webhook/' + this.formData.id, webHook).then(response => {
        let new_webhook = response.data.result
        for (let i = 0; i < this.webhookList.length; i++) {
          if (this.webhookList[i].id == new_webhook.id) {
            this.webhookList[i] = new_webhook
          }
        }
        this.$Message.info('修改成功');
        this.setCreateDialogShow(false)
        this.is_create = true;
        this.initFormData()
      }, response => {
        this.$Message.info('创建失败');
      }
      )

    },

    initFormData() {
      this.formData = {
        name: '',
        created_by: 0,
        callback_url: '',
        auth_key: '',
        class_name: '',
        attribute_name: '',
        is_create: false,
        is_modified: false,
        is_del: false
      }
    },
    cancel() {
      this.setCreateDialogShow(false)
      this.is_create = true;
      this.initFormData()
      //this.$refs['webHookForm'].resetFields()
    },

    createKey() {
      this.formData.key = createRandom(8)
    }

  },

  created: function () {
    this.getWebHookList()
    this.getClassAttribute()
  },

  mounted: function () {

  },

  watch: {
  },

  components: {
  }
}
</script>
