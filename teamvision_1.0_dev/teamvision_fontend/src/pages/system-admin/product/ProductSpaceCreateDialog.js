//初始化项目以及版本信息
import axios from 'axios'


const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('Please enter your password'));
  } else {
    // callback(new Error('空间KEY已经存在'));
    callback()
  }
};

let spaceValidateRules={
  Title: [
    { type: 'string', required: true, min: 1, max: 6, message: '长度1-6位字符串' }
  ],
  Key: [
    { type: 'string', required: true, min: 1, max: 10, message: '标示Key长度必须在1-10个字符之间！', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ]
}

export {
  spaceValidateRules
}
