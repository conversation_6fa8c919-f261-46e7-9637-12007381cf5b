<template>
  <div>
    <div :style="'padding-top:16px;max-height:' + containerHeight + 'px;overflow:scroll'">
      <Card v-for="product in products" :padding="10" :key="product.id" :bordered="false" dis-hover
        class="member-card cursor-hand">
        <Row>
          <Col :xl="4" :lg="4" :md="12" :sm="12">
          <span style="font-weight: bold;">
            <span style="float: left;margin-right: 10px;">
              <Icon type="ios-pricetag" :color="product.LabelColor" :size="24" />
            </span>
            <label-editor-input style="max-width: 400px;float: left;" :maxLength="6" @updateValue="updateProductTitle"
              placeHolder="产品线名称" :id="product.id" :displayText="product.Title"></label-editor-input>
          </span>
          </Col>
          <Col :xl="16" :lg="16" :md="6" :sm="6">
          <span>
            <label-editor-select @updateValue="updateSpaceAdmin" :optionList="editorUserlist"
              :displayText="product.ViewData.SpaceAdmin" :value="product.SpaceAdmin" :itemID="product.id">
              <template slot-scope="slotProps">
                <span style="width: 22px;display: inline-block">
                  <Icon v-if="slotProps.mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
                </span>
                <span style="font-size: 12px;">
                  <Tooltip transfer content="空间管理员">
                    <Avatar style="background-color:#22aaaa">{{ product.ViewData.SpaceAdmin }}</Avatar>
                  </Tooltip>
                </span>
              </template>
            </label-editor-select>
          </span>
          </Col>
          <Col :xl="4" :lg="4" :md="6" :sm="6">
          <!--<span>-->
          <!--<Dropdown transfer @on-click="deleteTag">-->
          <!--<a href="javascript:void(0)" style="color: inherit;">-->
          <!--<span>-->
          <!--<Icon :size="24" type="ios-more" />-->
          <!--</span>-->
          <!--</a>-->
          <!--<DropdownMenu slot="list">-->
          <!--<DropdownItem :name="product.id+':'+product.PTitle"><Icon type="ios-trash-outline" />-->
          <!--<span >删除</span>-->
          <!--</DropdownItem>-->
          <!--</DropdownMenu>-->
          <!--</Dropdown>-->
          <!--</span>-->
          </Col>
        </Row>
      </Card>
    </div>

    <Modal :value="createDialogShow" title="新建空间" :width="600" @on-cancel="cancel" :styles="{ bottom: '20px', top: '50px' }">
      <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
        <Form ref="createSpace" :model="formData" :label-width="80" :rules="ruleCustom">
          <FormItem label="名称" prop="Title">
            <Input v-model="formData.Title" maxlength="6" show-word-limit placeholder="空间名称" />
          </FormItem>
          <FormItem label="标示" prop="Key">
            <Input v-model="formData.Key" maxlength="10" show-word-limit placeholder="空间标示" />
          </FormItem>
          <FormItem label="管理员">
            <Select v-model="formData.SpaceAdmin" transfer filterable placeholder="默认为创建者">
              <Option v-for="user in userList" :key="user.id" :value="user.id">{{ user.name }}
              </Option>
            </Select>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button type="success" style="width: 80px; height:30px;" shape="circle"
          @click="createProductSpace('createSpace')">添加
        </Button>
        <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'
import labelEditorSelect from '../../../components/common/LabelEditor-Select.vue'
import { spaceValidateRules } from './ProductSpaceCreateDialog'


export default {
  name: 'Product',
  props: [''],
  data() {
    return {
      products: [],
      userList: [],
      editorUserlist: [],
      formData: {
        Title: '',
        Key: '',
        SpaceAdmin: 0,
        LabelColor: '',
        Creator: 0
      },
      ruleCustom: {
        ...spaceValidateRules
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('projectglobal', ['createDocumentType']),
    ...mapGetters('document', ['breadNav']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyMainHeight - 130
    }

  },

  methods: {

    ...mapMutations('systemglobal', ['setCreateDialogShow', 'setViewDialogShow', 'setObjectChange']),


    ok(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.setCreateDialogShow(false)
          this.$axios.post('/api/project/' + this.formItem.Project[0] + '/version/' + this.formItem.Project[1] + '/issues', this.formItem).then(response => {
            this.setIssueChange(true)
            this.formItem.Title = ' '
            this.formItem.Desc = '<p>步骤:</p></br><p>实际结果:</p></br><p>期望结果:</p>'
            this.formItem.uploadList = []
          }, response => {
          })
          this.$refs[name].resetField()
        }
      })
    },

    updateProductTitle: function (value, id) {
      let parameters = { Title: value }
      if (value !== '') {
        this.updateSpaceProperty(id, parameters, "空间名称")
      }
    },

    cancel() {
      this.setCreateDialogShow(false)
    },

    updateSpaceAdmin: function (newValue, oldValue, id) {
      let parameters = { SpaceAdmin: newValue }
      if (newValue !== 0 && newValue !== oldValue) {
        this.updateSpaceProperty(id, parameters, "空间管理员")
        //        for(let i=0; i<this.products.length; i++) {
        //          if(this.products[i].id === id) {
        //            this.products[i].SpaceAdmin = newValue
        //            this.products[i].ViewData.SpaceAdmin = this.getUserName(newValue)
        //          }
        //        }
      }
    },

    getUserName: function (userID) {
      let userName = "--"
      for (let i = 0; i < this.userList.length; i++) {
        if (this.userList[i].id === userID) {
          userName = this.userList[i].name
        }
      }
      return userName
    },

    updateSpaceProperty: function (id, parameters, propertyName) {
      this.$axios.patch('/api/project/product/' + id, parameters).then(response => {
        for (let i = 0; i < this.products.length; i++) {
          if (this.products[i].id === parseInt(id)) {
            this.products.splice(i, 1, response.data.result)
            break
          }
        }
        this.$Message.success({
          content: propertyName + '更新成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: propertyName + '标题更新失败',
          duration: 10,
          closable: true
        })
      })
    },


    getUserList: function () {
      this.$axios.get('/api/common/users/list').then(response => {
        this.userList = response.data.result
        for (let i = 0; i < this.userList.length; i++) {
          let tempItem = response.data.result[i]
          tempItem['label'] = tempItem.name
          tempItem['id'] = tempItem.id
          this.editorUserlist.push(tempItem)
        }
      }, response => {
        // error callback
      })
    },

    createProductSpace: function (name) {
      this.formData.LabelColor = this.randomColor()
      this.formData.Creator = this.userInfo.id
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.post('/api/project/product_spaces', this.formData).then(response => {
            this.products.push(response.data.result)
            this.setCreateDialogShow(false)
            this.$Message.success({
              content: '产品添加成功',
              duration: 10,
              closable: true
            })
            this.$refs[name].resetField()
          }, response => {
            this.setCreateDialogShow(false)
            this.$Message.error({
              content: '产品添加失败',
              duration: 10,
              closable: true
            })
          })
        }
      })
    },

    randomColor: function () {
      let colorList = ['#339933', '#99cc66', '#ffff99', '#669966', '#66cc99', '#999966', '#666699,', '#339933', '#66cc66', '#99b3ff', '#a6bcff', '#00b2b3', '#c299ff', '#e0ccff', '#ecb3ff', '#009999', '#b2b300', '#cccc8f', '#9494b8', '#c2c2f0']
      let index = Math.floor(Math.random() * 20)
      if (this.products.length > 0) {
        index = (this.products.length + 1) % 20
      }
      return colorList[index]
    },

    getProducts: function () {
      this.$axios.get('/api/project/products').then(response => {
        this.products = response.data.result
      }, response => {
      })
    }


  },

  created: function () {
    this.getProducts()
    this.getUserList()
  },

  mounted: function () {
  },

  watch: {
  },

  components: {
    labelEditorInput,
    labelEditorSelect
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 50px;
  /*margin-right: 16px;*/
  /*margin-left: 16px;*/
  margin-bottom: 2px;
}
</style>
