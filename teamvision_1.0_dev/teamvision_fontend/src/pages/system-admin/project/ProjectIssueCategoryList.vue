<template>
  <div style="padding: 16px;">
    <div style="width: 60%;margin-left: auto;margin-right: auto; margin-bottom: 16px;">
      <Input v-model="formData.Name" search enter-button="添加"  @on-search="createCategory" icon="ios-clock-outline" :maxlength="50" placeholder="输入问题分类名称，回车创建" style="width: 100%" />
    </div>
    <div :style="'padding-top:16px;max-height:'+ containerHeight+ 'px;overflow:scroll'">
      <Card  v-for="category in issueCategories"  :padding="10" :key="category.id" :bordered="false" dis-hover class="member-card cursor-hand">
        <Row>
          <Col :xl="20" :lg="20" :md="18" :sm="18">
          <span style="font-weight: bold;">
            <span style="float: left;margin-right: 10px;">
              #{{ category.id }}
            </span>
             <label-editor-input style="max-width: 400px;float: left;"  @updateValue="updateCategoryTitle" placeHolder="产品线名称" :id="category.id" :displayText="category.Name"></label-editor-input>
          </span>
          </Col>
          <Col :xl="4" :lg="4" :md="6" :sm="6">
            <!--<span>-->
            <!--<Dropdown transfer @on-click="deleteTag">-->
            <!--<a href="javascript:void(0)" style="color: inherit;">-->
            <!--<span>-->
            <!--<Icon :size="24" type="ios-more" />-->
            <!--</span>-->
            <!--</a>-->
            <!--<DropdownMenu slot="list">-->
            <!--<DropdownItem :name="product.id+':'+product.PTitle"><Icon type="ios-trash-outline" />-->
            <!--<span >删除</span>-->
            <!--</DropdownItem>-->
            <!--</DropdownMenu>-->
            <!--</Dropdown>-->
            <!--</span>-->
          </Col>
        </Row>
      </Card>
    </div>
  </div>
</template>

<script>
  import { mapState, mapGetters, mapMutations } from 'vuex'
  import labelEditorInput from '../../../components/common/LabelEditor-Input.vue'

  export default {
    name: 'IssueCategory',
    props: [''],
    data () {
      return {
        issueCategories: [],
        formData: {
          Name: '',
          Value: 0,
          Project: 0,
          IsActive: true
        }
      }
    },
    computed: {
      ...mapState(['appBodyMainHeight',]),
      ...mapGetters('projectglobal', ['createDocumentType']),
      ...mapGetters('document', ['breadNav']),
      containerHeight: function () {
        return this.appBodyMainHeight-130
      }

    },

    methods: {

      updateCategoryTitle: function (value, id) {
        let parameters = {Name: value}
        if (value !== '') {
          this.$axios.patch('/api/project/issue/category/' + id , parameters).then(response => {
            for (let i = 0; i < this.issueCategories.length; i++) {
              if (this.issueCategories[i].id === parseInt(id)) {
                this.issueCategories.splice(i, 1, response.data.result)
                break
              }
            }
            this.$Message.success({
              content: '标题更新成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '标题更新失败',
              duration: 10,
              closable: true
            })
          })
        }

      },

      createCategory: function () {
        if (this.formData.Name.trim() !== '') {
          this.formData.Value = this.issueCategories.length+1
          this.$axios.post('/api/project/issue/categories', this.formData).then(response => {
            this.issueCategories.push(response.data.result)
            this.$Message.success({
              content: '问题分类添加成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '问题分类添加失败',
              duration: 10,
              closable: true
            })
          })
        } else {
          this.$Message.error({
            content: '问题分类名称不能为空',
            duration: 10,
            closable: true
          })
        }
      },

      loadIssueCategories: function () {
        this.$axios.get('/api/project/issue/categories').then(response => {
          this.issueCategories = response.data.result
        }, response => {
        })
      },


    },

    created: function () {
      this.loadIssueCategories()
    },

    mounted: function () {
    },

    watch: {
    },

    components: {
      labelEditorInput
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

  .member-card {
    width: 100%;
    height: 50px;
    /*margin-right: 16px;*/
    /*margin-left: 16px;*/
    margin-bottom: 2px;
    color: #5578aa;
  }

</style>
