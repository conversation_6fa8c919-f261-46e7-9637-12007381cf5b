<template>
  <div>
    <Row>
      <Col :lg="3" :sm="6" :xs="8">
        <Card title="项目全局配置" icon="ios-options" :padding="16" shadow>
          <CellGroup>
            <Cell title="问题类别" :selected="routerName === 'systemProjectIssueCategory'" to="/system/project/issueCategory">
              <Icon slot="icon" type="ios-bug" :size="20" />
            </Cell>
            <Cell title="问题阶段" :selected="routerName === 'systemProjectIssuePhrase'" to="/system/project/issuePhrase">
              <Icon slot="icon" type="ios-basket" :size="20" />
            </Cell>
          </CellGroup>
        </Card>
      </Col>

      <Col :lg="21" :sm="18" :xs="16">
        <issue-category-list v-if="routerName === 'systemProjectIssueCategory'"></issue-category-list>
        <issue-phrase-list v-if="routerName === 'systemProjectIssuePhrase'"></issue-phrase-list>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import IssueCategoryList from './ProjectIssueCategoryList.vue'
import IssuePhraseList from './ProjectIssuePhraseList.vue'

export default {
  name: 'UserAdmin',
  props: [],
  data () {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formItem.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0 ) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }
    return {
      userList: [],
      groups: [],
      showUserDetail: false,
      selectedUser: 0,
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyHeight
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),

    cancel: function () {
      this.setCreateDialogShow(false)
    },

    addUser: function () {
      this.$axios.post('/api/auth/users', this.formItem).then(response => {
        this.userList.push(response.data.result)
        this.$Message.success({
          content: '成员用户成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '用户添加失败',
          duration: 10,
          closable: true
        })
      })
      this.setCreateDialogShow(false)
    },

    onSelectUser: function (value) {
      this.selectedUser = value
      this.showUserDetail = true
    },

    onCloseDetailPanel: function () {
      this.showUserDetail = false
      this.selectedUser = 0
    },

    userExists: function (email) {
      let result = false
      for (let i =0; i< this.userList.length; i++) {
        if (email.trim() === this.userList[i].email) {
          result = true
        }
      }
      return result
    },

    getUserList: function () {
      this.$axios.get('/api/auth/users').then(response => {
        this.userList = response.data.result
      }, response => {
        // error callback
      })
    },

    onModalStatusChange: function () {
      this.initFormItem()
    },

    initFormItem: function () {
      this.formItem = {
        email: '',
        password: '',
        confirmPassword: '',
        last_name: '',
        first_name: ''
      }
    }
  },

  created: function () {
    this.getUserList()
  },

  mounted: function () {

  },

  watch: {},

  components: {
    IssueCategoryList,
    IssuePhraseList
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

  .member-card {
    width: 100%;
    height: 45px;
    margin-right: 16px;
    margin-left: 16px;
    margin-bottom: 2px;
  }

  .member-avatar {
    height:20px;
    width: 20px;
    border-radius: 10px;
    padding-bottom: 2px;
    margin-right: 15px;
  }

  .member-tag {
    font-size:10px;
    height: 18px;
    line-height: 18px;
  }

</style>
