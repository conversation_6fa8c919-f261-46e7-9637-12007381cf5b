<template>
  <div class="app-body-head-default">
    <div class="app-body-header-bar-default">
      <Row>
        <Col :lg="16" :sm="18">
        <div class="app-body-header-leftbar-default pull-left">
          <ul class="app-body-head-menu">
            <router-link v-if="userInfo.system_permision < 2" to="/system/setting" :exact="false" tag="li"
              active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
              <a href=""><i class="fa fa-fw  fa-cog"></i>系统设置</a>
            </router-link>
            <router-link to="/system/user" :exact="false" tag="li" active-class="app-body-head-menu-item-active"
              class="app-body-head-menu-item">
              <a href=""><i class="fa fa-fw  fa-user"></i>用户</a>
            </router-link>
            <!--<router-link to="/system/usergroup" tag="li" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">-->
            <!--<a :href="'/project/'+projectID+'/fortesting'" >-->
            <!--<i class="fa fa-fw  fa-bus"></i>用户组-->
            <!--</a>-->
            <!--</router-link>-->
            <router-link v-if="userInfo.system_permision < 2" to="/system/product" tag="li"
              active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
              <a href=""><i class="fa fa-fw  fa-bus"></i>空间</a>
            </router-link>
            <span v-if="userInfo.system_permision < 2">
              <router-link v-if="$route.name === 'systemProjectIssuePhrase'" to="/system/project/issuePhrase" tag="li"
                :exact="false" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                <a style="color:inherit" href="">
                  <Icon size="18" type="ios-bonfire" /> 项目
                </a>
              </router-link>
              <router-link v-else="$route.name ==='systemProjectIssueCategory'" to="/system/project/issueCategory"
                tag="li" :exact="false" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
                <a style="color:inherit" href="">
                  <Icon size="18" type="ios-bonfire" />项目
                </a>
              </router-link>
            </span>
            <router-link to="/system/webhook" :exact="false" tag="li" active-class="app-body-head-menu-item-active"
              class="app-body-head-menu-item">
              <a href="">
                <Icon size="18" type="logo-wordpress" />WebHook
              </a>
            </router-link>
            <router-link to="/system/message/config" :exact="false" tag="li"
              active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
              <a href="">
                <Icon size="18" type="ios-paper-plane" />消息
              </a>
            </router-link>
          </ul>
        </div>
        </Col>
        <Col :lg="2" :sm="0" :xs="0">
        <div class="pull-left" style="padding-top: 20px;">
          <div v-if="headMenu.searchBox">
            <Input v-model="systemAdminearchKey" @on-keyup="searchObject" search size="small" style="width:200px;"
              placeholder="输入用户名，邮箱查找" />
          </div>
        </div>
        </Col>
        <Col :lg="6" :sm="6" :xs="6">
        <div class="app-body-header-rightbar-default pull-right">
          <span @click="newObject" v-if="headMenu.newObject">
            <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
          </span>
        </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapState } from 'vuex'

export default {
  components: {
  },
  name: 'SystemAdminHead',
  props: ['projectID'],
  data() {
    return {
      systemAdminearchKey: ''
    }
  },

  computed: {
    ...mapState(['appHeadShow', 'dynamicMenu', 'login']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['headMenu'])

  },
  methods: {
    ...mapMutations(['setDynamicMenu']),
    ...mapMutations('systemglobal', ['setCreateDialogShow', 'setSystemAdminearchKey']),

    newObject() {
      this.setCreateDialogShow(true)
    },

    searchObject: function () {
      this.setSystemAdminearchKey(this.systemAdminearchKey)
    }
  },
  created: function () {
  },

  watch: {},

  // beforeRouteUpdate (to, from, next) {
  //   this.setDynamicMenu({})
  //   next()
  // },

  beforeRouteLeave(to, from, next) {
    this.setDynamicMenu({})
    next()
  }

}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import '../../layout/appBody';
@import '../../layout/appHead';
@import '../../assets/teamvision/global/less/global';
</style>
