<template>
  <div>
    <Row>
      <Col :lg="3" :sm="6" :xs="8">
        <Card title="管理" icon="ios-options" :padding="16" shadow>
          <CellGroup>
            <Cell title="用户" :selected="routerName === 'systemAdminUser'" to="/system/user">
              <Icon slot="icon" type="ios-person" :size="20" />
            </Cell>
            <!--<Cell title="用户组" :selected="routerName === 'systemAdminUserGroup'" to="/system/usergroup">-->
              <!--<Icon slot="icon" type="ios-people" :size="20" />-->
            <!--</Cell>-->
          </CellGroup>
        </Card>
      </Col>
      <Col :lg="21" :sm="18" :xs="16">
        <user-list  v-if="routerName === 'systemAdminUser'" @on-selectUser="onSelectUser" :users="userList"></user-list>
        <user-group v-if="routerName === 'systemAdminUserGroup'" :groups="groups"></user-group>
      </Col>
    </Row>
    <Modal :value="createDialogShow" title="添加用户" :width="600" @on-visible-change="onModalStatusChange" @on-cancel="cancel" :styles="{bottom:'20px',top: '50px'}">
      <div>
        <i-form ref="createAccount" :model="formItem" :label-width="80" :rules="ruleCustom">
        <!--<Form ref="createAccount" :model="formItem" :label-width="80" :rules="ruleCustom">-->
          <FormItem label="空间" prop="space">
            <Select v-model="formItem.space" :filterable="true" placeholder="团队空间">
              <Option v-for="product in productSpaces" :key="product.id" :value="product.id">{{ product.Title }}</Option>
            </Select>
          </FormItem>
          <FormItem label="邮箱" prop="email">
            <Input  type="email" v-model="formItem.email" placeholder="用户邮箱"/>
          </FormItem>
          <FormItem label="密码" prop="password">
            <Input type="password" v-model="formItem.password" placeholder="密码"/>
          </FormItem>
          <FormItem label="确认密码" prop="confirmPassword">
            <Input type="password" v-model="formItem.confirmPassword" placeholder="确认密码"/>
          </FormItem>
          <FormItem label="姓氏" prop="last_name">
            <Input v-model="formItem.last_name" placeholder="用户姓氏"/>
          </FormItem>
          <FormItem label="名字" prop="last_name">
            <Input v-model="formItem.first_name" placeholder="用户名字"/>
          </FormItem>
        <!--</Form>-->
        </i-form>
      </div>
      <div slot="footer">
        <Button  type="success" style="width: 80px; height:30px;" @click="addUser('createAccount')" shape="circle">添加
        </Button>
        <Button type="default"  style="width: 80px; height:30px;"  shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
    <div v-if='showUserDetail'>
      <Drawer :value="showUserDetail" @on-close="onCloseDetailPanel" :inner="true" :transfer="false" :width="50" :mask="false">
      <div slot style=""></div>
      <user-detail :userID="selectedUser" :userList="userList" :spaceList="productSpaces"></user-detail>
    </Drawer>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex'
import UserList from './UserList.vue'
import UserGroup from './UserGroup.vue'
import { userValidateRules } from './SystemAdminCreate'
import UserDetail from './UserDetail.vue'

export default {
  name: 'UserAdmin',
  props: [],
  data () {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formItem.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0 ) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }
    return {
      userList: [],
      groups: [],
      showUserDetail: false,
      selectedUser: 0,
      productSpaces: [],
      formItem: {
        email: '',
        password: '',
        confirmPassword: '',
        last_name: '',
        first_name: '',
        space: 0
      },
      ruleCustom: {
        ...userValidateRules,
        confirmPassword: [
          { validator: validatePassCheck, trigger: 'blur' }
        ],
        email: [
          { type: 'email',required: true, validator: validateEmailCheck, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),

    containerHeight: function () {
      return this.appBodyMainHeight
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),

    cancel: function () {
      this.setCreateDialogShow(false)
    },

    addUser: function (userfrom) {
      this.$refs[userfrom].validate((valid) => {
        if (valid) {
          this.$axios.post('/api/auth/users', this.formItem).then(response => {
            this.userList.push(response.data.result)
            this.$Message.success({
              content: '成员用户成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '用户添加失败',
              duration: 10,
              closable: true
            })
          })
          this.setCreateDialogShow(false)
        }
      })
    },

    onSelectUser: function (value) {
      this.selectedUser = value
      this.showUserDetail = true
    },

    onCloseDetailPanel: function () {
      this.showUserDetail = false
      //this.selectedUser = 0
    },

    userExists: function (email) {
      let result = false
      for (let i =0; i< this.userList.length; i++) {
        if (email.trim() === this.userList[i].email) {
          result = true
        }
      }
      return result
    },

    getUserList: function () {
      this.$axios.get('/api/common/users/list').then(response => {
        this.userList = response.data.result
      }, response => {
        // error callback
      })
    },

    getSpaceList: function () {
      this.$axios.get('/api/project/product_spaces').then(response => {
        this.productSpaces = response.data.result
      }, response => {
        // error callback
      })
    },


    onModalStatusChange: function () {
      this.initFormItem()
    },

    initFormItem: function () {
      this.formItem = {
        email: '',
        password: '',
        confirmPassword: '',
        last_name: '',
        first_name: ''
      }
    }
  },

  created: function () {
    this.getUserList()
    this.getSpaceList()
  },

  mounted: function () {

  },

  watch: {
  },

  components: {
    UserList,
    UserGroup,
    UserDetail
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

  .member-card {
    width: 100%;
    height: 45px;
    margin-right: 16px;
    margin-left: 16px;
    margin-bottom: 2px;
  }

  .member-avatar {
    height:20px;
    width: 20px;
    border-radius: 10px;
    padding-bottom: 2px;
    margin-right: 15px;
  }

  .member-tag {
    font-size:10px;
    height: 18px;
    line-height: 18px;
  }

</style>
