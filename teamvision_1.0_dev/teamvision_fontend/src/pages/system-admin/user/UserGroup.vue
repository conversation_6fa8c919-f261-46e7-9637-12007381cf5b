<template>
  <div>
    <Card v-for="user in userGroups" :key="user.id" :bordered="false" dis-hover class="member-card cursor-hand">
      <Row>
        <Col :xl="16" :lg="16" :md="16" :sm="16">
        <span style="font-weight: bold;">
          <img class="member-avatar" :src="user.avatar" />
          <span>{{ user.last_name }}{{ user.first_name }}</span>
          <span v-if="userInfo.id === user.id" style="margin-left: 15px;">
            <Tag color="success" class="member-tag">It's you!</Tag>
          </span>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span style="margin-left: 15px;">
          <span style="width: 60px;display: inline-block">
          </span>
        </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
        <span>
          <Dropdown transfer @on-click="deleteUser">
            <a href="javascript:void(0)" style="color: inherit;">
              <span>
                <Icon :size="24" type="ios-more" />
              </span>
            </a>
            <DropdownMenu slot="list">
              <DropdownItem :name="user.id + ':' + user.last_name + user.first_name">
                <Icon type="ios-trash-outline" />
                <span>删除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'UserGroup',
  props: ['groups'],
  data() {
    return {
      userGroups: []
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyMainHeight - 110
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),
  },

  created: function () {
    this.userGroups = this.groups
  },

  mounted: function () {

  },

  watch: {
    groups: function () {
      this.userGroups = this.groups
    }
  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 45px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

.member-avatar {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  padding-bottom: 2px;
  margin-right: 15px;
}

.member-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
}
</style>
