<template>
  <div
    :style="'padding-top:10px;padding-left: 20px; height:' + containerHeight + 'px;overflow-y:auto;overflow-x:hidden;'">
    <List size="small">
      <ListItem v-for="user in userList" :key="user.id">
        <ListItemMeta :avatar="user.avatar">
          <template slot="title">
            <span style="font-weight: bold;">
              <span style="text-decoration: underline" @click="showUserDetail(user.id)">{{ user.name }}</span>
              <span v-if="userInfo.id === user.id" style="margin-left: 15px;">
                <Tag color="success" class="member-tag">It's you!</Tag>
              </span>
              <span v-if="user.system_role_label === 'A'" style="margin-right: 0px; width:50px;display: inline-block;">
                <Tag color="success" class="member-tag">Admin</Tag>
              </span>
              <span v-if="user.system_role_label === 'M'" style="margin-right: 0px; width:50px;display: inline-block;">
                <Tag color="success" class="member-tag">Manager</Tag>
              </span>
              <span v-if="user.system_role_label === 'U'" style="margin-right: 0px; width:50px;display: inline-block;">
              </span>
            </span>
          </template>
          <template slot="description">
            <span style="margin-right:10px">ID: {{ user.id }}</span>
            <span style="margin-right:10px">邮箱: {{ user.email }}</span>
            <span style="margin-right:10px">最后登陆时间: {{ user.last_login }}</span>
          </template>
        </ListItemMeta>
        <template slot="action">
          <li>
            <span>
              <Dropdown transfer @on-click="deleteUser">
                <a href="javascript:void(0)" style="color: inherit;">
                  <span>
                    <Icon :size="24" type="ios-more" />
                  </span>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem :name="user.id + ':' + user.name">
                    <Icon type="ios-trash-outline" />
                    <span>删除</span>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </span>
          </li>
        </template>
      </ListItem>
    </List>
    <!--
    <Card v-for="user in userList"  :key="user.id" :bordered="false" dis-hover :class="'member-card cursor-hand '+ selectedUserClass(user.id)">
      <Row>
        <Col :xl="12" :lg="12" :md="12" :sm="12">
          <span style="font-weight: bold;">
            <img class="member-avatar" :src="user.avatar"/>
            <span style="text-decoration: underline" @click="showUserDetail(user.id)" >{{ user.name }}</span>
            <span v-if="userInfo.id === user.id" style="margin-left: 15px;"><Tag color="success" class="member-tag">It's you!</Tag></span>
              <span v-if="user.system_role_label === 'A'" style="margin-right: 0px; width:50px;display: inline-block;">
              <Tooltip content="Admin用户">
               <Tag color="success" class="member-tag">Admin</Tag>
              </Tooltip>
            </span>
            <span v-if="user.system_role_label === 'M'" style="margin-right: 0px; width:50px;display: inline-block;">
              <Tooltip content="Manager用户">
               <Tag color="success" class="member-tag">Manager</Tag>
              </Tooltip>
            </span>
            <span v-if="user.system_role_label === 'U'" style="margin-right: 0px; width:50px;display: inline-block;">
            </span>
          </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
            <span style="margin-left: 15px;">
              <span style="width: 60px;display: inline-block">
               {{ user.last_login }}
              </span>
            </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
            <span style="margin-left: 15px;">
              <span style="width: 60px;display: inline-block">
               <Icon type="ios-mail-outline" /> {{ user.email }}
              </span>
            </span>
        </Col>
        <Col :xl="4" :lg="4" :md="4" :sm="4">
          <span>
            <Dropdown transfer @on-click="deleteUser">
             <a href="javascript:void(0)" style="color: inherit;">
                <span>
                  <Icon :size="24" type="ios-more"/>
                </span>
             </a>
              <DropdownMenu slot="list">
                 <DropdownItem :name="user.id+':'+user.last_name + user.first_name"><Icon type="ios-trash-outline"/>
                   <span>删除</span>
                 </DropdownItem>
              </DropdownMenu>
           </Dropdown>
          </span>
        </Col>
      </Row>
    </Card>
    -->
    <Spin v-if="showLoading" fix size="large"></Spin>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'UserList',
  props: ['users'],
  data() {
    return {
      userList: [],
      selectedUser: 0,
      showLoading: false
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight', 'appBodyHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['systemAdminearchKey']),

    containerHeight: function () {
      return this.appBodyHeight
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),
    ...mapMutations('user', ['setShowUserDetail']),

    deleteUser: function (value) {
      let deleteValues = value.split(':')
      this.$Modal.confirm({
        title: '删除确认',
        content: '您即将删除用户[' + deleteValues[1] + ']',
        onOk: () => {
          for (let i = 0; i < this.userList.length; i++) {
            if (this.userList[i].id === parseInt(deleteValues[0])) {
              this.userList.splice(i, 1)
              break
            }
          }

          this.$axios.delete('/api/auth/user/' + deleteValues[0]).then(response => {
            this.$Message.success({
              content: '用户已经成功移除',
              duration: 3,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '移除用户失败',
              duration: 3,
              closable: true
            })
          })

        }
      })
    },

    showUserDetail: function (value) {
      this.$emit('on-selectUser', value)
      this.selectedUser = parseInt(value)
    },

    selectedUserClass: function (userID) {
      if (this.selectedUser === userID) {
        return 'selectedUser'
      } else {
        return ''
      }
    }
  },

  created: function () {
    this.userList = this.users
  },

  mounted: function () {

  },

  watch: {
    users: function () {
      this.userList = this.users
      this.showLoading = false
    },

    systemAdminearchKey: function (value) {
      this.userList = []
      if (value.trim() === '') {
        this.userList = this.users
      } else {
        for (let i = 0; i < this.users.length; i++) {
          if (this.users[i].name.indexOf(value) > -1 || this.users[i].email.indexOf(value) > -1) {
            this.userList.push(this.users[i])
          }
        }
      }
    }
  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.member-card {
  width: 100%;
  height: 45px;
  margin-right: 16px;
  margin-left: 16px;
  margin-bottom: 2px;
}

.member-avatar {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  margin-right: 15px;
}

.member-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
}

.selectedUser {
  background-color: #f0faff;

}
</style>
