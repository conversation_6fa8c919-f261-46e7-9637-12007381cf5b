<template>
  <div
    :style="'padding-top:16px;padding-left: 10px; height:' + containerHeight + 'px;overflow-y:auto;overflow-x:hidden;'">
    <Card :bordered="false" dis-hover>
      <div class="panel-title">
        <span>修改账号：</span>
        <span>{{ userData.name }}</span>
      </div>
      <Divider class="divider" />
      <div style="height: 260px;">
        <Form ref="editAccount" :model="userData" :label-width="80" :rules="ruleCustom">
          <FormItem label="邮箱" prop="email"
            v-if="userInfo.system_permision < 3 && userInfo.system_permision <= userData.system_permision">
            <Input type="email" v-model="userData.email" placeholder="用户邮箱" />
          </FormItem>
          <FormItem label="姓氏" prop="last_name">
            <Input v-model="userData.last_name" placeholder="用户姓氏" />
          </FormItem>
          <FormItem label="名字" prop="first_name">
            <Input v-model="userData.first_name" placeholder="用户名字" />
          </FormItem>
        </Form>
        <Button type="success" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
          @click="updateProfiles('editAccount')">保存</Button>
      </div>
      <div class="panel-title">
        <span>空间设置：</span>
      </div>
      <Divider class="divider" />
      <div style="height: 160px;">
        <Form ref="editAccount" :model="userData" :label-width="80" :rules="ruleCustom">
          <FormItem label="空间" prop="space">
            <Select v-model="userData.ViewData.space_list" multiple :filterable="true" placeholder="产品空间">
              <Option v-for="product in spaceList" :key="product.id" :value="product.id">{{ product.Title }}</Option>
            </Select>
          </FormItem>
        </Form>
        <Button type="success" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
          @click="addUserToSpace">保存</Button>
      </div>

      <div class="panel-title" v-if="userInfo.system_role_label === 'A'">
        <span>权限设置：</span>
      </div>
      <Divider v-if="userInfo.system_permision < 3 && userInfo.system_permision <= userData.system_permision"
        class="divider" />
      <div style="margin-bottom: 60px;"
        v-if="userInfo.system_permision < 3 && userInfo.system_permision <= userData.system_permision">
        <div style="margin-bottom: 20px;padding-left: 80px; ">
          <RadioGroup v-model="systemRole" vertical>
            <Radio :label="27" v-if="userInfo.system_permision < 2">
              <Icon type="locked" :size="20"></Icon>
              <span>Admin</span>
              <span style="color:#737373;padding-left: 20px;">系统管理员组，拥有所有权限.</span>
            </Radio>
            <Radio :label="28" v-if="userInfo.system_permision < 3">
              <Icon type="contrast" :size="20"></Icon>
              <span>Manager</span>
              <span style="color:#737373;padding-left: 20px;">管理员组，拥有除创建用户组以外的所有权限。</span>
            </Radio>
            <Radio :label="29" v-if="userInfo.system_permision < 3">
              <Icon type="ios-world" :size="20"></Icon>
              <span>User</span>
              <span style="color:#737373;padding-left: 20px;">项目对所有人员可见</span>
            </Radio>
          </RadioGroup>
        </div>
        <Button type="error" style="width: 80px; height:30px; margin-left: 30px;" shape="circle"
          @click="updateUserGroup">保存</Button>
      </div>

      <div v-if="userInfo.system_permision < 3 && userInfo.system_permision <= userData.system_permision">
        <div class="panel-title">
          <span>密码重置：</span>
        </div>
        <Divider class="divider" />
        <div>
          <Form ref="resetPassword" :model="userData" :label-width="80" :rules="ruleCustom">
            <FormItem label="新密码" prop="password">
              <Input type="password" v-model="userData.password" placeholder="密码" />
            </FormItem>
            <FormItem label="确认新密码" prop="confirmPassword">
              <Input type="password" v-model="userData.confirmPassword" placeholder="确认密码" />
            </FormItem>
          </Form>
          <Button type="error" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
            @click="resetPassword('resetPassword')">重置</Button>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { userValidateRules } from './SystemAdminCreate'

export default {
  name: 'UserDetail',
  props: ['userID', 'userList', 'spaceList'],
  data() {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.userData.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }

    return {
      userData: {
        "ViewData": {
          "space_list": []
        }
      },
      systemRole: 0,
      ruleCustom: {
        ...userValidateRules,
        confirmPassword: [
          { validator: validatePassCheck, trigger: 'blur' }
        ],
        email: [
          { type: 'email', required: true, validator: validateEmailCheck, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyHeight - 70
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),
    updateProfiles: function (name) {
      this.$axios.post('/api/auth/user/' + this.userID, this.userData).then(response => {
        this.$Message.success({
          content: '更新信息成功',
          duration: 3,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '更新信息失败',
          duration: 3,
          closable: true
        })
      })
    },

    addUserToSpace: function () {
      let parameters = {
        spaceList: this.userData.ViewData.space_list,
        userID: this.userData.id
      }
      this.$axios.post('/api/project/space/user/add', parameters).then(response => {

        this.$Message.success({
          content: '设置空间成员成功',
          duration: 3,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '设置空间成员失败',
          duration: 3,
          closable: true
        })
      })

    },

    resetPassword: function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.put('/api/auth/user/' + this.userID, this.userData).then(response => {
            this.$Message.success({
              content: '重置密码成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '重置密码失败',
              duration: 10,
              closable: true
            })
          })
        }
      })
    },

    updateUserGroup: function () {
      let parameters = { userGroup: this.systemRole }
      this.$axios.patch('/api/auth/user/' + this.userID, parameters).then(response => {
        this.$Message.success({
          content: '用户角色修改成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '用户角色修改失败',
          duration: 10,
          closable: true
        })
      })
    },

    userExists: function (email) {
      let result = false
      for (let i = 0; i < this.userList.length; i++) {
        if (email.trim() === this.userList[i].email && parseInt(this.userID) !== this.userList[i].id) {
          result = true
        }
      }
      return result
    },

    getUserInfo: function (userID) {
      this.$axios.get('/api/auth/user/' + userID).then(response => {
        this.userData = response.data.result
        this.systemRole = response.data.result.groups[0].id
      }, response => {
      })
    }
  },

  created: function () {
    this.getUserInfo(this.userID)
  },

  mounted: function () {
    //this.getUserInfo(this.userID)
  },

  watch: {
    userID: function (value) {
      this.getUserInfo(value)
    }
  },

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.divider {
  margin-top: 6px;
}

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit;
}
</style>
