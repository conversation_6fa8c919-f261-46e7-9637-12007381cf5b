<template>
  <div>
    <Row>
      <Col :lg="3" :sm="6" :xs="8">
        <Card title="消息中心配置" icon="ios-options" :padding="16" shadow>
          <CellGroup>
            <Cell title="消息列表" :selected="routerName === 'systemMessageConfig'" to="/system/message/config">
              <Icon slot="icon" type="ios-bug" :size="20" />
            </Cell>
            <Cell title="消息渠道" :selected="routerName === 'systemMessageChannel'" to="/system/message/channel">
              <Icon slot="icon" type="ios-basket" :size="20" />
            </Cell>
          </CellGroup>
        </Card>
      </Col>
      <Col :lg="21" :sm="18" :xs="16">
        <message-config-list v-if="routerName === 'systemMessageConfig'"></message-config-list>
        <message-channel-list v-if="routerName === 'systemMessageChannel'"></message-channel-list>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import MessageConfigList from './MessageConfigList.vue'
import MessageChannelList from './MessageChannelList.vue'

export default {
  name: 'MessageCenterAdmin',
  props: [],
  data () {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formItem.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0 ) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }
    return {
      userList: [],
      groups: [],
      showUserDetail: false,
      selectedUser: 0,
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyHeight
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),

    cancel: function () {
      this.setCreateDialogShow(false)
    },

    userExists: function (email) {
      let result = false
      for (let i =0; i< this.userList.length; i++) {
        if (email.trim() === this.userList[i].email) {
          result = true
        }
      }
      return result
    },

  },

  created: function () {
  },

  mounted: function () {
  },

  watch: {},

  components: {
    MessageConfigList,
    MessageChannelList
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
  .member-card {
    width: 100%;
    height: 45px;
    margin-right: 16px;
    margin-left: 16px;
    margin-bottom: 2px;
  }

  .member-avatar {
    height:20px;
    width: 20px;
    border-radius: 10px;
    padding-bottom: 2px;
    margin-right: 15px;
  }

  .member-tag {
    font-size:10px;
    height: 18px;
    line-height: 18px;
  }

</style>
