<template>
  <div :style="'padding:4px;max-height:' + containerHeight + 'px;overflow:scroll'">
    <Table size="small" :columns="columns1" :data="messageConfigList">
      <template slot-scope="{ row, index }" slot="active">
        <i-switch :value="row.is_active" size="small" @on-change="changeActive(index)"> </i-switch>
      </template>
      <template slot-scope="{ row, index }" slot="delete">
        <Button size="small" icon="md-trash" @click="deleteMessage(index)">Delete</Button>
        <Button size="small" icon="md-build" @click="showdialog(index)">Edit</Button>
      </template>
    </Table>
    <Modal v-model="dialogShow" title="新建消息通知" :width="600" @on-cancel="cancel" :styles="{ bottom: '20px', top: '50px' }">
      <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
        <Form ref="messageConfigForm" :model="formData" :label-width="80" :rules="ruleCustom">
          <FormItem label="消息名称" prop="name">
            <Input v-model="formData.name" maxlength="60" show-word-limit placeholder="名称" />
          </FormItem>
          <FormItem label="所属项目" prop="project_id">
            <Select v-model="formData.project_id" :max-tag-count="2">
              <Option v-for="item in projectList" :key="item.id" :value="item.id">{{ item.PBTitle }}</Option>
            </Select>
          </FormItem>
          <FormItem label="消息渠道" prop="channel_id">
            <Select v-model="formData.channel_id" :max-tag-count="2">
              <Option v-for="item in messageChannelList" :key="item.id" :value="item.id">{{ item.name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="类名称" prop="class_name">
            <Select v-model="formData.class_name" :max-tag-count="2" @on-change="changeAttribute">
              <Option v-for="item in classList" :key="item.id" :value="item.class_name">{{ item.class_name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="属性" prop="attribute_name">
            <Select v-model="formData.attribute_name" transfer filterable placeholder="属性名">
              <Option v-for="item in attributeList" :key="item.id" :value="item.attribute_name">{{
                item.attribute_name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="触发状态" prop="status">
            <CheckboxGroup>
              <Checkbox label="新建" v-model="formData.is_create"></Checkbox>
              <Checkbox label="修改" v-model="formData.is_modified"></Checkbox>
              <Checkbox label="删除" v-model="formData.is_del"></Checkbox>
            </CheckboxGroup>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button v-if="isCreateDialog" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="createMessage()">添加</Button>
        <Button v-if="!isCreateDialog" type="success" style="width: 80px; height:30px;" shape="circle"
          @click="updateMessage()">更新</Button>
        <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { messageConfigValidateRules } from './MessageCenter'

export default {
  name: 'MessageConfigList',
  props: [''],
  data() {
    return {
      columns1: [
        {
          title: 'Id',
          key: 'id',
          align: 'center',
          width: 60,
        },
        {
          title: '创建时间',
          key: 'created_time',
          align: 'center',
        },
        {
          title: '名称',
          key: 'name',
          align: 'center',
        },
        {
          title: '项目',
          key: 'project_id',
          align: 'center',
        }, {
          title: '渠道',
          key: 'channel_id',
          align: 'center',
        },
        {
          title: '类名',
          key: 'class_name',
          align: 'center',
        },
        {
          title: '属性名',
          key: 'attribute_name',
          align: 'center',
        },
        {
          title: '触发条件',
          align: 'center',
          children: [
            {
              title: '新建',
              key: 'is_create',
              align: 'center',
              width: 80,
            },
            {
              title: '修改',
              key: 'is_modified',
              align: 'center',
              width: 80,
            },
            {
              title: '删除',
              key: 'is_del',
              align: 'center',
              width: 80,
            },
          ]
        },
        {
          title: '开启/关闭',
          key: 'is_active',
          slot: 'active',
          align: 'center',
          width: 100,
        },
        {
          title: '删除/修改',
          key: 'is_active',
          slot: 'delete',
          align: 'center',
          width: 180,
        },
      ],
      messageConfigList: [],
      messageChannelList: [],
      formData: {
        name: '',
        created_by: 0,
        project_id: 0,
        class_name: '',
        attribute_name: '',
        is_create: false,
        is_modified: false,
        is_del: false
      },
      ruleCustom: {
        ...messageConfigValidateRules
      },
      classList: [],
      attributeList: [],
      isCreateDialog: true
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),
    ...mapState(['appBodyMainHeight',]),
    ...mapState('systemglobal', ['createDialogShow']),
    ...mapState('project', ['projectList']),
    ...mapGetters('systemglobal', ['systemAdminearchKey']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },
    dialogShow: function () {
      if (this.$route.name === 'systemMessageConfig') {
        return this.createDialogShow
      }
      return false
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),
    ...mapMutations('user', ['setShowUserDetail']),

    getMessageConfigList() {
      this.$axios.get('/api/message/config/list').then(response => {
        this.messageConfigList = response.data.result
      }
      )
    },

    getMessageChannelList() {
      this.$axios.get('/api/message/channel/list').then(response => {
        this.messageChannelList = response.data.result
      }
      )
    },

    getClassAttribute() {
      this.$axios.get('/api/webhook/class/list').then(response => {
        this.classList = response.data.result
      }
      )
    },

    changeAttribute() {
      for (let i = 0; i < this.classList.length; i++) {
        // console.log(this.formData.class_name)
        // console.log(this.classList[i].class_name)
        if (this.formData.class_name == this.classList[i].class_name) {
          this.attributeList = this.classList[i].attribute
        }
      }
    },

    changeActive(index) {
      let webhookActive = {
        "is_active": !this.messageConfigList[index].is_active
      }
      this.$axios.patch('/api/message/config/' + this.messageConfigList[index].id, webhookActive).then(response => {
        this.$Message.info('开启状态：' + response.data.result.is_active);
        this.messageConfigList[index].is_active = response.data.result.is_active
      }
      )
    },

    createMessage() {
      let messageConfig = {
        "name": this.formData.name,
        "created_by": this.userInfo.id,
        "project_id": this.formData.project_id,
        "channel_id": this.formData.channel_id,
        "class_name": this.formData.class_name,
        "attribute_name": this.formData.attribute_name,
        "is_create": this.formData.is_create,
        "is_modified": this.formData.is_modified,
        "is_del": this.formData.is_del
      }
      this.$axios.post('/api/message/config/list', messageConfig).then(response => {
        let newMessageConfig = response.data.result
        this.messageConfigList.push(newMessageConfig)
        this.setCreateDialogShow(false)
        this.formData = {
          name: '',
          created_by: 0,
          project_id: 0,
          class_name: '',
          attribute_name: '',
          is_create: false,
          is_modified: false,
          is_del: false
        }
        this.$Message.info('创建成功');
        this.$refs['messageConfigForm'].resetFields()
      }, response => {
        this.$Message.info('创建失败');
      }
      )
    },

    deleteMessage(index) {
      this.$Modal.confirm({
        title: '删除确认',
        onOk: () => {
          this.$axios.delete('/api/message/config/' + this.messageConfigList[index].id).then(response => {
            this.messageConfigList.splice(index, 1)
            this.$Message.success({
              content: '成功删除',
              duration: 3,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '删除失败',
              duration: 3,
              closable: true
            })
          })

        }
      })
    },

    showdialog(index) {
      this.isCreateDialog = false;
      this.formData = this.messageConfigList[index]
      this.setCreateDialogShow(true);
      for (let i = 0; i < this.classList.length; i++) {
        if (this.formData.class_name == this.classList[i].class_name) {
          this.attributeList = this.classList[i].attribute;
        }
      }
    },

    updateMessage() {
      let messageConfig = {
        "name": this.formData.name,
        "created_by": this.userInfo.id,
        "project_id": this.formData.project_id,
        "channel_id": this.formData.channel_id,
        "class_name": this.formData.class_name,
        "attribute_name": this.formData.attribute_name,
        "is_create": this.formData.is_create,
        "is_modified": this.formData.is_modified,
        "is_del": this.formData.is_del
      }
      this.$axios.put('/api/message/config/' + this.formData.id, messageConfig).then(response => {
        let newMessageConfig = response.data.result
        for (let i = 0; i < this.messageConfigList.length; i++) {
          if (this.messageConfigList[i] == newMessageConfig.id) {
            this.messageConfigList[i] = newMessageConfig
          }
        }
        this.setCreateDialogShow(false)
        this.isCreateDialog = true;
        this.$Message.info('更新成功');
        this.initFormData()
      }, response => {
        this.$Message.info('创建失败');
      }
      )

    },

    initFormData() {
      this.formData = {
        name: '',
        created_by: 0,
        project_id: 0,
        class_name: '',
        attribute_name: '',
        is_create: false,
        is_modified: false,
        is_del: false
      }
    },

    cancel() {
      this.setCreateDialogShow(false)
      this.isCreateDialog = true;
      //this.$refs.webHookForm.resetFields()
      this.initFormData()
    },

  },

  created: function () {
    this.getMessageConfigList()
    this.getMessageChannelList()
    this.getClassAttribute()
  },

  mounted: function () {

  },

  watch: {
  },

  components: {
  }
}
</script>

