<template>
    <div :style="'padding:4px;max-height:' + containerHeight + 'px;overflow:scroll'">
        <Table size="small" :columns="columns1" :data="messageChannelList">
            <template slot-scope="{ row, index }" slot="active">
                <i-switch :value="row.is_active" size="small" @on-change="changeActive(index)"> </i-switch>
            </template>
            <template slot-scope="{ row, index }" slot="delete">
                <Button size="small" icon="md-trash" @click="deleteMessageChannel(index)">Delete</Button>
                <Button size="small" icon="md-build" @click="showdialog(index)">Edit</Button>
            </template>
        </Table>
        <Modal v-model="dialogShow" title="新建消息渠道配置" :width="600" @on-cancel="cancel"
            :styles="{ bottom: '20px', top: '50px' }">
            <div :style="'max-height:' + containerHeight + 'px;overflow-y: scroll;overflow-x: hidden'">
                <Form ref="messageChannelForm" :model="formData" :label-width="120" :rules="ruleCustom">
                    <FormItem label="名称" prop="name">
                        <Input v-model="formData.name" maxlength="100" show-word-limit placeholder="名称" />
                    </FormItem>
                    <FormItem label="渠道" prop="channel">
                        <Select v-model="formData.channel" :max-tag-count="2">
                            <Option v-for="item in channelList" :key="item.key" :value="item.key">{{ item.value }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="message_base_url" prop="message_base_url">
                        <Input v-model="formData.message_base_url" maxlength="100" show-word-limit
                            placeholder="message_base_url" />
                    </FormItem>
                    <FormItem label="auth_base_url" prop="auth_base_url">
                        <Input v-model="formData.auth_base_url" maxlength="100" show-word-limit
                            placeholder="auth_base_url" />
                    </FormItem>
                    <FormItem label="app_id" prop="app_id">
                        <Input v-model="formData.app_id" maxlength="100" show-word-limit placeholder="app_id" />
                    </FormItem>
                    <FormItem label="app_secret" prop="app_secret">
                        <Input v-model="formData.app_secret" maxlength="100" show-word-limit placeholder="app_secret" />
                    </FormItem>
                    <FormItem label="chat_id" prop="chat_id">
                        <Input v-model="formData.chat_id" maxlength="100" show-word-limit placeholder="chat_id" />
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button v-if="isCreateDialog" type="success" style="width: 80px; height:30px;" shape="circle"
                    @click="createMessageChannel()">添加</Button>
                <Button v-if="!isCreateDialog" type="success" style="width: 80px; height:30px;" shape="circle"
                    @click="updateMessageChannel()">更新</Button>
                <Button type="success" style="width: 80px; height:30px;" shape="circle" @click="cancel">取消</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { messageConfigValidateRules } from './MessageCenter'

export default {
    name: 'MessageConfigList',
    props: [''],
    data() {
        return {
            columns1: [
                {
                    title: 'Id',
                    key: 'id',
                    align: 'center',
                    width: 60,
                },
                {
                    title: '创建时间',
                    key: 'created_time',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '名称',
                    key: 'name',
                    align: 'center',
                },
                {
                    title: '渠道',
                    key: 'channel',
                    align: 'center',
                    width: 80,
                },
                {
                    title: 'message_base_url',
                    key: 'message_base_url',
                    align: 'center',
                },
                {
                    title: 'auth_base_url',
                    key: 'auth_base_url',
                    align: 'center',
                },
                {
                    title: 'app_id',
                    key: 'app_id',
                    align: 'center',
                },
                {
                    title: 'app_secret',
                    key: 'app_secret',
                    align: 'center',
                },
                {
                    title: 'chat_id',
                    key: 'chat_id',
                    align: 'center',
                },
                {
                    title: '删除/修改',
                    key: 'is_active',
                    slot: 'delete',
                    align: 'center',
                    width: 180,
                },
            ],
            messageChannelList: [],
            channelList: [],
            formData: {
                name: '',
                created_by: 0,
                channel: '',
                message_base_url: '',
                auth_base_url: '',
                app_id: '',
                app_secret: '',
                chat_id: ''
            },
            ruleCustom: {
                ...messageConfigValidateRules
            },
            isCreateDialog: true
        }
    },
    computed: {
        ...mapState('usercenter', ['userInfo']),
        ...mapState(['appBodyMainHeight',]),
        ...mapState('systemglobal', ['createDialogShow']),
        ...mapGetters('systemglobal', ['systemAdminearchKey']),
        containerHeight: function () {
            return this.appBodyMainHeight
        },
        dialogShow: function () {
            if (this.$route.name === 'systemMessageChannel') {
                return this.createDialogShow
            }
            return false
        }
    },

    methods: {
        ...mapMutations('systemglobal', ['setCreateDialogShow']),
        ...mapMutations('user', ['setShowUserDetail']),

        getMessageChannelList() {
            this.$axios.get('/api/message/channel/list').then(response => {
                this.messageChannelList = response.data.result
            }
            )
        },
        getChannelList() {
            this.$axios.get('/api/message/channels').then(response => {
                this.channelList = response.data.result
            }
            )
        },

        createMessageChannel() {
            let messageChannel = {
                "name": this.formData.name,
                "created_by": this.userInfo.id,
                "channel": this.formData.channel,
                "message_base_url": this.formData.message_base_url,
                "auth_base_url": this.formData.auth_base_url,
                "app_id": this.formData.app_id,
                "app_secret": this.formData.app_secret,
                "chat_id": this.formData.chat_id,
            }
            this.$axios.post('/api/message/channel/list', messageChannel).then(response => {
                let newMessageChannel = response.data.result
                this.messageChannelList.push(newMessageChannel)
                this.setCreateDialogShow(false)
                this.formData = {
                    name: '',
                    created_by: 0,
                    channel: '',
                    message_base_url: '',
                    auth_base_url: '',
                    app_id: '',
                    app_secret: '',
                    chat_id: ''
                }
                this.$Message.info('创建成功');
                this.$refs['messageChannelForm'].resetFields()
            }, response => {
                this.$Message.info('创建失败');
            }
            )
        },

        deleteMessageChannel(index) {
            this.$Modal.confirm({
                title: '删除确认',
                onOk: () => {
                    this.$axios.delete('/api/message/channel/' + this.messageChannelList[index].id).then(response => {
                        this.messageChannelList.splice(index, 1)
                        this.$Message.success({
                            content: '成功删除',
                            duration: 3,
                            closable: true
                        })
                    }, response => {
                        this.$Message.error({
                            content: '删除失败',
                            duration: 3,
                            closable: true
                        })
                    })

                }
            })
        },

        showdialog(index) {
            this.isCreateDialog = false;
            this.formData = this.messageChannelList[index]
            this.setCreateDialogShow(true);
            for (let i = 0; i < this.classList.length; i++) {
                if (this.formData.class_name == this.classList[i].class_name) {
                    this.attributeList = this.classList[i].attribute;
                }
            }
        },

        updateMessageChannel() {
            let messageChannel = {
                "name": this.formData.name,
                "created_by": this.userInfo.id,
                "channel": this.formData.channel,
                "message_base_url": this.formData.message_base_url,
                "auth_base_url": this.formData.auth_base_url,
                "app_id": this.formData.app_id,
                "app_secret": this.formData.app_secret,
                "chat_id": this.formData.chat_id,
            }
            this.$axios.put('/api/message/channel/' + this.formData.id, messageChannel).then(response => {
                let newMessageConfig = response.data.result
                for (let i = 0; i < this.messageChannelList.length; i++) {
                    if (this.messageChannelList[i] == newMessageConfig.id) {
                        this.messageChannelList[i] = newMessageConfig
                    }
                }
                this.setCreateDialogShow(false)
                this.isCreateDialog = true;
                this.$Message.info('更新成功');
                this.initFormData()
            }, response => {
                this.$Message.info('更新失败');
            }
            )

        },
        initFormData() {
            this.formData = {
                name: '',
                created_by: 0,
                channel: '',
                message_base_url: '',
                auth_base_url: '',
                app_id: '',
                app_secret: '',
                chat_id: ''
            }
        },

        cancel() {
            this.initFormData()
            this.setCreateDialogShow(false)
            this.isCreateDialog = true;
        },

    },

    created: function () {
        this.getMessageChannelList()
        this.getChannelList()
    },

    mounted: function () {

    },

    watch: {
    },

    components: {
    }
}
</script>

