<template>
  <div :style="'height:' + containerHeight + 'px;overflow-y:scroll;'">
    <div class="setting-body">
      <div v-for="dicType in messageQueueConfig" :key="dicType.id">
        <Divider orientation="left">{{ dicType.DicTypeLabel }}</Divider>
        <div style="margin-bottom: 50px;">
          <Form ref="editAccount" :model="dicType" :label-width="120" :rules="ruleCustom">
            <FormItem v-for="dicData in dicType.DicDatas" v-if="dicData.Scope === 'Global'" :key="dicData.id"
              :label="dicData.DicDataLabel" prop="email">
              <Input type="text" v-model="dicData.DicDataValue" :placeholder="dicData.DicDataDesc" />
            </FormItem>
          </Form>
        </div>
      </div>
      <Button type="success" style="width: 80px; height:30px;margin-left:120px; position:absolute;bottom:20px;"
        shape="circle" @click="updateGlobalConfig">保存</Button>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { userValidateRules } from './SettingValidator'

export default {
  name: 'SystemSettings',
  props: [],
  data() {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formItem.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }
    return {
      messageQueueConfig: {},
      groups: [],
      showUserDetail: false,
      selectedUser: 0,
      userData: {
        email: '',
        password: '',
        confirmPassword: '',
        last_name: '',
        first_name: ''
      },
      ruleCustom: {
        ...userValidateRules,
        confirmPassword: [
          { validator: validatePassCheck, trigger: 'blur' }
        ],
        email: [
          { type: 'email', required: true, validator: validateEmailCheck, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight',]),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyMainHeight - 30
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),

    getMessageQueueConfig: function () {
      this.$axios.get('/api/common/dicconfig/all_configs?Scope=Global').then(response => {
        this.messageQueueConfig = response.data.result
      }, response => {
        // error callback
      })
    },
    updateGlobalConfig: function () {
      let parameters = { data: this.messageQueueConfig }
      this.$axios.put('/api/common/dicconfig/all_configs/', parameters).then(response => {
        this.$Message.success({
          content: '系统设置保存成功',
          duration: 10,
          closable: true
        })
      }, response => {
        this.$Message.error({
          content: '系统设置保存失败',
          duration: 10,
          closable: true
        })
      })
    }
  },

  created: function () {
    this.getMessageQueueConfig()
  },

  mounted: function () {},

  watch: {},
  components: {}
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.setting-body {
  width: 70%;
  margin-right: auto;
  margin-left: auto;
  margin-top: 16px;
  height: inherit;
}
</style>
