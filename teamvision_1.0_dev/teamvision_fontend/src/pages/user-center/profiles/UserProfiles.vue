<template>
  <div
    :style="'padding-top:10px;padding-left: 10px; height:' + containerHeight + 'px;overflow-y:auto;overflow-x:hidden;'">
    <div class="info-body">
      <Divider orientation="left">{{ userData.name }}</Divider>
      <div style="height: 350px;">
        <Form ref="editAccount" :model="userData" :label-width="80" :rules="ruleCustom">
          <FormItem label="头像">
            <div @click="changeAvatar" class="cursor-hand">
              <Avatar style="width: 70px;height: 70px; border-radius: 35px;" :src="userAvatar" />
              <Icon style="margin-top: -50px;" :size="20" type="ios-create-outline" />
            </div>
          </FormItem>
          <FormItem label="邮箱" prop="email">
            <Input type="email" v-model="userData.email" placeholder="用户邮箱" />
          </FormItem>
          <FormItem label="姓氏" prop="last_name">
            <Input v-model="userData.last_name" placeholder="用户姓氏" />
          </FormItem>
          <FormItem label="名字" prop="last_name">
            <Input v-model="userData.first_name" placeholder="用户名字" />
          </FormItem>
        </Form>
        <Button type="success" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
          @click="updateProfiles('editAccount')">保存</Button>
      </div>
      <Divider orientation="left">密码修改</Divider>
      <div>
        <Form ref="resetPassword" :model="userData" :label-width="80" :rules="ruleCustom">
          <FormItem label="旧密码" prop="oldPassword">
            <Input type="password" v-model="userData.oldPassword" placeholder="密码" />
          </FormItem>
          <FormItem label="新密码" prop="password">
            <Input type="password" v-model="userData.password" placeholder="密码" />
          </FormItem>
          <FormItem label="确认新密码" prop="confirmPassword">
            <Input type="password" v-model="userData.confirmPassword" placeholder="确认密码" />
          </FormItem>
        </Form>
        <Button type="error" style="width: 80px; height:30px;margin-left:30px;" shape="circle"
          @click="changePassword('resetPassword')">修改</Button>
      </div>
      <Modal :value="showUplaodAvatar" title="上传头像" @on-cancel="closeDialog" :mask-closable="false">
        <div style="margin-bottom: 10px;margin-left: 10px;">
          <img :src="userAvatar" style="width: 60px;height:60px;border-radius: 30px;" />
        </div>
        <Upload ref="upload" type="drag" paste :show-upload-list="false" action="/api/ucenter/attachment/upload"
          :on-success="handleSuccess" :on-remove="handleRemove" :format="[]" :max-size="10240"
          :default-file-list="defaultList" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize">
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>点击，拖拽，粘贴上传附件</p>
          </div>
        </Upload>
        <div slot="footer">
          <Button @click="onUpdateAvatar" type="primary" size="default" shape="circle">确认更新</Button>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { userValidateRules } from './UserProfile'

export default {
  name: 'UserAdmin',
  props: ['userID'],
  data() {
    const validatePassCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.userData.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    const validateEmailCheck = (rule, value, callback) => {
      if (value.trim() === '' || value.indexOf('@') < 0) {
        callback(new Error('请输入合法用户邮箱'))
      } else if (this.userExists(value)) {
        callback(new Error('用户邮箱已经存在，请使用其他邮箱。'))
      } else {
        callback()
      }
    }

    return {
      userData: {
        oldPassword: '',
        email: '',
        password: '',
        confirmPassword: '',
        last_name: '',
        first_name: ''
      },
      defaultList: [],
      tempFileList: [],
      userAvatar: '',
      uploadFile: '',
      showUplaodAvatar: false,
      userList: {},
      ruleCustom: {
        ...userValidateRules,
        confirmPassword: [
          { validator: validatePassCheck, trigger: 'blur' }
        ],
        email: [
          { type: 'email', required: true, validator: validateEmailCheck, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight']),
    ...mapState('usercenter', ['userInfo']),
    ...mapGetters('systemglobal', ['createDialogShow']),
    containerHeight: function () {
      return this.appBodyMainHeight
    },

    routerName: function () {
      return this.$route.name
    }
  },

  methods: {
    ...mapMutations('systemglobal', ['setCreateDialogShow']),

    updateProfiles: function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.post('/api/auth/user/' + this.userID, this.userData).then(response => {
            this.$Message.success({
              content: '更新信息成功',
              duration: 10,
              closable: true
            })
          }, response => {
            this.$Message.error({
              content: '更新信息失败',
              duration: 10,
              closable: true
            })
          })
        }
      })

    },

    changeAvatar: function () {
      this.showUplaodAvatar = true
    },

    closeDialog: function () {
      this.showUplaodAvatar = false
      this.clearTempFiles()
    },
    changePassword: function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$axios.put('/api/auth/user/' + this.userID, this.userData).then(response => {
            if (response.data.result.code === 1) {
              this.$Message.success({
                content: response.data.result.message,
                duration: 10,
                closable: true
              })
            } else {
              this.$Message.error({
                content: response.data.result.message,
                duration: 10,
                closable: true
              })
            }
          }, response => {
            this.$Message.error({
              content: '密码修改失败',
              duration: 10,
              closable: true
            })
          })
        }
      })
    },

    onUpdateAvatar: function () {
      let parameters = { uploadList: [this.uploadFile] }
      this.$axios.patch('/api/ucenter/profiles/update_avatar', parameters).then(response => {
        this.clearTempFiles()
      }, response => {
      })
      this.showUplaodAvatar = false
    },


    clearTempFiles: function () {
      this.$axios.delete('/api/ucenter/profiles/clear_tempfiles/' + this.tempFileList).then(response => {
        this.tempFileList = []
      }, response => {
      })
    },

    handleSuccess(res, file, fileList) {
      file.url = res.result.url
      file.id = res.result.file_id
      this.userAvatar = file.url
      this.uploadFile = file.id
      this.tempFileList.push(file.id)
    },

    handleRemove(file, fileList) {
      this.uploadList = fileList
    },

    handleFormatError(file) {
      this.$Message.warning({
        content: '文件格式不正确,格式：\'jpg\',\'jpeg\',\'png\'',
        duration: 10,
        closable: true
      })
    },
    handleMaxSize(file) {
      this.$Message.warning({
        content: '文件大小超过10M限制',
        duration: 10,
        closable: true
      })
    },

    getUserInfo: function () {
      this.$axios.get('/api/auth/user/' + this.userID).then(response => {
        this.userData = response.data.result
        this.userData.oldPassword = ''
        this.userAvatar = this.userData.avatar
      }, response => {
      })
    },

    userExists: function (email) {
      let result = false
      for (let i = 0; i < this.userList.length; i++) {
        if (email.trim() === this.userList[i].email && parseInt(this.userID) !== this.userList[i].id) {
          result = true
        }
      }
      return result
    },

    getUserList: function () {
      this.$axios.get('/api/auth/users').then(response => {
        this.userList = response.data.result
      }, response => {
        // error callback
      })
    },
  },

  created: function () {
    this.getUserInfo()
    this.getUserList()
  },

  mounted: function () {

  },

  watch: {},

  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.info-body {
  width: 75%;
  padding-top: 16px;
  margin-left: auto;
  margin-right: auto;
}
</style>
