<template>
  <div class="app-body-head-default">
    <div class="app-body-header-bar-default">
      <Row>
        <Col :lg="16" :sm="18">
        <div class="app-body-header-leftbar-default pull-left">
          <ul class="app-body-head-menu">
            <router-link :to="'/ucenter/' + userID + '/profiles'" :exact="false" tag="li"
              active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
              <a href=""><i class="fa fa-fw  fa-bug"></i>账号</a>
            </router-link>
            <!--<router-link to="/system/usergroup" tag="li" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">-->
            <!--<a :href="'/project/'+projectID+'/fortesting'" >-->
            <!--<i class="fa fa-fw  fa-bus"></i>用户组-->
            <!--</a>-->
            <!--</router-link>-->

            <!--<router-link to="/system/usergroup" tag="li" active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">-->
            <!--<a :href="'/project/'+projectID+'/fortesting'" >-->
            <!--<i class="fa fa-fw  fa-bus"></i>通知-->
            <!--</a>-->
            <!--</router-link>-->

            <!--<router-link v-if="routerName === 'projectSettings'"-->
            <!--:to="{ name: '', params: { projectID: projectID,page: '1' }}" tag="li"-->
            <!--class="app-body-head-menu-item-active app-body-head-menu-item">-->
            <!--<a :href="'/project/'+projectID+'/documents'">-->
            <!--<Icon :size="24" type="ios-settings"/>-->

            <!--</a>-->
            <!--</router-link>-->
          </ul>
        </div>
        </Col>
        <Col :lg="2" :sm="0" :xs="0">
        <div class="pull-left" style="padding-top: 20px;" v-if="headMenu.searchBox">
          <div>
            <Input v-model="systemAdminearchKey" @on-keyup="searchObject" search size="small" style="width:200px;"
              placeholder="输入用户名，邮箱查找" />
          </div>
        </div>
        </Col>
        <Col :lg="6" :sm="6" :xs="6">
        <div class="app-body-header-rightbar-default pull-right">
          <span @click="newObject" v-if="headMenu.newObject">
            <Avatar style="background-color: #32be77;" class="cursor-hand" icon="md-add" />
          </span>
        </div>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex'

export default {
  components: {
  },
  name: 'UCenterHead',
  props: ['userID'],
  data() {
    return {
      systemAdminearchKey: ''
    }
  },

  computed: {
    ...mapGetters('ucenterglobal', ['headMenu'])

  },
  methods: {
    ...mapMutations(['setDynamicMenu']),
    ...mapMutations('ucenterglobal', ['setCreateDialogShow', 'setSystemAdminearchKey']),
    newObject() {
      this.setCreateDialogShow(true)
    },

    searchObject: function () {
      this.setSystemAdminearchKey(this.systemAdminearchKey)
    }
  },
  created: function () {
  },

  watch: {},

  // beforeRouteUpdate (to, from, next) {
  //   this.setDynamicMenu({})
  //   next()
  // },

  beforeRouteLeave(to, from, next) {
    this.setDynamicMenu({})
    next()
  }

}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import '../../layout/appBody';
@import '../../layout/appHead';
@import '../../assets/teamvision/global/less/global';
</style>
