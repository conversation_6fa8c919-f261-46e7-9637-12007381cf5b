import { Message } from 'view-design';
const getCurrDateDifference = (days) => {
  let nowDate = new Date()
  let nowYear = nowDate.getFullYear();
  let nowMonth = (nowDate.getMonth() + 1).toString().padStart(2, '0');
  let nowDay = nowDate.getDate().toString().padStart(2, '0');
  let endDate = nowYear + "-" + nowMonth + "-" + nowDay;

  let beforeDate = new Date(nowDate.getTime() - days * 24 * 3600 * 1000)
  let startY = beforeDate.getFullYear();
  let startM = (beforeDate.getMonth() + 1).toString().padStart(2, '0');
  let startD = beforeDate.getDate().toString().padStart(2, '0');
  let startDate = startY + "-" + startM + "-" + startD;
  return [startDate, endDate]
}

const appendQueryParams = (uri, params) => {
  const searchParams = new URLSearchParams(params);
  const queryString = searchParams.toString();
  if (queryString) {
    uri = uri + (uri.includes('?') ? '&' : '?') + queryString;
  }
  return decodeURIComponent(uri);
}

const dateFormat = (time) => {
  var date = new Date(time);
  var year = date.getFullYear();
  /* 在日期格式中，月份是从0开始的，因此要加0
   * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
   * */
  var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
  var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  var minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  var seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return (year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds);
}

/**
 * 根据输入的 TestCase ID，查找下一个 TestCase ID
 * @param {Array} treeData 树形数据
 * @param {Number} targetId 输入的 TestCase ID
 * @returns {Number|null} 返回下一个 TestCase ID，或 null（如果没有找到）
 */
function findNextTestCaseId(treeData, targetId) {
  let found = false; // 标记是否找到目标 TestCase ID
  let nextTestCaseId = null; // 保存下一个 TestCase ID

  // 深度优先遍历树，查找下一个 TestCase ID
  function traverseTree(nodes) {
    for (const node of nodes) {
      // 如果之前找到了目标 ID，则当前节点的 TestCase 就是下一个目标
      if (found && node.TestCase) {
        nextTestCaseId = node.TestCase;
        return true; // 结束递归
      }

      // 如果当前节点的 TestCase 等于目标 ID，设置 found 为 true
      if (node.TestCase === targetId) {
        found = true;
      }

      // 如果有子节点，继续递归遍历
      if (node.children && node.children.length > 0) {
        const isFound = traverseTree(node.children);
        if (isFound) return true; // 如果找到结果，结束递归
      }
    }
    return false; // 遍历结束
  }

  // 开始从根节点递归
  traverseTree(treeData);

  // 返回结果
  return nextTestCaseId;
}

function copyLink(link) {
  try {
    navigator.clipboard.writeText(link);
    setTimeout(() => {
      Message.success('复制到粘贴板 成功！');
    }, 200);
  } catch (err) {
    Message.error('复制失败，请手动复制链接！');
  }
}

export {
  getCurrDateDifference,
  appendQueryParams,
  findNextTestCaseId,
  copyLink
}
