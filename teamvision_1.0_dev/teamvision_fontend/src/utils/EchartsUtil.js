import * as echarts from "echarts";

export const createStakedColumnChart = (data, xdata, ydata, xtype, ytype, stack) => {
  for (let i = 0; i < data['series_data'].length; i++) {
    data['series_data'][i]['label'] = { show: true }
    data['series_data'][i]['type'] = 'bar'
    if (stack === true) {
      data['series_data'][i]['stack'] = 'Total'
    }
  }
  let option = {
    title: {
      text: data['chart_title']
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
      }
    },
    legend: {
      type: 'scroll'
    },
    grid: {
      left: '20',
      right: '20',
      bottom: '20',
      containLabel: true
    },
    yAxis: {
      type: ytype,
      data: ydata
    },
    xAxis: {
      type: xtype,
      data: xdata,
      axisLabel: {
        interval: 0,
        rotate: 40
      },
    },

    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'slider',
        xAxisIndex: [0],
        filterMode: 'filter',
        moveHandleSize: 0,
      },
      // {
      //   id: 'dataZoomY',
      //   type: 'slider',
      //   yAxisIndex: [0],
      //   filterMode: 'empty'
      // }
    ],
    series: data['series_data']
  }
  return option
}

export const createStackedLineChart = (data, xdata, ydata, xtype, ytype, smooth, stack) => {
  for (let i = 0; i < data['series_data'].length; i++) {
    data['series_data'][i].smooth = smooth
    data['series_data'][i]['label'] = { show: true }
    data['series_data'][i]['type'] = 'line'
    data['series_data'][i]['stack'] = stack
  }
  let option = {
    title: {
      text: data['chart_title']
    },
    legend: {
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    yAxis: {
      type: ytype,
      data: ydata
    },
    xAxis: {
      type: xtype,
      data: xdata,
      axisLabel: {
        interval: 0,
        rotate: 40
      },
    },
    series: data['series_data'],
    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'slider',
        xAxisIndex: [0],
        filterMode: 'filter',
        moveHandleSize: 0
      },
      // {
      //   id: 'dataZoomY',
      //   type: 'slider',
      //   yAxisIndex: [0],
      //   filterMode: 'empty'
      // }
    ],
  }
  return option
}

export const createCirclePieChart = (data, title) => {
  for (let i = 0; i < data['series_data'].length; i++) {
    data['series_data'][i]['label'] = {
      show: true,
      formatter(param) {
        // correct the percentage
        return param.name + ' (' + param.percent + '%)';
      }
    }
    data['series_data'][i]['type'] = 'pie'
    data['series_data'][i]['radius'] = ['40%', '70%']
  }
  let option = {
    title: {
      text: title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 'bottom'
    },
    series: data['series_data']

  }
  return option
}


export const initLineChart = (chartID, chartTitle) => {
  let initChart = echarts.init(document.getElementById(chartID))
  let option = {
    title: {
      text: chartTitle,
      x: 'center'
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      type: 'scroll',
      //orient: 'vertical',
      right: "5%",
      left: "5%",
      //top: "10%",
      bottom: 0,
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        //saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["-"],
    },
    yAxis: {
      type: "value",
    },
    series: [],
  };
  initChart.setOption(option);
  window.addEventListener("resize", function () {
    initChart.resize();
  });
  return initChart
}
