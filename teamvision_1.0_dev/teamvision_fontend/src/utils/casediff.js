/**
 *@description: 
 *@author: zhangpeng
 *@date: 2024-01-16
 */

// function deepClone(obj) {
//   if (typeof obj === "object") {
//     return JSON.parse(JSON.stringify(obj));
//   } else {
//     return obj;
//   }
// }

// function compareObjects(object1, object2) {
//   if (object1 === object2) return true;
//   const keys1 = Object.keys(object1);
//   const keys2 = Object.keys(object2);

//   if (keys1.length !== keys2.length) {
//     return false;
//   }

//   for (let key of keys1) {
//     if (object1[key] !== object2[key]) {
//       return false;
//     }
//   }

//   return true;
// }

// function findNodeById(tree, id) {
//   if (tree.children && tree.children.length > 0) {
//     for (let child of tree.children) {
//       if (child.data && child.data.id === id) {
//         return child
//       }
//     }
//   }
//   return null;
// }

// function compareTrees(oldTree, newTree, patches) {
//   if (!compareObjects(oldTree.data, newTree.data)) {
//     patches.modified.push(newTree.data);
//   }

//   if (oldTree.children && oldTree.children.length > 0) {
//     for (let oldChild of oldTree.children) {
//       const newChild = findNodeById(newTree, oldChild.data.id);
//       if (!newChild) {
//         patches.deleted.push(oldChild.data);
//       } else {
//         compareTrees(oldChild, newChild, patches);
//       }
//     }
//   }

//   if (newTree.children) {
//     for (let child of newTree.children) {
//       if (!findNodeById(oldTree, child.data.id)) {
//         child.data['Parent'] = newTree.data['id']
//         patches.added.push(deepClone(child));
//       }
//     }
//   }
// }

// export function diffCaseTree(oldTree, newTree) {
//   var patches = {
//     'added': [],
//     'deleted': [],
//     'modified': []
//   };

//   compareTrees(oldTree, newTree, patches);
//   return patches;
// }

//------------------------------------------------------------------------------------------------

function isEqual(nodeA, nodeB) {
  //console.log("nodeA, nodeB", nodeA, nodeB)
  if (nodeA === nodeB) return true;
  if (typeof nodeA !== 'object' || typeof nodeB !== 'object' || nodeA === null || nodeB === null) return false;
  const keysA = Object.keys(nodeA);
  const keysB = new Set(Object.keys(nodeB));
  if (keysA.length !== keysB.size) return false;
  for (const key of keysA) {
    if (key === 'Desc' || key === 'ExpectResult' || key === 'Precondition' || key === 'Title' || key === 'Priority') {
      if (!keysB.has(key)) return false;
      if (!isEqual(nodeA[key], nodeB[key])) return false;
    }
  }
  return true;
}

function isEqual_2(nodeA, nodeB) {
  if (nodeA === nodeB) return true;
  if (typeof nodeA !== 'object' || typeof nodeB !== 'object' || nodeA === null || nodeB === null) return false;
  const keysA = Object.keys(nodeA);
  const keysB = new Set(Object.keys(nodeB));
  if (keysA.length !== keysB.size) return false;
  for (const key of keysA) {
    if (!keysB.has(key)) return false;
    if (!isEqual(nodeA[key], nodeB[key])) return false;
  }
  return true;
}

function compareNodes(oldNode, newNode, changes) {
  if (!isEqual(oldNode.data, newNode.data)) {
    changes.modified.push(newNode.data);
  }
  compareChildren(oldNode.children, newNode.children, newNode.data['id'], changes);
}

function compareChildren(oldChildren, newChildren, newTreeId, changes) {
  const oldMap = new Map(oldChildren.map(child => [child.data.id, child]));
  const newMap = new Map(newChildren.map(child => [child.data.id, child]));

  oldMap.forEach((oldChild, id) => {
    if (!newMap.has(id)) {
      changes.deleted.push(oldChild.data);
    }
  });

  newMap.forEach((newChild, id) => {
    if (!oldMap.has(id)) {
      newChild.data['Parent'] = newTreeId
      changes.added.push(newChild);
    }
  });

  newMap.forEach((newChild, id) => {
    if (oldMap.has(id)) {
      const oldChild = oldMap.get(id);
      compareNodes(oldChild, newChild, changes);
    }
  });
}

export function diffCaseTree(oldTree, newTree) {
  const changes = {
    added: [],
    deleted: [],
    modified: []
  };
  compareNodes(oldTree, newTree, changes);
  return changes;
}
