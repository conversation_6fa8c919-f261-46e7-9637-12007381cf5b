import Cookies from 'js-cookie'

export const TOKEN_KEY = 'token'

export const setToken = (token, KEY = TOKEN_KEY) => {
  Cookies.set(KEY, token, { expires: 7 })
}

export const getToken = () => {
  const token = Cookies.get(TOKEN_KEY)
  if (token) return token
  else return false
}

export const createRandom = (length) => {
  var tmp = '';
  //var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    tmp += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return tmp
}

