<!--
@file: view数据卡片
-->
<template>
  <FlexBox class="number-card" justify="space-between">
    <div :class="'number-card__line ' + linePlace" v-if="hasLine" :style="'background: ' + lineColor + ';'"></div>
    <FlexBox class="number-card__header" v-if="hasHeader">
      <slot name="header"></slot>
    </FlexBox>
    <FlexBox :class="{
                'number-card__item': true,
                [item.class]: true
            }" dir="row" justify="center" align="flex-start" v-for="(item, key) in data" :key="key">
      <template v-if="item.slot">
        <slot :name="item.slot" :scope="item"></slot>
      </template>
      <template v-else>
        <div>
          <div class="number-card__title">
            {{item.title}}
            <Help v-if="item.desc" :content="item.desc"></Help>
          </div>
          <template>
            <el-tooltip :content="item.number" placement="top" v-if="isShowToolTip(item)">
              <div class="number-card__number">{{item.number|setLengthOmit(numberLengthLimit)}}</div>
            </el-tooltip>
            <div v-else style="text-align: center;">
              <div>
                <div class="number-card__number">{{item.number}}</div>
                <div class="number-card__unit">{{item.unit}}</div>
              </div>
              <div v-if="item.demand">
                <div class="number-card__number">{{item.demand}}</div>
                <div class="number-card__unit">{{item.unit}}</div>
              </div>
              <div v-if="item.allcase">
                <div class="number-card__number">{{item.allcase}}</div>
                <div class="number-card__unit">{{item.unit}}</div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="item.radio" :class="`number-card__ratio ${item.ratio ? '' : 'isHide'}`" :style="item.ratioStyle">占比{{item.ratio}}</div>
      </template>
    </FlexBox>
  </FlexBox>
</template>

<style lang="less">
@import "./index";
.number-card {
  min-height: 140px;
  height: 120px;
  max-height: 120px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid @tableBorder;
  font-size: 12px;
  // padding: 0 20px;
  position: relative;
  z-index: 10;
  overflow-x: auto;
  flex-wrap: wrap;
  .number-card__line {
    position: absolute;
    z-index: 20;
    top: 0;
    left: 0;
    content: "";
    display: block;
    margin: 0;
    &.left {
      width: 7px;
      height: 100%;
    }
    &.top {
      width: 100%;
      height: 2px;
    }
  }
  .number-card__header {
    font-size: 16px;
    flex: 1 0 100%;
  }
  .number-card__item {
    position: relative;
    padding: 0 10px;
    .flex(1);
    flex-basis: 80px;
    flex-shrink: 0;
  }
  .number-card__item:after {
    content: "";
    position: absolute;
    width: 1px;
    height: 80px;
    background: #ececec;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
  .number-card__item:last-child:after {
    content: "";
    display: none;
  }

  .number-card__title {
    font-size: 14px;
    color: @minorText;
    // font-weight: bold;
    .icon {
      font-size: 14px;
    }
  }
  .number-card__number {
    // font-family: Dinbold, Arial, Helvetica, sans-serif;
    color: #242d37;
    font-size: 16px;
    letter-spacing: 0;
    margin-top: 8px;
    // position: relative;
    // top: 10px;
    // line-height: 43px;
    display: inline-block;
  }
  .number-card__unit {
    // font-family: Dinbold, Arial, Helvetica, sans-serif;
    color: #242d37;
    font-size: 12px;
    letter-spacing: 0;
    margin-top: 8px;
    // position: relative;
    // top: 10px;
    // line-height: 43px;
    display: inline-block;
  }
  .number-card__ratio {
    font-size: 14px;
    color: @ignoreText;
    &.isHide {
      visibility: hidden;
    }
    // align-self: flex-end;
  }
}
</style>

<script>
import FlexBox from "./FlexBox.vue";

export default {
  components: { FlexBox },
  props: {
    data: {
      type: Object,
    },
    // 线的颜色
    lineColor: {
      type: String,
      default: "",
    },
    // 线的位置，只有left 和 top 2中
    linePlace: {
      type: String,
      default: "left",
    },
    numberLengthLimit: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  computed: {
    hasLine() {
      return this.lineColor && this.linePlace ? true : false;
    },
    hasHeader() {
      return this.$slots.header ? true : false;
    },
  },
  methods: {
    isShowToolTip(item) {
      let isShowToolTip = false;
      const numberStr = String(item.number);
      if (this.numberLengthLimit && numberStr.length > this.numberLengthLimit) {
        isShowToolTip = true;
      }
      return isShowToolTip;
    },
  },
};
</script>
