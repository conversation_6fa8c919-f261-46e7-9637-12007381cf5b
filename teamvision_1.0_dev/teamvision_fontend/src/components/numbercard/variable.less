// less变量
@turquoise: #00d1b2;
@greenColor: #1ec773;
@blueColor: #00b0e5;
@redColor: #ff0000;
@orangeColor: #ff930f;
@grayColor: #cccccc;
@blackColor: hsl(0, 0%, 4%);
@smokeColor: whitesmoke;
@whiteColor: #ffffff;

// body全局顶层定义
@body-background-color: whitesmoke;
@body-family: Arial, Helvetica, sans-serif;
@body-size: 14px;
@body-color: @blackColor;
@link: @turquoise;
@link-hover: @blackColor;

// 请求进度条
@progressColor: @turquoise;

// button使用
@disabledColor: @grayColor;
@primaryColor: @blackColor;
@infoColor: @blueColor;
@successColor: @greenColor;
@warningColor: @orangeColor;
@dangerColor: @redColor;

// 分页使用
@currentColor: @greenColor;


/*stable color*/
@ksRed: #fe4444;
@blue: #2792fd;
@orange: #ff9c28;
@gree: #00b740;
@ksBlue: #557DFC;
/**
 系统样式变量
*/
//global set
@maxZindex: 9999;
//global set
@elementZindex: 2000;
// 标题文字颜色
@titleText: #262626;
// 次要文字
@minorText: #778088;
// 可忽略文字
@ignoreText: #737373;
// 内容文字颜色
@contentText: #4c4c4c;
// 背景色/表头
@backgroundColor: #f3f6fb;

// 表格头部背景色
@tableHeader: #f3f6fb;
// 表格线框色
@tableBorder: #e4e8ef;
// 组件外框
@compShadow: #d3d3d3;
// 提醒色
@toastColor: #ff9c28;
// 次图表色
@minorChart: #2792fd;

// 修改icon
@editColor: #6b798e;

// silder
@sidebarWidth: 200px;
@sidebarBackgroundColor: #ffffff;
@sidebarBackgroundLinerImage: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
@sidebarItemBackgroundColor: transparent;
@sidebarItemActiveBackgroundColor: @mainBtnBackgroundColor;
@sidebarFontColor: @minorText;
@sidebarFontSize: 14px;
@sidebarActiveFontColor: #ffffff;
@siderbarCollWidth: 86px;
@siderbarTransition: 0.3s ease-in-out;

// 左侧整体缩进
@blockPaddingLeft: 20px;

// 顶部navbar
// @navbarBgColor: #19233c;
@navbarBgColor: #ffffff;
@navbarColor: @minorText;
@navbarHeight: 50px;

/*
 base var rewrite
 **/
@progressColor: @ksBlue;
@link: @ksBlue;
@link-hover: @ksBlue;

/**
    边框弧度： 10px
*/
@borderRadius10: 10px;
// 边框弧度 20pxp
@borderRadius20: 20px;

// 主内容区域 背景色
@mainContentBackgroundColor: #ffffff;
// 主按钮 hover checked 选中色
@mainBtnBackgroundColor: @ksBlue;
// form 表单background
@formInputBackgroundColor: #1d2338;
// 主文字颜色
@mainTextColor: #000000;
// 主题字体大小
@fontSize14: 14px;
// 表格头部颜色
@tableBackgroundColor: @tableHeader;
// 表格身体背景颜色
@tableBodyBackgroundColor: @tableHeader;
// 表格边框颜色
@tableBorderColor: @tableBorder;

// 系统设置 title背景色
@systemTitleBackgroundColor: #ffffff;
// 实时统计背景颜色
@systemGridTitleBackgroundColor: #ffffff;
// 系统设置title高度
@systemTitleHeight: 55px;
// 系统设置title line颜色
@systemTitleLineColor: #787ca4;
// 系统设置 title line 长度
@systemTitleLineWidth: 256px;
@padding28: 28px;
@loginContentColor: #222A41;
@checkedBackgroundColor: #409EFF;
@hoverBackgroundColor: #ecf5ff;
@borderColor: #409EFF;
@borderHoverColor: #ecf5ff;