<template>
  <div class="BarWrapper-TestStatusBarStyle">
    <ul class="Bar">
      <li type="PASSED"  :style="{width: pass+'%'}" class="BarSection-passStyle-HoverStyle">
      </li>
      <li type="RETEST" :style="{width: reTest+'%'}" class="BarSection-retestStyle-HoverStyle"></li>
      <li type="BLOCKED" :style="{width: blocked+'%'}" class="BarSection-blockedStyle-HoverStyle"></li>
      <li type="FAILED" :style="{width: fail+'%'}" class="BarSection-failedStyle-HoverStyle"></li>
      <li type="UNTESTED" :style="{width: norun+'%'}" class="BarSection-HoverStyle"></li>
    </ul>
    <div></div>
  </div>
</template>

<script>
  export default {
    name: 'ProcessBar',
    props: ['pass','reTest','fail','blocked','norun'],
    data () {
      return {
        msg: 'Welcome to Your Vue.js App'
      }
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">

  li {
    list-style: none;
  }
  .BarWrapper-TestStatusBarStyle {
    display: inline-flex;
    position: relative;
    height: 16px;
    width: 100%;
  }

  .Bar {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    display: inline-flex;
    min-width: 160px;
    box-sizing: border-box;
    margin-right: -2px;
    border-radius: 3px;
    background: rgb(255, 255, 255);
  }

  .child {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }

  .Bar li:first-of-type {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }

  .BarSection-passStyle-HoverStyle {
    /*width: 100%;*/
    height: 100%;
    cursor: pointer;
    margin: 0px 2px 0px 0px;
    border-width: 0px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
    background: rgb(45, 211, 111);
  }

  .BarSection-retestStyle-HoverStyle {
    /*width: 2%;*/
    height: 100%;
    cursor: pointer;
    margin: 0px 2px 0px 0px;
    border-width: 0px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
    background: rgb(255, 191, 0);
  }

  .BarSection-blockedStyle-HoverStyle {
    /*width: 2%;*/
    height: 100%;
    cursor: pointer;
    margin: 0px 2px 0px 0px;
    border-width: 0px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
    background: rgb(108, 110, 150);
  }

  .BarSection-failedStyle-HoverStyle {
    /*width: 8%;*/
    height: 100%;
    cursor: pointer;
    margin: 0px 2px 0px 0px;
    border-width: 0px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
    background: rgb(246, 77, 62);
  }

  .BarSection-HoverStyle {
    /*width: 61%;*/
    height: 100%;
    cursor: pointer;
    margin: 0px 2px 0px 0px;
    background: rgb(222, 223, 229);
    border-width: 0px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  }

</style>
