<template>
  <div style="display: inline-flex">
    <div v-show="readonly" @click="clickDisplayText(id)">
      <Poptip :disabled="displayValue.length < 10" trigger="hover" transfer  :content="displayValue" placement="top-start" padding="2px 4px 2px 4px" word-wrap width="200">
        <span :style="'max-width:' + displayWidth +'px'" class="text-display cursor-hand">
            <router-link v-if="url" :to="url" tag="span" style="max-width:100%">
                <span class="text-display" >{{ displayValue }}</span>
            </router-link>
            <span v-if="url===''" class="text-display" >{{ displayValue }}</span>
        </span>
      </Poptip>
    </div>
    <Input v-if="edited" v-model="editValue" search icon="md-return-left"  @on-search="onOk(id)"  :maxlength="maxlength" :style="'width:100%'" placeholder="placeHolder">
    <ButtonGroup slot="suffix" style="width: 100px; margin-left: 10px; padding-top: 0px;" shape="circle" size="small">
      <Icon :size="20" @click="onOk(id)" color="black" class="cursor-hand" type="md-checkmark" />
      <Icon :size="20" @click="onCancel(id)" color="black" class="cursor-hand" type="md-close" />
    </ButtonGroup>
    </Input>
  </div>
</template>

<script>
  export default {
    name: 'labelEditorInput',
    props: ['placeHolder', 'displayText','editing','displayWidth','id','maxlength','clickEdit','url'],
    data () {
      return {
        editValue: '',
        displayValue: '',
        mouseHover: false,
        edited: false,
        readonly: true,

      }
    },
    computed: {
    },
    methods: {
      onMouseOver: function () {
        this.mouseHover = true
      },

      onMouseOut: function () {
        this.mouseHover = false
      },

      onClickEdit: function (e) {
        this.mouseHover = false
        this.readonly = false
        this.edited = true
      },
      onCancel: function (id) {
        this.mouseHover = false
        this.readonly = true
        this.edited = false
        this.$emit('cancelUpdate', this.editValue,id)
      },
      onOk: function (id) {
        this.displayValue = this.editValue
        this.$emit('updateValue', this.editValue,id)
        this.mouseHover = false
        this.readonly = true
        this.edited = false
      },

      clickDisplayText: function (id) {
        this.$emit('clickText',id)
      },

      initInputDisplayState: function () {
        if (this.editing) {
          this.readonly = false
          this.edited = true
        }
      }

    },
    created: function () {
      this.displayValue = this.displayText
      this.editValue = this.displayText
      this.initInputDisplayState()
    },
    watch: {
      displayText: function () {
        this.displayValue = this.displayText
        this.editValue = this.displayText
        this.show = true
        this.mouseHover = false
      },
      clickEdit: function (value) {
          if((value+'' === this.id+'') && value !==0){
              this.mouseHover = false
              this.readonly = false
              this.edited = true
          }
      }
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.text-display {
  display: inline-flex;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  min-width: 0px;
  /*flex: 1 1 auto;*/
  padding: 0px 2px;
  /*overflow: hidden;*/
  color: #606266;
  max-width: 100%;
}

 
 .ivu-poptip-popper{
   min-width: 20px;
 }

</style>
