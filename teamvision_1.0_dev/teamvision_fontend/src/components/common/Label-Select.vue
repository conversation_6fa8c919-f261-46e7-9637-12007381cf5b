<template>
  <div class="hover-select-wrapper" @mouseover="onMouseOver" @mouseleave="onMouseOut">
    <span v-if="readonly || !showSelect" class="label">
      <slot>
        <span style="text-overflow:ellipsis;overflow: hidden;">{{ displayText }}</span>
      </slot>
    </span>
    <span v-else>
      <span>
        <Select v-model="selectedValue" placeholder="请选择" @on-change="updateValue" label-in-value>
          <Option v-for="item in options" :key="item.id" :value="item.id">{{ item.label }}</Option>
        </Select>
        <!-- <Icon type="ios-create-outline" :size="18" /> -->
      </span>
    </span>
  </div>
</template>

<script>
export default {
  name: 'labelSelect',
  props: {
    displayText: {
      type: String,
      default: "无",
    },
    options: {
      type: Array,
      required: true,
    },
    value: {
      type: Number,
      default: null,
    },
    readonly: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
  },
  data() {
    return {
      showSelect: false,
      selectedValue: this.value,
    }
  },

  methods: {
    onMouseOver: function () {
      if (!this.readonly) {
        this.showSelect = true;
      }
    },

    onMouseOut: function () {
      this.showSelect = false
    },

    updateValue(value) {
      this.$emit('updateValue', value.value, value.label);
    },
  },

  created() {
    console.log()
  },

  watch: {
    value(newVal) {
      if (this.selectedValue !== newVal && !this.readonly) {
        this.selectedValue = newVal;
      }
    },
    readonly(newVal) {
      this.showSelect = !newVal;
    },
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.hover-select-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
  /* 当鼠标悬停时显示手型光标，表明可以点击或交互 */
}

.select-container {
  position: absolute;
  left: 0;
  top: 100%;
  /* Adjust this based on your design needs */
  z-index: 1;
  background-color: white;
  border: 1px solid #dcdcdc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hover-select-wrapper /deep/ .ivu-select-dropdown {
  margin: 0px;
  padding: 0px;
}
</style>
