<template>
  <div class="labelEditorInput">
    <div class="labelEditor" v-if="readonly" @mouseover="onMouseOver" @mouseout="onMouseOut">
      <Poptip trigger="hover" transfer :content="displayValue">
        <span @click="onClickEdit" class="text-display cursor-hand">
          <slot :mouseHover="mouseHover">
            <span style="text-overflow:ellipsis;overflow: hidden;white-space: nowrap;display:inline-block">
              <span> {{ displayValue }}</span>
              <span style="width: 10px;display: inline-block;margin-right: 5px;">
                <Icon v-if="mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
              </span>
            </span>
          </slot>
        </span>
      </Poptip>
    </div>
    <Input v-if="edited" v-model="editValue" @on-enter="onOk(id)" :style="'width:' + displayWidth + 'px'"
      :maxlength="maxLength" :show-word-limit="maxLength > 0" :placeholder="placeHolder">
    <ButtonGroup slot="suffix" style="width: 100px; margin-left: 10px; padding-top: 0px;" shape="circle">
      <Icon :size="18" @click="onOk(id)" color="black" class="cursor-hand" type="md-checkmark" />
      <Icon :size="18" @click="onCancel(id)" color="black" class="cursor-hand" type="md-close" />
    </ButtonGroup>
    </Input>
  </div>
</template>

<script>
export default {
  name: "labelEditorInput",
  props: [
    "placeHolder",
    "displayText",
    "editing",
    "displayWidth",
    "id",
    "maxLength",
    "search",
  ],
  data() {
    return {
      editValue: "",
      displayValue: "",
      mouseHover: false,
      edited: false,
      readonly: true,
      editorWidth: 0,
    };
  },
  computed: {},
  methods: {
    onMouseOver: function () {
      this.mouseHover = true;
    },

    onMouseOut: function () {
      this.mouseHover = false;
    },

    onClickEdit: function (e) {
      this.mouseHover = false;
      this.readonly = false;
      this.edited = true;
    },
    onCancel: function (id) {
      this.mouseHover = false;
      this.readonly = true;
      this.edited = false;
      this.$emit("cancelUpdate", this.editValue, id);
    },
    onOk: function (id) {
      this.displayValue = this.editValue;
      this.$emit("updateValue", this.editValue, id);
      this.mouseHover = false;
      this.readonly = true;
      this.edited = false;
    },

    initInputDisplayState: function () {
      if (this.editing) {
        this.readonly = false;
        this.edited = true;
      }
    },
  },
  created: function () {
    this.displayValue = this.displayText;
    this.editValue = this.displayText;
    this.initInputDisplayState();
  },
  watch: {
    displayText: function () {
      this.displayValue = this.displayText;
      this.editValue = this.displayText;
      this.show = true;
      this.mouseHover = false;
    },
    displayWidth: function (value) {
      if (!displayWidth) {
        this.editorWidth = "100%";
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less">
.text-display {
  display: inline;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 22px;
}

.labelEditorInput .ivu-input {
  caret-color: rgb(0, 102, 255);
  box-sizing: border-box;
  width: 100%;
  color: rgb(32, 45, 64);
  position: relative;
  white-space: nowrap;
  height: 32px;
  line-height: 20px;
  font-size: 13px;
  box-shadow: none !important;
  outline: none;
  border-radius: 2px;
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1) 0s;
  /*overflow: hidden;*/
  padding: 6px 12px 6px 24px;
  border-width: initial !important;
  border-style: none !important;
  border-color: initial !important;
  border-image: initial !important;
  border-bottom: 1px solid #e8eaec !important;
}

.labelEditor {
  height: 32px;
  line-height: 20px;
  font-size: 16px;
  padding: 6px 12px 6px 0px;
  font-weight: bold;
}
</style>
