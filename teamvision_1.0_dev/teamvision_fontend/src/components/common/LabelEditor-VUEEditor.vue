<template>
  <div>
    <Tooltip content="点击开始编辑" transfer="">
      <div v-if="show" @mouseout="onMouseOut" class="cursor-hand" @click="onClickEdit" @mouseover="onMouseOver">
        <span style="" v-html="displayValue"></span>
        <span style="margin-top:-10px;">
          <Icon type="ios-create-outline" :size="18" class="cursor-hand" />
        </span>
      </div>
    </Tooltip>
    <vue-editor v-if="edited" v-model="editValue" :editorToolbar="editorToolBar" useCustomImageHandler
      @imageAdded="handleImageAdded" :placeholder="placeHolder">
    </vue-editor>
    <ButtonGroup v-if="edited" slot="suffix" style="width: 100px; margin-left: 0px; padding-top: 13px;" shape="circle"
      size="small">
      <Icon :size="20" @click="onOk" color="black" class="cursor-hand" type="md-checkmark" />
      <Icon :size="20" @click="onCancel" color="black" class="cursor-hand" type="md-close" />
    </ButtonGroup>
  </div>
</template>

<script>
import { VueEditor } from 'vue2-editor'
export default {
  name: 'labelEditorVueEditor',
  props: ['placeHolder', 'displayText'],
  data() {
    return {
      editorToolBar: [
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }], [{ 'color': [] }, { 'background': [] }],
        ['image']
      ],
      editValue: '',
      displayValue: '',
      mouseHover: false,
      edited: false,
      show: true
    }
  },
  computed: {
  },
  methods: {
    onMouseOver: function () {
      this.mouseHover = true
    },

    onMouseOut: function () {
      this.mouseHover = false
    },

    onClickEdit: function () {
      this.mouseHover = false
      this.show = false
      this.edited = true
    },
    onCancel: function () {
      this.mouseHover = false
      this.show = true
      this.edited = false
    },
    onOk: function () {
      this.displayValue = this.editValue
      this.$emit('updateValue', this.editValue)
      this.mouseHover = false
      this.show = true
      this.edited = false
    },
    handleImageAdded: function (file, Editor, cursorLocation, resetUploader) {
      // An example of using FormData
      // NOTE: Your key could be different such as:
      // formData.append('file', file)

      if (file.size / 1024 / 1024 > 10) {
        this.$Message.error({
          content: '附件文件不能大于10M',
          duration: 10,
          closable: true
        })
        return
      }

      if (/^image/.test(file.type)) {
        //创建一个reader
        var that = this
        let reader = new FileReader()
        //将图片转成base64格式
        reader.readAsDataURL(file)
        //读取成功后的回调
        reader.onloadend = function () {
          let result = this.result
          let img = new Image()
          img.src = result
          //console.log('********未压缩前的图片大小********')
          //console.log(result.length / 1024)
          img.onload = function () {
            if (result.length / 1024 / 1024 < 1) {
              let data = that.compressImage(img, 0.8)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }

            else if (result.length / 1024 / 1024 > 1 && result.length / 1024 / 1024 < 3) {
              let data = that.compressImage(img, 0.5)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }
            else if (result.length / 1024 / 1024 > 3 && result.length / 1024 / 1024 < 6) {
              let data = that.compressImage(img, 0.3)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }
            else if (result.length / 1024 / 1024 > 6) {
              let data = that.compressImage(img, 0.2)
              Editor.insertEmbed(cursorLocation, 'image', data);
            }

          }
        }
      }

      let formData = new FormData()

      //      formData.append("file", file)
      //      this.$axios({
      //        url: '/api/project/issue/attachments',
      //        method: "POST",
      //        data: formData
      //      })
      //        .then(result => {
      //          console.log(result)
      //          let url = result.data.result.url; // Get url from response
      //          Editor.insertEmbed(cursorLocation, 'image', url);
      //          resetUploader();
      //        })
      //        .catch(err => {
      //          console.log(err);
      //        });
    },

    compressImage(img, size) {
      let canvas = document.createElement('canvas')
      let ctx = canvas.getContext('2d')
      let initSize = img.src.length
      let width = img.width
      let height = img.height

      if (width > 600) {
        width = 600
      }

      if (height > 800) {
        height = 800
      }
      canvas.width = width
      canvas.height = height
      // 铺底色
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0, width, height)
      //进行最小压缩
      let ndata = canvas.toDataURL('image/jpeg', size)
      // console.log('*******压缩后的图片大小*******')
      // console.log(ndata)
      // console.log(ndata.length / 1024)
      return ndata
    },

  },
  created: function () {
    this.displayValue = this.displayText
    this.editValue = this.displayText
    this.edited = false
  },
  watch: {
    displayText: function () {
      this.displayValue = this.displayText
      this.editValue = this.displayText
      this.show = true
      this.mouseHover = false
      this.edited = false
    }
  },
  components: {
    VueEditor
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
