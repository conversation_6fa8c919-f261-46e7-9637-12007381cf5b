<template>
  <div>
    <div v-if="readonly" @mouseout="onMouseOut" @mouseover="onMouseOver">
      <span @click="onClickEdit">
        <span>
          <slot :mouseHover="mouseHover">
            <span style="width: 22px;display: inline-block">
              <Icon v-if="mouseHover" type="ios-create-outline" :size="18" class="cursor-hand" />
            </span>
            <span style="text-overflow:ellipsis;overflow: hidden;">{{ displayValue }}</span>
          </slot>
        </span>
      </span>
    </div>
    <div v-if="edited">
      <Select style="width: 60%" transfer v-model="editValue" filterable :label-in-value="true"
        @on-change="onSelectValueChange">
        <Option v-for="item in optionList" :value="item.id" :key="item.id">{{ item.label }}</Option>
      </Select>
      <ButtonGroup style="margin-left:10px; z-index: 1000;" shape="circle" transfer class="cursor-hand">
        <Button transfer @click="onOk" color="black" icon="md-checkmark"></Button>
        <Button transfer @click="onCancel" color="black" icon="md-close"></Button>
      </ButtonGroup>
    </div>
  </div>
</template>

<script>
export default {
  name: 'labelEditorSelect',
  props: ['optionList', 'displayText', 'value', 'itemID'],
  data() {
    return {
      editValue: '',
      displayValue: '',
      editText: '',
      mouseHover: false,
      edited: false,
      show: true,
      readonly: true
    }
  },
  methods: {
    onMouseOver: function () {
      this.mouseHover = true
    },

    onMouseOut: function () {
      this.mouseHover = false
    },

    onClickEdit: function () {
      this.mouseHover = false
      this.show = false
      this.edited = true
      this.readonly = false
    },
    onCancel: function () {
      this.mouseHover = false
      this.show = true
      this.edited = false
      this.readonly = true
    },
    onOk: function () {
      this.displayValue = this.editText
      this.$emit('updateValue', this.editValue, this.value, this.itemID)
      this.mouseHover = false
      this.show = true
      this.edited = false
      this.readonly = true
    },

    onSelectValueChange: function (value) {
      this.editValue = value.value
      this.editText = value.label
    }
  },
  created: function () {
    this.editValue = this.value
    this.displayValue = this.displayText
    this.editText = this.displayText
  },
  watch: {
    displayText: function () {
      this.displayValue = this.displayText
      this.editText = this.displayText
    },
    value: function () {
      this.editValue = this.value
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less"></style>
