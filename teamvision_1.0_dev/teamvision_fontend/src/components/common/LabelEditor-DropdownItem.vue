<template>
  <div>
    <div class="dropdown-editor" @mouseout="onMouseOut" @mouseover="onMouseOver">
      <Dropdown transfer trigger="click" @on-click="setCaseTag">
        <span v-if="showIcon" style="display: inline-flex;align-items: center;width: 20px;">
          <Icon :color="textColor" type="md-disc" />
        </span>
        <span>
          <span style="padding-left: 6px;padding-right: 6px;">{{ displayValue }}</span>
          <Icon v-if="edited" type="ios-arrow-down"></Icon>
        </span>
        <DropdownMenu slot="list">
          <div v-if="search" style="padding:6px;">
            <Input v-model="searchWord" size="small" search placeholder="搜索成员" />
          </div>
          <DropdownItem v-for="item in itemList" v-if="item.label.indexOf(searchWord) > -1" :key="item.value"
            :name="item.value + ':' + item.label + ':' + item.color">
            <span v-if="showIcon" style="display: inline-flex; align-items: center; width: 20px;">
              <Icon :color="item.color" type="md-disc" />
            </span>
            <span style="display: inline-flex; align-items: center; width: 50px;">
              {{ item.label }}
            </span>
            <span v-if="item.value + '' === editValue + ''" style="display: inline-block;width: 20px; padding-left: 16px;">
              <Icon type="md-checkmark" />
            </span>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: 'labelEditorDropdownItem',
  props: ['itemList', 'displayText', 'itemID', 'value', 'displayColor', 'showIcon', 'search'],
  data() {
    return {
      editValue: '',
      searchWord: '',
      displayValue: '',
      textColor: '',
      editText: '',
      mouseHover: false,
      edited: false,
      show: true,
      readonly: true
    }
  },
  methods: {
    onMouseOver: function () {
      this.edited = true
    },

    onMouseOut: function () {
      //        this.mouseHover = false
      this.edited = false
    },

    onSelectValueChange: function (value) {
      this.editValue = value.value
      this.editText = value.label
    },

    setCaseTag: function (tag) {
      let tagItem = tag.split(':')
      this.displayValue = tagItem[1]
      this.textColor = tagItem[2]
      this.editValue = tagItem[0]
      this.$emit('updateValue', this.itemID, this.editValue)

    },

    onSearchMember: function (e) {
      //console.log(e)
    }

  },
  created: function () {
    this.editValue = this.value
    this.displayValue = this.displayText
    this.editText = this.displayText
  },
  watch: {
    displayText: function () {
      this.displayValue = this.displayText
      this.editText = this.displayText
    },
    displayColor: function (value) {
      //console.log(this.displayColor)
      this.textColor = this.displayColor
    },
    value: function () {
      this.editValue = this.value
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
/*.dropdown-editor:hover{*/
/*padding: 6px 6px 6px 6px;*/
/*}*/

.dropdown-editor {
  display: inline-flex;
}

.dropdown-editor:hover {
  background-color: #e8eaec;
  padding: 6px 6px 6px 0px;
}
</style>
