<template>
  <div style="float: right; padding-right: 80px;">
    <Page :total="tvPage.total" show-total :page-size="tvPage.pageSize" :current="tvPage.current" @on-change="changePage" />
  </div>
</template>

<script>
export default {
  name: 'TvPage',
  props: {
    page: {
      type: Array,
      required: true,
      // default: {
      // }
    },
  },
  data() {
    return {
      tvPage: {
        total: 1,
        current: 1,
        pageSize: 12,
      },
    }
  },
  methods: {
    changePage(page) {
      this.$emit('changePage', page)
    }
  },
  created: function () {
    this.tvPage = this.page
  }
}
</script>