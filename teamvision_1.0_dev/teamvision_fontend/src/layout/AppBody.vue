<template>
  <el-container :style="'height:' + appHeight + 'px;'">
    <el-header :height="appBodyHeaderHeight + 'px'" class="body-head">
      <router-view name="bodyhead"></router-view>
    </el-header>
    <el-main :style="'height:' + appBodyHeight + 'px'">
      <router-view name="bodybody" v-if="isRouterAlive"></router-view>
    </el-main>
  </el-container>
</template>

<script>
import BodyHead from "./BodyHead.vue";
import HomeProjectWebpart from "../components/HomeProjectWebpart.vue";
//import {initWebapp,getCookie} from './appBody.js'
import { mapGetters, mapMutations, mapState } from "vuex";
import Head from "../pages/home/<USER>";

export default {
  name: "AppBody",
  provide() { // 父组件中返回要传给下级的数据
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      msg: "Welcome to Your Vue.js App",
      isRouterAlive: true,
    };
  },

  computed: {
    ...mapState(['appHeight', 'appBodyHeight', 'appBodyHeaderHeight', 'appBodyMainHeight'])
  },

  methods: {
    ...mapMutations([]),
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function () {
        this.isRouterAlive = true
      })
    },
  },

  components: {
    BodyHead,
    HomeProjectWebpart,
    Head
  },
  created: function () {
  },

  // render:function(createElement){
  //     console.log('render')
  //     return createElement('div','hahaha')
  // },

  mounted: function () {
    //    this.$options.timer = setInterval(function () {
    //        console.log('setInterval')
    //         this.msg+='!'
    //    }.bind(this),500)
  },

};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import "../assets/teamvision/global/less/global";
@import "./appBody";
</style>

<style>
.el-main {
  width: 100%;
  height: 100%;
  padding: 0.5px 0px 0px 0px !important
}

.body-head .el-header {
  padding: 10px;
}
</style>
