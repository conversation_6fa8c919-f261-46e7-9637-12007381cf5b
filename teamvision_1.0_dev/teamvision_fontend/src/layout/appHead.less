@import '../assets/teamvision/global/less/app-global-variables.less';
@import '../assets/teamvision/global/less/global-base.less';


/* Style for header*************************************************************/


/*header整体设置*/

@head-item-top: 20px;

.app-head {
  /*background-color:#24272D;*/
  background-color: @app-head-background;
  height: @app-head-height;
  width: @app-head-width;
  z-index: @app-head-z-index;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, .05);
  //.doraemon-font();
  //top:0px;
  //border-left: 1px solid #f2f3f5;
  ///border-top: 1px solid #f2f3f5;
  //border-right: 1px solid #f2f3f5;
  text-align: center;
  line-height: @app-head-height;
  padding-left: 10px;
  padding-right: 10px;
}

.app-head-item {
  display: inline-block;
  //.doraemon-font(@body-font-family, @title-size, @header-font-color);
  font-weight: 400;
  opacity: 0.9;
  height: @app-head-height;
  //.font-family();
}

.app-head-item-right {
  display: inline-block;
  //.doraemon-font(@body-font-family, @title-size, @header-font-color);
  font-weight: 400;
  opacity: 0.9;
  height: @app-head-height;
  //.font-family();
  float: right;
  padding-right: 10px;
}


/*app header title 样式####################################################################################*/
.app-head-title {
  font-size: 16px;
  //padding: 0px 20px 0px  20px;
  width: 120px;
  display: block-inline;
  float: left;
}

/*header 菜单样式####################################################################################*/
.app-head-menu {
  padding-top: 4px;
  //padding-left:100px;
}

.app-head-menu-item {
  display: inline-block;
  //padding-top:@head-item-top;
  font-size: 16px;
  height: @app-head-menu-item-height;
  .font-family();
  float: left;
  min-width: 80px;
  color: hsla(0, 0%, 100%, .5);
  margin-left: 10px;
  margin-right: 10px;
}

.app-head-menu-item a {
  color: hsla(0, 0%, 100%, .5);
}

.app-head-menu-item-active {
  border-bottom: 2px solid #32be77;
  margin-left: 10px;
  margin-right: 10px;
  font-size: 18px;
}

.app-head-menu-item-active a {
  color: #fff;
  font-weight: 700;
}



/*header 设置样式####################################################################################*/
.app-head-settings {
  /*background-color:#24272D;*/
  padding-top: @head-item-top;
  //width:150px;
  //margin-right:20px;
}

//.app-head-system-admin ul li
//{
//  width: 40px;
//  padding: 0px 5px 0px 0px;
//  height: 25px;
//
//}
//
//.app-head-system-admin ul li i
//{
//
//  display:inline-block;
//  font-size:16px !important;
//  opacity:0.6;
//}


.ivu-menu-dark {
  background: #334866;
}

.el-menu-item {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  padding: 0 20px;
  cursor: pointer;
  -webkit-transition: border-color .3s, background-color .3s, color .3s;
  transition: border-color .3s, background-color .3s, color .3s;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}