<template>
  <div class="app-sider">
    <div class="sider-logo">
      <router-link to="/">
        <img src="../assets/teamvision/global/images/logo.jpg" style="width: 40px;height: 40px; border-radius: 20px;" />
      </router-link>
    </div>
    <div class="sider-menu">
      <Menu ref="sideMenu" :activeName="activeMenu" mode="vertical" theme="dark" width="60px" class="app-sider-menu">
        <MenuItem name="home" to="/home" class="ive-menu-item">
        <Tooltip content="需求协作" transfer placement="right" theme="light">
          <Icon type="ios-home" size="24" />
          <div>协作</div>
        </Tooltip>
        </MenuItem>
        <MenuItem name="project" to="/project" class="ive-menu-item">
        <Tooltip content="项目管理" transfer placement="right" theme="light">
          <Icon type="ios-bus" size="24" />
          <div>项目</div>
        </Tooltip>
        </MenuItem>
        <MenuItem name="ci" to="/ci" class="ive-menu-item">
        <Tooltip content="代码构建" transfer placement="right" theme="light">
          <Icon type="ios-planet-outline" size="24" />
          <div>构建</div>
        </Tooltip>
        </MenuItem>
        <MenuItem name="wiki" to="/wiki" class="ive-menu-item">
        <Tooltip content="wiki文档" transfer placement="right" theme="light">
          <Icon type="ios-bookmarks-outline" size="24" />
          <div>文档</div>
        </Tooltip>
        </MenuItem>
        <MenuItem v-if="dynamicMenu.show" :name="dynamicMenu.parameters.meta" :to="dynamicMenu.path"
          class="ive-menu-item">
        <Tooltip :content="dynamicMenu.text" transfer placement="right" theme="light">
          <Icon :type="dynamicMenu.icon" size="24" />
          <div>{{ dynamicMenu.text }}</div>
        </Tooltip>

        <!-- <router-link v-if="dynamicMenu.show" :to="dynamicMenu.path" tag="li" class="app-head-menu-item-active app-head-menu-item">
                <a href="/ci"><i :class="'fa fa-fw '+ dynamicMenu.icon"></i>{{ dynamicMenu.text }}</a>
              </router-link>
        -->
        </MenuItem>
        <!--<Submenu name="2" :style="{'text-align': 'start','margin-left':'-11px'}">-->
        <!--<template slot="title" :style="{'text-align': 'start'}" >-->
        <!--<Icon type="ios-cog-outline" />-->
        <!--<span>设置</span>-->
        <!--</template>-->
        <!--<MenuItem name="2-1" to="/system/user" style="padding-left: 24px;">-->
        <!--<Icon type="ios-cog-outline" />-->
        <!--<span>用户</span>-->
        <!--</MenuItem>-->
        <!--<MenuItem name="2-2" to="/system/setting" style="padding-left: 24px;">-->
        <!--<Icon type="ios-build-outline" />-->
        <!--<span>系统</span>-->
        <!--</MenuItem>-->
        <!--<MenuItem name="2-3"  to="/system/project/issueCategory" style="padding-left: 24px;">-->
        <!--<Icon type="ios-build-outline" />-->
        <!--<span>项目</span>-->
        <!--</MenuItem>-->
        <!--<MenuItem name="2-4"  to="/system/product" style="padding-left: 24px;">-->
        <!--<Icon type="ios-build-outline" />-->
        <!--<span>空间</span>-->
        <!--</MenuItem>-->
        <!--</Submenu>-->
      </Menu>
    </div>
    <div class="sider-setup">
      <el-menu router mode="vertical" :collapse="isCollapse" class="el-menu-vertical" background-color="#334866"
        text-color="#fff" @select="userAction">
        <el-submenu index="1">
          <template slot="title">
            <i class="el-icon-user"></i>
            <span slot="title">个人设置</span>
          </template>
          <el-menu-item-group>
            <span slot="title">个人设置</span>
            <el-menu-item index="ucenter" :route="'/ucenter/' + this.userInfo.id + '/profiles'">
              <template slot="title">
                <i class="el-icon-location"></i>
                <span>个人中心</span>
              </template>
            </el-menu-item>
            <el-menu-item index="logout">
              <template slot="title">
                <i class="el-icon-s-tools"></i>
                <span>退出登录</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-submenu>
      </el-menu>
      <el-menu :default-active="defSpace.id.toString()" mode="vertical" :collapse="isCollapse" class="el-menu-vertical"
        background-color="#334866" text-color="#fff" @select="changeSpace">
        <el-submenu index="1-5">
          <template slot="title">
            <i class="el-icon-finished"></i>
            <span slot="title">切换空间</span>
          </template>
          <el-menu-item-group>
            <span slot="title">切换空间</span>
            <el-menu-item v-for="space in spaceList" :key="space.id" :index="space.id.toString()">
              <template slot="title">
                <span>{{ space.Title }}</span>
                <i v-if="defSpace.id == space.id" class="el-icon-check"></i>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-submenu>
      </el-menu>
      <el-menu v-if="userInfo.system_permision < 2 || userInfo.is_space_admin" router mode="vertical"
        :collapse="isCollapse" class="el-menu-vertical" background-color="#334866" text-color="#fff">
        <el-submenu index="1">
          <template slot="title">
            <i class="el-icon-setting"></i>
            <span slot="title">系统设置</span>
          </template>
          <el-menu-item-group>
            <span slot="title">系统设置</span>
            <el-menu-item index="1-1" route="/system/user">
              <template slot="title">
                <i class="el-icon-location"></i>
                <span>用户管理</span>
              </template>
            </el-menu-item>
            <el-menu-item index="1-2" route="/system/setting">
              <template slot="title">
                <i class="el-icon-s-tools"></i>
                <span>系统设置</span>
              </template>
            </el-menu-item>
            <el-menu-item index="1-3" route="/system/project/issueCategory">
              <template slot="title">
                <i class="el-icon-star-on"></i>
                <span>项目管理</span>
              </template>
            </el-menu-item>
            <el-menu-item index="1-4" route="/system/product">
              <template slot="title">
                <i class="el-icon-menu"></i>
                <span>空间管理</span>
              </template>
            </el-menu-item>
          </el-menu-item-group>
        </el-submenu>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'

export default {
  name: 'AppSider',
  inject: [
    'reload'
  ],
  data() {
    return {
      activeMenu: '',
      isCollapse: true,
    }
  },
  computed: {
    ...mapState(['appBodyHeight', 'appBodyTop', 'dynamicMenu', 'spaceList']),
    ...mapState('usercenter', ['isLogin', 'userInfo', 'defSpace']),
  },

  methods: {
    ...mapMutations('usercenter', ['setLogin', 'setDefSpace']),
    ...mapActions(['getSpaceList', 'getSpaceUserList']),
    ...mapActions('usercenter', ['handleLogOut', 'getUserInfo']),
    ...mapActions('project', ['loadMyProjectList']),
    ...mapActions('issue', ['getIssueStatus', 'getIssueSeverity', 'getIssuePhrase', 'getIssueCategories', 'getIssuePriority']),

    changeSpace(spaceID, keyPath) {
      var space = this.spaceList.find((v) => v.id == spaceID)
      if (this.defSpace.id != spaceID) {
        let selectSpace = {
          'id': space.id,
          'Title': space.Title
        }

        localStorage.setItem("ProductSpace", JSON.stringify(selectSpace))
        this.setDefSpace(selectSpace)
        let parameters = { default_space: space.id };
        this.$axios.patch("/api/auth/user/" + this.userInfo.id + "/extend", parameters).then((response) => {
          this.getUserInfo()
          this.loadMyProjectList();
          if (this.$route.matched[0].meta == 'project') {
            this.$router.push('/project')
          } else {
            this.reload()
          }
        },
        );
      }
    },

    userAction(key, keyPath) {
      if (key === 'logout') {
        this.handleLogOut()
        this.$router.push({ path: "/login" });
      }
    }
  },

  created: function () {
    this.getSpaceList();
    this.getSpaceUserList();
    this.loadMyProjectList();
    //console.log('ProjectTesting.vue created')
    if (this.$route.meta == 'project') {
      this.activeMenu = 'project'
    }
    this.projectID = parseInt(this.$route.params.projectID);
    this.getIssueStatus();
    this.getIssueSeverity();
    this.getIssuePhrase();
    this.getIssueCategories();
    this.getIssuePriority();
  },

  mounted: function () {
    switch (this.$route.matched[0].meta) {
      case 'home':
        this.activeMenu = 'home'
        break;
      case 'project':
        this.activeMenu = 'project'
        break;
      case 'ci':
        this.activeMenu = 'ci'
        break;
      case 'wiki':
        this.activeMenu = 'wiki'
        break;
      case 'system':
        this.activeMenu = 'system'
        break
      default:
        this.activeMenu = this.$route.matched[0].meta
        break;
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped lang="less">
@import './appSider.less';
</style>