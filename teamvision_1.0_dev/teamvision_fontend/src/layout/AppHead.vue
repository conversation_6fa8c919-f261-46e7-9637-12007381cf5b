<template>
  <div class="app-head">
    <div v-if="appHeadShow" class="app-head-title">
      <el-dropdown trigger="click" @command="changeSpace">
        <span class="el-dropdown-link">
          {{ defSpace.Title }} <i class="el-icon-caret-bottom"></i>
        </span>
        <el-dropdown-menu v-if="spaceList.length > 1" slot="dropdown">
          <el-dropdown-item v-for="space in spaceList" :key="space.id" :command="space">{{ space.Title
            }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="app-head-item-right">
      <el-dropdown trigger="click" v-if="userInfo.system_permision < 2 || userInfo.is_space_admin">
        <i style="font-size:16px; opacity:0.6;" class="fa fa-cogs fa-fw fa-lg cursor-hand"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item icon="el-icon-user-solid">
            <router-link style="color:#333" to="/system/user">
              <a style="color:inherit" href="#">用户管理</a>
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item icon="el-icon-s-tools" v-if="userInfo.system_permision < 2">
            <router-link style="color:#333" to="/system/setting">
              <a style="color:inherit" href="#">系统设置</a>
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item icon="el-icon-star-on" v-if="userInfo.system_permision < 2">
            <router-link style="color:#333" to="/system/project/issueCategory"
              active-class="app-body-head-menu-item-active" class="app-body-head-menu-item">
              <a style="color:inherit" href="">项目管理</a>
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item icon="el-icon-menu" v-if="userInfo.system_permision < 2">
            <router-link style="color:#333" to="/system/product" active-class="app-body-head-menu-item-active"
              class="app-body-head-menu-item">
              <a style="color:inherit" href="">空间管理</a>
            </router-link>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown @command="userAction" style="margin-left: 20px" placement="bottom-end">
        <i style="font-size:16px;opacity:0.6;" class="fa fa-user-o fa-fw fa-lg cursor-hand"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item name="ucenter" icon="el-icon-s-home">
            <router-link style="color:#333" :to="'/ucenter/' + userInfo.id + '/profiles'">个人中心</router-link>
          </el-dropdown-item>
          <el-dropdown-item name="logout" icon="el-icon-s-promotion" command="logout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations, mapActions } from "vuex";

export default {
  name: "AppHead",
  inject: [
    'reload'
  ],
  components: {
  },

  data() {
    return {
      systemPermission: 99,
      userID: 0,
      // showHeadProject: false,
      // projectID: 0,
      router: null,
    };
  },

  computed: {
    ...mapState(['appHeadShow', 'dynamicMenu', 'spaceList']),
    ...mapState('usercenter', ['isLogin', 'userInfo', 'defSpace']),
  },

  methods: {
    ...mapMutations('usercenter', ['setLogin', 'setDefSpace']),
    ...mapActions(['getSpaceList']),
    ...mapActions('usercenter', ['handleLogOut', 'getUserInfo']),
    ...mapActions('project', ['loadMyProjectList']),

    changeSpace: function (space) {
      //console.log(this.$route.matched[0].meta == 'project')
      if (this.defSpace.id != space.id) {
        let selectSpace = {
          'id': space.id,
          'Title': space.Title
        }
        localStorage.setItem("ProductSpace", JSON.stringify(selectSpace))
        this.setDefSpace(selectSpace)
        let parameters = { default_space: space.id };
        this.$axios.patch("/api/auth/user/" + this.userInfo.id + "/extend", parameters).then((response) => {
          this.getUserInfo()
          this.loadMyProjectList();
          if (this.$route.matched[0].meta == 'project') {
            this.$router.push('/project')
          } else {
            this.reload()
          }
        },
        );
      }
    },

    userAction: function (value) {
      if (value === "logout") {
        this.handleLogOut()
        this.$router.push({ path: "/login" });

        // this.$axios.delete("/api/common/user/logout").then(
        //   (response) => {
        //     this.setLogin(false);
        //     this.$router.push({ path: "/login" });
        //   }
        // );
      }
    },

  },

  created: function () {
    this.getSpaceList();
    this.loadMyProjectList();
  },

  mounted: function () {
  },

  watch: {
    $route(to, from) {
      if (to.name !== "projectRoot" && to.name.indexOf("project") > -1) {
        this.showHeadProject = true;
        this.projectID = to.params.projectID;
        this.router = to;
      } else {
        this.showHeadProject = false;
        this.projectID = 0;
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
@import "./appHead.less";
</style>
