/*通用够公共变量*/
/*母版页header属性设置*/
/*系统 body 页面字体*/
/*页面主容器属性*/
/*页面左边栏属性变量*/
/*left subnav 变量*/
/*page conent container 样式*/
/*list item分隔线颜色*/
/* 草绿色button 样式*/
/* Style for sider*************************************************************/
/*sider整体设置*/
.app-sider {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* 设定侧边栏的高度为视口高度 */
}
.sider-logo {
  background-color: #334866;
  text-align: center;
  padding-top: 8px;
  width: 100%;
  /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  align-self: flex-start;
  /* 将第一个和第二个 div 上对齐 */
}
.sider-menu {
  width: 100%;
  /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  align-self: flex-start;
  /* 将第一个和第二个 div 上对齐 */
}
.sider-menu .app-sider-menu {
  height: 100%;
}
.sider-menu .app-sider-menu .ive-menu-item {
  background-color: #334866;
  font-size: 12px;
  padding: 14px 14px;
}
.sider-setup {
  width: 100%;
  /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  margin-top: auto;
  /* 将第三个 div 的顶部向下推至底部对齐 */
}
.sider-setup .el-menu {
  border-right: #5578aa;
}
.sider-setup .el-menu .el-menu-item {
  height: 36px;
  line-height: 36px;
}
.sider-setup .el-menu-vertical-demo {
  width: 200px;
  min-height: 400px;
}
.sider-setup .el-menu--collapse {
  width: 60px;
}
.sider-setup .ivu-menu-submenu-title {
  padding: 14px 20px;
}
