@import '../assets/teamvision/global/less/app-global-variables.less';

/* Style for sider*************************************************************/

/*sider整体设置*/

.app-sider{
  display: flex;
  flex-direction: column;
  height: 100vh; /* 设定侧边栏的高度为视口高度 */
}

.sider-logo{
  background-color: @app-background;
  text-align: center;
  padding-top:8px;
  width: 100%; /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  align-self: flex-start; /* 将第一个和第二个 div 上对齐 */
}

.sider-menu{
  width: 100%; /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  align-self: flex-start; /* 将第一个和第二个 div 上对齐 */

  .app-sider-menu{
    height: 100%;
    .ive-menu-item{
      background-color: #334866;
      font-size: 12px;
      padding: 14px 14px
    };
    // .el-menu-item {
    //   font-size: 16px;
    //   font-weight:bold;
    //   //color: #303133;
    //   padding: 0 20px;
    //   cursor: pointer;
    //   -webkit-transition: border-color .3s,background-color .3s,color .3s;
    //   transition: border-color .3s,background-color .3s,color .3s;
    //   -webkit-box-sizing: border-box;
    //   box-sizing: border-box;
    // }

  }
}

.sider-setup{
  width: 100%; /* 设置每个 div 元素的宽度为父容器的宽度 */
  box-sizing: border-box;
  margin-top: auto; /* 将第三个 div 的顶部向下推至底部对齐 */
  .el-menu {
    border-right:#5578aa;

    .el-menu-item {
      height: 36px;
      line-height: 36px;
    }
  }

  .el-menu-vertical-demo {
    width: 200px;
    min-height: 400px;
  }

  .el-menu--collapse{
    width: 60px;
  }
  .ivu-menu-submenu-title{
    padding: 14px 20px
  }
}

