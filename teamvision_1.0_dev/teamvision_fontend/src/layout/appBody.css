/*通用够公共变量*/
/*母版页header属性设置*/
/*系统 body 页面字体*/
/*页面主容器属性*/
/*页面左边栏属性变量*/
/*left subnav 变量*/
/*page conent container 样式*/
/*list item分隔线颜色*/
/* 草绿色button 样式*/
/*通用字体类*/
/*左侧子导航样式#########################################################################*/
.left_sub_menu_container {
  position: fixed;
}
.left_sub_menu_container ul {
  width: 160px;
  text-align: center;
}
.left_sub_menu_item {
  color: #555 !important;
  font-size: 13px;
}
.left_sub_menu_button {
  color: #555 !important;
  font-size: 13px;
}
.left_sub_menu_item_hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.left_sub_meun_active {
  background-color: rgba(0, 0, 0, 0.05) !important;
}
.leftmenu-plus {
  width: 90% !important;
  margin-left: 5px;
  margin-top: 10px;
  padding-left: -5px;
  border: 2px dashed #a1a3a6;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  font-size: 15px !important;
}
.button-inlist {
  height: 25px;
  margin-left: 10%;
  margin-right: 35%;
  margin-bottom: 10px;
  padding-top: 3px !important;
  font-size: 14px;
  border-radius: 15px;
  background-color: #32be77 !important;
  opacity: 0.8;
  color: #FFF !important;
}
.remove_icon-inlist {
  color: #d9534f;
  opacity: 0.8;
  display: none;
  padding-top: 10px;
  margin-right: -10px;
}
.sub_side_bar {
  width: 160px;
  float: left;
  height: 100%;
  padding: 0px 0px 0px 0px;
  position: fixed;
  border-right: 1px solid #f0f0f0;
}
.sub_sidebar_brother {
  padding-left: 140px;
  width: 100%;
  float: left;
}
.sub_bar_header {
  padding: 20px 10px 20px 30px;
}
body {
  margin: 0px 0px 0px 0px;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  background-color: #eef2f6;
  font-family: '微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
  display: block;
  height: 100%;
  color: #5578aa;
}
#mainContainer {
  height: 89%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  padding: 60px 0 0 0;
  width: 100%;
}
/*默认按钮样式*/
.button {
  width: 50px;
  line-height: 20px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 1px #333;
  border-radius: 5px;
  /*margin:0 20px 20px 0;*/
  position: relative;
  overflow: hidden;
}
.button.gray {
  color: #8c96a0;
  text-shadow: 1px 1px 1px #fff;
  border: 1px solid #dce1e6;
  box-shadow: 0 1px 2px #fff inset, 0 -1px 0 #a8abae inset;
  background: -webkit-linear-gradient(to top, #f2f3f7, #e4e8ec);
  background: -moz-linear-gradient(to top, #f2f3f7, #e4e8ec);
  background: linear-gradient(to top, #f2f3f7, #e4e8ec);
}
.operationbutton {
  cursor: pointer;
}
.listview-window-container {
  padding: 20px 20px 20px 20px;
}
.listview-window {
  width: 100%;
  height: 100%;
}
.listview-window-title {
  border-bottom: 1px solid #eef2f6;
  font-weight: 400;
  font-size: 16px;
  padding-top: 10px;
  margin-bottom: 20px;
  height: 40px;
}
.android-color {
  color: #1d953f;
}
.left_sub_nav {
  width: 160px;
  float: left;
  height: 100%;
  padding: 0px 0px 0px 0px;
  position: fixed;
  border-right: 1px solid #f0f0f0;
}
.unfinished-check {
  color: gray;
  margin-right: 30px;
}
.finished-check {
  color: #019775;
  margin-right: 30px;
}
.postion-absolute {
  position: absolute;
}
.filed-pop-calender {
  position: absolute;
  margin-top: 5px;
  opacity: 0.9;
  z-index: 9000;
  border-radius: 7px;
  box-shadow: -5px 5px 5px rgba(0, 0, 0, 0.1);
}
.filed-pop-meun {
  position: absolute;
  margin-top: 5px;
  opacity: 0.9;
  z-index: 9000;
  border-radius: 7px;
  box-shadow: -5px 5px 5px rgba(0, 0, 0, 0.1);
}
.middle-modal-dialog {
  margin-top: 0px;
}
.modal-dialog-alert-middle {
  margin-top: 40%;
}
a {
  color: #FFA487;
  cursor: pointer;
}
.status-success {
  color: #32be77;
}
.status-fail {
  color: red;
}
.status-default {
  color: #009ad6;
  opacity: 0.7;
}
.status-cancel {
  color: #f58220;
  opacity: 0.7;
}
.status-background-success {
  background-color: #32be77;
}
.status-background-fail {
  background-color: red;
}
.status-background-default {
  background-color: #009ad6;
  opacity: 0.7;
}
.status-background-cancel {
  background-color: #f58220;
  opacity: 0.7;
}
.body-head {
  background-color: #ffffff;
  height: 48px;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
  text-align: center;
  line-height: 48px;
  padding: 0 0 0 0;
}
.app-body-view-default {
  background-color: #f5f7f9;
  padding: 0px;
  margin: 0px;
  position: fixed;
  width: 100%;
}
.app-body-head-default {
  height: 48px;
  border-bottom: px solid #eef2f6;
  background-color: #eef2f6;
  box-sizing: border-box;
  margin: 0px;
  position: fixed;
  width: 100%;
  padding: 0 0 0 0;
  width: inherit;
}
.app-body-head-menu {
  list-style: none;
  margin-top: 0px;
  text-align: center;
}
.app-body-head-menu-item {
  min-width: 90px;
  list-style-type: none;
  float: left;
  display: block;
  font-size: 16px;
}
.app-body-head-menu-item a {
  font-family: '微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
  font-size: 14px;
  color: #6a6c6f;
}
.app-body-default {
  padding: 0px 0px 10px 0px;
  margin: 0px;
  overflow-x: auto;
  overflow-y: hidden;
  border-bottom: 1px solid #eef2f6;
  background-color: #f5f7f9;
  position: fixed;
  width: 100%;
  top: 115px;
  bottom: 5px;
  white-space: nowrap;
  width: inherit;
  text-align: left;
}
.app-body-header-bar-default {
  width: 100%;
  height: 48px;
  background-color: #ffffff;
  text-align: center;
  line-height: 48px;
}
.app-body-header-leftbar-default {
  width: auto;
  padding: 0px 0px 0px 0px;
  padding-left: 0px;
}
.app-body-header-rightbar-default {
  width: 200px;
  padding: 0px 0px 0px 0px;
}
.app-body-header-bar-item-default {
  opacity: 0.8;
  padding-left: 5px;
  padding-right: 5px;
}
.app-body-header-bar-active {
  opacity: 1 !important;
}
.app-body-right-panel-default {
  width: 60%;
  height: 850px;
  top: 60px;
  position: fixed;
  right: 0px;
  background-color: #fff;
  box-shadow: -15px 0 5px rgba(0, 0, 0, 0.1);
  display: none;
}
.app-body-head-menu-item-active {
  border-bottom: 2px solid #515a6e;
  color: #515a6e;
  font-size: 16px;
}
.project-body-head-menu-project {
  min-width: 80px;
  list-style-type: none;
  float: left;
  display: block;
  font-size: 16px;
}
.project-body-head-menu-item {
  min-width: 80px;
  list-style-type: none;
  float: left;
  display: block;
  font-size: 16px;
}
.project-body-head-menu-item a {
  font-family: '微软雅黑', 'Helvetica Neue', sans-serif, SimHei;
  font-size: 14px;
  color: #6a6c6f;
}
