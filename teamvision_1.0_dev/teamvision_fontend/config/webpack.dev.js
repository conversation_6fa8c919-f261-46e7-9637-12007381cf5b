const baseConfig = require('./webpack.common')
const { merge } = require('webpack-merge')
const path = require('path')

module.exports = merge(baseConfig(false), {
  mode: 'development',
  devtool: 'source-map',
  devServer: {
    hot: true,
    host: '127.0.0.1',
    port: 8080,
    open: false,
    compress: true,
    historyApiFallback: true,
    client: {
      overlay: {
        warnings: false,
        errors: true
      },
    },
    static: {
      directory: path.join(__dirname, '/'),
      publicPath: "/assets/"
    },
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
      },
      '/static': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
      },
      '/assets': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
      },
      '/materils_v2.0': {
        target: 'http://************:8080/',
        changeOrigin: true,
      }
    },
  },
  optimization: {
    minimize: true,
  }
})
