const baseConfig = require('./webpack.common.js')
const { merge } = require('webpack-merge')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin")
const webpack = require('webpack')
const { PurgeCSSPlugin } = require('purgecss-webpack-plugin')
const glob = require('glob')
const CompressionPlugin = require('compression-webpack-plugin')
const TerserWebpackPlugin = require('terser-webpack-plugin')

module.exports = merge(baseConfig(true), {
  mode: 'production',
  devtool: 'cheap-module-source-map',
  plugins: [
    new CleanWebpackPlugin(),
    new CopyWebpackPlugin({
      patterns: [{
        from: 'public',
        to: 'public',
        globOptions: {
          ignore: ['**/index.html']
        }
      }]
    }),
    new MiniCssExtractPlugin({
      filename: 'css/[name].[fullhash].css',
      chunkFilename: 'css/[id].[fullhash].css',
    }),
    new CompressionPlugin({
      test: /\.(css|js)$/i,
      algorithm: 'gzip'
    })
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 200000,
      maxSize: 200000,
      minChunks: 3,
      cacheGroups: {
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/, //符合组的要求就给构建venders
          priority: -10, //优先级用来判断打包到哪个里面去
          name: "vendors", //指定chunks名称
        },
      }
    },

    minimizer: [
      new CssMinimizerPlugin(),
      new TerserWebpackPlugin({
        extractComments: false // 不要注释生成的文件
      })
    ],
  },

})
