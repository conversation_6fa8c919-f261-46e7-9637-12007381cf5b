const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const { VueLoaderPlugin } = require('vue-loader')
const resolveDir = require('./paths')
const ESLintWebpackPlugin = require('eslint-webpack-plugin')

module.exports = (isProduction) => {
  const cssFinalLoader = isProduction ? MiniCssExtractPlugin.loader : 'style-loader'

  return {
    entry: {
      app: './src/main.js',
    },
    output: {
      path: resolveDir('dist'),
      filename: 'js/[name].[chunkhash].js',
      chunkFilename: 'js/[id].chunk.[chunkhash].js',
      publicPath: '/',
    },
    resolve: {
      extensions: ['.js', '.jsx', '.vue', '.json',],
      alias: {
        '@': resolveDir('src'),
        'vue$': 'vue/dist/vue.esm.js',
      }
    },
    externals: {
      // 不希望依赖打进包中，走外链cdn等
      // '$': 'Jquery',
      // react: 'React',
      // antd: 'antd',
    },
    module: {
      rules: [
        {
          test: /\.vue$/,
          use: 'vue-loader'
        },
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: 'babel-loader'
        },
        {
          test: /\.css$/,
          use: [
            cssFinalLoader,
            {
              loader: 'css-loader',
              options: {
                esModule: false, // css不使用esModule，直接输出
                importLoaders: 1 // 使用本loader前使用1个其他处理器
              }
            },
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    'postcss-preset-env'
                  ]
                }
              }
            }
          ],
          sideEffects: true // 希望保留副作用
        },
        {
          test: /\.less$/,
          use: [
            cssFinalLoader,
            {
              loader: 'css-loader',
              options: {
                importLoaders: 2
              }
            },
            'postcss-loader', 'less-loader'
          ]
        },
        {
          test: /\.(png|gif|jpe?g|svg)$/,
          type: 'asset', // webpack5使用内置静态资源模块，且不指定具体，根据以下规则 使用
          generator: {
            filename: 'img/[name][ext]' // ext本身会附带 点，放入img目录下
          },
          parser: {
            dataUrlCondition: {
              maxSize: 10 * 1024 // 超过10kb的进行复制，不超过则直接使用base64
            }
          }
        },
        {
          test: /\.(ttf|woff2?|eot)$/,
          type: 'asset/resource', // 指定静态资源类复制
          generator: {
            filename: 'font/[name].[chunkhash][ext]' // 放入font目录下
          }
        },
      ]
    },
    plugins: [
      new VueLoaderPlugin(),
      new webpack.DefinePlugin({
        BASE_URL: './'
      }),
      new HtmlWebpackPlugin({
        template: 'src/pages/index.html'
      }),
      new ESLintWebpackPlugin({
        context: resolveDir('src')
      })
    ],
    optimization: {
      runtimeChunk: 'single', // 模块抽取，利用浏览器缓存
      chunkIds: 'named',
      minimizer: []
    }
  }
}
