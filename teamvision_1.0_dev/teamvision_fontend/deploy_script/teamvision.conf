upstream websocket {
    server 127.0.0.1:8066;
}

upstream webapi {
    server 127.0.0.1:8055;
}

map $http_upgrade $connection_upgrade {
    default upgrade;
    "" close;
}

server {
    listen      80;
    server_name  teamcat.com;
    gzip on;
    gzip_types *;
    gzip_comp_level 6;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_http_version 1.1;
    gzip_disable "MSIE[1-6]\.";
    gzip_proxied off;
    gzip_buffers 4 16k;
    #charset koi8-r;
    access_log  /data/web/www/teamvision/logs/access.log ;
    error_log   /data/web/www/teamvision/logs/error.log  ;

    location / {
        root /data/web/www/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        if ($request_filename ~* .*\.(js|css|woff|png|jpg|jpeg)$) {
            expires    30d; 
        }
     
        if ($request_filename ~* .*\.(?:htm|html)$) {
            add_header Cache-Control "no-store";
        }
    }

    location  ^~ /api/ {
        include  uwsgi_params;
        uwsgi_pass  webapi;
        client_max_body_size 500m;
        expires -1;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 300;
    }

    location /static {
        alias /data/web/www/teamvision/teamvision/static; 
        expires 30d;
    }

    location /materils_v2.0 {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_pass http://10.146.0.157:8080;
    }

    location /ws {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_pass http://websocket;
        #include  uwsgi_params;
        #uwsgi_pass  websocket;
    }

}
