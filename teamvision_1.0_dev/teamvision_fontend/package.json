{"name": "teamvision_fontend", "version": "1.0.0", "description": "A Vue.js project", "author": "iquality <<EMAIL>>", "private": true, "scripts": {"dev": "webpack serve --progress --config config/webpack.dev.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --ext .js,.vue src test/unit test/e2e/specs", "build": "webpack --config config/webpack.prod.js --mode=production", "build:dev": "webpack --progress --config config/webpack.dev.js"}, "dependencies": {"@babel/polyfill": "^7.0.0", "@babel/preset-react": "^7.23.3", "@fortawesome/vue-fontawesome": "^2.0.10", "@progress/kendo-datasource-vue-wrapper": "^2023.3.1010", "@progress/kendo-dialog-vue-wrapper": "^2023.3.1010", "@progress/kendo-popups-vue-wrapper": "^2023.3.1010", "@progress/kendo-theme-material": "^7.2.0", "@progress/kendo-treeview-vue-wrapper": "^2023.3.1010", "@progress/kendo-ui": "^2023.3.1114", "@progress/kendo-window-vue-wrapper": "^2023.3.1010", "@tinymce/tinymce-vue": "^3.2.8", "axios": "^1.6.7", "babel-loader": "^9.1.3", "babel-plugin-syntax-jsx": "^6.18.0", "clipboard": "^2.0.11", "echarts": "^5.4.3", "element-ui": "^2.8.2", "fast-json-patch": "^3.1.1", "font-awesome": "^4.7.0", "highcharts": "^11.3.0", "html-webpack-plugin": "^5.6.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "portfinder": "^1.0.32", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.0", "tinymce": "^5.10.9", "url-loader": "^4.1.1", "view-design": "^4.7.0", "vue": "^2.7.16", "vue-axios": "^3.5.2", "vue-kityminder-editor": "^0.1.4", "vue-loader": "^15.9.8", "vue-router": "^3.6.5", "vue-video-player": "^5.0.1", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "webpack": "^5.90.3", "webpack-cli": "^5.1.4"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.23.3", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.23.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.23.9", "@babel/register": "^7.23.7", "@babel/runtime": "^7.23.9", "@nuxt/friendly-errors-webpack-plugin": "^2.6.0", "autoprefixer": "^10.4.17", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^9.0.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^29.7.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-vue-jsx": "^3.7.0", "chalk": "^5.3.0", "chromedriver": "^121.0.0", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^12.0.2", "cross-spawn": "^7.0.3", "css-loader": "^6.10.0", "css-minimizer-webpack-plugin": "^6.0.0", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^4.1.0", "eslint-plugin-vue": "^9.20.1", "eslint-webpack-plugin": "^4.0.1", "file-loader": "^6.2.0", "iview-loader": "^1.3.0", "jest": "^29.7.0", "jest-serializer-vue": "^3.1.0", "jquery": "^3.7.1", "less": "^4.2.0", "less-loader": "^7.3.0", "mini-css-extract-plugin": "^2.7.7", "nightwatch": "^3.4.0", "node-notifier": "^10.0.1", "ora": "^8.0.1", "popper.js": "^1.14.2", "postcss-import": "^16.0.0", "postcss-url": "^10.1.3", "purgecss-webpack-plugin": "^5.0.0", "rimraf": "^5.0.5", "selenium-server": "^3.141.59", "semver": "^7.5.4", "shelljs": "^0.8.5", "style-loader": "^3.3.4", "stylus-loader": "^8.0.0", "url": "^0.11.3", "vue-jest": "^3.0.7", "vue-style-loader": "^4.1.3", "webpack-bundle-analyzer": "^4.10.1", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0"}, "engines": {"node": ">= v14.21.3", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}