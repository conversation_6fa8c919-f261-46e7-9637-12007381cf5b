'use strict'
const path = require('path')
const webpack = require('webpack')
const utils = require('./utils')
const config = require('./config')
const vueLoaderConfig = require('./vue-loader.conf')
const ExtractTextPlugin = require('extract-text-webpack-plugin');

function resolve(dir) {
  return path.join(__dirname, '..', dir)
}

const createLintingRule = () => ({
  test: /\.(js|vue)$/,
  loader: 'eslint-loader',
  enforce: 'pre',
  include: [resolve('src'), resolve('test')],
  options: {
    formatter: require('eslint-friendly-formatter'),
    emitWarning: !config.dev.showEslintErrorsInOverlay
  }
})

const extractCSS = new ExtractTextPlugin({
  filename: 'src/assets/font-awesome/css/[name]-css-[chunkHash:5].css',
  allChunks: true
});

const extractLESS = new ExtractTextPlugin({
  filename: 'src/assets/font-awesome/css/[name]-css-[chunkHash:5].css',
  allChunks: true
});

module.exports = {
  context: path.resolve(__dirname, '../'),
  entry: {
    app: './src/main.js',
  },
  output: {
    path: config.build.assetsRoot,
    filename: '[name].js',
    publicPath: process.env.NODE_ENV === 'production'
      ? config.build.assetsPublicPath
      : config.dev.assetsPublicPath
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
      'vue$': 'vue/dist/vue.esm.js',
      '@': resolve('src'),
    }
  },
  module: {
    rules: [
      // ...(config.dev.useEslint ? [createLintingRule()] : []),
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: vueLoaderConfig
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        include: [resolve('src'), resolve('test'), resolve('node_modules/webpack-dev-server/client')]
      },
      {
        test: /\.css$/,
        loader: 'style-loader!css-loader!less-loader?',
        include: [resolve('src/assets/font-awesome')]
      },
      // {
      //   test: /\.css$/,
      //   use: extractCSS.extract({
      //     fallback: 'style-loader',
      //     use: 'css-loader'
      //   })
      //   // use: ['style-loader', 'css-loader']
      // },
      // {
      //   test: /\.less$/,
      //   use: extractLESS.extract({
      //     fallback: 'style-loader',
      //     use: ['css-loader', 'less-loader']
      //   })
      //   // use: ['style-loader', 'css-loader', 'less-loader']
      // },

      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('img/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('media/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(eot|svg|ttf|woff|woff2)\w*/,
        loader: 'url-loader?limit=1000000'
      },
      // {
      //   test: /\.svg$/,
      //   use: [
      //     {loader: 'svg-sprite-loader', options: {}},
      //     {loader: 'svgo-loader', options: {
      //       plugins: [
      //         // 还有很多配置，具体可以查看https://github.com/svg/svgo
      //         { removeViewBox: false },
      //         { removeXMLNS: true }
      //       ]
      //     }
      //     }
      //   ]
      // }
    ]
  },
  plugins: [
    new webpack.ProvidePlugin({
      $: 'jquery',
      jQuery: 'jquery'
    })

    // extractCSS,
    // extractLESS
  ],
  node: {
    // prevent webpack from injecting useless setImmediate polyfill because Vue
    // source contains it (although only uses it if it's native).
    setImmediate: false,
    // prevent webpack from injecting mocks to Node native modules
    // that does not make sense for the client
    dgram: 'empty',
    fs: 'empty',
    net: 'empty',
    tls: 'empty',
    child_process: 'empty'
  }
}
